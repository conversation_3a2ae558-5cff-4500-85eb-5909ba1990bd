# TawkTo Chat Widget Setup Guide

This guide will help you set up the TawkTo chat widget for your application.

## Step 1: Create a TawkTo Account

1. Go to [TawkTo.com](https://www.tawk.to/) and sign up for a free account
2. Complete the registration process

## Step 2: Create a Property

1. After logging in, you'll be prompted to create a property
2. Enter your website details:
   - **Property Name**: Your website/app name (e.g., "RF Learn")
   - **Website URL**: Your domain (e.g., "https://rflearn.com" or "http://localhost:8080" for development)
3. Click "Add Property"

## Step 3: Get Your Widget Configuration

1. In your TawkTo dashboard, go to **Administration** → **Chat Widget**
2. You'll see a JavaScript code snippet that looks like this:

```javascript
<!--Start of Tawk.to Script-->
<script type="text/javascript">
var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
(function(){
var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
s1.async=true;
s1.src='https://embed.tawk.to/PROPERTY_ID/WIDGET_ID';
s1.charset='UTF-8';
s1.setAttribute('crossorigin','*');
s0.parentNode.insertBefore(s1,s0);
})();
</script>
<!--End of Tawk.to Script-->
```

3. From the URL `https://embed.tawk.to/PROPERTY_ID/WIDGET_ID`, extract:
   - **PROPERTY_ID**: The first part (e.g., `6845d5f5eafd4a190b35bb6c`)
   - **WIDGET_ID**: The second part (e.g., `1it8cnijc`)

## Step 4: Configure Environment Variables

1. Copy `.env.example` to `.env` (if you haven't already)
2. Update the TawkTo configuration in your `.env` file:

```env
# TawkTo Chat Widget Configuration
VITE_TAWKTO_ENABLED=true
VITE_TAWKTO_PROPERTY_ID=your_actual_property_id_here
VITE_TAWKTO_WIDGET_ID=your_actual_widget_id_here
```

## Step 5: Test the Widget

1. Restart your development server
2. Open your homepage
3. You should see a small chat bubble in the bottom-right corner
4. Check the browser console for TawkTo debug messages

## Troubleshooting

### Widget Not Appearing
- Check browser console for errors
- Verify your Property ID and Widget ID are correct
- Make sure `VITE_TAWKTO_ENABLED=true` in your `.env` file
- Restart your development server after changing environment variables

### 404 Error on Script Load
- Double-check your Property ID and Widget ID from the TawkTo dashboard
- Make sure your property is active in TawkTo

### Widget Appears But No Chat History
- This is normal for new properties
- Test by sending a message from the widget

## Customization Options

You can customize the widget appearance in your TawkTo dashboard:
- **Administration** → **Chat Widget** → **Appearance**
- Change colors, position, and behavior
- Set up automated messages and triggers

## Production Deployment

For production, make sure to:
1. Update your property settings in TawkTo with your production domain
2. Set the correct environment variables in your production environment
3. Test the widget on your live site

## Support

If you need help with TawkTo setup:
- Visit [TawkTo Help Center](https://help.tawk.to/)
- Contact TawkTo support through their dashboard

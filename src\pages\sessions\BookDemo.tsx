import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, useLocation } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/Tabs";
import { Calendar } from "@/components/ui/Calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Badge } from "@/components/ui/Badge";
import { Textarea } from "@/components/ui/TextArea";
import { useToast } from "@/hooks/use-toast";
import {
  Star,
  Clock,
  Calendar as CalendarIcon,
  MapPin,
  BookOpen,
  CheckCircle,
  Clock as ClockIcon,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext"; // Import auth context
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";
import { Label } from "@/components/ui/Label";
import { Input } from "@/components/ui/Input";
import { supabase } from "@/lib/supabaseClient";
import { tutors } from "@/pages/tutor/Search";

// Get day of week from date
const getDayOfWeek = (date: Date) => {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  return days[date.getDay()];
};

// Available time slots
const timeSlots = [
  "9:00 AM",
  "10:00 AM",
  "11:00 AM",
  "1:00 PM",
  "2:00 PM",
  "3:00 PM",
  "4:00 PM",
  "5:00 PM",
];

// Timezone options
const timezones = [
  { value: "America/New_York", label: "Eastern Time (ET)" },
  { value: "America/Chicago", label: "Central Time (CT)" },
  { value: "America/Denver", label: "Mountain Time (MT)" },
  { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
  { value: "America/Anchorage", label: "Alaska Time (AKT)" },
  { value: "Pacific/Honolulu", label: "Hawaii Time (HT)" },
  { value: "Europe/London", label: "Greenwich Mean Time (GMT)" },
  { value: "Europe/Paris", label: "Central European Time (CET)" },
  { value: "Asia/Tokyo", label: "Japan Standard Time (JST)" },
  { value: "Australia/Sydney", label: "Australian Eastern Time (AET)" },
];

const BookSession = () => {
  const { tutorId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { session } = useAuth(); // Get session from auth context
  const location = useLocation();

  const [tutor, setTutor] = useState<any>(null);
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [timeSlot, setTimeSlot] = useState<string>("");
  const [duration, setDuration] = useState<string>("60");
  const [topic, setTopic] = useState<string>("");
  const [notes, setNotes] = useState<string>("");
  const [isBooking, setIsBooking] = useState<boolean>(false);
  const [step, setStep] = useState<number>(1);

  // User info form state
  const [userType, setUserType] = useState<string>("parent");
  const [firstName, setFirstName] = useState<string>("");
  const [lastName, setLastName] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [phone, setPhone] = useState<string>("");
  const [message, setMessage] = useState<string>("");

  // Available time slots for selected date
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([]);
  const [userTimezone, setUserTimezone] = useState<string>("");
  const [timezone, setTimezone] = useState<string>("");

  // Get user's timezone on component mount
  useEffect(() => {
    const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setUserTimezone(detectedTimezone);
    setTimezone(detectedTimezone);

    // Pre-fill user info if logged in
    if (session?.user) {
      setEmail(session.user.email || "");

      // Fetch user profile data from profiles table
      const fetchUserProfile = async () => {
        const { data, error } = await supabase
          .from("profiles")
          .select("first_name, last_name")
          .eq("id", session.user.id)
          .single();

        if (data && !error) {
          if (data.first_name) setFirstName(data.first_name);
          if (data.last_name) setLastName(data.last_name);
        }
      };

      fetchUserProfile();
    }
  }, [session]);

  useEffect(() => {
    if (tutorId) {
      const selectedTutor = tutors.find((t) => t.id === parseInt(tutorId));
      if (selectedTutor) {
        setTutor(selectedTutor);
      } else {
        navigate("/tutor-search");
      }
    }
  }, [tutorId, navigate]);

  // Update available time slots when date changes
  useEffect(() => {
    if (date && tutor) {
      const dayOfWeek = getDayOfWeek(date);
      if (tutor.availability.includes(dayOfWeek)) {
        // Simulate some slots being unavailable
        const availableTimes = timeSlots.filter(() => Math.random() > 0.3);
        setAvailableTimeSlots(availableTimes);
      } else {
        setAvailableTimeSlots([]);
      }
    } else {
      setAvailableTimeSlots([]);
    }
  }, [date, tutor]);

  // Add this effect to handle pre-filling data from login redirect
  useEffect(() => {
    // Check if we have booking data from a login redirect
    if (location.state?.bookingData) {
      const {
        tutorId: savedTutorId,
        date: savedDate,
        timeSlot: savedTimeSlot,
        duration: savedDuration,
        topic: savedTopic,
        notes: savedNotes,
      } = location.state.bookingData;

      // Restore the saved booking data
      if (savedDate) setDate(new Date(savedDate));
      if (savedTimeSlot) setTimeSlot(savedTimeSlot);
      if (savedDuration) setDuration(savedDuration);
      if (savedTopic) setTopic(savedTopic);
      if (savedNotes) setNotes(savedNotes);

      // Directly set the step to 3 (user info step)
      setStep(3);

      // Clear the state after using it
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handleReviewSubmit = () => {
    if (!date || !timeSlot || !duration || !topic) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Always go to step 3 regardless of login status
    setStep(3);

    // If user is logged in, pre-fill their information
    if (session?.user) {
      setEmail(session.user.email || "");

      if (session.user.user_metadata) {
        setFirstName(session.user.user_metadata.first_name || "");
        setLastName(session.user.user_metadata.last_name || "");
      }
    }
  };

  const handleUserInfoSubmit = () => {
    if (!firstName || !lastName || !email) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    handleSubmit();
  };

  const handleSubmit = async () => {
    setIsBooking(true);

    // Include timezone and user info in booking data
    const bookingData = {
      tutorId: tutor.id,
      date: date,
      timeSlot: timeSlot,
      duration: duration,
      topic: topic,
      notes: notes,
      timezone: timezone || userTimezone,
      userInfo: {
        type: userType,
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        message: message,
      },
    };

    // Simulate API call with a delay
    setTimeout(() => {
      // Success
      toast({
        title: "Session booked successfully!",
        description: `Your session with ${tutor.name} has been confirmed.`,
      });
      setStep(4); // Now confirmation is step 4
      setIsBooking(false);
    }, 1500);
  };

  // Calculate total cost
  const totalCost = tutor ? tutor.rate * (parseInt(duration) / 60) : 0;

  if (!tutor) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow flex items-center justify-center">
          <p>Loading...</p>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          {/* Add BookingSteps component here */}
          <div className="w-full max-w-3xl mx-auto mb-8">
            <div className="flex items-center justify-between">
              {[
                { title: "CHOOSE TIME", id: 1 },
                { title: "REVIEW", id: 2 },
                { title: "YOUR INFO", id: 3 },
                { title: "CONFIRMATION", id: 4 },
              ].map((stepItem, index) => (
                <div key={stepItem.id} className="flex items-center flex-grow">
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        step >= stepItem.id
                          ? "bg-red-500 text-white"
                          : "bg-gray-200 text-gray-500"
                      }`}
                    >
                      {step > stepItem.id ? "✓" : stepItem.id}
                    </div>
                    <span className="text-xs mt-1">{stepItem.title}</span>
                  </div>
                  {index < 3 && (
                    <div className="h-[2px] flex-grow mx-2 bg-gray-200">
                      {step > stepItem.id && (
                        <div
                          className="h-full bg-red-500"
                          style={{ width: "100%" }}
                        ></div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {step === 1 && (
            <>
              <div className="text-center mb-8">
                <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                  Book a Session with {tutor.name}
                </h1>
                <p className="mt-4 text-xl text-gray-500 max-w-2xl mx-auto">
                  Select your preferred date and time for your tutoring session.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Select Date and Time</CardTitle>
                      <CardDescription>
                        Choose a date and time slot for your session with{" "}
                        {tutor.name}.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                          <h3 className="text-sm font-medium mb-2">
                            Select a Date
                          </h3>
                          <div className="border rounded-md">
                            <Calendar
                              mode="single"
                              selected={date}
                              onSelect={setDate}
                              className="rounded-md"
                              disabled={(date) => {
                                // Disable dates in the past
                                const today = new Date();
                                today.setHours(0, 0, 0, 0);

                                // Disable dates where tutor is not available
                                const dayOfWeek = getDayOfWeek(date);
                                return (
                                  date < today ||
                                  !tutor.availability.includes(dayOfWeek)
                                );
                              }}
                            />
                          </div>
                          <div className="mt-2 text-sm text-gray-500">
                            Available days: {tutor.availability.join(", ")}
                          </div>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium mb-2">
                            Select a Time Slot
                          </h3>
                          {date ? (
                            availableTimeSlots.length > 0 ? (
                              <div className="grid grid-cols-2 gap-2">
                                {availableTimeSlots.map((time) => (
                                  <Button
                                    key={time}
                                    variant={
                                      timeSlot === time ? "default" : "outline"
                                    }
                                    className={
                                      timeSlot === time ? "bg-rfpurple-600" : ""
                                    }
                                    onClick={() => setTimeSlot(time)}
                                  >
                                    {time}
                                  </Button>
                                ))}
                              </div>
                            ) : (
                              <p className="text-gray-500">
                                No available time slots for the selected date.
                              </p>
                            )
                          ) : (
                            <p className="text-gray-500">
                              Please select a date first.
                            </p>
                          )}
                          {/* Add timezone selector here, only when a time slot is selected */}
                          {timeSlot && (
                            <div className="mt-4">
                              <h3 className="text-sm font-medium mb-2">
                                Select Timezone
                              </h3>
                              <Select
                                value={timezone}
                                onValueChange={setTimezone}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select timezone" />
                                </SelectTrigger>
                                <SelectContent>
                                  {timezones.map((tz) => (
                                    <SelectItem key={tz.value} value={tz.value}>
                                      {tz.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <p className="text-xs text-gray-500 mt-1">
                                Times are shown in your local timezone by
                                default
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="mt-8 space-y-4">
                        <div>
                          <h3 className="text-sm font-medium mb-2">
                            Session Duration
                          </h3>
                          <Select value={duration} onValueChange={setDuration}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select duration" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="60">
                                1 hour (${tutor.rate})
                              </SelectItem>
                              <SelectItem value="90">
                                1.5 hours (${(tutor.rate * 1.5).toFixed(2)})
                              </SelectItem>
                              <SelectItem value="120">
                                2 hours (${tutor.rate * 2})
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium mb-2">
                            Session Topic
                          </h3>
                          <Select value={topic} onValueChange={setTopic}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a topic" />
                            </SelectTrigger>
                            <SelectContent>
                              {tutor.specialties.map((specialty: string) => (
                                <SelectItem key={specialty} value={specialty}>
                                  {specialty}
                                </SelectItem>
                              ))}
                              <SelectItem value="custom">
                                Other (specify in notes)
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium mb-2">
                            Additional Notes
                          </h3>
                          <Textarea
                            placeholder="Share any specific questions or topics you'd like to cover"
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            className="min-h-[100px]"
                          />
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button
                        variant="outline"
                        onClick={() => navigate("/tutor-search")}
                      >
                        Back to Tutors
                      </Button>
                      <Button
                        className="button-gradient text-white"
                        disabled={!date || !timeSlot || !duration || !topic}
                        onClick={() => setStep(2)}
                      >
                        Continue to Review
                      </Button>
                    </CardFooter>
                  </Card>
                </div>

                <div>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">
                        Tutor Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center mb-4">
                        <img
                          src={tutor.photo}
                          alt={tutor.name}
                          className="w-16 h-16 rounded-full object-cover mr-4"
                        />
                        <div>
                          <h3 className="font-medium">{tutor.name}</h3>
                          <div className="flex items-center mt-1">
                            <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                            <span className="ml-1 text-sm font-medium">
                              {tutor.rating}
                            </span>
                            <span className="ml-1 text-sm text-gray-500">
                              ({Array.isArray(tutor.reviews) ? tutor.reviews.length : tutor.reviewCount} reviews)
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2 mb-4">
                        <div className="flex items-center text-sm">
                          <Clock className="h-4 w-4 mr-2 text-gray-500" />
                          <span className="text-gray-700">
                            {tutor.experience} experience
                          </span>
                        </div>
                        <div className="flex items-center text-sm">
                          <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                          <span className="text-gray-700">
                            {tutor.location}
                          </span>
                        </div>
                        <div className="flex items-center text-sm">
                          <BookOpen className="h-4 w-4 mr-2 text-gray-500" />
                          <span className="text-gray-700">
                            {tutor.education}
                          </span>
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium mb-2">
                          Specialties:
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {tutor.specialties.map((specialty: string) => (
                            <Badge
                              key={specialty}
                              variant="secondary"
                              className="bg-gray-100 text-gray-700"
                            >
                              {specialty}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="pt-4 border-t border-gray-100">
                        <h4 className="text-sm font-medium mb-2">Rate:</h4>
                        <div className="text-2xl font-bold text-rfpurple-600">
                          ${tutor.rate}/hr
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </>
          )}

          {step === 2 && (
            <>
              <div className="text-center mb-8">
                <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                  Review and Confirm
                </h1>
                <p className="mt-4 text-xl text-gray-500 max-w-2xl mx-auto">
                  Please review your session details before confirming.
                </p>
              </div>

              <Card className="max-w-2xl mx-auto">
                <CardHeader>
                  <CardTitle>Session Details</CardTitle>
                  <CardDescription>
                    Your tutoring session with {tutor.name}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="flex items-center">
                    <img
                      src={tutor.photo}
                      alt={tutor.name}
                      className="w-16 h-16 rounded-full object-cover mr-4"
                    />
                    <div>
                      <h3 className="font-medium">{tutor.name}</h3>
                      <div className="flex items-center mt-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                        <span className="ml-1 text-sm font-medium">
                          {tutor.rating}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">
                          Date and Time
                        </h3>
                        <div className="flex items-center mt-1">
                          <CalendarIcon className="h-4 w-4 mr-2 text-gray-700" />
                          <span className="font-medium">
                            {date?.toLocaleDateString("en-US", {
                              weekday: "long",
                              year: "numeric",
                              month: "long",
                              day: "numeric",
                            })}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center mt-2">
                        <ClockIcon className="h-4 w-4 mr-2 text-gray-700" />
                        <div>
                          <span>
                            {timeSlot}{" "}
                            {timezone && timezone !== userTimezone
                              ? `(${
                                  timezones.find((tz) => tz.value === timezone)
                                    ?.label || timezone
                                })`
                              : ""}
                          </span>
                          <span className="mx-2">• Duration:</span>
                          <span className="font-medium">
                            {duration === "60"
                              ? "1 hour"
                              : duration === "90"
                              ? "1.5 hours"
                              : "2 hours"}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">
                          Topic
                        </h3>
                        <p className="font-medium">{topic}</p>
                      </div>

                      {notes && (
                        <div>
                          <h3 className="text-sm font-medium text-gray-500">
                            Additional Notes
                          </h3>
                          <p className="text-sm text-gray-700">{notes}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <span>Hourly Rate</span>
                      <span>${tutor.rate}/hour</span>
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <span>Duration</span>
                      <span>
                        {duration === "60"
                          ? "1 hour"
                          : duration === "90"
                          ? "1.5 hours"
                          : "2 hours"}
                      </span>
                    </div>
                    <div className="flex justify-between items-center font-bold text-lg mt-4 pt-4 border-t border-gray-200">
                      <span>Total</span>
                      <span>${totalCost.toFixed(2)}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setStep(1)}>
                    Back
                  </Button>
                  <Button
                    className="button-gradient text-white"
                    onClick={handleReviewSubmit}
                    disabled={isBooking}
                  >
                    {isBooking ? "Processing..." : "Continue"}
                  </Button>
                </CardFooter>
              </Card>
            </>
          )}

          {step === 3 && (
            <>
              <div className="text-center mb-8">
                <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                  Your Information
                </h1>
                <p className="mt-4 text-xl text-gray-500 max-w-2xl mx-auto">
                  Please provide your details to complete the booking.
                </p>
              </div>

              <Card className="max-w-2xl mx-auto">
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                  <CardDescription>
                    We'll use this information to send you confirmation and
                    session details.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* User type selection */}
                  <RadioGroup
                    value={userType}
                    onValueChange={setUserType}
                    className="flex space-x-8"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="parent" id="parent" />
                      <Label htmlFor="parent">Parent</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="student" id="student" />
                      <Label htmlFor="student">Student</Label>
                    </div>
                  </RadioGroup>

                  {/* Session date/time display */}
                  <div className="py-2 px-4 bg-gray-50 rounded-md">
                    <p className="text-sm text-gray-700">
                      {date?.toLocaleDateString("en-US", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}{" "}
                      {timeSlot}
                      <Button
                        variant="link"
                        className="p-0 h-auto text-rfpurple-600 ml-2"
                        onClick={() => setStep(1)}
                      >
                        Edit
                      </Button>
                    </p>
                  </div>

                  {/* Name fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First name</Label>
                      <Input
                        id="firstName"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last name *</Label>
                      <Input
                        id="lastName"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        required
                      />
                    </div>
                  </div>

                  {/* Contact fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Your email address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number (optional)</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                      />
                    </div>
                  </div>

                  {/* Login option */}
                  <div className="pt-4 border-t border-gray-200">
                    <p className="text-sm text-gray-600">
                      Already have an account?
                      <Button
                        variant="link"
                        className="p-0 h-auto text-rfpurple-600 ml-1"
                        onClick={() =>
                          navigate("/login", {
                            state: {
                              returnTo: `/book-session/${tutorId}?step=3`,
                              bookingData: {
                                tutorId,
                                date,
                                timeSlot,
                                duration,
                                topic,
                                notes,
                              },
                            },
                          })
                        }
                      >
                        Log in
                      </Button>{" "}
                      to pre-fill your information.
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setStep(2)}>
                    Back
                  </Button>
                  <Button
                    className="button-gradient text-white"
                    onClick={handleUserInfoSubmit}
                    disabled={isBooking}
                  >
                    {isBooking ? "Processing..." : "Confirm Booking"}
                  </Button>
                </CardFooter>
              </Card>
            </>
          )}

          {step === 4 && (
            <>
              <div className="text-center mb-8">
                <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                  Booking Confirmed!
                </h1>
                <p className="mt-4 text-xl text-gray-500 max-w-2xl mx-auto">
                  Your session with {tutor.name} has been booked successfully.
                </p>
              </div>

              <Card className="max-w-2xl mx-auto text-center">
                <CardHeader>
                  <div className="mx-auto mb-4">
                    <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
                  </div>
                  <CardTitle>Session Confirmed</CardTitle>
                  <CardDescription>
                    We've sent a confirmation email with all the details.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium text-lg mb-2">
                      Session Details
                    </h3>
                    <p className="mb-1">
                      <span className="font-semibold">Date:</span>{" "}
                      {date?.toLocaleDateString("en-US", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </p>
                    <p className="mb-1">
                      <span className="font-semibold">Time:</span> {timeSlot}
                    </p>
                    <p className="mb-1">
                      <span className="font-semibold">Duration:</span>{" "}
                      {duration === "60"
                        ? "1 hour"
                        : duration === "90"
                        ? "1.5 hours"
                        : "2 hours"}
                    </p>
                    <p className="mb-1">
                      <span className="font-semibold">Topic:</span> {topic}
                    </p>
                    <p className="font-semibold mt-4">
                      Total: ${totalCost.toFixed(2)}
                    </p>
                  </div>
                  <p className="text-gray-600">
                    You can view and manage your upcoming sessions from your
                    dashboard. The meeting link will be provided 30 minutes
                    before the session.
                  </p>
                </CardContent>
                <CardFooter className="justify-center space-x-4">
                  <Button
                    variant="outline"
                    onClick={() => navigate("/dashboard")}
                  >
                    Go to Dashboard
                  </Button>
                  <Button
                    className="button-gradient text-white"
                    onClick={() => navigate("/")}
                  >
                    Return to Home
                  </Button>
                </CardFooter>
              </Card>
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BookSession;

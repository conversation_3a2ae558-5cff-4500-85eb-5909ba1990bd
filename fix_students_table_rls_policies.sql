-- =====================================================
-- FIX STUDENTS TABLE RLS POLICIES
-- =====================================================
-- This script creates proper Row Level Security policies for the students table
-- to allow students to manage their own profile data

-- First, check if RLS is enabled on students table and enable it if not
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to start fresh
DROP POLICY IF EXISTS "Students can view their own data" ON students;
DROP POLICY IF EXISTS "Students can insert their own data" ON students;
DROP POLICY IF EXISTS "Students can update their own data" ON students;
DROP POLICY IF EXISTS "Admins can view all student data" ON students;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all student data" ON students;
DROP POLICY IF EXISTS "Service role can manage students" ON students;

-- =====================================================
-- STUDENT POLICIES
-- =====================================================

-- Policy 1: Students can view their own data
CREATE POLICY "Students can view their own data" ON students
    FOR SELECT USING (
        id = auth.uid()
    );

-- Policy 2: Students can insert their own data
CREATE POLICY "Students can insert their own data" ON students
    FOR INSERT WITH CHECK (
        id = auth.uid()
    );

-- Policy 3: Students can update their own data
CREATE POLICY "Students can update their own data" ON students
    FOR UPDATE USING (
        id = auth.uid()
    );

-- =====================================================
-- ADMIN POLICIES
-- =====================================================

-- Policy 4: Admins can view all student data
CREATE POLICY "Admins can view all student data" ON students
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'admin'
        )
    );

-- Policy 5: Admins can manage all student data
CREATE POLICY "Admins can manage all student data" ON students
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'admin'
        )
    );

-- =====================================================
-- SERVICE ROLE POLICY
-- =====================================================

-- Policy 6: Service role can manage all student data (for system operations)
CREATE POLICY "Service role can manage students" ON students
    FOR ALL USING (
        auth.role() = 'service_role'
    );

-- =====================================================
-- VERIFY POLICIES
-- =====================================================

-- Check that policies were created successfully
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'students'
ORDER BY policyname;

-- Show current RLS status
SELECT 
    schemaname,
    tablename,
    rowsecurity,
    forcerowsecurity
FROM pg_tables 
WHERE tablename = 'students';

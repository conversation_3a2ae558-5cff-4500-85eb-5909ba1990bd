import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/Label";

interface LearningGoalsProps {
  learningGoals: string[];
  setLearningGoals: (goals: string[]) => void;
}

const LearningGoals: React.FC<LearningGoalsProps> = ({
  learningGoals,
  setLearningGoals,
}) => {
  const goals = [
    { id: "improve_grades", label: "Improve my grades" },
    { id: "exam_prep", label: "Prepare for exams" },
    { id: "learn_new_skills", label: "Learn new skills" },
    { id: "career_advancement", label: "Advance my career" },
    { id: "personal_interest", label: "Personal interest" },
    { id: "homework_help", label: "Get help with homework" },
  ];

  const toggleGoal = (goalId: string) => {
    if (learningGoals.includes(goalId)) {
      setLearningGoals(learningGoals.filter((id) => id !== goalId));
    } else {
      setLearningGoals([...learningGoals, goalId]);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-2">What are your learning goals?</h2>
      <p className="text-gray-600 mb-6">Select all that apply to you.</p>

      <div className="space-y-3">
        {goals.map((goal) => (
          <div
            key={goal.id}
            className="flex items-start space-x-3 border p-4 rounded-lg hover:bg-gray-50"
          >
            <Checkbox
              id={goal.id}
              checked={learningGoals.includes(goal.id)}
              onCheckedChange={() => toggleGoal(goal.id)}
            />
            <div className="grid gap-1.5 leading-none">
              <Label htmlFor={goal.id} className="cursor-pointer">
                {goal.label}
              </Label>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LearningGoals;

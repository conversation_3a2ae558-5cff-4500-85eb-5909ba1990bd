import { useAuth } from "@/context/AuthContext";
import { USER_STATUS } from "@/constants/auth";
import { useUserStatusStore } from "@/store/userStatusStore";
import { useToast } from "@/components/ui/UseToast";

export const useUserStatusCheck = (skipCheck = false) => {
  const { fetchUserDataByEmail } = useAuth();
  const { toast } = useToast();
  const {
    userStatus,
    userType,
    isOnboarded,
    isLoading,
    error,
    setEmail,
    setUserStatus,
    setUserType,
    setIsOnboarded,
    setIsLoading,
    setError,
    reset,
  } = useUserStatusStore();

  // Function to check user status by email
  const checkUserStatus = async (emailToCheck: string) => {
    if (!emailToCheck || skipCheck)
      return {
        isRegistered: false,
        userType: null,
        isOnboarded: false,
        error: null,
      };

    setIsLoading(true);
    setEmail(emailToCheck);
    setError(null);

    try {
      const {
        userStatus: fetchedUserStatus,
        userType: fetchedUserType,
        isOnboarded: fetchedOnboardingStatus,
        error: fetchError,
      } = await fetchUserDataByEmail(emailToCheck);

      if (fetchError) {
        setError(fetchError.message || "Failed to check user status");
        toast({
          title: "Error",
          description:
            "There was a problem checking your email. Please try again.",
          variant: "destructive",
        });
        return {
          isRegistered: false,
          userType: null,
          isOnboarded: false,
          error: fetchError.message,
        };
      }

      setUserStatus(fetchedUserStatus);
      setUserType(fetchedUserType);
      setIsOnboarded(fetchedOnboardingStatus);

      return {
        isRegistered: fetchedUserStatus === "registered",
        userType: fetchedUserType,
        isOnboarded: fetchedOnboardingStatus,
        error: null,
      };
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred");
      toast({
        title: "Error",
        description: err.message || "An unexpected error occurred",
        variant: "destructive",
      });
      return {
        isRegistered: false,
        userType: null,
        isOnboarded: false,
        error: err.message,
      };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    checkUserStatus,
    isRegistered: userStatus === USER_STATUS.REGISTERED,
    needsOnboarding: userStatus === USER_STATUS.REGISTERED && !isOnboarded,
    isGuest: userStatus === USER_STATUS.GUEST,
    isNew: userStatus === USER_STATUS.NEW || userStatus === null,
    userStatus,
    userType,
    isOnboarded,
    isLoading,
    error,
    reset,
  };
};

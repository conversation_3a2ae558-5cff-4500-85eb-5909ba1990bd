import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import AdminSidebar from "@/components/admin/Sidebar";
import UserProfileMenu from "@/components/UserProfileMenu";
import { useProfileData } from "@/hooks/useProfileData";
import { Edit, Calendar, Clock } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Textarea } from "@/components/ui/TextArea";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";

const EditSession = () => {
  const navigate = useNavigate();
  const profileData = useProfileData();

  // Mock session data - in a real app, you'd fetch this based on ID
  const [session, setSession] = useState({
    id: "123",
    subject: "Mathematics",
    date: "2023-12-15",
    startTime: "14:00",
    duration: "60",
    location: "online",
    tutor: "tutor1",
    student: "student1",
    notes: "Focus on algebra and equations",
  });

  const handleChange = (field, value) => {
    setSession({ ...session, [field]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Updated session:", session);
    navigate("/admin/sessions");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Edit Session</h1>
            <UserProfileMenu
              isAdmin={true}
              isAdminPage={true}
            />
          </div>

          <Card className="max-w-3xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Edit className="mr-2 h-5 w-5 text-rfpurple-600" />
                Edit Session Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input
                    id="subject"
                    value={session.subject}
                    onChange={(e) => handleChange("subject", e.target.value)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Date</Label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                        <Calendar className="h-4 w-4" />
                      </span>
                      <Input
                        id="date"
                        type="date"
                        className="rounded-l-none"
                        value={session.date}
                        onChange={(e) => handleChange("date", e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="time">Start Time</Label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                        <Clock className="h-4 w-4" />
                      </span>
                      <Input
                        id="time"
                        type="time"
                        className="rounded-l-none"
                        value={session.startTime}
                        onChange={(e) =>
                          handleChange("startTime", e.target.value)
                        }
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="duration">Duration (minutes)</Label>
                    <Select
                      value={session.duration}
                      onValueChange={(value) => handleChange("duration", value)}
                    >
                      <SelectTrigger id="duration">
                        <SelectValue placeholder="Select duration" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">60 minutes</SelectItem>
                        <SelectItem value="90">90 minutes</SelectItem>
                        <SelectItem value="120">120 minutes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Select
                      value={session.location}
                      onValueChange={(value) => handleChange("location", value)}
                    >
                      <SelectTrigger id="location">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="online">Online</SelectItem>
                        <SelectItem value="in-person">In-Person</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Additional Notes</Label>
                  <Textarea
                    id="notes"
                    value={session.notes}
                    onChange={(e) => handleChange("notes", e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
              </form>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => navigate("/admin/sessions")}
              >
                Cancel
              </Button>
              <Button onClick={handleSubmit} className="button-gradient">
                Save Changes
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default EditSession;

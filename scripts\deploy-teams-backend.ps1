# Teams Backend Deployment Script (PowerShell)
# This script deploys the Supabase Edge Functions for Teams integration

Write-Host "🚀 Deploying Microsoft Teams Backend Integration" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Check if Supabase CLI is installed
try {
    $null = Get-Command supabase -ErrorAction Stop
    Write-Host "✅ Supabase CLI found" -ForegroundColor Green
} catch {
    Write-Host "❌ Supabase CLI is not installed. Please install it first:" -ForegroundColor Red
    Write-Host "   npm install -g supabase" -ForegroundColor Yellow
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "supabase/config.toml")) {
    Write-Host "❌ Please run this script from the project root directory" -ForegroundColor Red
    exit 1
}

# Deploy Edge Functions
Write-Host ""
Write-Host "📦 Deploying Edge Functions..." -ForegroundColor Blue

Write-Host "  → Deploying teams-auth function..." -ForegroundColor Cyan
supabase functions deploy teams-auth

Write-Host "  → Deploying teams-meetings function..." -ForegroundColor Cyan
supabase functions deploy teams-meetings

Write-Host "✅ Edge Functions deployed successfully" -ForegroundColor Green

# Run database migrations
Write-Host ""
Write-Host "🗄️  Running database migrations..." -ForegroundColor Blue
supabase db push

Write-Host "✅ Database migrations completed" -ForegroundColor Green

# Set environment variables (if provided)
Write-Host ""
Write-Host "🔧 Setting up environment variables..." -ForegroundColor Blue

if (-not $env:AZURE_CLIENT_ID) {
    Write-Host "⚠️  AZURE_CLIENT_ID not set. Please set it manually:" -ForegroundColor Yellow
    Write-Host "   supabase secrets set AZURE_CLIENT_ID=your_client_id" -ForegroundColor Gray
} else {
    Write-Host "  → Setting AZURE_CLIENT_ID..." -ForegroundColor Cyan
    supabase secrets set "AZURE_CLIENT_ID=$env:AZURE_CLIENT_ID"
}

if (-not $env:AZURE_CLIENT_SECRET) {
    Write-Host "⚠️  AZURE_CLIENT_SECRET not set. Please set it manually:" -ForegroundColor Yellow
    Write-Host "   supabase secrets set AZURE_CLIENT_SECRET=your_client_secret" -ForegroundColor Gray
} else {
    Write-Host "  → Setting AZURE_CLIENT_SECRET..." -ForegroundColor Cyan
    supabase secrets set "AZURE_CLIENT_SECRET=$env:AZURE_CLIENT_SECRET"
}

if (-not $env:AZURE_TENANT_ID) {
    Write-Host "⚠️  AZURE_TENANT_ID not set. Please set it manually:" -ForegroundColor Yellow
    Write-Host "   supabase secrets set AZURE_TENANT_ID=your_tenant_id" -ForegroundColor Gray
} else {
    Write-Host "  → Setting AZURE_TENANT_ID..." -ForegroundColor Cyan
    supabase secrets set "AZURE_TENANT_ID=$env:AZURE_TENANT_ID"
}

if (-not $env:FRONTEND_URL) {
    Write-Host "  → Setting default FRONTEND_URL..." -ForegroundColor Cyan
    supabase secrets set "FRONTEND_URL=http://localhost:8080"
} else {
    Write-Host "  → Setting FRONTEND_URL..." -ForegroundColor Cyan
    supabase secrets set "FRONTEND_URL=$env:FRONTEND_URL"
}

Write-Host ""
Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Blue
Write-Host "1. Update your Azure App Registration redirect URI to:" -ForegroundColor White
Write-Host "   https://your-supabase-project.supabase.co/functions/v1/teams-auth/callback" -ForegroundColor Yellow
Write-Host ""
Write-Host "2. Test the integration by running your frontend and trying to connect to Teams" -ForegroundColor White
Write-Host ""
Write-Host "3. Check function logs if you encounter issues:" -ForegroundColor White
Write-Host "   supabase functions logs teams-auth" -ForegroundColor Gray
Write-Host "   supabase functions logs teams-meetings" -ForegroundColor Gray
Write-Host ""
Write-Host "📖 For detailed setup instructions, see TEAMS_BACKEND_IMPLEMENTATION.md" -ForegroundColor Blue

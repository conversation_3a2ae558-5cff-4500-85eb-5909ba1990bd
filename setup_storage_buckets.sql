-- =====================================================
-- SUPABASE STORAGE BUCKET SETUP FOR PHOTO UPLOADS
-- =====================================================

-- This script sets up the necessary storage buckets and policies
-- for the photo upload feature with crop and rotate functionality

-- =====================================================
-- STEP 1: CREATE STORAGE BUCKETS
-- =====================================================

-- Create student-uploads bucket for profile photos and gallery images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'student-uploads',
    'student-uploads',
    true,
    5242880, -- 5MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp']
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create avatars bucket for profile pictures (alternative bucket)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'avatars',
    'avatars',
    true,
    5242880, -- 5MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp']
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- =====================================================
-- STEP 2: CREATE STORAGE POLICIES FOR STUDENT-UPLOADS
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can upload their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Public can view student uploads" ON storage.objects;
DROP POLICY IF EXISTS "Admins can manage all student uploads" ON storage.objects;

-- Policy 1: Allow authenticated users to upload files to their own folder
CREATE POLICY "Students can upload their own files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Policy 2: Allow users to view their own files
CREATE POLICY "Students can view their own files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Policy 3: Allow users to update their own files
CREATE POLICY "Students can update their own files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Policy 4: Allow users to delete their own files
CREATE POLICY "Students can delete their own files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Policy 5: Allow public read access to student uploads (for profile pictures)
CREATE POLICY "Public can view student uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'student-uploads'
    );

-- Policy 6: Allow admins to manage all student uploads
CREATE POLICY "Admins can manage all student uploads" ON storage.objects
    FOR ALL USING (
        bucket_id = 'student-uploads' AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- STEP 3: CREATE STORAGE POLICIES FOR AVATARS BUCKET
-- =====================================================

-- Policy 1: Allow authenticated users to upload avatars
CREATE POLICY "Users can upload avatars" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' AND
        auth.role() = 'authenticated'
    );

-- Policy 2: Allow users to view avatars
CREATE POLICY "Users can view avatars" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'avatars'
    );

-- Policy 3: Allow users to update their own avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'avatars' AND
        auth.role() = 'authenticated'
    );

-- Policy 4: Allow users to delete their own avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'avatars' AND
        auth.role() = 'authenticated'
    );

-- =====================================================
-- STEP 4: VERIFICATION QUERIES
-- =====================================================

-- Check that buckets were created
SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id IN ('student-uploads', 'avatars');

-- Check storage policies
SELECT 
    policyname,
    cmd,
    permissive,
    roles,
    qual
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;

-- =====================================================
-- STEP 5: GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant usage on storage schema to authenticated users
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- Grant usage to anon users for public access
GRANT USAGE ON SCHEMA storage TO anon;
GRANT SELECT ON storage.objects TO anon;
GRANT SELECT ON storage.buckets TO anon;

-- =====================================================
-- NOTES
-- =====================================================

/*
This script sets up:

1. Two storage buckets:
   - student-uploads: For profile photos and gallery images
   - avatars: Alternative bucket for profile pictures

2. Comprehensive RLS policies that allow:
   - Students to upload files to their own folders
   - Students to manage their own files
   - Public read access for profile pictures
   - Admin access to all files

3. Proper file size limits (5MB) and MIME type restrictions

4. Folder structure: profiles/{user_id}/{filename}

To use this:
1. Run this SQL in your Supabase SQL editor
2. Verify the buckets and policies were created
3. Test file uploads from your application

The policies ensure that:
- Users can only upload to their own folders
- File paths follow the pattern: profiles/{user_id}/filename
- Public can view uploaded images (for profile pictures)
- Admins have full access for moderation
*/

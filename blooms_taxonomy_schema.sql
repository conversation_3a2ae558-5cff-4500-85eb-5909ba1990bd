-- <PERSON>'s Taxonomy Integration for Learning Platform

-- <PERSON><PERSON> <PERSON>'s Taxonomy Levels table
CREATE TABLE bloom_taxonomy_levels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    level_name TEXT NOT NULL,
    level_order INTEGER NOT NULL, -- 1 = Knowledge, 2 = Comprehension, etc.
    description TEXT,
    key_verbs TEXT[], -- Array of verbs associated with this level
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(level_name),
    UNIQUE(level_order)
);

-- Create Learning Objectives table
CREATE TABLE learning_objectives (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subtopic_id UUID REFERENCES subtopics(id) NOT NULL,
    taxonomy_level_id UUID REFERENCES bloom_taxonomy_levels(id) NOT NULL,
    objective_text TEXT NOT NULL,
    display_order INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create Assessment Types table based on Bloom's Taxonomy
CREATE TABLE assessment_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    taxonomy_level_id UUID REFERENCES bloom_taxonomy_levels(id) NOT NULL,
    is_formative BOOLEAN NOT NULL, -- True for formative, False for summative
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(name)
);

-- Create Assessments table
CREATE TABLE assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subtopic_id UUID REFERENCES subtopics(id) NOT NULL,
    assessment_type_id UUID REFERENCES assessment_types(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    instructions TEXT,
    duration_min INTEGER, -- Duration in minutes
    passing_score DECIMAL(5,2), -- e.g., 70.00 for 70%
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create Assessment Questions table
CREATE TABLE assessment_questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    assessment_id UUID REFERENCES assessments(id) NOT NULL,
    question_text TEXT NOT NULL,
    question_type TEXT NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'essay', 'matching')),
    taxonomy_level_id UUID REFERENCES bloom_taxonomy_levels(id) NOT NULL,
    points INTEGER NOT NULL,
    display_order INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create Assessment Responses table
CREATE TABLE assessment_responses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES sessions(id) NOT NULL,
    assessment_id UUID REFERENCES assessments(id) NOT NULL,
    student_id UUID REFERENCES profiles(id) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    score DECIMAL(5,2),
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(session_id, assessment_id, student_id)
);

-- Alter Resources table to add taxonomy level
ALTER TABLE resources ADD COLUMN taxonomy_level_id UUID REFERENCES bloom_taxonomy_levels(id);

-- Alter Subtopics table to add primary taxonomy level
ALTER TABLE subtopics ADD COLUMN primary_taxonomy_level_id UUID REFERENCES bloom_taxonomy_levels(id);

-- Create Session Learning Objectives table to track which objectives were covered in a session
CREATE TABLE session_learning_objectives (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES sessions(id) NOT NULL,
    learning_objective_id UUID REFERENCES learning_objectives(id) NOT NULL,
    was_covered BOOLEAN DEFAULT true,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(session_id, learning_objective_id)
);

-- Create Student Learning Progress table to track student progress on learning objectives
CREATE TABLE student_learning_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) NOT NULL,
    learning_objective_id UUID REFERENCES learning_objectives(id) NOT NULL,
    mastery_level TEXT NOT NULL CHECK (mastery_level IN ('not_started', 'introduced', 'practicing', 'mastered')),
    last_assessed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(student_id, learning_objective_id)
);

-- Apply the update timestamp trigger to new tables
CREATE TRIGGER update_bloom_taxonomy_levels_timestamp BEFORE UPDATE ON bloom_taxonomy_levels FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_learning_objectives_timestamp BEFORE UPDATE ON learning_objectives FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_assessment_types_timestamp BEFORE UPDATE ON assessment_types FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_assessments_timestamp BEFORE UPDATE ON assessments FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_assessment_questions_timestamp BEFORE UPDATE ON assessment_questions FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_assessment_responses_timestamp BEFORE UPDATE ON assessment_responses FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_learning_objectives_timestamp BEFORE UPDATE ON session_learning_objectives FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_student_learning_progress_timestamp BEFORE UPDATE ON student_learning_progress FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- Insert the six levels of Bloom's Taxonomy
INSERT INTO bloom_taxonomy_levels (level_name, level_order, description, key_verbs) VALUES
('Knowledge', 1, 'Recall or recognize information, ideas, and principles in the approximate form in which they were learned.', 
 ARRAY['define', 'describe', 'identify', 'label', 'list', 'match', 'memorize', 'name', 'recall', 'recognize', 'state']),
 
('Comprehension', 2, 'Understand the meaning of information and materials.', 
 ARRAY['classify', 'convert', 'explain', 'generalize', 'give examples', 'paraphrase', 'restate', 'summarize', 'translate']),
 
('Application', 3, 'Use information, ideas, or principles in new situations.', 
 ARRAY['apply', 'compute', 'construct', 'demonstrate', 'modify', 'operate', 'predict', 'prepare', 'produce', 'solve', 'use']),
 
('Analysis', 4, 'Break down complex information into its component parts to understand its organizational structure.', 
 ARRAY['analyze', 'categorize', 'compare', 'contrast', 'differentiate', 'distinguish', 'examine', 'experiment', 'question', 'test']),
 
('Synthesis', 5, 'Create something new by combining different ideas or elements.', 
 ARRAY['arrange', 'assemble', 'compose', 'construct', 'create', 'design', 'develop', 'formulate', 'organize', 'plan', 'prepare', 'propose']),
 
('Evaluation', 6, 'Make judgments based on criteria and standards.', 
 ARRAY['appraise', 'argue', 'assess', 'critique', 'defend', 'evaluate', 'judge', 'justify', 'recommend', 'support']);

-- Insert sample assessment types
INSERT INTO assessment_types (name, description, taxonomy_level_id, is_formative) VALUES
('Multiple Choice Quiz', 'Basic quiz with multiple choice questions to test recall of facts', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Knowledge'), true),
 
('Concept Map', 'Visual representation of relationships between concepts', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Comprehension'), true),
 
('Problem Set', 'Collection of problems requiring application of learned principles', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'), true),
 
('Case Study Analysis', 'In-depth analysis of a real-world scenario', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Analysis'), false),
 
('Creative Project', 'Project requiring synthesis of multiple concepts into a new creation', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Synthesis'), false),
 
('Critical Review', 'Evaluation of a work, theory, or approach based on specific criteria', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Evaluation'), false);

import React from 'react';

interface HorizontalDividerProps {
  className?: string;
  noMargin?: boolean;
}

/**
 * A shared horizontal divider component that ensures consistent border styling
 * across the application. This component should be used wherever a horizontal
 * separator line is needed.
 *
 * @param {string} className - Additional CSS classes to apply
 * @param {boolean} noMargin - If true, removes all margins (used for tutor pages)
 */
const HorizontalDivider: React.FC<HorizontalDividerProps> = ({
  className = "",
  noMargin = false
}) => {
  return (
    <div
      className={`w-full h-[1px] bg-[#e5e7eb] ${noMargin ? 'mt-0 mx-0' : ''} ${className}`}
    ></div>
  );
};

export default HorizontalDivider;

import React, { useEffect, useRef } from "react";
import { useTutorAvailabilityStore } from "@/store/tutorAvailabilityStore";
import { useTutorPreferencesUIStore } from "@/store/tutorPreferencesUIStore";
import { useTimezone } from "@/hooks/useTimezone";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/components/ui/UseToast";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Clock, Moon, Timer, Globe, RefreshCw } from "lucide-react";
import { Badge } from "@/components/ui/Badge";
import { supabase } from "@/lib/supabaseClient";

// List of timezones with user-friendly display names
const timezones = [
  // Indian Standard Time
  { value: "Asia/Kolkata", label: "India Standard Time (IST)" },

  // US Timezones
  { value: "America/New_York", label: "Eastern Time (ET)" },
  { value: "America/Chicago", label: "Central Time (CT)" },
  { value: "America/Denver", label: "Mountain Time (MT)" },
  { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
  { value: "America/Anchorage", label: "Alaska Time (AKT)" },
  { value: "Pacific/Honolulu", label: "Hawaii Time (HST)" },
  { value: "America/Phoenix", label: "Arizona Time (MST)" },

  // Other Common Timezones
  { value: "Europe/London", label: "Greenwich Mean Time (GMT)" },
  { value: "Europe/Paris", label: "Central European Time (CET)" },
  { value: "Asia/Tokyo", label: "Japan Standard Time (JST)" },
  { value: "Australia/Sydney", label: "Australian Eastern Time (AET)" },
  { value: "Asia/Shanghai", label: "China Standard Time (CST)" },
  { value: "Asia/Dubai", label: "Gulf Standard Time (GST)" },
  { value: "America/Toronto", label: "Eastern Time - Canada" },
  { value: "America/Vancouver", label: "Pacific Time - Canada" },
];

const TutorPreferences: React.FC = () => {
  const { preferences, updatePreferences } = useTutorAvailabilityStore();
  const { user, refreshUserData } = useAuth();
  const { toast } = useToast();
  const {
    getTimezoneDisplayInfo,
    detectUserTimezone
  } = useTimezone();

  // Zustand store for UI state
  const {
    isEditing,
    selectedTimezone,
    isSavingTimezone,
    isDetecting,
    editedPreferences,
    newDuration,
    setIsEditing,
    setSelectedTimezone,
    setIsSavingTimezone,
    setIsDetecting,
    setEditedPreferences,
    setNewDuration,
    initializePreferences,
    resetToDefaults,
    cancelEditing,
  } = useTutorPreferencesUIStore();

  // Track if initialization has happened
  const isInitialized = useRef(false);

  // Initialize preferences on component mount
  useEffect(() => {
    if (!isInitialized.current && preferences) {
      initializePreferences(preferences);
      isInitialized.current = true;
    }
  }, [preferences, initializePreferences]);

  // Handle timezone detection separately
  useEffect(() => {
    // If no timezone is set in preferences, use detected timezone
    if (isInitialized.current && (!preferences.timezone || preferences.timezone === '')) {
      const detected = detectUserTimezone();
      setSelectedTimezone(detected);
      updatePreferences({ timezone: detected });
    }
  }, [preferences.timezone, setSelectedTimezone, detectUserTimezone, updatePreferences]); // Only run when timezone changes

  // Auto-detect timezone function
  const handleAutoDetectTimezone = async () => {
    setIsDetecting(true);

    try {
      // Simulate a brief delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));

      const detected = detectUserTimezone();
      setSelectedTimezone(detected);

      toast({
        title: "Timezone Detected",
        description: `Detected timezone: ${getTimezoneDisplayInfo(detected).label}`,
      });
    } catch (error) {
      toast({
        title: "Detection Failed",
        description: "Could not detect timezone automatically. Please select manually.",
        variant: "destructive",
      });
    } finally {
      setIsDetecting(false);
    }
  };

  // Save timezone to database
  const handleSaveTimezone = async () => {
    if (!user?.id) {
      toast({
        title: "Error",
        description: "User not found. Please try logging in again.",
        variant: "destructive",
      });
      return;
    }

    setIsSavingTimezone(true);

    try {
      // Update in database
      const { error } = await supabase
        .from('profiles')
        .update({ timezone: selectedTimezone })
        .eq('id', user.id);

      if (error) throw error;

      // Update in store
      updatePreferences({ timezone: selectedTimezone });

      // Refresh the complete profile data in AuthContext to ensure consistency
      try {
        await refreshUserData();
        console.log('Profile data refreshed after timezone update in TutorPreferences');
      } catch (refreshError) {
        console.error('Error refreshing profile data after timezone update:', refreshError);
        // Don't fail the save operation if refresh fails
      }

      toast({
        title: "Timezone Saved",
        description: "Your timezone preference has been updated successfully.",
      });

      setIsEditing(false);
    } catch (error) {
      console.error('Error saving timezone:', error);
      toast({
        title: "Error",
        description: "Failed to save timezone settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSavingTimezone(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof typeof editedPreferences, value: any) => {
    setEditedPreferences({
      ...editedPreferences,
      [field]: value,
    });
  };

  // Handle nested object changes
  const handleNestedChange = (
    parent: keyof typeof editedPreferences,
    field: string,
    value: any
  ) => {
    const parentValue = editedPreferences[parent];

    // Ensure the parent value is an object before spreading
    if (typeof parentValue === 'object' && parentValue !== null) {
      setEditedPreferences({
        ...editedPreferences,
        [parent]: {
          ...parentValue,
          [field]: value,
        },
      });
    }
  };

  // Add a new session duration
  const handleAddDuration = () => {
    const duration = parseInt(newDuration);
    if (
      duration > 0 &&
      !editedPreferences.sessionDurations.includes(duration)
    ) {
      setEditedPreferences({
        ...editedPreferences,
        sessionDurations: [...editedPreferences.sessionDurations, duration].sort(
          (a, b) => a - b
        ),
      });
      setNewDuration("");
    }
  };

  // Remove a session duration
  const handleRemoveDuration = (duration: number) => {
    setEditedPreferences({
      ...editedPreferences,
      sessionDurations: editedPreferences.sessionDurations.filter(
        (d) => d !== duration
      ),
    });
  };

  // Save changes
  const handleSaveChanges = () => {
    updatePreferences(editedPreferences);
    setIsEditing(false);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    cancelEditing(preferences);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Tutor Preferences</h2>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>Edit Preferences</Button>
        ) : (
          <div className="space-x-2">
            <Button variant="outline" onClick={handleCancelEdit}>
              Cancel
            </Button>
            <Button onClick={handleSaveChanges}>Save Changes</Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Working Hours */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2 text-blue-500" />
              Default Working Hours
            </CardTitle>
            <CardDescription>
              Set your standard working hours for availability
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Start Time</Label>
                    <Input
                      type="time"
                      value={editedPreferences.defaultWorkingHours.startTime}
                      onChange={(e) =>
                        handleNestedChange(
                          "defaultWorkingHours",
                          "startTime",
                          e.target.value
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>End Time</Label>
                    <Input
                      type="time"
                      value={editedPreferences.defaultWorkingHours.endTime}
                      onChange={(e) =>
                        handleNestedChange(
                          "defaultWorkingHours",
                          "endTime",
                          e.target.value
                        )
                      }
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="py-2">
                <p className="text-lg font-medium">
                  {preferences.defaultWorkingHours.startTime} - {preferences.defaultWorkingHours.endTime}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  These hours will be used as defaults when creating new availability slots
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Session Durations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Timer className="h-5 w-5 mr-2 text-blue-500" />
              Session Duration Presets
            </CardTitle>
            <CardDescription>
              Define standard session lengths that students can book
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {editedPreferences.sessionDurations.map((duration) => (
                    <Badge key={duration} variant="secondary">
                      {duration} min
                      <button
                        className="ml-1 text-gray-500 hover:text-red-500"
                        onClick={() => handleRemoveDuration(duration)}
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <Input
                    type="number"
                    placeholder="Duration in minutes"
                    value={newDuration}
                    onChange={(e) => setNewDuration(e.target.value)}
                    min="5"
                    step="5"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleAddDuration}
                  >
                    Add
                  </Button>
                </div>
              </div>
            ) : (
              <div className="py-2">
                <div className="flex flex-wrap gap-2">
                  {preferences.sessionDurations.map((duration) => (
                    <Badge key={duration} variant="secondary">
                      {duration} min
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Buffer Time */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2 text-blue-500" />
              Buffer Time Between Sessions
            </CardTitle>
            <CardDescription>
              Set a break period between consecutive sessions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Buffer Time (minutes)</Label>
                  <Input
                    type="number"
                    value={editedPreferences.bufferTime}
                    onChange={(e) =>
                      handleInputChange("bufferTime", parseInt(e.target.value))
                    }
                    min="0"
                    step="5"
                  />
                </div>
              </div>
            ) : (
              <div className="py-2">
                <p className="text-lg font-medium">{preferences.bufferTime} minutes</p>
                <p className="text-sm text-gray-500 mt-1">
                  This time will be automatically blocked between sessions
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Do Not Disturb */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Moon className="h-5 w-5 mr-2 text-blue-500" />
              Do-Not-Disturb Hours
            </CardTitle>
            <CardDescription>
              Set hours when you don't want to be booked
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Start Time</Label>
                    <Input
                      type="time"
                      value={editedPreferences.doNotDisturbHours.startTime}
                      onChange={(e) =>
                        handleNestedChange(
                          "doNotDisturbHours",
                          "startTime",
                          e.target.value
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>End Time</Label>
                    <Input
                      type="time"
                      value={editedPreferences.doNotDisturbHours.endTime}
                      onChange={(e) =>
                        handleNestedChange(
                          "doNotDisturbHours",
                          "endTime",
                          e.target.value
                        )
                      }
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="py-2">
                <p className="text-lg font-medium">
                  {preferences.doNotDisturbHours.startTime} - {preferences.doNotDisturbHours.endTime}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  No sessions will be scheduled during these hours
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Timezone */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="h-5 w-5 mr-2 text-blue-500" />
              Time Zone
            </CardTitle>
            <CardDescription>
              Your timezone is automatically detected and can be corrected manually if needed.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isEditing ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Your Time Zone</Label>
                  <div className="flex gap-2">
                    <Select
                      value={selectedTimezone}
                      onValueChange={setSelectedTimezone}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select time zone" />
                      </SelectTrigger>
                      <SelectContent>
                        {timezones.map((tz) => (
                          <SelectItem key={tz.value} value={tz.value}>
                            {tz.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAutoDetectTimezone}
                      disabled={isDetecting}
                      className="px-3"
                    >
                      {isDetecting ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Click the refresh button to auto-detect your timezone
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleSaveTimezone}
                    disabled={isSavingTimezone}
                    size="sm"
                  >
                    {isSavingTimezone ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Timezone"
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => cancelEditing(preferences)}
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div className="py-2">
                <p className="text-lg font-medium">
                  {timezones.find(tz => tz.value === selectedTimezone)?.label || selectedTimezone}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  All times are displayed in your local time zone
                </p>
                <div className="mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                  >
                    Change Timezone
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TutorPreferences;

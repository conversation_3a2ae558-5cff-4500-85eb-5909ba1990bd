import { create } from "zustand";

// Types for tutor data
export interface TutorAvailabilitySlot {
  day_of_week: number; // 0 = Sunday, 6 = Saturday
  start_time: string;
  end_time: string;
  status: "available" | "auto_accept" | "manual_approval";
}

// Represents the tutor data from the database
export interface TutorDBRecord {
  id: string;
  education_level?: string;
  hourly_rate?: number;
  subjects_taught?: string;
  teaching_experience?: string;
  bio?: string;
  availability?: any; // jsonb in the database
  verification_status?: string;
  cv_file_path?: string;
  rating?: number;
  date_of_birth?: string;
  created_at: string;
  updated_at: string;
}

// Represents the combined tutor data with profile information
export interface Tu<PERSON> extends TutorDBRecord {
  // Fields from profiles table
  first_name: string;
  last_name: string;
  email: string;
  profile_picture_url?: string; // This is the field name in the database schema
  timezone?: string;

  // Computed or transformed fields
  availability_slots?: TutorAvailabilitySlot[];
  subjects?: string[]; // Parsed from subjects_taught
}

// Tutor store state and actions
interface TutorStore {
  // State
  tutors: Tu<PERSON>[];
  selectedTutor: Tutor | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchTutors: () => Promise<void>;
  fetchTutorById: (id: string) => Promise<Tutor | null>;
  createTutor: (tutor: {
    id: string; // User ID is required
    education_level?: string;
    hourly_rate?: number;
    subjects_taught?: string;
    teaching_experience?: string;
    bio?: string;
    verification_status?: string;
    cv_file_path?: string;
    date_of_birth?: string;
    availability_slots?: TutorAvailabilitySlot[];
  }) => Promise<Tutor | null>;
  updateTutor: (id: string, updates: Partial<Tutor>) => Promise<boolean>;
  deleteTutor: (id: string) => Promise<boolean>;
  setSelectedTutor: (tutor: Tutor | null) => void;
  searchTutors: (query: string) => Tutor[];
  filterTutorsBySubject: (subjectId: string) => Tutor[];
}

// Create the store
export const useTutorStore = create<TutorStore>((set, get) => ({
  // Initial state
  tutors: [],
  selectedTutor: null,
  isLoading: false,
  error: null,

  // Actions
  fetchTutors: async () => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Join tutors table with profiles to get complete tutor information
      const { data, error } = await supabase
        .from("tutors")
        .select(`
          id,
          education_level,
          hourly_rate,
          subjects_taught,
          teaching_experience,
          bio,
          availability,
          verification_status,
          cv_file_path,
          rating,
          date_of_birth,
          created_at,
          updated_at,
          profiles:id (
            first_name,
            last_name,
            email,
            profile_picture_url,
            timezone
          )
        `)
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Transform the data to match our Tutor interface
      const transformedData = data?.map(tutor => {
        const profile = tutor.profiles || {};

        // Type assertion for profile to avoid TypeScript errors
        const typedProfile = profile as {
          first_name?: string;
          last_name?: string;
          email?: string;
          profile_picture_url?: string;
          timezone?: string;
        };

        // Parse subjects_taught into an array if it exists
        const subjects = tutor.subjects_taught ?
          tutor.subjects_taught.split(',').map((s: string) => s.trim()) :
          [];

        // Parse availability JSON into structured slots
        const availabilitySlots = tutor.availability ?
          Object.entries(tutor.availability).map(([key, value]: [string, any]) => ({
            day_of_week: parseInt(key),
            ...value
          })) :
          [];

        // Create a new object without the profiles property
        const { profiles: _, ...restData } = tutor;

        return {
          ...restData,
          first_name: typedProfile.first_name || '',
          last_name: typedProfile.last_name || '',
          email: typedProfile.email || '',
          profile_picture_url: typedProfile.profile_picture_url,
          timezone: typedProfile.timezone,
          subjects,
          availability_slots: availabilitySlots
        };
      });

      set({ tutors: transformedData || [], isLoading: false });
    } catch (error) {
      console.error("Error fetching tutors:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load tutors",
        isLoading: false
      });
    }
  },

  fetchTutorById: async (id) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { data, error } = await supabase
        .from("tutors")
        .select(`
          id,
          education_level,
          hourly_rate,
          subjects_taught,
          teaching_experience,
          bio,
          availability,
          verification_status,
          cv_file_path,
          rating,
          date_of_birth,
          created_at,
          updated_at,
          profiles:id (
            first_name,
            last_name,
            email,
            profile_picture_url,
            timezone
          )
        `)
        .eq("id", id)
        .single();

      if (error) throw error;

      // Transform the data to match our Tutor interface
      const profile = data.profiles || {};

      // Parse subjects_taught into an array if it exists
      const subjects = data.subjects_taught ?
        data.subjects_taught.split(',').map((s: string) => s.trim()) :
        [];

      // Parse availability JSON into structured slots
      const availabilitySlots = data.availability ?
        Object.entries(data.availability).map(([key, value]: [string, any]) => ({
          day_of_week: parseInt(key),
          ...value
        })) :
        [];

      // Create a new object without the profiles property
      const { profiles: _, ...restData } = data;

      // Type assertion for profile to avoid TypeScript errors
      const typedProfile = profile as {
        first_name?: string;
        last_name?: string;
        email?: string;
        profile_picture_url?: string;
        timezone?: string;
      };

      const transformedData: Tutor = {
        ...restData,
        first_name: typedProfile.first_name || '',
        last_name: typedProfile.last_name || '',
        email: typedProfile.email || '',
        profile_picture_url: typedProfile.profile_picture_url,
        timezone: typedProfile.timezone,
        subjects,
        availability_slots: availabilitySlots
      };

      set({ selectedTutor: transformedData, isLoading: false });
      return transformedData;
    } catch (error) {
      console.error(`Error fetching tutor with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to load tutor with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  createTutor: async (tutorData) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Extract fields that belong to the tutors table
      const {
        id, // User ID must be provided
        education_level,
        hourly_rate,
        subjects_taught,
        teaching_experience,
        bio,
        verification_status,
        cv_file_path,
        date_of_birth,
        availability_slots
      } = tutorData;

      if (!id) {
        throw new Error("User ID is required to create a tutor profile");
      }

      // Convert availability_slots to the format expected by the database
      const availability = availability_slots?.reduce((acc, slot) => {
        acc[slot.day_of_week] = {
          start_time: slot.start_time,
          end_time: slot.end_time,
          status: slot.status
        };
        return acc;
      }, {});

      // Insert into tutors table
      const { data, error } = await supabase
        .from("tutors")
        .insert({
          id,
          education_level,
          hourly_rate,
          subjects_taught,
          teaching_experience,
          bio,
          availability,
          verification_status,
          cv_file_path,
          date_of_birth
        })
        .select()
        .single();

      if (error) throw error;

      // Fetch the profile data to combine with tutor data
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("first_name, last_name, email, profile_picture_url, timezone")
        .eq("id", data.id)
        .single();

      if (profileError) throw profileError;

      // Combine the data
      const combinedData: Tutor = {
        ...data,
        first_name: profileData.first_name || '',
        last_name: profileData.last_name || '',
        email: profileData.email || '',
        profile_picture_url: profileData.profile_picture_url,
        timezone: profileData.timezone,
        subjects: data.subjects_taught ? data.subjects_taught.split(',').map((s: string) => s.trim()) : [],
        availability_slots: data.availability ?
          Object.entries(data.availability).map(([key, value]: [string, any]) => ({
            day_of_week: parseInt(key),
            ...value
          })) :
          []
      };

      // Update the tutors list with the new tutor
      set(state => ({
        tutors: [...state.tutors, combinedData],
        isLoading: false
      }));

      return combinedData;
    } catch (error) {
      console.error("Error creating tutor:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to create tutor",
        isLoading: false
      });
      return null;
    }
  },

  updateTutor: async (id, updates) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Extract fields that belong to the tutors table
      const {
        education_level,
        hourly_rate,
        subjects_taught,
        teaching_experience,
        bio,
        verification_status,
        cv_file_path,
        date_of_birth,
        availability_slots,
        first_name,
        last_name,
        email,
        profile_picture_url,
        timezone
      } = updates;

      // Prepare tutor table updates
      const tutorUpdates: any = {};

      if (education_level !== undefined) tutorUpdates.education_level = education_level;
      if (hourly_rate !== undefined) tutorUpdates.hourly_rate = hourly_rate;
      if (subjects_taught !== undefined) tutorUpdates.subjects_taught = subjects_taught;
      if (teaching_experience !== undefined) tutorUpdates.teaching_experience = teaching_experience;
      if (bio !== undefined) tutorUpdates.bio = bio;
      if (verification_status !== undefined) tutorUpdates.verification_status = verification_status;
      if (cv_file_path !== undefined) tutorUpdates.cv_file_path = cv_file_path;
      if (date_of_birth !== undefined) tutorUpdates.date_of_birth = date_of_birth;

      // Convert availability_slots to the format expected by the database
      if (availability_slots) {
        const availability = availability_slots.reduce((acc, slot) => {
          acc[slot.day_of_week] = {
            start_time: slot.start_time,
            end_time: slot.end_time,
            status: slot.status
          };
          return acc;
        }, {});

        tutorUpdates.availability = availability;
      }

      // Update tutors table if there are tutor-specific updates
      if (Object.keys(tutorUpdates).length > 0) {
        const { error } = await supabase
          .from("tutors")
          .update(tutorUpdates)
          .eq("id", id);

        if (error) throw error;
      }

      // Prepare profile table updates
      const profileUpdates: any = {};

      if (first_name !== undefined) profileUpdates.first_name = first_name;
      if (last_name !== undefined) profileUpdates.last_name = last_name;
      if (email !== undefined) profileUpdates.email = email;
      if (profile_picture_url !== undefined) profileUpdates.profile_picture_url = profile_picture_url;
      if (timezone !== undefined) profileUpdates.timezone = timezone;

      // Update profiles table if there are profile-specific updates
      if (Object.keys(profileUpdates).length > 0) {
        const { error } = await supabase
          .from("profiles")
          .update(profileUpdates)
          .eq("id", id);

        if (error) throw error;
      }

      // Fetch the updated tutor to ensure we have the latest data
      const updatedTutor = await get().fetchTutorById(id);

      if (!updatedTutor) {
        throw new Error(`Failed to fetch updated tutor with ID ${id}`);
      }

      // Update the tutors list with the updated tutor
      set(state => ({
        tutors: state.tutors.map(tutor =>
          tutor.id === id ? updatedTutor : tutor
        ),
        selectedTutor: updatedTutor,
        isLoading: false
      }));

      return true;
    } catch (error) {
      console.error(`Error updating tutor with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update tutor with ID ${id}`,
        isLoading: false
      });
      return false;
    }
  },

  deleteTutor: async (id) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { error } = await supabase
        .from("tutors")
        .delete()
        .eq("id", id);

      if (error) throw error;

      // Remove the deleted tutor from the tutors list
      set(state => ({
        tutors: state.tutors.filter(tutor => tutor.id !== id),
        selectedTutor: state.selectedTutor?.id === id ? null : state.selectedTutor,
        isLoading: false
      }));

      return true;
    } catch (error) {
      console.error(`Error deleting tutor with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete tutor with ID ${id}`,
        isLoading: false
      });
      return false;
    }
  },

  setSelectedTutor: (tutor) => {
    set({ selectedTutor: tutor });
  },

  searchTutors: (query) => {
    const { tutors } = get();
    const lowercaseQuery = query.toLowerCase();

    return tutors.filter(tutor =>
      tutor.first_name.toLowerCase().includes(lowercaseQuery) ||
      tutor.last_name.toLowerCase().includes(lowercaseQuery) ||
      tutor.email.toLowerCase().includes(lowercaseQuery) ||
      (tutor.subjects && tutor.subjects.some((subject: string) =>
        subject.toLowerCase().includes(lowercaseQuery)
      )) ||
      (tutor.teaching_experience &&
        tutor.teaching_experience.toLowerCase().includes(lowercaseQuery)
      )
    );
  },

  filterTutorsBySubject: (subjectId) => {
    const { tutors } = get();

    return tutors.filter(tutor =>
      tutor.subjects && tutor.subjects.includes(subjectId)
    );
  }
}));

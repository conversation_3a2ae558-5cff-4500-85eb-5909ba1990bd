-- RLS Policies for Session-Related Tables
-- This file contains Row Level Security policies for sessions, session_details, session_feedback, and session_requests tables

-- =====================================================
-- 1. SESSIONS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on sessions table
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view their own sessions" ON sessions;
DROP POLICY IF EXISTS "Tutors can view their assigned sessions" ON sessions;
DROP POLICY IF EXISTS "Ad<PERSON> can view all sessions" ON sessions;
DROP POLICY IF EXISTS "Students can create sessions" ON sessions;
DROP POLICY IF EXISTS "Tutors can create sessions" ON sessions;
DROP POLICY IF EXISTS "Admins can create sessions" ON sessions;
DROP POLICY IF EXISTS "Students can update their own sessions" ON sessions;
DROP POLICY IF EXISTS "Tutors can update their assigned sessions" ON sessions;
DROP POLICY IF EXISTS "Admins can update all sessions" ON sessions;
DROP POLICY IF EXISTS "Service role full access sessions" ON sessions;

-- Students can view sessions where they are the student
CREATE POLICY "Students can view their own sessions"
ON public.sessions
FOR SELECT
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Tutors can view sessions where they are the tutor
CREATE POLICY "Tutors can view their assigned sessions"
ON public.sessions
FOR SELECT
TO authenticated
USING (
  tutor_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'tutor'
  )
);

-- Admins can view all sessions
CREATE POLICY "Admins can view all sessions"
ON public.sessions
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can create sessions (when they are the student)
CREATE POLICY "Students can create sessions"
ON public.sessions
FOR INSERT
TO authenticated
WITH CHECK (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Tutors can create sessions (when they are the tutor)
CREATE POLICY "Tutors can create sessions"
ON public.sessions
FOR INSERT
TO authenticated
WITH CHECK (
  tutor_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'tutor'
  )
);

-- Admins can create any sessions
CREATE POLICY "Admins can create sessions"
ON public.sessions
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can update their own sessions (limited fields)
CREATE POLICY "Students can update their own sessions"
ON public.sessions
FOR UPDATE
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
)
WITH CHECK (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Tutors can update their assigned sessions
CREATE POLICY "Tutors can update their assigned sessions"
ON public.sessions
FOR UPDATE
TO authenticated
USING (
  tutor_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'tutor'
  )
)
WITH CHECK (
  tutor_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'tutor'
  )
);

-- Admins can update all sessions
CREATE POLICY "Admins can update all sessions"
ON public.sessions
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access sessions"
ON public.sessions
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 2. SESSION_DETAILS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on session_details table
ALTER TABLE public.session_details ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view their session details" ON session_details;
DROP POLICY IF EXISTS "Tutors can view their session details" ON session_details;
DROP POLICY IF EXISTS "Admins can view all session details" ON session_details;
DROP POLICY IF EXISTS "Students can create session details" ON session_details;
DROP POLICY IF EXISTS "Tutors can create session details" ON session_details;
DROP POLICY IF EXISTS "Admins can create session details" ON session_details;
DROP POLICY IF EXISTS "Students can update their session details" ON session_details;
DROP POLICY IF EXISTS "Tutors can update their session details" ON session_details;
DROP POLICY IF EXISTS "Admins can update all session details" ON session_details;
DROP POLICY IF EXISTS "Service role full access session details" ON session_details;

-- Students can view session details for their sessions
CREATE POLICY "Students can view their session details"
ON public.session_details
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Tutors can view session details for their sessions
CREATE POLICY "Tutors can view their session details"
ON public.session_details
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.tutor_id = auth.uid()
    AND p.user_type = 'tutor'
  )
);

-- Admins can view all session details
CREATE POLICY "Admins can view all session details"
ON public.session_details
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can create session details for their sessions
CREATE POLICY "Students can create session details"
ON public.session_details
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Tutors can create session details for their sessions
CREATE POLICY "Tutors can create session details"
ON public.session_details
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.tutor_id = auth.uid()
    AND p.user_type = 'tutor'
  )
);

-- Admins can create any session details
CREATE POLICY "Admins can create session details"
ON public.session_details
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can update session details for their sessions
CREATE POLICY "Students can update their session details"
ON public.session_details
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.student_id = auth.uid()
    AND p.user_type = 'student'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Tutors can update session details for their sessions
CREATE POLICY "Tutors can update their session details"
ON public.session_details
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.tutor_id = auth.uid()
    AND p.user_type = 'tutor'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_details.session_id
    AND s.tutor_id = auth.uid()
    AND p.user_type = 'tutor'
  )
);

-- Admins can update all session details
CREATE POLICY "Admins can update all session details"
ON public.session_details
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access session details"
ON public.session_details
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 3. SESSION_FEEDBACK TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on session_feedback table
ALTER TABLE public.session_feedback ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Tutors can view session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Admins can view all session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Students can create session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Tutors can create session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Admins can create session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Students can update their session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Tutors can update their session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Admins can update all session feedback" ON session_feedback;
DROP POLICY IF EXISTS "Service role full access session feedback" ON session_feedback;

-- Students can view feedback for sessions they participated in
CREATE POLICY "Students can view session feedback"
ON public.session_feedback
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_feedback.session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
    AND p.user_type = 'student'
  )
);

-- Tutors can view feedback for sessions they participated in
CREATE POLICY "Tutors can view session feedback"
ON public.session_feedback
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_feedback.session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
    AND p.user_type = 'tutor'
  )
);

-- Admins can view all session feedback
CREATE POLICY "Admins can view all session feedback"
ON public.session_feedback
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Students can create feedback for sessions they participated in
CREATE POLICY "Students can create session feedback"
ON public.session_feedback
FOR INSERT
TO authenticated
WITH CHECK (
  submitted_by = auth.uid()
  AND user_role = 'student'
  AND EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_feedback.session_id
    AND s.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Tutors can create feedback for sessions they participated in
CREATE POLICY "Tutors can create session feedback"
ON public.session_feedback
FOR INSERT
TO authenticated
WITH CHECK (
  submitted_by = auth.uid()
  AND user_role = 'tutor'
  AND EXISTS (
    SELECT 1 FROM public.sessions s
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE s.id = session_feedback.session_id
    AND s.tutor_id = auth.uid()
    AND p.user_type = 'tutor'
  )
);

-- Admins can create any session feedback
CREATE POLICY "Admins can create session feedback"
ON public.session_feedback
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Students can update their own feedback
CREATE POLICY "Students can update their session feedback"
ON public.session_feedback
FOR UPDATE
TO authenticated
USING (
  submitted_by = auth.uid()
  AND user_role = 'student'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
)
WITH CHECK (
  submitted_by = auth.uid()
  AND user_role = 'student'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Tutors can update their own feedback
CREATE POLICY "Tutors can update their session feedback"
ON public.session_feedback
FOR UPDATE
TO authenticated
USING (
  submitted_by = auth.uid()
  AND user_role = 'tutor'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'tutor'
  )
)
WITH CHECK (
  submitted_by = auth.uid()
  AND user_role = 'tutor'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'tutor'
  )
);

-- Admins can update all session feedback
CREATE POLICY "Admins can update all session feedback"
ON public.session_feedback
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access session feedback"
ON public.session_feedback
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 4. SESSION_REQUESTS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on session_requests table
ALTER TABLE public.session_requests ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view session requests" ON session_requests;
DROP POLICY IF EXISTS "Tutors can view session requests" ON session_requests;
DROP POLICY IF EXISTS "Admins can view all session requests" ON session_requests;
DROP POLICY IF EXISTS "Students can create session requests" ON session_requests;
DROP POLICY IF EXISTS "Tutors can create session requests" ON session_requests;
DROP POLICY IF EXISTS "Admins can create session requests" ON session_requests;
DROP POLICY IF EXISTS "Students can update session requests" ON session_requests;
DROP POLICY IF EXISTS "Tutors can update session requests" ON session_requests;
DROP POLICY IF EXISTS "Admins can update all session requests" ON session_requests;
DROP POLICY IF EXISTS "Service role full access session requests" ON session_requests;

-- Students can view session requests where they are the student or the target tutor
CREATE POLICY "Students can view session requests"
ON public.session_requests
FOR SELECT
TO authenticated
USING (
  (student_id = auth.uid() OR tutor_id = auth.uid())
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Tutors can view session requests where they are the tutor or the target student
CREATE POLICY "Tutors can view session requests"
ON public.session_requests
FOR SELECT
TO authenticated
USING (
  (tutor_id = auth.uid() OR student_id = auth.uid())
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'tutor'
  )
);

-- Admins can view all session requests
CREATE POLICY "Admins can view all session requests"
ON public.session_requests
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Students can create session requests (when they are the student)
CREATE POLICY "Students can create session requests"
ON public.session_requests
FOR INSERT
TO authenticated
WITH CHECK (
  student_id = auth.uid()
  AND requested_by = 'student'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Tutors can create session requests (when they are the tutor)
CREATE POLICY "Tutors can create session requests"
ON public.session_requests
FOR INSERT
TO authenticated
WITH CHECK (
  tutor_id = auth.uid()
  AND requested_by = 'tutor'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'tutor'
  )
);

-- Admins can create any session requests
CREATE POLICY "Admins can create session requests"
ON public.session_requests
FOR INSERT
TO authenticated
WITH CHECK (
  requested_by = 'admin'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Students can update session requests where they are involved
CREATE POLICY "Students can update session requests"
ON public.session_requests
FOR UPDATE
TO authenticated
USING (
  (student_id = auth.uid() OR tutor_id = auth.uid())
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
)
WITH CHECK (
  (student_id = auth.uid() OR tutor_id = auth.uid())
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Tutors can update session requests where they are involved
CREATE POLICY "Tutors can update session requests"
ON public.session_requests
FOR UPDATE
TO authenticated
USING (
  (tutor_id = auth.uid() OR student_id = auth.uid())
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'tutor'
  )
)
WITH CHECK (
  (tutor_id = auth.uid() OR student_id = auth.uid())
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'tutor'
  )
);

-- Admins can update all session requests
CREATE POLICY "Admins can update all session requests"
ON public.session_requests
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access session requests"
ON public.session_requests
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 5. VERIFICATION QUERIES
-- =====================================================

-- Function to verify RLS policies are enabled on all session tables
CREATE OR REPLACE FUNCTION verify_session_rls_policies()
RETURNS TABLE (
  table_name TEXT,
  rls_enabled BOOLEAN,
  policy_count INTEGER
) AS $$
BEGIN
  SET search_path TO public;

  RETURN QUERY
  SELECT
    t.tablename::TEXT,
    t.rowsecurity as rls_enabled,
    (
      SELECT COUNT(*)::INTEGER
      FROM pg_policies p
      WHERE p.tablename = t.tablename
    ) as policy_count
  FROM pg_tables t
  WHERE t.schemaname = 'public'
  AND t.tablename IN (
    'sessions', 'session_details', 'session_feedback', 'session_requests'
  )
  ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to list all session-related policies
CREATE OR REPLACE FUNCTION list_session_policies()
RETURNS TABLE (
  table_name TEXT,
  policy_name TEXT,
  command TEXT,
  roles TEXT[],
  policy_condition TEXT
) AS $$
BEGIN
  SET search_path TO public;

  RETURN QUERY
  SELECT
    p.tablename::TEXT,
    p.policyname::TEXT,
    p.cmd::TEXT,
    p.roles,
    p.qual::TEXT
  FROM pg_policies p
  WHERE p.tablename IN (
    'sessions', 'session_details', 'session_feedback', 'session_requests'
  )
  ORDER BY p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. USAGE EXAMPLES AND VERIFICATION
-- =====================================================

-- Check RLS status for all session tables
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE
        WHEN rowsecurity THEN 'RLS is ENABLED'
        ELSE 'RLS is DISABLED'
    END as status
FROM pg_tables
WHERE tablename IN ('sessions', 'session_details', 'session_feedback', 'session_requests')
ORDER BY tablename;

-- Count policies per table
SELECT
    tablename,
    COUNT(*) as policy_count
FROM pg_policies
WHERE tablename IN ('sessions', 'session_details', 'session_feedback', 'session_requests')
GROUP BY tablename
ORDER BY tablename;

-- =====================================================
-- 7. SECURITY NOTES AND BEST PRACTICES
-- =====================================================

/*
SECURITY CONSIDERATIONS:

1. BIDIRECTIONAL ACCESS: Session requests and feedback allow both students and tutors
   to view/modify records where they are involved, supporting the bidirectional
   session booking workflow.

2. PAYMENT DATA PROTECTION: These policies ensure tutors cannot access student
   payment information, as per user preferences.

3. ADMIN OVERSIGHT: Admins have full access to all session data for management
   and support purposes.

4. SERVICE ROLE: Backend processes can perform all operations through the
   service role for automated workflows.

5. PROFILE VALIDATION: All policies validate user_type from the profiles table
   to ensure users can only access data appropriate to their role.

TESTING RECOMMENDATIONS:

1. Test with different user types (student, tutor, admin)
2. Verify cross-user access is properly restricted
3. Test session request approval workflows
4. Verify feedback visibility between participants
5. Test admin access to all data

PERFORMANCE CONSIDERATIONS:

1. Policies use EXISTS clauses for efficient lookups
2. Consider adding indexes on frequently queried columns:
   - sessions(student_id, tutor_id)
   - session_details(session_id)
   - session_feedback(session_id, submitted_by)
   - session_requests(student_id, tutor_id, status)

MAINTENANCE:

1. Review policies when adding new user types
2. Update policies if session workflow changes
3. Monitor query performance and adjust indexes as needed
4. Regular security audits of policy effectiveness
*/

-- Usage examples:
-- SELECT * FROM verify_session_rls_policies();
-- SELECT * FROM list_session_policies();

-- End of Session Tables RLS Policies

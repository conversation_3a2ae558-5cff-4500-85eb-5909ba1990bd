import { useState, useRef } from "react";
import { toast } from "@/components/ui/UseToast";
import { supabase } from "@/lib/supabaseClient";

type FileUploadStatus = "idle" | "uploading" | "success" | "error";

interface UseFileUploadOptions {
  maxSize?: number;
  acceptedFileTypes?: string[];
  bucketName: string;
  folderPath?: string;
  onSuccess?: (filePath: string) => void;
  validateFile?: (file: File) => { valid: boolean; error?: string };
}

interface UseFileUploadReturn {
  uploadStatus: FileUploadStatus;
  fileName: string;
  uploadError: string;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleFileSelect: (file: File) => Promise<void>;
  handleFileRemove: () => void;
  uploadFile: (file: File) => Promise<string>;
}

export function useFileUpload({
  maxSize = 5 * 1024 * 1024, // 5MB default
  acceptedFileTypes = [],
  bucketName,
  folderPath = "",
  onSuccess,
  validateFile,
}: UseFileUploadOptions): UseFileUploadReturn {
  const [uploadStatus, setUploadStatus] = useState<FileUploadStatus>("idle");
  const [fileName, setFileName] = useState<string>("");
  const [uploadError, setUploadError] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFileType = (file: File): boolean => {
    if (acceptedFileTypes.length === 0) return true;

    const fileExtension = `.${file.name.split(".").pop()?.toLowerCase()}`;
    return acceptedFileTypes.some((type) =>
      type.startsWith(".")
        ? fileExtension === type.toLowerCase()
        : file.type.match(new RegExp(type, "i"))
    );
  };

  const uploadFile = async (file: File): Promise<string> => {
    try {
      // Check file size
      if (file.size > maxSize) {
        throw new Error(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
      }

      // Check file type
      if (!validateFileType(file)) {
        throw new Error(
          `File type not supported. Accepted formats: ${acceptedFileTypes.join(
            ", "
          )}`
        );
      }

      // Custom validation if provided
      if (validateFile) {
        const validation = validateFile(file);
        if (!validation.valid) {
          throw new Error(validation.error || "Invalid file");
        }
      }

      // Get current user session
      const {
        data: { session },
      } = await supabase.auth.getSession();

      // Generate a unique file name
      const fileExt = file.name.split(".").pop();
      const fileName = `${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 15)}.${fileExt}`;

      let filePath;

      // For guest users, use a special guest folder
      if (!session?.user) {
        // Get the email from localStorage for guest users
        const guestEmail = localStorage.getItem("pendingTutorEmail");
        if (!guestEmail) {
          throw new Error(
            "Guest email not found. Please enter your email first."
          );
        }

        // Create a hash of the email to use as a folder name
        const emailHash = btoa(guestEmail).replace(/[+/=]/g, "");
        filePath = `guest/${emailHash}/${fileName}`;
      } else {
        // For authenticated users, use their user ID
        filePath = `${folderPath}/${session.user.id}/${fileName}`;
      }

      // Upload file to Supabase Storage
      const { error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          // Use unauthenticated upload for guest users
          upsert: true,
        });

      if (error) {
        console.error("Error uploading file:", error);
        throw new Error(error.message);
      }

      return filePath;
    } catch (error) {
      console.error("Error in file upload:", error);
      throw error;
    }
  };

  const handleFileSelect = async (file: File): Promise<void> => {
    // Check if this is an error file (from file-upload component)
    if (!file.size || (file as any).error) {
      setUploadStatus("error");
      setUploadError((file as any).errorMessage || "Invalid file");
      setFileName(file.name || "");
      return;
    }

    setFileName(file.name);
    setUploadStatus("uploading");

    try {
      const filePath = await uploadFile(file);

      setUploadStatus("success");
      setUploadError(""); // Clear any previous error messages

      if (onSuccess) {
        onSuccess(filePath);
      }

      toast({
        title: "File uploaded successfully",
        description: "Your file has been uploaded.",
      });
    } catch (error) {
      setUploadStatus("error");
      setUploadError(error instanceof Error ? error.message : "Upload failed");

      toast({
        title: "Upload failed",
        description:
          error instanceof Error ? error.message : "Please try again",
        variant: "destructive",
      });
    }
  };

  const handleFileRemove = (): void => {
    setFileName("");
    setUploadStatus("idle");
    setUploadError("");

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    toast({
      title: "File removed",
      description: "Your file has been removed",
    });
  };

  return {
    uploadStatus,
    fileName,
    uploadError,
    fileInputRef,
    handleFileSelect,
    handleFileRemove,
    uploadFile,
  };
}

import { But<PERSON> } from "@/components/ui/Button";
import { Facebook, Linkedin } from "lucide-react";
import { FcGoogle } from "react-icons/fc";
import { BsMicrosoft } from "react-icons/bs";
import { supabase } from "@/lib/supabaseClient";
import { useState } from "react";
import { useToast } from "@/components/ui/UseToast";

interface SocialLoginButtonsProps {
  isSubmitting: boolean;
  userType?: string; // Add this parameter
}

const SocialLoginButtons = ({
  isSubmitting,
  userType = "student", // Default to student if not provided
}: SocialLoginButtonsProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});

  const handleSocialLogin = async (
    provider: "google" | "azure" | "facebook" | "linkedin_oidc"
  ) => {
    try {
      setIsLoading((prev) => ({ ...prev, [provider]: true }));

      // For now, only implement Google OAuth as it's the most commonly used
      // You can enable others in Supabase dashboard as needed
      if (provider !== "google") {
        toast({
          title: "Provider not available",
          description: `${provider} login is not configured yet. Please use Google or email login.`,
          variant: "destructive",
        });
        return;
      }

      // OAuth userType is now passed via URL parameter to the callback

      // Redirect to auth/callback with userType parameter so it knows where to go next
      const redirectUrl = `${window.location.origin}/auth/callback?oauth_usertype=${userType}`;

      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: redirectUrl,
        },
      });

      if (error) {
        throw error;
      }

      // If this code runs, redirectTo didn't immediately redirect
      toast({
        title: "Authentication initiated",
        description:
          "Please complete the authentication process in the new window.",
      });
    } catch (error: any) {
      console.error(`${provider} authentication error:`, error);
      toast({
        title: "Authentication failed",
        description: error.message || `Failed to authenticate with ${provider}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading((prev) => ({ ...prev, [provider]: false }));
    }
  };

  return (
    <div className="grid grid-cols-2 gap-3 mt-4">
      <Button
        type="button"
        variant="outline"
        onClick={() => handleSocialLogin("google")}
        disabled={isSubmitting || isLoading["google"]}
        className="flex items-center justify-center gap-2"
      >
        <FcGoogle size={20} />
        Google
      </Button>

      {/* Temporarily disable other providers until they're configured in Supabase */}
      <Button
        type="button"
        variant="outline"
        disabled={true}
        className="flex items-center justify-center gap-2 opacity-50"
      >
        <BsMicrosoft size={20} className="text-blue-500" />
        Microsoft
      </Button>

      <Button
        type="button"
        variant="outline"
        disabled={true}
        className="flex items-center justify-center gap-2 opacity-50"
      >
        <Facebook size={20} className="text-blue-600" />
        Facebook
      </Button>

      <Button
        type="button"
        variant="outline"
        disabled={true}
        className="flex items-center justify-center gap-2 opacity-50"
      >
        <Linkedin size={20} className="text-blue-700" />
        LinkedIn
      </Button>
    </div>
  );
};

export default SocialLoginButtons;

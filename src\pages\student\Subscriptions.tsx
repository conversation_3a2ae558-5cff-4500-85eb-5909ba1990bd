import React, { useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useBillingStore } from "@/store/billingStore";
import SubscriptionCard from "@/components/student/billing/SubscriptionCard";
import EnrollmentStatus from "@/components/student/billing/EnrollmentStatus";
import LoadingSpinner from "@/components/LoadingSpinner";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/hooks/useToast";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/Dialog";
import { useNavigate } from "react-router-dom";
import CurriculumConfigurationModal from "@/components/student/curriculum/CurriculumConfigurationModal";

const Subscriptions: React.FC = () => {
  const { user } = useAuth();
  const profileData = useProfileData();
  const navigate = useNavigate();
  const { toast } = useToast();
  const {
    subscriptions,
    isLoading,
    error,
    fetchSubscriptions,
    cancelSubscription
  } = useBillingStore();

  const [cancelDialogOpen, setCancelDialogOpen] = React.useState(false);
  const [selectedSubscriptionId, setSelectedSubscriptionId] = React.useState<string | null>(null);

  // Curriculum configuration modal state
  const [curriculumModalOpen, setCurriculumModalOpen] = React.useState(false);
  const [selectedSubscriptionForConfig, setSelectedSubscriptionForConfig] = React.useState<{
    id: string;
    productType: string;
    productName: string;
  } | null>(null);

  // Note: New subscription workflow is now handled via navigation instead of modal

  useEffect(() => {
    if (user?.id) {
      fetchSubscriptions(user.id);
    }
  }, [fetchSubscriptions, user?.id]);

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
    if (!selectedSubscriptionId) return;

    const success = await cancelSubscription(selectedSubscriptionId);

    if (success) {
      toast({
        title: "Subscription cancelled",
        description: "Your subscription has been cancelled successfully",
        type: "success",
      });
    } else {
      toast({
        title: "Cancellation failed",
        description: "There was an error cancelling your subscription. Please try again.",
        type: "error",
      });
    }

    setCancelDialogOpen(false);
    setSelectedSubscriptionId(null);
  };

  // Handle cancel button click
  const handleCancelClick = (subscriptionId: string) => {
    setSelectedSubscriptionId(subscriptionId);
    setCancelDialogOpen(true);
  };

  // Handle curriculum configuration button click
  const handleConfigureCurriculum = (subscriptionId: string, productType: string) => {
    const subscription = subscriptions.find(sub => sub.id === subscriptionId);
    if (subscription) {
      setSelectedSubscriptionForConfig({
        id: subscriptionId,
        productType: productType,
        productName: subscription.product_name
      });
      setCurriculumModalOpen(true);
    }
  };

  // Handle curriculum modal close
  const handleCurriculumModalClose = () => {
    setCurriculumModalOpen(false);
    setSelectedSubscriptionForConfig(null);
    // Refresh subscriptions to update configuration status
    if (user?.id) {
      fetchSubscriptions(user.id);
    }
  };

  // Get active, expired, and cancelled subscriptions
  const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active' && (sub.days_remaining || 0) > 0);
  const expiredSubscriptions = subscriptions.filter(sub => sub.status === 'expired' || ((sub.status === 'active' && (sub.days_remaining || 0) <= 0)));
  const cancelledSubscriptions = subscriptions.filter(sub => sub.status === 'cancelled');

  return (
    <StudentPageLayout
      title="My Subscriptions"
      profileData={profileData}
      description="Manage your learning subscriptions"
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading subscriptions...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => user?.id && fetchSubscriptions(user.id)}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Enrollment Status Card */}
          <EnrollmentStatus />

          {/* Active Subscriptions */}
          {activeSubscriptions.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Active Subscriptions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {activeSubscriptions.map((subscription) => (
                  <SubscriptionCard
                    key={subscription.id}
                    subscription={subscription}
                    onCancel={handleCancelClick}
                    onConfigureCurriculum={handleConfigureCurriculum}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Expired Subscriptions */}
          {expiredSubscriptions.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Expired Subscriptions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {expiredSubscriptions.map((subscription) => (
                  <SubscriptionCard
                    key={subscription.id}
                    subscription={{...subscription, status: 'expired'}}
                    onCancel={() => {}}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Cancelled Subscriptions */}
          {cancelledSubscriptions.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Cancelled Subscriptions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {cancelledSubscriptions.map((subscription) => (
                  <SubscriptionCard
                    key={subscription.id}
                    subscription={subscription}
                    onCancel={() => {}}
                  />
                ))}
              </div>
            </div>
          )}


        </div>
      )}

      {/* Cancel Subscription Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this subscription? You will lose access to the product when your current subscription period ends.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setCancelDialogOpen(false)}>
              Keep Subscription
            </Button>
            <Button
              variant="destructive"
              onClick={handleCancelSubscription}
            >
              Cancel Subscription
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Curriculum Configuration Modal */}
      {selectedSubscriptionForConfig && (
        <CurriculumConfigurationModal
          isOpen={curriculumModalOpen}
          onClose={handleCurriculumModalClose}
          subscriptionId={selectedSubscriptionForConfig.id}
          productType={selectedSubscriptionForConfig.productType as 'booster' | 'custom' | 'preparation'}
          productName={selectedSubscriptionForConfig.productName}
        />
      )}

      {/* Note: New subscription workflow is now handled via dedicated pages with breadcrumb navigation */}
    </StudentPageLayout>
  );
};

export default Subscriptions;
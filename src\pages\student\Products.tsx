// src/pages/student/Products.tsx
import React, { useEffect, useState } from "react";
import { useAuth } from "@/context/AuthContext";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useBillingStore } from "@/store/billingStore";
import ProductCard from "@/components/student/billing/ProductCard";
import LoadingSpinner from "@/components/LoadingSpinner";
import { AlertCircle, ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/hooks/useToast";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/Dialog";

// FAQ data for rfLearn products
const faqData = [
  {
    id: 1,
    question: "How are rfLearn courses different from other platforms?",
    answer: "At rfLearn, we prioritize reinforcement-based learning combined with AI and human expertise. Our approach focuses on personalized learning paths, interactive sessions with qualified tutors, and adaptive content that adjusts to your learning pace. We believe in quality over quantity, providing focused, engaging content that helps you master concepts faster and retain information longer.",
    isOpen: false
  },
  {
    id: 2,
    question: "Are rfLearn subscriptions free?",
    answer: "We offer both free and premium subscription options. Our free tier includes access to basic courses and limited tutor sessions. Premium subscriptions unlock advanced courses, unlimited tutor access, personalized learning analytics, and priority support to accelerate your learning journey.",
    isOpen: false
  },
  {
    id: 3,
    question: "Why should I choose rfLearn for my education?",
    answer: "rfLearn combines the best of AI-powered learning with human expertise. Our platform offers personalized learning experiences, one-on-one tutoring sessions, adaptive assessments, and real-time progress tracking. We focus on practical skills and knowledge application, ensuring you're not just learning but truly understanding and applying concepts.",
    isOpen: false
  },
  {
    id: 4,
    question: "Are there prerequisites for taking courses on rfLearn?",
    answer: "Most of our courses are designed to be accessible to learners at different levels. While some advanced courses may have prerequisites, we clearly indicate these requirements on each course page. For beginners, we recommend starting with our foundational courses that build essential skills progressively.",
    isOpen: false
  },
  {
    id: 5,
    question: "Will I get a certificate upon completion?",
    answer: "Yes! Upon successful completion of our courses, you'll receive a verified certificate that you can share on your professional profiles. Our certificates are recognized by industry partners and demonstrate your mastery of the subject matter through our comprehensive assessment system.",
    isOpen: false
  },
  {
    id: 6,
    question: "Can I access courses offline?",
    answer: "Selected course materials can be downloaded for offline access with premium subscriptions. However, interactive sessions with tutors and real-time assessments require an internet connection to provide the best learning experience.",
    isOpen: false
  },
  {
    id: 7,
    question: "What payment methods do you accept?",
    answer: "We accept major credit cards, PayPal, and bank transfers. All payments are processed securely, and you can manage your subscription and payment methods from your account settings.",
    isOpen: false
  }
];

const Products: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const {
    products,
    subscriptions,
    isLoading,
    error,
    fetchProducts,
    fetchSubscriptions,
    purchaseProduct
  } = useBillingStore();

  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<string>("credit_card");
  const [purchaseState, setPurchaseState] = useState<{
    isProcessing: boolean;
    productId: string | null;
    error: string | null;
    step: 'idle' | 'processing' | 'success' | 'error';
  }>({
    isProcessing: false,
    productId: null,
    error: null,
    step: 'idle'
  });

  // FAQ state management
  const [faqItems, setFaqItems] = useState(faqData);

  // Toggle FAQ item
  const toggleFaqItem = (id: number) => {
    setFaqItems(items =>
      items.map(item =>
        item.id === id ? { ...item, isOpen: !item.isOpen } : item
      )
    );
  };

  useEffect(() => {
    fetchProducts();
    if (user?.id) {
      fetchSubscriptions(user.id);
    }
  }, [user?.id]); // Only depend on user.id, not the functions

  // Check if student already has an active subscription for a product
  const isSubscribed = (productId: string) => {
    return subscriptions.some(sub =>
      sub.product_id === productId &&
      sub.status === 'active' &&
      (sub.days_remaining || 0) > 0
    );
  };

  // Handle product selection
  const handleSelectProduct = (productId: string) => {
    setSelectedProduct(productId);
    setConfirmDialogOpen(true);
  };

  // Handle purchase confirmation with comprehensive state management
  const handleConfirmPurchase = async () => {
    if (!user?.id || !selectedProduct) return;

    // Prevent multiple executions
    if (purchaseState.isProcessing || purchaseState.productId === selectedProduct) {
      console.log('Purchase already in progress for this product');
      return;
    }

    // Set processing state
    setPurchaseState({
      isProcessing: true,
      productId: selectedProduct,
      error: null,
      step: 'processing'
    });

    try {
      const success = await purchaseProduct(user.id, selectedProduct, paymentMethod);

      if (success) {
        // Success state
        setPurchaseState({
          isProcessing: false,
          productId: null,
          error: null,
          step: 'success'
        });

        toast({
          title: "🎉 Purchase Successful!",
          description: "Your subscription has been activated and is ready to use.",
          type: "success",
        });

        // Close dialog after short delay to show success
        setTimeout(() => {
          setConfirmDialogOpen(false);
          setSelectedProduct(null);
          setPurchaseState({
            isProcessing: false,
            productId: null,
            error: null,
            step: 'idle'
          });
        }, 1500);

      } else {
        // Get detailed error from store
        const storeError = error || "Unknown error occurred";

        // Set error state with detailed message
        setPurchaseState({
          isProcessing: false,
          productId: selectedProduct,
          error: getDetailedErrorMessage(storeError),
          step: 'error'
        });

        toast({
          title: "❌ Purchase Failed",
          description: "Don't worry - no payment has been charged. Please try again.",
          type: "error",
        });
      }
    } catch (err) {
      // Handle unexpected errors
      const errorMessage = err instanceof Error ? err.message : "Unexpected error occurred";

      setPurchaseState({
        isProcessing: false,
        productId: selectedProduct,
        error: getDetailedErrorMessage(errorMessage),
        step: 'error'
      });

      toast({
        title: "❌ Purchase Failed",
        description: "No payment has been charged. Please try again.",
        type: "error",
      });
    }
  };

  // Get user-friendly error messages
  const getDetailedErrorMessage = (error: string): string => {
    if (error.includes('already have an active subscription')) {
      return "You already have an active subscription for this product. Check your subscriptions page.";
    }
    if (error.includes('network') || error.includes('fetch')) {
      return "Network connection issue. Please check your internet connection and try again.";
    }
    if (error.includes('payment')) {
      return "Payment processing failed. Please verify your payment method and try again.";
    }
    if (error.includes('product')) {
      return "Product information could not be loaded. Please refresh the page and try again.";
    }
    return `Purchase failed: ${error}. No payment has been charged. Please try again or contact support if the issue persists.`;
  };

  // Handle retry purchase
  const handleRetryPurchase = () => {
    setPurchaseState({
      isProcessing: false,
      productId: null,
      error: null,
      step: 'idle'
    });
  };

  // Handle cancel purchase
  const handleCancelPurchase = () => {
    if (!purchaseState.isProcessing) {
      setConfirmDialogOpen(false);
      setSelectedProduct(null);
      setPurchaseState({
        isProcessing: false,
        productId: null,
        error: null,
        step: 'idle'
      });
    }
  };

  // Get selected product details
  const getSelectedProduct = () => {
    return products.find(p => p.id === selectedProduct);
  };

  return (
    <StudentPageLayout
      title="Learning Products"
      description="Explore our learning products and subscriptions"
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading products...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => fetchProducts()}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {products.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                isSubscribed={isSubscribed(product.id)}
                onPurchase={handleSelectProduct}
                isProcessing={purchaseState.isProcessing && purchaseState.productId === product.id}
                purchaseStep={purchaseState.productId === product.id ? purchaseState.step : 'idle'}
              />
            ))}
          </div>

          {/* FAQ Section */}
          <div className="max-w-4xl">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">FAQs</h2>
            <div className="space-y-4">
              {faqItems.map((faq) => (
                <div
                  key={faq.id}
                  className="border border-gray-200 rounded-lg overflow-hidden bg-white"
                >
                  <button
                    onClick={() => toggleFaqItem(faq.id)}
                    className="w-full px-6 py-4 text-left bg-white hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors duration-200"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900 pr-4">
                        {faq.question}
                      </h3>
                      <div className="flex-shrink-0">
                        {faq.isOpen ? (
                          <ChevronUp className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-500" />
                        )}
                      </div>
                    </div>
                  </button>
                  {faq.isOpen && (
                    <div className="px-6 pb-4 bg-white">
                      <p className="text-gray-700 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Purchase Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={handleCancelPurchase}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {purchaseState.step === 'processing' && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
              {purchaseState.step === 'success' && (
                <div className="text-green-600">✅</div>
              )}
              {purchaseState.step === 'error' && (
                <div className="text-red-600">❌</div>
              )}
              {purchaseState.step === 'processing' && "Processing Purchase..."}
              {purchaseState.step === 'success' && "Purchase Successful!"}
              {purchaseState.step === 'error' && "Purchase Failed"}
              {purchaseState.step === 'idle' && "Confirm Purchase"}
            </DialogTitle>
            <DialogDescription>
              {purchaseState.step === 'processing' && (
                <span className="text-blue-600">
                  Please wait while we process your purchase. Do not close this window.
                </span>
              )}
              {purchaseState.step === 'success' && (
                <span className="text-green-600">
                  Your subscription to {getSelectedProduct()?.name} has been activated!
                </span>
              )}
              {purchaseState.step === 'error' && (
                <span className="text-red-600">
                  {purchaseState.error}
                </span>
              )}
              {purchaseState.step === 'idle' && (
                `You are about to purchase ${getSelectedProduct()?.name}`
              )}
            </DialogDescription>
          </DialogHeader>

          {/* Show purchase details only in idle state */}
          {purchaseState.step === 'idle' && (
            <div className="space-y-4 py-4">
              <div className="flex justify-between">
                <span>Product:</span>
                <span className="font-medium">{getSelectedProduct()?.name}</span>
              </div>
              <div className="flex justify-between">
                <span>Price:</span>
                <span className="font-medium">${getSelectedProduct()?.price.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Duration:</span>
                <span className="font-medium">{getSelectedProduct()?.duration_days} days</span>
              </div>

              <div className="border-t pt-4">
                <p className="font-medium mb-2">Payment Method</p>
                <select
                  className="w-full p-2 border rounded-md"
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  disabled={purchaseState.isProcessing}
                >
                  <option value="credit_card">Credit Card</option>
                  <option value="paypal">PayPal</option>
                  <option value="bank_transfer">Bank Transfer</option>
                </select>
              </div>
            </div>
          )}

          {/* Processing state */}
          {purchaseState.step === 'processing' && (
            <div className="py-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Processing your purchase...</p>
              <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
            </div>
          )}

          {/* Success state */}
          {purchaseState.step === 'success' && (
            <div className="py-8 text-center">
              <div className="text-green-600 text-6xl mb-4">🎉</div>
              <p className="text-gray-600">Your subscription is now active!</p>
              <p className="text-sm text-gray-500 mt-2">You can start using your subscription immediately</p>
            </div>
          )}

          {/* Error state */}
          {purchaseState.step === 'error' && (
            <div className="py-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="flex items-start">
                  <div className="text-red-600 mr-3 mt-1">⚠️</div>
                  <div>
                    <h4 className="font-medium text-red-800 mb-1">What happened?</h4>
                    <p className="text-red-700 text-sm mb-3">{purchaseState.error}</p>
                    <div className="bg-green-100 border border-green-200 rounded p-3">
                      <p className="text-green-800 text-sm font-medium">💳 No payment has been charged</p>
                      <p className="text-green-700 text-xs mt-1">Your payment method was not charged for this failed transaction.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            {purchaseState.step === 'idle' && (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancelPurchase}
                  disabled={purchaseState.isProcessing}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleConfirmPurchase}
                  disabled={purchaseState.isProcessing}
                  className="min-w-[140px]"
                >
                  {purchaseState.isProcessing ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Processing...
                    </div>
                  ) : (
                    'Confirm Purchase'
                  )}
                </Button>
              </>
            )}

            {purchaseState.step === 'processing' && (
              <Button disabled className="w-full">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Processing Purchase...
                </div>
              </Button>
            )}

            {purchaseState.step === 'success' && (
              <Button onClick={handleCancelPurchase} className="w-full bg-green-600 hover:bg-green-700">
                Continue
              </Button>
            )}

            {purchaseState.step === 'error' && (
              <>
                <Button variant="outline" onClick={handleCancelPurchase}>
                  Cancel
                </Button>
                <Button onClick={handleRetryPurchase} className="bg-blue-600 hover:bg-blue-700">
                  Try Again
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </StudentPageLayout>
  );
};

export default Products;
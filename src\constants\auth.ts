// User status constants
export const USER_STATUS = {
  REGISTERED: "registered" as const,
  GUEST: "guest" as const,
  NEW: "new" as const,
};

// User type constants
export const USER_TYPE = {
  STUDENT: "student" as const,
  TUTOR: "tutor" as const,
  ADMIN: "admin" as const,
  GUEST: "guest" as const,
};

// Type definitions
export type UserType = "student" | "tutor" | "admin" | "guest";
export type IsOnboarded = boolean;
export type UserStatus = "guest" | "new" | "registered";

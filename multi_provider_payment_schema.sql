-- Multi-Provider Payment Schema Enhancement
-- This schema extends the current design to support multiple payment providers
-- (Stripe, PayPal, Apple Pay, Google Pay, etc.)
--
-- IMPORTANT: This script should be run AFTER the Razorpay-specific migration:
-- - supabase/migrations/20241201000000_razorpay_backend_setup.sql
--
-- The Razorpay migration creates the payment_providers table, basic provider columns, and Razorpay configuration.
-- This script adds support for additional providers (Stripe, PayPal, etc.) and enhanced multi-provider features.

-- =====================================================
-- 1. PAYMENT PROVIDERS TABLE
-- =====================================================
-- Note: payment_providers table and Razorpay provider are created in the Razorpay-specific migration
-- This section adds additional providers (Stripe, PayPal, etc.) to the existing table

-- Insert additional providers (excluding Razorpay which is handled separately)
INSERT INTO payment_providers (name, display_name, supported_currencies, supported_methods) VALUES
('stripe', 'Stripe', ARRAY['usd', 'eur', 'gbp', 'cad'], ARRAY['card', 'bank_transfer', 'apple_pay', 'google_pay']),
('paypal', 'PayPal', ARRAY['usd', 'eur', 'gbp', 'cad'], ARRAY['paypal_account', 'card']),
('apple_pay', 'Apple Pay', ARRAY['usd', 'eur', 'gbp'], ARRAY['apple_pay']),
('google_pay', 'Google Pay', ARRAY['usd', 'eur', 'gbp'], ARRAY['google_pay'])
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- 2. ENHANCED PAYMENTS TABLE
-- =====================================================
-- Note: Basic provider columns (provider_id, provider_payment_id, provider_order_id, provider_data)
-- are added in the Razorpay-specific migration. This section adds additional columns for multi-provider support.

-- Add additional provider support columns to existing payments table
ALTER TABLE payments
ADD COLUMN IF NOT EXISTS provider_customer_id TEXT, -- Generic provider customer ID
ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}', -- Provider-specific data
ADD COLUMN IF NOT EXISTS provider_created_at TIMESTAMP WITH TIME ZONE;

-- Make stripe fields nullable since we now support multiple providers
ALTER TABLE payments ALTER COLUMN stripe_payment_intent_id DROP NOT NULL;

-- Add constraint to ensure either stripe or generic provider fields are used
ALTER TABLE payments ADD CONSTRAINT check_payment_provider
CHECK (
    (stripe_payment_intent_id IS NOT NULL AND provider_id IS NULL) OR
    (stripe_payment_intent_id IS NULL AND provider_id IS NOT NULL AND provider_payment_id IS NOT NULL)
);

-- Update indexes (basic provider indexes are created in Razorpay migration)
CREATE INDEX IF NOT EXISTS idx_payments_provider_customer_id ON payments(provider_customer_id);

-- =====================================================
-- 3. ENHANCED PAYMENT METHODS TABLE
-- =====================================================

-- Add provider support to payment methods
ALTER TABLE payment_methods 
ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
ADD COLUMN IF NOT EXISTS provider_payment_method_id TEXT, -- Generic provider method ID
ADD COLUMN IF NOT EXISTS provider_customer_id TEXT, -- Generic provider customer ID
ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}'; -- Provider-specific data

-- Make stripe fields nullable
ALTER TABLE payment_methods ALTER COLUMN stripe_payment_method_id DROP NOT NULL;
ALTER TABLE payment_methods ALTER COLUMN stripe_customer_id DROP NOT NULL;

-- Add constraint for provider consistency
ALTER TABLE payment_methods ADD CONSTRAINT check_payment_method_provider 
CHECK (
    (stripe_payment_method_id IS NOT NULL AND provider_id IS NULL) OR
    (stripe_payment_method_id IS NULL AND provider_id IS NOT NULL AND provider_payment_method_id IS NOT NULL)
);

-- Update indexes
CREATE INDEX IF NOT EXISTS idx_payment_methods_provider_id ON payment_methods(provider_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_provider_method_id ON payment_methods(provider_payment_method_id);

-- =====================================================
-- 4. ENHANCED SUBSCRIPTIONS TABLE
-- =====================================================

-- Add provider support to subscriptions
ALTER TABLE subscriptions 
ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
ADD COLUMN IF NOT EXISTS provider_subscription_id TEXT, -- Generic provider subscription ID
ADD COLUMN IF NOT EXISTS provider_customer_id TEXT, -- Generic provider customer ID
ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}'; -- Provider-specific data

-- Update indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_provider_id ON subscriptions(provider_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_provider_subscription_id ON subscriptions(provider_subscription_id);

-- =====================================================
-- 5. ENHANCED INVOICES TABLE
-- =====================================================

-- Add provider support to invoices
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
ADD COLUMN IF NOT EXISTS provider_invoice_id TEXT, -- Generic provider invoice ID
ADD COLUMN IF NOT EXISTS provider_customer_id TEXT, -- Generic provider customer ID
ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}'; -- Provider-specific data

-- Update indexes
CREATE INDEX IF NOT EXISTS idx_invoices_provider_id ON invoices(provider_id);
CREATE INDEX IF NOT EXISTS idx_invoices_provider_invoice_id ON invoices(provider_invoice_id);

-- =====================================================
-- 6. ENHANCED PAYMENT EVENTS TABLE
-- =====================================================

-- Add provider support to payment events
ALTER TABLE payment_events 
ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
ADD COLUMN IF NOT EXISTS provider_event_id TEXT; -- Generic provider event ID

-- Make stripe fields nullable
ALTER TABLE payment_events ALTER COLUMN stripe_event_id DROP NOT NULL;

-- Update indexes
CREATE INDEX IF NOT EXISTS idx_payment_events_provider_id ON payment_events(provider_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_provider_event_id ON payment_events(provider_event_id);

-- =====================================================
-- 7. HELPER FUNCTIONS FOR MULTI-PROVIDER SUPPORT
-- =====================================================

-- Function to get payment provider by name
CREATE OR REPLACE FUNCTION get_payment_provider(provider_name TEXT)
RETURNS UUID AS $$
DECLARE
    provider_id UUID;
BEGIN
    SELECT id INTO provider_id 
    FROM payment_providers 
    WHERE name = provider_name AND is_active = TRUE;
    
    IF provider_id IS NULL THEN
        RAISE EXCEPTION 'Payment provider % not found or inactive', provider_name;
    END IF;
    
    RETURN provider_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create payment with provider
CREATE OR REPLACE FUNCTION create_payment_with_provider(
    p_workflow_id UUID,
    p_student_id UUID,
    p_provider_name TEXT,
    p_provider_payment_id TEXT,
    p_amount DECIMAL(10,2),
    p_currency TEXT DEFAULT 'usd',
    p_description TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    payment_id UUID;
    provider_id UUID;
BEGIN
    -- Get provider ID
    provider_id := get_payment_provider(p_provider_name);
    
    -- Create payment record
    INSERT INTO payments (
        workflow_id,
        student_id,
        provider_id,
        provider_payment_id,
        amount,
        currency,
        status,
        description
    ) VALUES (
        p_workflow_id,
        p_student_id,
        provider_id,
        p_provider_payment_id,
        p_amount,
        p_currency,
        'pending',
        p_description
    ) RETURNING id INTO payment_id;
    
    RETURN payment_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update payment status (provider agnostic)
CREATE OR REPLACE FUNCTION update_payment_status_generic(
    p_provider_payment_id TEXT,
    p_provider_name TEXT,
    p_status TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    provider_id UUID;
BEGIN
    -- Get provider ID
    provider_id := get_payment_provider(p_provider_name);
    
    -- Update payment status
    UPDATE payments 
    SET 
        status = p_status,
        succeeded_at = CASE WHEN p_status = 'succeeded' THEN NOW() AT TIME ZONE 'UTC' ELSE succeeded_at END,
        failed_at = CASE WHEN p_status = 'failed' THEN NOW() AT TIME ZONE 'UTC' ELSE failed_at END,
        updated_at = NOW() AT TIME ZONE 'UTC'
    WHERE provider_payment_id = p_provider_payment_id 
    AND provider_id = provider_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. ENHANCED SUBSCRIPTION WORKFLOWS TABLE
-- =====================================================

-- Add provider support to subscription workflows
ALTER TABLE subscription_workflows
ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
ADD COLUMN IF NOT EXISTS provider_order_id TEXT, -- Generic provider order/payment ID
ADD COLUMN IF NOT EXISTS provider_customer_id TEXT, -- Generic provider customer ID
ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}'; -- Provider-specific data

-- Update indexes
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_provider_id ON subscription_workflows(provider_id);
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_provider_order_id ON subscription_workflows(provider_order_id);

-- =====================================================
-- 9. MIGRATION FOR EXISTING DATA
-- =====================================================

-- Migrate existing Stripe data to use provider system
DO $$
DECLARE
    stripe_provider_id UUID;
BEGIN
    -- Get Stripe provider ID
    SELECT id INTO stripe_provider_id FROM payment_providers WHERE name = 'stripe';
    
    IF stripe_provider_id IS NOT NULL THEN
        -- Update existing payments
        UPDATE payments 
        SET provider_id = stripe_provider_id,
            provider_payment_id = stripe_payment_intent_id,
            provider_customer_id = stripe_customer_id,
            provider_created_at = stripe_created_at
        WHERE stripe_payment_intent_id IS NOT NULL AND provider_id IS NULL;
        
        -- Update existing payment methods
        UPDATE payment_methods 
        SET provider_id = stripe_provider_id,
            provider_payment_method_id = stripe_payment_method_id,
            provider_customer_id = stripe_customer_id
        WHERE stripe_payment_method_id IS NOT NULL AND provider_id IS NULL;
        
        -- Update existing subscriptions
        UPDATE subscriptions 
        SET provider_id = stripe_provider_id,
            provider_subscription_id = stripe_subscription_id,
            provider_customer_id = stripe_customer_id
        WHERE stripe_subscription_id IS NOT NULL AND provider_id IS NULL;
        
        -- Update existing invoices
        UPDATE invoices 
        SET provider_id = stripe_provider_id,
            provider_invoice_id = stripe_invoice_id,
            provider_customer_id = stripe_customer_id
        WHERE stripe_invoice_id IS NOT NULL AND provider_id IS NULL;
        
        -- Update existing payment events
        UPDATE payment_events 
        SET provider_id = stripe_provider_id,
            provider_event_id = stripe_event_id
        WHERE stripe_event_id IS NOT NULL AND provider_id IS NULL;
        
        RAISE NOTICE 'Migrated existing Stripe data to multi-provider schema';
    END IF;
END $$;

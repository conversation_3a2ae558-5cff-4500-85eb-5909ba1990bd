import React, { useEffect} from "react";
import {
  LayoutDashboard,
  Calendar,
  BookOpen,
  User,
  MessageSquare,
  Edit3,
  Eye,
  Clock,
  LogOut,
  Menu,
  X,
  Home,
  Settings,
  ArrowUp,
  MessageCircle,
  CalendarPlus,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import Sidebar, { SidebarItemType } from "@/components/ui/Sidebar";
import { usePendingRequestsStore } from "@/store/pendingRequestsStore";

// Logo component for the tutor sidebar
const TutorLogo = () => (
  <div className="flex flex-col items-start">
    <span className="text-rfpurple-400 text-lg font-medium">Tutor</span>
    <div className="flex items-center">
      <span className="text-rfpurple-600 text-2xl font-bold">rfLearn</span>
    </div>
  </div>
);

const TutorSidebar: React.FC = () => {
  const { signOut, user } = useAuth();
  const { requests, fetchRequests } = usePendingRequestsStore();

  // Fetch pending requests when sidebar loads
  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  // Count of pending requests
  const pendingRequestsCount = requests.length;

  const menuItems: SidebarItemType[] = [
    {
      icon: <LayoutDashboard size={18} />,
      label: "Dashboard",
      path: "/tutor/dashboard",
    },
    {
      icon: <Calendar size={18} />,
      label: "My Schedule",
      path: "/tutor/schedule",
    },
    {
      icon: <Clock size={18} />,
      label: "Session Requests",
      badgeCount: pendingRequestsCount,
      subItems: [
        {
          icon: <Clock size={16} />,
          label: "Pending Requests",
          path: "/tutor/requests/pending",
          badgeCount: pendingRequestsCount,
        },
        {
          icon: <ArrowUp size={16} />,
          label: "Accepted Requests",
          path: "/tutor/requests/accepted",
        },
        {
          icon: <MessageCircle size={16} />,
          label: "Rejection Approvals",
          path: "/tutor/requests/rejections",
        },
      ],
    },
    {
      icon: <BookOpen size={18} />,
      label: "Sessions",
      subItems: [
        {
          icon: <CalendarPlus size={16} />,
          label: "Book Sessions",
          path: "/tutor/book-sessions",
        },
        {
          icon: <Calendar size={16} />,
          label: "Upcoming Sessions",
          path: "/tutor/sessions/upcoming",
        },
        {
          icon: <Clock size={16} />,
          label: "Session History",
          path: "/tutor/sessions/history",
        },
        {
          icon: <BookOpen size={16} />,
          label: "All Sessions",
          path: "/tutor/sessions/all",
        },
      ],
    },
    {
      icon: <MessageSquare size={18} />,
      label: "Messages",
      path: "/tutor/messages",
    },
    {
      icon: <User size={18} />,
      label: "My Students",
      subItems: [
        {
          icon: <Eye size={16} />,
          label: "Overview",
          path: "/tutor/students/overview",
        },
        {
          icon: <Edit3 size={16} />,
          label: "Performance & Activity",
          path: "/tutor/students/performance",
        },
        {
          icon: <BookOpen size={16} />,
          label: "Learning Journey",
          path: "/tutor/students/journey",
        },
      ],
    },
    {
      icon: <Settings size={18} />,
      label: "Profile",
      subItems: [
        {
          icon: <Eye size={16} />,
          label: "View Profile",
          path: "/tutor/profile",
        },
        {
          icon: <Edit3 size={16} />,
          label: "Edit Profile",
          path: "/tutor/profile/edit",
        },
        {
          icon: <BookOpen size={16} />,
          label: "Resources",
          path: "/tutor/profile/resources",
        },
      ],
    },
  ];

  const handleLogout = async () => {
    await signOut();
  };

  return (
    <Sidebar
      title={
        <div className="flex flex-col">
          <span className="text-rfpurple-500 font-bold">rfLearn</span>
          <span className="text-sm border border-rfpurple-500 rounded-full px-2 text-center">
            Tutor
          </span>
        </div>
      }
      menuItems={menuItems}
      onLogout={handleLogout}
      logoutIcon={<LogOut size={18} />}
      headerColor="text-rfpurple-500"
      homeLink="/"
      homeIcon={<Home size={20} />}
      expandIcon={<Menu size={16} />}
      collapseIcon={<X size={16} />}
    />
  );
};

export default TutorSidebar;


import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/Dialog";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Star, Clock, MapPin, BookOpen, Award, Calendar, Briefcase, GraduationCap, Languages, Users, ThumbsUp } from "lucide-react";
// Removed Link import as we don't want to navigate away from the page

// Featured Review Component
const FeaturedReview = ({ review }: { review: any }) => (
  <Card className="p-5 bg-gradient-to-r from-rfpurple-50 to-white border-l-4 border-rfpurple-400 my-6">
    <div className="flex items-center gap-2 mb-2">
      <Star size={16} fill="#facc15" className="text-yellow-500" />
      <span className="font-semibold">{review.studentName}</span>
      <span className="text-xs text-gray-400">{review.date}</span>
    </div>
    <div className="text-gray-800 italic mb-2">"{review.comment}"</div>
    {review.tutorResponse && (
      <div className="text-sm text-gray-600 border-l-2 border-rfpurple-300 pl-3 mt-2">
        <span className="font-semibold text-rfpurple-600">Tutor Response:</span>{" "}
        {review.tutorResponse}
      </div>
    )}
  </Card>
);

interface TutorProfileModalProps {
  tutor: any;
  isOpen: boolean;
  onClose: () => void;
}

const TutorProfileModal = ({
  tutor,
  isOpen,
  onClose,
}: TutorProfileModalProps) => {
  if (!tutor) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        // Make sure to call onClose when the dialog is closed
        onClose();
      }
    }}>
      <DialogContent className="max-w-3xl max-h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-2xl">Tutor Profile</DialogTitle>
        </DialogHeader>

        <div
          className="flex-1 overflow-y-auto overflow-x-hidden pr-2 pb-4 focus:outline-none scrollbar-hide-when-inactive"
          tabIndex={0}
          style={{ overscrollBehavior: 'contain' }}
        >
          <div className="flex flex-col md:flex-row gap-6">
            <div className="md:w-1/3">
              <img
                src={tutor.photo || tutor.profilePictureUrl || "/images/tutors/default.jpg"}
                alt={tutor.name || (tutor.firstName && tutor.lastName ? `${tutor.firstName} ${tutor.lastName}` : 'Tutor')}
                className="w-full rounded-lg object-cover shadow-md"
              />

              {/* Availability section */}
              {tutor.availability && (
                <div className="mt-4 bg-gray-50 p-3 rounded-lg">
                  <h3 className="font-medium flex items-center mb-2">
                    <Calendar className="h-4 w-4 mr-2 text-rfpurple-500" />
                    Availability
                  </h3>
                  <div className="flex flex-wrap gap-1">
                    {Array.isArray(tutor.availability) && tutor.availability.map((day: any, index: number) => (
                      <Badge key={index} variant="outline" className="bg-white">
                        {typeof day === 'string' ? day : day.day}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Rate information */}
              <div className="mt-4 bg-rfpurple-50 p-3 rounded-lg">
                <h3 className="font-medium mb-1">Hourly Rate</h3>
                <p className="text-2xl font-bold text-rfpurple-600">${tutor.rate || tutor.hourlyRate || 0}/hr</p>
              </div>
            </div>

            <div className="md:w-2/3">
              <h2 className="text-2xl font-bold">
                {tutor.name || (tutor.firstName && tutor.lastName ? `${tutor.firstName} ${tutor.lastName}` : 'Tutor')}
              </h2>

              {tutor.rating && (
                <div className="flex items-center mt-2">
                  <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                  <span className="ml-1 font-medium">{tutor.rating}</span>
                  <span className="ml-1 text-gray-500">
                    ({Array.isArray(tutor.reviews) ? tutor.reviews.length : (tutor.reviewCount || 0)} reviews)
                  </span>
                </div>
              )}

              {tutor.location && (
                <div className="flex items-center mt-3">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="ml-2 text-gray-700">{tutor.location}</span>
                </div>
              )}

              {tutor.experience && (
                <div className="flex items-center mt-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="ml-2 text-gray-700">
                    {tutor.experience} experience
                  </span>
                </div>
              )}

              {tutor.education && typeof tutor.education === 'string' && (
                <div className="flex items-center mt-2">
                  <GraduationCap className="h-4 w-4 text-gray-500" />
                  <span className="ml-2 text-gray-700">{tutor.education}</span>
                </div>
              )}

              {/* Languages section */}
              {tutor.languages && (
                <div className="flex items-center mt-2">
                  <Languages className="h-4 w-4 text-gray-500" />
                  <span className="ml-2 text-gray-700">
                    {tutor.languages.join(", ")}
                  </span>
                </div>
              )}

              {/* Achievements section */}
              {tutor.achievements && (
                <div className="mt-4 bg-amber-50 p-3 rounded-lg">
                  <h3 className="font-medium flex items-center mb-2">
                    <Award className="h-4 w-4 mr-2 text-amber-500" />
                    Achievements
                  </h3>
                  <ul className="space-y-2">
                    {tutor.achievements.map((achievement: string, index: number) => (
                      <li key={index} className="flex items-start">
                        <ThumbsUp className="h-4 w-4 text-amber-500 mr-2 mt-1" />
                        <span>{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="mt-4">
                <h3 className="font-medium mb-2">Specialties</h3>
                <div className="flex flex-wrap gap-2">
                  {(tutor.specialties || tutor.specializations || []).map((specialty: string, index: number) => (
                    <Badge key={index} variant="outline" className="bg-gray-100">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Professional experience */}
              {tutor.professionalExperience && (
                <div className="mt-4">
                  <h3 className="font-medium flex items-center mb-2">
                    <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                    Professional Experience
                  </h3>
                  <ul className="space-y-2 text-gray-700">
                    {tutor.professionalExperience.map((exp: string, index: number) => (
                      <li key={index}>{exp}</li>
                    ))}
                  </ul>
                </div>
              )}

              {tutor.bio && (
                <div className="mt-4">
                  <h3 className="font-medium mb-2">Bio</h3>
                  <p className="text-gray-700">{tutor.bio}</p>
                </div>
              )}

              {/* Featured Review */}
              <div className="mt-4">
                <h3 className="font-medium mb-2">Featured Review</h3>
              </div>
              {tutor.reviews && tutor.reviews.length > 0 && (
                <FeaturedReview review={tutor.reviews[0]} />
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="mt-4 pt-4 border-t border-gray-100">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button
            className="button-gradient text-white"
            onClick={() => {
              // Here you can add any action you want when booking a session
              // For now, we'll just close the modal to prevent navigation
              onClose();
            }}
          >
            Book a Session
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TutorProfileModal;






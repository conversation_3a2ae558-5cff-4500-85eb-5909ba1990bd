import { create } from "zustand";
import {
  tutorAvailabilityService,
  autoAcceptRulesService,
  transformAvailabilitySlotForUI,
  transformAvailabilitySlotForDB,
  dayNameToDayOfWeek,
  dayOfWeekToDayName,
  TutorAvailabilitySlot
} from "@/services/tutorAvailabilityService";

// Types for availability management
export interface AvailabilitySlot {
  id: string;
  day: string;
  startTime: string;
  endTime: string;
  status: "available" | "auto_accept" | "manual_approval";
}

export interface AutoAcceptRule {
  id: string;
  name: string;
  active: boolean;
  conditions: {
    topics?: string[];
    existingStudentsOnly?: boolean;
    timeRanges?: {
      days: string[];
      startTime?: string;
      endTime?: string;
    }[];
  };
}

export interface TutorPreferences {
  defaultWorkingHours: {
    startTime: string;
    endTime: string;
  };
  sessionDurations: number[]; // in minutes
  bufferTime: number; // in minutes
  doNotDisturbHours: {
    startTime: string;
    endTime: string;
  };
  timezone: string;
}

export interface GoogleCalendarSettings {
  connected: boolean;
  syncAvailability: boolean;
  pullBusyTimes: boolean;
  sendReminders: boolean;
  conflictAlerts: boolean;
}

interface TutorAvailabilityState {
  // State
  tutorId: string | null;
  availabilitySlots: AvailabilitySlot[];
  autoAcceptRules: AutoAcceptRule[];
  preferences: TutorPreferences;
  googleCalendarSettings: GoogleCalendarSettings;
  isLoading: boolean;
  error: string | null;

  // UI state
  selectedDay: string | null;
  selectedSlot: string | null;
  isCreatingSlot: boolean;
  isEditingPreferences: boolean;

  // Grid UI state
  startHour: string;
  endHour: string;
  isDragging: boolean;
  dragStart: { day: string; time: string } | null;
  dragEnd: { day: string; time: string } | null;
  slotStatus: "available" | "auto_accept" | "manual_approval";

  // Actions
  setTutorId: (tutorId: string) => void;
  addAvailabilitySlot: (slot: Omit<AvailabilitySlot, "id">) => Promise<string>;
  updateAvailabilitySlot: (id: string, updates: Partial<AvailabilitySlot>) => Promise<void>;
  deleteAvailabilitySlot: (id: string) => Promise<void>;

  addAutoAcceptRule: (rule: Omit<AutoAcceptRule, "id">) => void;
  updateAutoAcceptRule: (id: string, updates: Partial<AutoAcceptRule>) => void;
  deleteAutoAcceptRule: (id: string) => void;
  toggleAutoAcceptRule: (id: string) => void;

  updatePreferences: (updates: Partial<TutorPreferences>) => void;
  updateGoogleCalendarSettings: (updates: Partial<GoogleCalendarSettings>) => void;

  setSelectedDay: (day: string | null) => void;
  setSelectedSlot: (slotId: string | null) => void;
  setIsCreatingSlot: (isCreating: boolean) => void;
  setIsEditingPreferences: (isEditing: boolean) => void;

  // Grid UI actions
  setStartHour: (hour: string) => void;
  setEndHour: (hour: string) => void;
  setIsDragging: (isDragging: boolean) => void;
  setDragStart: (dragStart: { day: string; time: string } | null) => void;
  setDragEnd: (dragEnd: { day: string; time: string } | null) => void;
  setSlotStatus: (status: "available" | "auto_accept" | "manual_approval") => void;
  handleCreateSlot: () => void;

  // Mock data loading functions (to be replaced with actual API calls)
  fetchAvailabilityData: () => Promise<void>;
  saveAvailabilityData: () => Promise<void>;
}

// Initial empty state - data will be loaded from database

// Create the store
export const useTutorAvailabilityStore = create<TutorAvailabilityState>((set, get) => ({
  // Initial state
  tutorId: null,
  availabilitySlots: [],
  autoAcceptRules: [],
  preferences: {
    defaultWorkingHours: {
      startTime: "09:00",
      endTime: "17:00",
    },
    sessionDurations: [30, 45, 60],
    bufferTime: 15,
    doNotDisturbHours: {
      startTime: "22:00",
      endTime: "08:00",
    },
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  },
  googleCalendarSettings: {
    connected: false,
    syncAvailability: true,
    pullBusyTimes: true,
    sendReminders: true,
    conflictAlerts: true,
  },
  isLoading: false,
  error: null,

  // UI state
  selectedDay: null,
  selectedSlot: null,
  isCreatingSlot: false,
  isEditingPreferences: false,

  // Grid UI state
  startHour: "08",
  endHour: "24",
  isDragging: false,
  dragStart: null,
  dragEnd: null,
  slotStatus: "available",

  // Actions
  setTutorId: (tutorId: string) => {
    set({ tutorId });
  },

  addAvailabilitySlot: async (slot) => {
    const { tutorId } = get();
    if (!tutorId) {
      throw new Error("Tutor ID is required to add availability slot");
    }

    console.log("Adding availability slot with data:", slot);
    set({ isLoading: true, error: null });

    try {
      // Transform UI slot to database format
      const dbSlot = transformAvailabilitySlotForDB(slot, tutorId);

      // Create slot in database
      const createdSlot = await tutorAvailabilityService.createAvailabilitySlot(dbSlot);

      // Transform back to UI format and add to state
      const uiSlot = transformAvailabilitySlotForUI(createdSlot);

      set((state) => ({
        availabilitySlots: [...state.availabilitySlots, uiSlot],
        isLoading: false
      }));

      console.log("Slot created successfully with ID:", createdSlot.id);
      return createdSlot.id;
    } catch (error) {
      console.error("Error adding availability slot:", error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to add availability slot"
      });
      throw error;
    }
  },

  updateAvailabilitySlot: async (id, updates) => {
    console.log("Updating availability slot:", id, updates);
    set({ isLoading: true, error: null });

    try {
      // Transform updates to database format if needed
      const dbUpdates: any = {};
      if (updates.status) dbUpdates.status = updates.status;
      if (updates.startTime) dbUpdates.start_time = updates.startTime;
      if (updates.endTime) dbUpdates.end_time = updates.endTime;
      if (updates.day) dbUpdates.day_of_week = dayNameToDayOfWeek(updates.day);

      // Update slot in database
      const updatedSlot = await tutorAvailabilityService.updateAvailabilitySlot(id, dbUpdates);

      // Transform back to UI format and update state
      const uiSlot = transformAvailabilitySlotForUI(updatedSlot);

      set((state) => ({
        availabilitySlots: state.availabilitySlots.map((slot) =>
          slot.id === id ? uiSlot : slot
        ),
        isLoading: false
      }));

      console.log("Slot updated successfully");
    } catch (error) {
      console.error("Error updating availability slot:", error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to update availability slot"
      });
      throw error;
    }
  },

  deleteAvailabilitySlot: async (id) => {
    console.log("Deleting availability slot:", id);
    set({ isLoading: true, error: null });

    try {
      // Delete slot from database
      await tutorAvailabilityService.deleteAvailabilitySlot(id);

      // Remove from state
      set((state) => ({
        availabilitySlots: state.availabilitySlots.filter((slot) => slot.id !== id),
        isLoading: false
      }));

      console.log("Slot deleted successfully");
    } catch (error) {
      console.error("Error deleting availability slot:", error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to delete availability slot"
      });
      throw error;
    }
  },

  addAutoAcceptRule: (rule) => {
    const newRule = {
      ...rule,
      id: `rule-${Date.now()}`,
    };
    set((state) => ({
      autoAcceptRules: [...state.autoAcceptRules, newRule],
    }));
  },

  updateAutoAcceptRule: (id, updates) => {
    set((state) => ({
      autoAcceptRules: state.autoAcceptRules.map((rule) =>
        rule.id === id ? { ...rule, ...updates } : rule
      ),
    }));
  },

  deleteAutoAcceptRule: (id) => {
    set((state) => ({
      autoAcceptRules: state.autoAcceptRules.filter((rule) => rule.id !== id),
    }));
  },

  toggleAutoAcceptRule: (id) => {
    set((state) => ({
      autoAcceptRules: state.autoAcceptRules.map((rule) =>
        rule.id === id ? { ...rule, active: !rule.active } : rule
      ),
    }));
  },

  updatePreferences: (updates) => {
    set((state) => ({
      preferences: { ...state.preferences, ...updates },
    }));
  },

  updateGoogleCalendarSettings: (updates) => {
    set((state) => ({
      googleCalendarSettings: { ...state.googleCalendarSettings, ...updates },
    }));
  },

  setSelectedDay: (day) => {
    set({ selectedDay: day });
  },

  setSelectedSlot: (slotId) => {
    set({ selectedSlot: slotId });
  },

  setIsCreatingSlot: (isCreating) => {
    set({ isCreatingSlot: isCreating });
  },

  setIsEditingPreferences: (isEditing) => {
    set({ isEditingPreferences: isEditing });
  },

  // Grid UI actions
  setStartHour: (hour) => {
    set({ startHour: hour });
  },

  setEndHour: (hour) => {
    set({ endHour: hour });
  },

  setIsDragging: (isDragging) => {
    set({ isDragging });
  },

  setDragStart: (dragStart) => {
    set({ dragStart });
  },

  setDragEnd: (dragEnd) => {
    set({ dragEnd });
  },

  setSlotStatus: (status) => {
    set({ slotStatus: status });
  },

  handleCreateSlot: () => {
    const { isDragging, dragStart, dragEnd, slotStatus, addAvailabilitySlot, availabilitySlots } = get();

    console.log("handleCreateSlot called in store", {
      isDragging,
      dragStart,
      dragEnd,
      slotStatus,
      availableSlotsCount: availabilitySlots.length
    });

    if (isDragging && dragStart && dragEnd) {
      // Ensure start time is before end time
      let startTime = dragStart.time;
      let endTime = dragEnd.time;

      console.log("Original times:", { startTime, endTime });

      const startMinutes = parseInt(startTime.split(":")[0]) * 60 + parseInt(startTime.split(":")[1]);
      const endMinutes = parseInt(endTime.split(":")[0]) * 60 + parseInt(endTime.split(":")[1]);

      console.log("Times in minutes:", { startMinutes, endMinutes, diff: Math.abs(endMinutes - startMinutes) });

      if (startMinutes > endMinutes) {
        console.log("Swapping start and end times because start is after end");
        [startTime, endTime] = [endTime, startTime];
      }

      // Add 30 minutes to end time (since the grid represents the start of each slot)
      const endHour = parseInt(endTime.split(":")[0]);
      const endMinute = parseInt(endTime.split(":")[1]);

      // Special handling for 23:30 slot - set end time to 24:00
      let adjustedEndTime: string;
      if (endHour === 23 && endMinute === 30) {
        console.log("Creating special slot ending at 24:00");
        adjustedEndTime = "24:00";
      } else {
        // Normal case - calculate end time (30 minutes after start time)
        const newEndMinute = endMinute + 30;
        const newEndHour = endHour + Math.floor(newEndMinute / 60);
        adjustedEndTime = `${newEndHour.toString().padStart(2, "0")}:${(newEndMinute % 60).toString().padStart(2, "0")}`;
      }

      console.log("Adjusted end time:", {
        originalEndTime: endTime,
        adjustedEndTime,
        endHour,
        endMinute
      });

      // Get the current slot status from the store
      const currentSlotStatus = get().slotStatus;

      // Check if a slot already exists that would overlap with this one
      const existingSlot = availabilitySlots.find(slot => {
        // Only check slots on the same day
        if (slot.day !== dragStart.day) return false;

        // Convert times to minutes for easier comparison
        const slotStartMinutes = parseInt(slot.startTime.split(":")[0]) * 60 + parseInt(slot.startTime.split(":")[1]);
        const slotEndMinutes = parseInt(slot.endTime.split(":")[0]) * 60 + parseInt(slot.endTime.split(":")[1]);
        const newSlotStartMinutes = parseInt(startTime.split(":")[0]) * 60 + parseInt(startTime.split(":")[1]);
        const newSlotEndMinutes = parseInt(adjustedEndTime.split(":")[0]) * 60 + parseInt(adjustedEndTime.split(":")[1]);

        // Check for any overlap between the slots
        const hasOverlap = (
          (newSlotStartMinutes < slotEndMinutes && newSlotEndMinutes > slotStartMinutes) ||
          (slotStartMinutes < newSlotEndMinutes && slotEndMinutes > newSlotStartMinutes)
        );

        console.log("Checking for overlap:", {
          existingSlot: `${slot.day} ${slot.startTime}-${slot.endTime}`,
          newSlot: `${dragStart.day} ${startTime}-${adjustedEndTime}`,
          slotStartMinutes,
          slotEndMinutes,
          newSlotStartMinutes,
          newSlotEndMinutes,
          hasOverlap
        });

        return hasOverlap;
      });

      if (existingSlot) {
        console.log("Slot already exists at this time:", existingSlot);
        // Reset drag state without creating a duplicate slot
        set({
          isDragging: false,
          dragStart: null,
          dragEnd: null
        });
        return;
      }

      console.log("Creating new slot", {
        day: dragStart.day,
        startTime,
        endTime: adjustedEndTime,
        status: currentSlotStatus, // Use the current status from the store
        dragStartTime: dragStart.time,
        dragEndTime: dragEnd.time,
        timeSpan: `${startTime} to ${adjustedEndTime}`
      });

      // Create new availability slot with the current status
      const newSlotId = addAvailabilitySlot({
        day: dragStart.day,
        startTime,
        endTime: adjustedEndTime,
        status: currentSlotStatus, // Use the current status from the store
      });

      console.log("Slot created successfully with ID:", newSlotId);

      // Reset drag state
      set({
        isDragging: false,
        dragStart: null,
        dragEnd: null
      });
    }
  },

  // Database operations
  fetchAvailabilityData: async () => {
    const { tutorId } = get();
    if (!tutorId) {
      console.warn("Cannot fetch availability data: tutorId is not set");
      return;
    }

    set({ isLoading: true, error: null });
    try {
      // Fetch availability slots from database
      const dbSlots = await tutorAvailabilityService.fetchAvailabilitySlots(tutorId);

      // Transform to UI format
      const uiSlots = dbSlots.map(transformAvailabilitySlotForUI);

      // Fetch auto accept rules from database
      const dbRules = await autoAcceptRulesService.fetchAutoAcceptRules(tutorId);

      // Transform auto accept rules to UI format
      const uiRules = dbRules.map(rule => ({
        id: rule.id,
        name: rule.name,
        active: rule.is_active,
        conditions: {
          topics: rule.topics.map(t => t.topic.name),
          existingStudentsOnly: rule.existing_students_only,
          timeRanges: rule.time_ranges.map(tr => ({
            days: [dayOfWeekToDayName(tr.day_of_week)],
            startTime: tr.start_time || undefined,
            endTime: tr.end_time || undefined
          }))
        }
      }));

      set({
        availabilitySlots: uiSlots,
        autoAcceptRules: uiRules,
        isLoading: false
      });
    } catch (error) {
      console.error("Error fetching availability data:", error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to load availability data"
      });
    }
  },

  saveAvailabilityData: async () => {
    // This function is kept for compatibility but individual operations
    // (add, update, delete) now save directly to the database
    console.log("saveAvailabilityData called - individual operations now save directly");
    return Promise.resolve();
  },
}));

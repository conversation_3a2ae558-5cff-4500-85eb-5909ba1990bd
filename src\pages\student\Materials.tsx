import React, { useState, useEffect } from "react";
import { useLocation, useSearchParams } from "react-router-dom";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/Card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/Select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";
import { create } from "zustand";
import {
  Search,
  Download,
  Play,
  Video,
  FileText,
  Image as FileImage,
  FileType as FilePdf,
  Archive as FileArchive,
  Table as FileSpreadsheet,
  Code as FileCode,
  File,
  Star,
  Calendar,
  Clock,
  BookOpen,
  ExternalLink,
  Info,
  ChevronDown,
  ChevronUp,
  X,
} from "lucide-react";

// Define types for our data
interface Material {
  id: string;
  title: string;
  description: string;
  type: "video" | "document" | "image" | "pdf" | "archive" | "spreadsheet" | "code" | "other";
  fileUrl: string;
  thumbnailUrl?: string;
  uploadedBy: string;
  uploadedAt: string;
  size: string;
  topic: string;
  subtopic: string;
  sessionId?: string;
  sessionDate?: string;
  isFavorite: boolean;
  tags: string[];
}

interface Recording {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl: string;
  duration: string;
  recordedAt: string;
  tutorName: string;
  tutorPhoto?: string;
  topic: string;
  subtopic: string;
  sessionId: string;
  isFavorite: boolean;
  watchedPercentage: number;
}

// Create a Zustand store for the Materials page
interface MaterialsState {
  materials: Material[];
  recordings: Recording[];
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  activeTab: string;
  topicFilter: string;
  typeFilter: string;
  sortBy: string;
  favorites: Set<string>;

  // Actions
  setSearchQuery: (query: string) => void;
  setActiveTab: (tab: string) => void;
  setTopicFilter: (topic: string) => void;
  setTypeFilter: (type: string) => void;
  setSortBy: (sort: string) => void;
  toggleFavorite: (id: string, type: "material" | "recording") => void;
  fetchMaterials: () => Promise<void>;
}

// Sample data for development
const sampleMaterials: Material[] = [
  {
    id: "mat-001",
    title: "Neural Networks Fundamentals",
    description: "Comprehensive guide to neural network architecture and training",
    type: "pdf",
    fileUrl: "/materials/neural-networks-fundamentals.pdf",
    thumbnailUrl: "/thumbnails/neural-networks.jpg",
    uploadedBy: "Dr. Sarah Johnson",
    uploadedAt: "2023-11-15T14:30:00",
    size: "4.2 MB",
    topic: "Neural Networks",
    subtopic: "Fundamentals",
    sessionId: "sess-001",
    sessionDate: "2023-11-10",
    isFavorite: true,
    tags: ["neural networks", "deep learning", "fundamentals"],
  },
  {
    id: "mat-002",
    title: "CNN Architecture Diagrams",
    description: "Visual diagrams explaining convolutional neural network architectures",
    type: "image",
    fileUrl: "/materials/cnn-architecture.png",
    thumbnailUrl: "/thumbnails/cnn-architecture.jpg",
    uploadedBy: "Dr. Sarah Johnson",
    uploadedAt: "2023-11-17T10:15:00",
    size: "1.8 MB",
    topic: "Neural Networks",
    subtopic: "Convolutional Neural Networks",
    sessionId: "sess-002",
    sessionDate: "2023-11-17",
    isFavorite: false,
    tags: ["CNN", "architecture", "diagrams"],
  },
  {
    id: "mat-003",
    title: "RNN Implementation Code",
    description: "Python code examples for implementing recurrent neural networks",
    type: "code",
    fileUrl: "/materials/rnn-implementation.py",
    uploadedBy: "Dr. Sarah Johnson",
    uploadedAt: "2023-11-24T15:45:00",
    size: "12.5 KB",
    topic: "Neural Networks",
    subtopic: "Recurrent Neural Networks",
    sessionId: "sess-003",
    sessionDate: "2023-11-24",
    isFavorite: false,
    tags: ["RNN", "python", "implementation"],
  },
  {
    id: "mat-004",
    title: "Reinforcement Learning Dataset",
    description: "Sample dataset for reinforcement learning experiments",
    type: "spreadsheet",
    fileUrl: "/materials/rl-dataset.xlsx",
    uploadedBy: "Prof. Michael Chen",
    uploadedAt: "2023-12-05T09:30:00",
    size: "8.7 MB",
    topic: "Reinforcement Learning",
    subtopic: "Q-Learning",
    isFavorite: false,
    tags: ["reinforcement learning", "dataset", "Q-learning"],
  },
  {
    id: "mat-005",
    title: "Deep Learning Research Papers",
    description: "Collection of important research papers in deep learning",
    type: "archive",
    fileUrl: "/materials/deep-learning-papers.zip",
    uploadedBy: "Prof. Michael Chen",
    uploadedAt: "2023-12-10T11:20:00",
    size: "45.3 MB",
    topic: "Deep Learning",
    subtopic: "Research",
    isFavorite: true,
    tags: ["research papers", "deep learning", "academic"],
  },
];

const sampleRecordings: Recording[] = [
  {
    id: "rec-001",
    title: "Introduction to Neural Networks",
    description: "First session covering the basics of neural networks and their applications",
    thumbnailUrl: "/thumbnails/neural-networks-video.jpg",
    videoUrl: "/recordings/neural-networks-intro.mp4",
    duration: "58:24",
    recordedAt: "2023-11-10T10:00:00",
    tutorName: "Dr. Sarah Johnson",
    tutorPhoto: "https://randomuser.me/api/portraits/women/1.jpg",
    topic: "Neural Networks",
    subtopic: "Fundamentals",
    sessionId: "sess-001",
    isFavorite: true,
    watchedPercentage: 100,
  },
  {
    id: "rec-002",
    title: "Convolutional Neural Networks Deep Dive",
    description: "Detailed exploration of CNN architectures and applications",
    thumbnailUrl: "/thumbnails/cnn-video.jpg",
    videoUrl: "/recordings/cnn-deep-dive.mp4",
    duration: "1:02:15",
    recordedAt: "2023-11-17T10:00:00",
    tutorName: "Dr. Sarah Johnson",
    tutorPhoto: "https://randomuser.me/api/portraits/women/1.jpg",
    topic: "Neural Networks",
    subtopic: "Convolutional Neural Networks",
    sessionId: "sess-002",
    isFavorite: false,
    watchedPercentage: 75,
  },
  {
    id: "rec-003",
    title: "Recurrent Neural Networks and Sequential Data",
    description: "Understanding RNNs and their application to sequential data problems",
    thumbnailUrl: "/thumbnails/rnn-video.jpg",
    videoUrl: "/recordings/rnn-sequential-data.mp4",
    duration: "55:40",
    recordedAt: "2023-11-24T10:00:00",
    tutorName: "Dr. Sarah Johnson",
    tutorPhoto: "https://randomuser.me/api/portraits/women/1.jpg",
    topic: "Neural Networks",
    subtopic: "Recurrent Neural Networks",
    sessionId: "sess-003",
    isFavorite: false,
    watchedPercentage: 30,
  },
  {
    id: "rec-004",
    title: "Introduction to Reinforcement Learning",
    description: "Fundamentals of reinforcement learning and its applications",
    thumbnailUrl: "/thumbnails/rl-video.jpg",
    videoUrl: "/recordings/reinforcement-learning-intro.mp4",
    duration: "1:10:05",
    recordedAt: "2023-12-05T09:00:00",
    tutorName: "Prof. Michael Chen",
    tutorPhoto: "https://randomuser.me/api/portraits/men/2.jpg",
    topic: "Reinforcement Learning",
    subtopic: "Fundamentals",
    sessionId: "sess-004",
    isFavorite: true,
    watchedPercentage: 15,
  },
];

// Create the store
export const useMaterialsStore = create<MaterialsState>((set, get) => ({
  materials: [],
  recordings: [],
  isLoading: false,
  error: null,
  searchQuery: "",
  activeTab: "recordings",
  topicFilter: "all",
  typeFilter: "all",
  sortBy: "newest",
  favorites: new Set(),

  setSearchQuery: (query) => set({ searchQuery: query }),
  setActiveTab: (tab) => set({ activeTab: tab }),
  setTopicFilter: (topic) => set({ topicFilter: topic }),
  setTypeFilter: (type) => set({ typeFilter: type }),
  setSortBy: (sort) => set({ sortBy: sort }),

  toggleFavorite: (id, type) => {
    if (type === "material") {
      set((state) => ({
        materials: state.materials.map((material) =>
          material.id === id
            ? { ...material, isFavorite: !material.isFavorite }
            : material
        ),
      }));
    } else {
      set((state) => ({
        recordings: state.recordings.map((recording) =>
          recording.id === id
            ? { ...recording, isFavorite: !recording.isFavorite }
            : recording
        ),
      }));
    }
  },

  fetchMaterials: async () => {
    set({ isLoading: true, error: null });

    try {
      // In a real app, you would fetch data from an API here
      // For now, we'll use the sample data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay

      set({
        materials: sampleMaterials,
        recordings: sampleRecordings,
        isLoading: false
      });
    } catch (error) {
      set({
        error: "Failed to load materials. Please try again.",
        isLoading: false
      });
    }
  }
}));

// Helper function to get the appropriate icon for a material type
const getMaterialIcon = (type: string) => {
  switch (type) {
    case "video":
      return <Video className="h-6 w-6 text-blue-500" />;
    case "document":
      return <FileText className="h-6 w-6 text-green-500" />;
    case "image":
      return <FileImage className="h-6 w-6 text-purple-500" />;
    case "pdf":
      return <FilePdf className="h-6 w-6 text-red-500" />;
    case "archive":
      return <FileArchive className="h-6 w-6 text-yellow-500" />;
    case "spreadsheet":
      return <FileSpreadsheet className="h-6 w-6 text-green-600" />;
    case "code":
      return <FileCode className="h-6 w-6 text-gray-700" />;
    default:
      return <File className="h-6 w-6 text-gray-500" />;
  }
};

// Format date for display
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

const Materials: React.FC = () => {
  const [expandedMaterial, setExpandedMaterial] = useState<string | null>(null);
  const [expandedRecording, setExpandedRecording] = useState<string | null>(null);
  const [displayedRecordingsCount, setDisplayedRecordingsCount] = useState<number>(6);
  const [displayedMaterialsCount, setDisplayedMaterialsCount] = useState<number>(6);
  const [searchParams] = useSearchParams();

  const {
    materials,
    recordings,
    isLoading,
    error,
    searchQuery,
    activeTab,
    topicFilter,
    typeFilter,
    sortBy,
    setSearchQuery,
    setActiveTab,
    setTopicFilter,
    setTypeFilter,
    setSortBy,
    toggleFavorite,
    fetchMaterials,
  } = useMaterialsStore();

  // First, fetch materials
  useEffect(() => {
    fetchMaterials();
  }, [fetchMaterials]);

  // Then, apply topic filter from URL parameters if present
  useEffect(() => {
    if (!isLoading && materials.length > 0 && recordings.length > 0) {
      const topicParam = searchParams.get('topic');
      if (topicParam) {
        setTopicFilter(topicParam);

        // If we have a topic filter from URL, switch to the appropriate tab
        // This ensures users see relevant content immediately
        if (materials.some(m => m.topic === topicParam)) {
          setActiveTab("materials");
        } else if (recordings.some(r => r.topic === topicParam)) {
          setActiveTab("recordings");
        }
      }
    }
  }, [searchParams, setTopicFilter, materials, recordings, setActiveTab, isLoading]);

  // Reset displayed counts when filters change or active tab changes
  useEffect(() => {
    setDisplayedRecordingsCount(6);
    setDisplayedMaterialsCount(6);
  }, [searchQuery, topicFilter, typeFilter, sortBy, activeTab]);

  // Handle loading more recordings
  const handleLoadMoreRecordings = () => {
    setDisplayedRecordingsCount(prev => prev + 6);
  };

  // Handle loading more materials
  const handleLoadMoreMaterials = () => {
    setDisplayedMaterialsCount(prev => prev + 6);
  };

  // Filter and sort materials based on current filters
  const filteredMaterials = materials
    .filter((material) => {
      const matchesSearch =
        material.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        material.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        material.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesTopic = topicFilter === "all" || material.topic === topicFilter;
      const matchesType = typeFilter === "all" || material.type === typeFilter;

      return matchesSearch && matchesTopic && matchesType;
    })
    .sort((a, b) => {
      if (sortBy === "newest") {
        return new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime();
      } else if (sortBy === "oldest") {
        return new Date(a.uploadedAt).getTime() - new Date(b.uploadedAt).getTime();
      } else if (sortBy === "name-asc") {
        return a.title.localeCompare(b.title);
      } else if (sortBy === "name-desc") {
        return b.title.localeCompare(a.title);
      }
      return 0;
    });

  // Filter and sort recordings based on current filters
  const filteredRecordings = recordings
    .filter((recording) => {
      const matchesSearch =
        recording.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        recording.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesTopic = topicFilter === "all" || recording.topic === topicFilter;

      return matchesSearch && matchesTopic;
    })
    .sort((a, b) => {
      if (sortBy === "newest") {
        return new Date(b.recordedAt).getTime() - new Date(a.recordedAt).getTime();
      } else if (sortBy === "oldest") {
        return new Date(a.recordedAt).getTime() - new Date(b.recordedAt).getTime();
      } else if (sortBy === "name-asc") {
        return a.title.localeCompare(b.title);
      } else if (sortBy === "name-desc") {
        return b.title.localeCompare(a.title);
      }
      return 0;
    });

  // Get unique topics for the filter dropdown
  const topics = ["all", ...new Set([...materials.map(m => m.topic), ...recordings.map(r => r.topic)])];

  // Get unique material types for the filter dropdown
  const materialTypes = ["all", ...new Set(materials.map(m => m.type))];

  return (
    <StudentPageLayout
      title="My Materials"
      description="Access your session recordings and learning materials"
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rfpurple-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your materials...</p>
          </div>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => fetchMaterials()}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <div className="space-y-6 h-[calc(100%-2rem)] overflow-y-auto">
          {/* Search and Filter Bar */}
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search materials and recordings..."
                className="pl-10 rounded-full border-gray-200 focus:ring-0 focus:ring-offset-0 focus:border-rfpurple-500 focus:border-2"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex flex-wrap gap-2 items-center">
              <Select value={topicFilter} onValueChange={setTopicFilter}>
                <SelectTrigger className="w-[180px] bg-white border-gray-200 rounded-full focus:ring-0 focus:ring-offset-0 focus:border-rfpurple-500 focus:border-2">
                  <SelectValue placeholder="Topic" />
                </SelectTrigger>
                <SelectContent>
                  {topics.map((topic) => (
                    <SelectItem key={topic} value={topic}>
                      {topic === "all" ? "All Topics" : topic}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {activeTab === "materials" && (
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[180px] bg-white border-gray-200 rounded-full focus:ring-0 focus:ring-offset-0 focus:border-rfpurple-500 focus:border-2">
                    <SelectValue placeholder="File Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {materialTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type === "all" ? "All Types" : type.charAt(0).toUpperCase() + type.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px] bg-white border-gray-200 rounded-full focus:ring-0 focus:ring-offset-0 focus:border-rfpurple-500 focus:border-2">
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                  <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto bg-gray-100 p-1 rounded-lg">
              <TabsTrigger value="recordings" className="rounded-md data-[state=active]:bg-white data-[state=active]:text-rfpurple-600 data-[state=active]:shadow-sm">Session Recordings</TabsTrigger>
              <TabsTrigger value="materials" className="rounded-md data-[state=active]:bg-white data-[state=active]:text-rfpurple-600 data-[state=active]:shadow-sm">Learning Materials</TabsTrigger>
            </TabsList>

            {/* Recordings Tab */}
            <TabsContent value="recordings" className="mt-6">
              {filteredRecordings.length === 0 ? (
                <div className="text-center py-12 bg-gray-50 rounded-lg">
                  <Video className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900">No recordings found</h3>
                  <p className="text-gray-500 mt-2">
                    {searchQuery
                      ? "Try adjusting your search or filters"
                      : "You don't have any session recordings yet"}
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredRecordings.slice(0, displayedRecordingsCount).map((recording) => (
                      <Card key={recording.id} className="overflow-hidden hover:shadow-md transition-shadow">
                        <div className="relative">
                          <div className="aspect-video bg-gray-100 relative">
                            <img
                              src={recording.thumbnailUrl}
                              alt={recording.title}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                              <div className="h-12 w-12 rounded-full bg-white bg-opacity-80 flex items-center justify-center">
                                <Play className="h-6 w-6 text-rfpurple-600" />
                              </div>
                            </div>

                            {/* Progress bar */}
                            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
                              <div
                                className="h-full bg-rfpurple-500"
                                style={{ width: `${recording.watchedPercentage}%` }}
                              ></div>
                            </div>
                          </div>

                          <Button
                            variant="ghost"
                            size="icon"
                            className="absolute top-2 right-2 h-8 w-8 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70"
                            onClick={() => toggleFavorite(recording.id, "recording")}
                          >
                            <Star
                              className={`h-4 w-4 ${recording.isFavorite ? 'text-yellow-400 fill-yellow-400' : 'text-white'}`}
                            />
                          </Button>
                        </div>

                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-medium line-clamp-1">{recording.title}</h3>
                              <p className="text-sm text-gray-500 mt-1 line-clamp-1">{recording.tutorName}</p>
                            </div>
                            <Badge variant="outline" className="ml-2 whitespace-nowrap">
                              {recording.duration}
                            </Badge>
                          </div>

                          <div className="flex items-center mt-2 text-xs text-gray-500">
                            <Calendar className="h-3 w-3 mr-1" />
                            <span>{formatDate(recording.recordedAt)}</span>
                          </div>

                          <div className="mt-2">
                            <Badge variant="secondary" className="mr-1">
                              {recording.topic}
                            </Badge>
                            {recording.subtopic && (
                              <Badge variant="outline" className="mr-1">
                                {recording.subtopic}
                              </Badge>
                            )}
                          </div>
                        </CardContent>

                        <CardFooter className="p-4 pt-0 flex justify-between">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs"
                            onClick={() => setExpandedRecording(expandedRecording === recording.id ? null : recording.id)}
                          >
                            {expandedRecording === recording.id ? (
                              <>Less <ChevronUp className="ml-1 h-3 w-3" /></>
                            ) : (
                              <>More <ChevronDown className="ml-1 h-3 w-3" /></>
                            )}
                          </Button>

                          <Button size="sm">
                            Watch
                          </Button>
                        </CardFooter>

                        {expandedRecording === recording.id && (
                          <div className="px-4 pb-4">
                            <p className="text-sm text-gray-600 mb-3">{recording.description}</p>
                            <div className="flex flex-wrap gap-2">
                              <Button variant="outline" size="sm">
                                <Download className="h-3 w-3 mr-1" /> Download
                              </Button>
                              <Button variant="outline" size="sm">
                                <ExternalLink className="h-3 w-3 mr-1" /> Open in New Tab
                              </Button>
                            </div>
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>

                  {/* Load More Button */}
                  {filteredRecordings.length > displayedRecordingsCount && (
                    <div className="flex justify-center mt-6">
                      <Button
                        variant="outline"
                        onClick={handleLoadMoreRecordings}
                        className="border-rfpurple-300 text-rfpurple-600 hover:bg-rfpurple-50"
                      >
                        Load More
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            {/* Materials Tab */}
            <TabsContent value="materials" className="mt-6">
              {filteredMaterials.length === 0 ? (
                <div className="text-center py-12 bg-gray-50 rounded-lg">
                  <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900">No materials found</h3>
                  <p className="text-gray-500 mt-2">
                    {searchQuery
                      ? "Try adjusting your search or filters"
                      : "You don't have any learning materials yet"}
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="space-y-4">
                    {filteredMaterials.slice(0, displayedMaterialsCount).map((material) => (
                      <Card key={material.id} className="overflow-hidden hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-start gap-4">
                            <div className="flex-shrink-0">
                              {getMaterialIcon(material.type)}
                            </div>

                            <div className="flex-grow min-w-0">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h3 className="font-medium line-clamp-1">{material.title}</h3>
                                  <p className="text-sm text-gray-500 mt-1 line-clamp-1">
                                    Uploaded by {material.uploadedBy} • {formatDate(material.uploadedAt)}
                                  </p>
                                </div>

                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 rounded-full"
                                  onClick={() => toggleFavorite(material.id, "material")}
                                >
                                  <Star
                                    className={`h-4 w-4 ${material.isFavorite ? 'text-yellow-400 fill-yellow-400' : 'text-gray-400'}`}
                                  />
                                </Button>
                              </div>

                              <div className="mt-2 flex flex-wrap gap-1">
                                <Badge variant="secondary" className="mr-1">
                                  {material.topic}
                                </Badge>
                                {material.subtopic && (
                                  <Badge variant="outline" className="mr-1">
                                    {material.subtopic}
                                  </Badge>
                                )}
                                <Badge variant="outline" className="bg-gray-100">
                                  {material.size}
                                </Badge>
                              </div>

                              {material.sessionDate && (
                                <div className="flex items-center mt-2 text-xs text-gray-500">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center cursor-help">
                                          <Info className="h-3 w-3 mr-1" />
                                          <span>From session on {formatDate(material.sessionDate)}</span>
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>This material was shared during your session</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                              )}
                            </div>
                          </div>

                          {expandedMaterial === material.id && (
                            <div className="mt-4 pl-10">
                              <p className="text-sm text-gray-600 mb-3">{material.description}</p>

                              {material.tags.length > 0 && (
                                <div className="flex flex-wrap gap-1 mb-3">
                                  {material.tags.map((tag) => (
                                    <Badge key={tag} variant="outline" className="bg-gray-50 text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}
                        </CardContent>

                        <CardFooter className="p-4 pt-0 flex justify-between pl-[72px]">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-xs"
                            onClick={() => setExpandedMaterial(expandedMaterial === material.id ? null : material.id)}
                          >
                            {expandedMaterial === material.id ? (
                              <>Less <ChevronUp className="ml-1 h-3 w-3" /></>
                            ) : (
                              <>More <ChevronDown className="ml-1 h-3 w-3" /></>
                            )}
                          </Button>

                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <ExternalLink className="h-3 w-3 mr-1" /> Open
                            </Button>
                            <Button size="sm">
                              <Download className="h-3 w-3 mr-1" /> Download
                            </Button>
                          </div>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>

                  {/* Load More Button */}
                  {filteredMaterials.length > displayedMaterialsCount && (
                    <div className="flex justify-center mt-6">
                      <Button
                        variant="outline"
                        onClick={handleLoadMoreMaterials}
                        className="border-rfpurple-300 text-rfpurple-600 hover:bg-rfpurple-50"
                      >
                        Load More
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      )}
    </StudentPageLayout>
  );
};

export default Materials;
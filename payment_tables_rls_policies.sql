-- RLS Policies for Payment Integration Tables
-- This file contains Row Level Security policies for all payment-related tables

-- =====================================================
-- 1. PAYMENTS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on payments table
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Students can view their own payments
CREATE POLICY "Students can view own payments"
ON public.payments
FOR SELECT
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Students can create their own payments
CREATE POLICY "Students can create own payments"
ON public.payments
FOR INSERT
TO authenticated
WITH CHECK (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- <PERSON>mins can view all payments
CREATE POLICY "Admins can view all payments"
ON public.payments
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Admins can update all payments
CREATE POLICY "Admins can update all payments"
ON public.payments
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access payments"
ON public.payments
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 2. INVOICES TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on invoices table
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;

-- Students can view their own invoices
CREATE POLICY "Students can view own invoices"
ON public.invoices
FOR SELECT
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Admins can view all invoices
CREATE POLICY "Admins can view all invoices"
ON public.invoices
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Admins can create invoices
CREATE POLICY "Admins can create invoices"
ON public.invoices
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Admins can update invoices
CREATE POLICY "Admins can update invoices"
ON public.invoices
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access invoices"
ON public.invoices
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 3. PAYMENT_METHODS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on payment_methods table
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;

-- Students can view their own payment methods
CREATE POLICY "Students can view own payment methods"
ON public.payment_methods
FOR SELECT
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Students can create their own payment methods
CREATE POLICY "Students can create own payment methods"
ON public.payment_methods
FOR INSERT
TO authenticated
WITH CHECK (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Students can update their own payment methods
CREATE POLICY "Students can update own payment methods"
ON public.payment_methods
FOR UPDATE
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
)
WITH CHECK (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Students can delete their own payment methods
CREATE POLICY "Students can delete own payment methods"
ON public.payment_methods
FOR DELETE
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- Admins can view all payment methods
CREATE POLICY "Admins can view all payment methods"
ON public.payment_methods
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access payment methods"
ON public.payment_methods
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 4. PAYMENT_EVENTS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on payment_events table
ALTER TABLE public.payment_events ENABLE ROW LEVEL SECURITY;

-- Admins can view all payment events
CREATE POLICY "Admins can view all payment events"
ON public.payment_events
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can view payment events for their payments
CREATE POLICY "Students can view own payment events"
ON public.payment_events
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.payments p
    WHERE p.id = payment_events.payment_id
    AND p.student_id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Service role full access
CREATE POLICY "Service role full access payment events"
ON public.payment_events
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 5. PAYMENT_ORDERS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on payment_orders table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_orders') THEN
        ALTER TABLE public.payment_orders ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Students can view their own payment orders
CREATE POLICY "Students can view own payment orders"
ON public.payment_orders
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.payments p
    WHERE p.id = payment_orders.payment_id
    AND p.student_id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Admins can view all payment orders
CREATE POLICY "Admins can view all payment orders"
ON public.payment_orders
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access payment orders"
ON public.payment_orders
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 6. PAYMENT_REFUNDS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on payment_refunds table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_refunds') THEN
        ALTER TABLE public.payment_refunds ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Students can view refunds for their payments
CREATE POLICY "Students can view own payment refunds"
ON public.payment_refunds
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.payments p
    WHERE p.id = payment_refunds.payment_id
    AND p.student_id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Admins can view all payment refunds
CREATE POLICY "Admins can view all payment refunds"
ON public.payment_refunds
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Admins can create payment refunds
CREATE POLICY "Admins can create payment refunds"
ON public.payment_refunds
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access payment refunds"
ON public.payment_refunds
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 7. PAYMENT_PROVIDERS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on payment_providers table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_providers') THEN
        ALTER TABLE public.payment_providers ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- All authenticated users can view active payment providers
CREATE POLICY "Authenticated users can view active payment providers"
ON public.payment_providers
FOR SELECT
TO authenticated
USING (is_active = true);

-- Admins can view all payment providers
CREATE POLICY "Admins can view all payment providers"
ON public.payment_providers
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Admins can update payment providers
CREATE POLICY "Admins can update payment providers"
ON public.payment_providers
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access payment providers"
ON public.payment_providers
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 8. INVOICE_ITEMS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on invoice_items table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoice_items') THEN
        ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Students can view invoice items for their invoices
CREATE POLICY "Students can view own invoice items"
ON public.invoice_items
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.invoices i
    WHERE i.id = invoice_items.invoice_id
    AND i.student_id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'student'
  )
);

-- Admins can view all invoice items
CREATE POLICY "Admins can view all invoice items"
ON public.invoice_items
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Admins can create invoice items
CREATE POLICY "Admins can create invoice items"
ON public.invoice_items
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Admins can update invoice items
CREATE POLICY "Admins can update invoice items"
ON public.invoice_items
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access invoice items"
ON public.invoice_items
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 9. LOGS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on logs table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'logs') THEN
        ALTER TABLE public.logs ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Only admins can view logs
CREATE POLICY "Admins can view all logs"
ON public.logs
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access logs"
ON public.logs
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 10. NOTIFICATIONS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on notifications table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
        ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Users can view their own notifications
CREATE POLICY "Users can view own notifications"
ON public.notifications
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid()
  OR recipient_id = auth.uid()
  OR (
    -- Handle different possible column names for user reference
    EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name = 'notifications'
      AND column_name = 'student_id'
    ) AND student_id = auth.uid()
  )
  OR (
    EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name = 'notifications'
      AND column_name = 'tutor_id'
    ) AND tutor_id = auth.uid()
  )
);

-- Users can update their own notifications (mark as read, etc.)
CREATE POLICY "Users can update own notifications"
ON public.notifications
FOR UPDATE
TO authenticated
USING (
  user_id = auth.uid()
  OR recipient_id = auth.uid()
  OR (
    EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name = 'notifications'
      AND column_name = 'student_id'
    ) AND student_id = auth.uid()
  )
  OR (
    EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name = 'notifications'
      AND column_name = 'tutor_id'
    ) AND tutor_id = auth.uid()
  )
)
WITH CHECK (
  user_id = auth.uid()
  OR recipient_id = auth.uid()
  OR (
    EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name = 'notifications'
      AND column_name = 'student_id'
    ) AND student_id = auth.uid()
  )
  OR (
    EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name = 'notifications'
      AND column_name = 'tutor_id'
    ) AND tutor_id = auth.uid()
  )
);

-- Admins can view all notifications
CREATE POLICY "Admins can view all notifications"
ON public.notifications
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Admins can create notifications
CREATE POLICY "Admins can create notifications"
ON public.notifications
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access notifications"
ON public.notifications
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 11. HELPER FUNCTIONS FOR PAYMENT SECURITY
-- =====================================================

-- Function to check if user can access payment data
CREATE OR REPLACE FUNCTION can_access_payment_data(
  target_student_id UUID,
  user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  user_type TEXT;
BEGIN
  -- Get user type
  SELECT p.user_type INTO user_type
  FROM public.profiles p
  WHERE p.id = user_id;

  -- Check access permissions
  CASE user_type
    WHEN 'student' THEN
      RETURN target_student_id = user_id;
    WHEN 'admin' THEN
      RETURN true;
    WHEN 'tutor' THEN
      -- Tutors have NO access to payment data
      RETURN false;
    ELSE
      RETURN false;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can modify payment data
CREATE OR REPLACE FUNCTION can_modify_payment_data(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = user_id
    AND user_type = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get accessible payment records for a user
CREATE OR REPLACE FUNCTION get_accessible_payments(user_id UUID)
RETURNS TABLE (
  payment_id UUID,
  access_type TEXT -- 'owner', 'admin'
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id as payment_id,
    CASE
      WHEN p.student_id = user_id THEN 'owner'
      WHEN prof.user_type = 'admin' THEN 'admin'
      ELSE 'none'
    END as access_type
  FROM public.payments p
  JOIN public.profiles prof ON prof.id = user_id
  WHERE
    p.student_id = user_id
    OR prof.user_type = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 12. PERFORMANCE INDEXES FOR RLS QUERIES
-- =====================================================

-- Indexes for payments table RLS queries
CREATE INDEX IF NOT EXISTS idx_payments_student_id_auth
ON public.payments(student_id) WHERE student_id IS NOT NULL;

-- Indexes for invoices table RLS queries
CREATE INDEX IF NOT EXISTS idx_invoices_student_id_auth
ON public.invoices(student_id) WHERE student_id IS NOT NULL;

-- Indexes for payment_methods table RLS queries
CREATE INDEX IF NOT EXISTS idx_payment_methods_student_id_auth
ON public.payment_methods(student_id) WHERE student_id IS NOT NULL;

-- Indexes for profiles table (used in RLS policies)
CREATE INDEX IF NOT EXISTS idx_profiles_user_type
ON public.profiles(user_type) WHERE user_type IS NOT NULL;

-- =====================================================
-- 13. SECURITY TRIGGERS FOR PAYMENT TABLES
-- =====================================================

-- Trigger to prevent unauthorized payment modifications
CREATE OR REPLACE FUNCTION prevent_unauthorized_payment_modification()
RETURNS TRIGGER AS $$
DECLARE
  user_type TEXT;
BEGIN
  -- Get the user type of the current user
  SELECT p.user_type INTO user_type
  FROM public.profiles p
  WHERE p.id = auth.uid();

  -- Only admins and service role can modify sensitive payment fields
  IF user_type != 'admin' AND current_setting('role') != 'service_role' THEN
    -- Prevent modification of critical payment fields by non-admins
    IF TG_TABLE_NAME = 'payments' THEN
      IF (
        OLD.amount IS DISTINCT FROM NEW.amount
        OR OLD.status IS DISTINCT FROM NEW.status
        OR OLD.stripe_payment_intent_id IS DISTINCT FROM NEW.stripe_payment_intent_id
        OR OLD.stripe_charge_id IS DISTINCT FROM NEW.stripe_charge_id
      ) THEN
        RAISE EXCEPTION 'Access denied: Only administrators can modify payment details'
          USING ERRCODE = '42501';
      END IF;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply payment modification trigger to payments table
DROP TRIGGER IF EXISTS prevent_payment_modification_trigger ON public.payments;
CREATE TRIGGER prevent_payment_modification_trigger
  BEFORE UPDATE ON public.payments
  FOR EACH ROW EXECUTE FUNCTION prevent_unauthorized_payment_modification();

-- =====================================================
-- 14. SUMMARY AND VERIFICATION
-- =====================================================

-- Function to verify RLS policies are enabled
CREATE OR REPLACE FUNCTION verify_payment_rls_policies()
RETURNS TABLE (
  table_name TEXT,
  rls_enabled BOOLEAN,
  policy_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    t.tablename::TEXT,
    t.rowsecurity as rls_enabled,
    (
      SELECT COUNT(*)::INTEGER
      FROM pg_policies p
      WHERE p.tablename = t.tablename
    ) as policy_count
  FROM pg_tables t
  WHERE t.schemaname = 'public'
  AND t.tablename IN (
    'payments', 'invoices', 'payment_methods', 'payment_events',
    'payment_orders', 'payment_refunds', 'payment_providers',
    'invoice_items', 'logs', 'notifications'
  )
  ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Usage: SELECT * FROM verify_payment_rls_policies();

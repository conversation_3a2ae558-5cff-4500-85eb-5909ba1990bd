import React from "react";
import { Card, CardContent } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import EditSectionModal, { FormInput } from "@/components/student/profile/EditSectionModal";
import { StudentExtendedProfileData } from "@/pages/student/Profile";
import SectionHeader from "./SectionHeader";

interface GeneralInformationSectionProps {
  profileData: StudentExtendedProfileData;
  editData: Partial<StudentExtendedProfileData>;
  activeSectionEdit: string | null;
  setActiveSectionEdit: (section: string | null) => void;
  updateEditData: (field: string, value: any) => void;
  updateProfile: (data: Partial<StudentExtendedProfileData>) => void;
  getLearningGoalText: (goal: string) => string;
  isModified?: boolean;
}

const GeneralInformationSection: React.FC<GeneralInformationSectionProps> = ({
  profileData,
  editData,
  activeSectionEdit,
  setActiveSectionEdit,
  updateEditData,
  updateProfile,
  getLearningGoalText,
  isModified = false
}) => {
  return (
    <>
      {/* General Information Section */}
      <Card>
        <SectionHeader
          title="General Information"
          isModified={isModified}
          onEdit={() => setActiveSectionEdit('general')}
        />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Subjects:</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {profileData.subjects_of_interest.length > 0 ? (
                  profileData.subjects_of_interest.map((subject, index) => (
                    <Badge key={index} variant="secondary">
                      {subject}
                    </Badge>
                  ))
                ) : (
                  <p className="text-gray-500">No subjects specified</p>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Learning Goals:</h3>
              <ul className="list-disc pl-5 space-y-1 mt-1">
                {profileData.learning_goals.length > 0 ? (
                  profileData.learning_goals.map((goal, index) => (
                    <li key={index} className="text-sm">{getLearningGoalText(goal)}</li>
                  ))
                ) : (
                  <p className="text-gray-500">No learning goals specified</p>
                )}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* General Information Edit Modal */}
      <EditSectionModal
        isOpen={activeSectionEdit === 'general'}
        onClose={() => setActiveSectionEdit(null)}
        title="Edit General Information"
        onSubmit={() => {
          // Save changes to pending changes
          const changes = {
            subjects_of_interest: editData.subjects_of_interest,
            learning_goals: editData.learning_goals
          };
          updateProfile(changes);
          setActiveSectionEdit(null);
        }}
        layout="default"
      >
        <FormInput
          label="Subjects"
          id="subjects_of_interest"
          placeholder="Add subjects separated by commas"
          helpText="Enter subjects you're interested in learning"
          value={editData.subjects_of_interest?.join(', ') || ''}
          onChange={(e) => {
            const subjectsArray = e.target.value.split(',').map(item => item.trim()).filter(Boolean);
            updateEditData("subjects_of_interest", subjectsArray);
          }}
        />

        <FormInput
          label="Learning Goals"
          id="learning_goals"
          placeholder="Add learning goals"
          helpText="What do you want to achieve through tutoring?"
          value={editData.learning_goals || ''}
          onChange={(e) => updateEditData("learning_goals", e.target.value)}
        />
      </EditSectionModal>
    </>
  );
};

export default GeneralInformationSection;

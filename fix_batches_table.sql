-- Fix for the error: "ERROR: 0A000: cannot use subquery in check constraint"
-- PostgreSQL doesn't allow subqueries in CHECK constraints

-- Create the batches table without the problematic CHECK constraints
CREATE TABLE batches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    student_id UUID REFERENCES users(id) NOT NULL,
    package_type TEXT NOT NULL CHECK (package_type IN ('complete_booster', 'preparation', 'customized')),
    package_name TEXT NOT NULL,  -- e.g., "Common Core", "SAT", "ESAP", etc.
    default_tutor_id UUID REFERENCES users(id),
    status TEXT NOT NULL CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    total_sessions INTEGER,
    remaining_sessions INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    -- Keep the valid date range constraint as it doesn't use a subquery
    CONSTRAINT valid_date_range CHECK (
        end_date IS NULL OR start_date IS NULL OR end_date > start_date
    )
);

-- Create a trigger function to validate user roles
CREATE OR REPLACE FUNCTION validate_batch_user_roles()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if student has the 'student' role
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = NEW.student_id AND role = 'student') THEN
        RAISE EXCEPTION 'User with ID % is not a student', NEW.student_id;
    END IF;
    
    -- Check if default_tutor has the 'tutor' role (if provided)
    IF NEW.default_tutor_id IS NOT NULL AND 
       NOT EXISTS (SELECT 1 FROM users WHERE id = NEW.default_tutor_id AND role = 'tutor') THEN
        RAISE EXCEPTION 'User with ID % is not a tutor', NEW.default_tutor_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on the batches table
CREATE TRIGGER check_batch_user_roles
BEFORE INSERT OR UPDATE ON batches
FOR EACH ROW EXECUTE FUNCTION validate_batch_user_roles();

import React, { useEffect } from 'react';
import { Clock } from 'lucide-react';
import {
  useTimezoneStore,
  COMMON_TIMEZONES,
  getUserTimezone,
  formatTimeInTimezone
} from '@/store/timezoneStore';

interface TimezoneSelectProps {
  value: string;
  onChange: (timezone: string) => void;
  className?: string;
  label?: string;
  required?: boolean;
  showCurrentTime?: boolean;
  error?: string;
  instanceId?: string; // Optional instance ID for multiple selectors
}

const TimezoneSelect: React.FC<TimezoneSelectProps> = ({
  value,
  onChange,
  className = '',
  label = 'Timezone',
  required = false,
  showCurrentTime = true,
  error,
  instanceId = 'default'
}) => {
  const {
    instances,
    initializeInstance,
    updateCurrentTime,
    setSearchTerm,
    setIsOpen,
    cleanupInstance
  } = useTimezoneStore();

  // Get the instance state or create default values
  const instanceState = instances[instanceId] || {
    currentTime: '',
    searchTerm: '',
    isOpen: false,
    filteredTimezones: COMMON_TIMEZONES
  };

  const { currentTime, searchTerm, isOpen, filteredTimezones } = instanceState;

  // Initialize instance on mount
  useEffect(() => {
    initializeInstance(instanceId);

    // Cleanup on unmount
    return () => {
      cleanupInstance(instanceId);
    };
  }, [instanceId, initializeInstance, cleanupInstance]);

  // Set initial value to user's timezone if not provided
  useEffect(() => {
    if (!value) {
      const userTimezone = getUserTimezone();
      onChange(userTimezone);
    }
  }, [value, onChange]);

  // Update current time every minute
  useEffect(() => {
    if (showCurrentTime && value) {
      const updateTime = () => {
        updateCurrentTime(instanceId, value);
      };

      updateTime();
      const interval = setInterval(updateTime, 60000);

      return () => clearInterval(interval);
    }
  }, [value, showCurrentTime, instanceId, updateCurrentTime]);

  // Find the selected timezone label
  const selectedTimezone = COMMON_TIMEZONES.find(tz => tz.value === value);
  const displayValue = selectedTimezone ? selectedTimezone.label : value;

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      <div className="relative">
        <div
          className={`flex items-center justify-between w-full px-3 py-2 border rounded-md shadow-sm ${
            error ? 'border-red-500' : 'border-gray-300'
          } focus-within:ring-1 focus-within:ring-indigo-500 focus-within:border-indigo-500 cursor-pointer`}
          onClick={() => setIsOpen(instanceId, !isOpen)}
        >
          <div className="flex items-center">
            <Clock size={18} className="text-gray-400 mr-2" />
            <span>{displayValue}</span>
          </div>
          {showCurrentTime && currentTime && (
            <span className="text-sm text-gray-500">{currentTime}</span>
          )}
        </div>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md border border-gray-300 max-h-60 overflow-auto">
            <div className="sticky top-0 bg-white p-2 border-b">
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Search timezones..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(instanceId, e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
            <ul>
              {filteredTimezones.map((tz) => (
                <li
                  key={tz.value}
                  className={`px-3 py-2 hover:bg-gray-100 cursor-pointer ${
                    tz.value === value ? 'bg-indigo-50 text-indigo-700' : ''
                  }`}
                  onClick={() => {
                    onChange(tz.value);
                    setIsOpen(instanceId, false);
                  }}
                >
                  <div className="flex justify-between items-center">
                    <span>{tz.label}</span>
                    <span className="text-sm text-gray-500">
                      {formatTimeInTimezone(tz.value)}
                    </span>
                  </div>
                </li>
              ))}
              {filteredTimezones.length === 0 && (
                <li className="px-3 py-2 text-gray-500">
                  No timezones found matching "{searchTerm}"
                </li>
              )}
            </ul>
          </div>
        )}
      </div>

      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default TimezoneSelect;

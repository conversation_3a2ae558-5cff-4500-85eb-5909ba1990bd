import axios from "axios";

export async function isDisposableEmail(email: string): Promise<boolean> {
  const apiKey = import.meta.env.VITE_KICKBOX_API_KEY;
  const [, domain] = email.split("@");

  try {
    const res = await axios.get(
      `https://open.kickbox.com/v1/disposable/${domain}`,
      {
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      }
    );

    return res.data.disposable === true;
  } catch (err) {
    console.error("Disposable email check failed:", err);
    return false; // Fail-safe
  }
}

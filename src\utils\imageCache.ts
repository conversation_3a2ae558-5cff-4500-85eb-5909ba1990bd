// Shared cache for signed URLs with expiration tracking
interface CachedUrl {
  signedUrl: string;
  expiresAt: number;
}

// Global cache shared between AuthenticatedImage and preloading functions
export const imageUrlCache = new Map<string, CachedUrl>();

// Clean up expired URLs from cache periodically
export const cleanupImageCache = () => {
  const now = Date.now();
  for (const [key, value] of imageUrlCache.entries()) {
    if (value.expiresAt <= now) {
      imageUrlCache.delete(key);
    }
  }
};

// Run cleanup every 10 minutes
setInterval(cleanupImageCache, 10 * 60 * 1000);

// Helper function to get cached URL if valid
export const getCachedImageUrl = (originalUrl: string): string | null => {
  const cached = imageUrlCache.get(originalUrl);
  const now = Date.now();
  
  // Return cached URL if it exists and hasn't expired (with 5 minute buffer)
  if (cached && cached.expiresAt > now + 5 * 60 * 1000) {
    return cached.signedUrl;
  }
  
  // Remove expired cache entry
  if (cached) {
    imageUrlCache.delete(originalUrl);
  }
  
  return null;
};

// Helper function to cache a signed URL
export const cacheImageUrl = (originalUrl: string, signedUrl: string, expirySeconds: number = 3600) => {
  const expiresAt = Date.now() + expirySeconds * 1000;
  imageUrlCache.set(originalUrl, {
    signedUrl,
    expiresAt
  });
};

import React from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Link } from "react-router-dom";
import {
  BookOpen,
  Clock,
  Users,
  Calculator,
  Target,
  CheckCircle,
  ArrowRight,
  Star,
  Play,
  Lock,
  Award
} from "lucide-react";

// Common Core Math Curriculum by Grade
const mathCurriculum = [
  {
    grade: "Kindergarten",
    level: 1,
    status: "available",
    color: "bg-green-500",
    topics: [
      "Counting and Cardinality",
      "Operations and Algebraic Thinking",
      "Number and Operations in Base Ten",
      "Measurement and Data",
      "Geometry"
    ],
    description: "Build foundational math skills through counting, basic addition/subtraction, and shape recognition.",
    duration: "Full Year Program"
  },
  {
    grade: "Grade 1",
    level: 2,
    status: "available",
    color: "bg-blue-500",
    topics: [
      "Operations and Algebraic Thinking",
      "Number and Operations in Base Ten",
      "Measurement and Data",
      "Geometry"
    ],
    description: "Develop addition and subtraction strategies, place value understanding, and measurement concepts.",
    duration: "Full Year Program"
  },
  {
    grade: "Grade 2",
    level: 3,
    status: "available",
    color: "bg-purple-500",
    topics: [
      "Operations and Algebraic Thinking",
      "Number and Operations in Base Ten",
      "Measurement and Data",
      "Geometry"
    ],
    description: "Master two-digit addition/subtraction, understand place value to 1000, and work with time and money.",
    duration: "Full Year Program"
  },
  {
    grade: "Grade 3",
    level: 4,
    status: "coming-soon",
    color: "bg-orange-500",
    topics: [
      "Operations and Algebraic Thinking",
      "Number and Operations in Base Ten",
      "Number and Operations—Fractions",
      "Measurement and Data",
      "Geometry"
    ],
    description: "Introduction to multiplication, division, and fractions with area and perimeter concepts.",
    duration: "Full Year Program"
  },
  {
    grade: "Grade 4",
    level: 5,
    status: "coming-soon",
    color: "bg-red-500",
    topics: [
      "Operations and Algebraic Thinking",
      "Number and Operations in Base Ten",
      "Number and Operations—Fractions",
      "Measurement and Data",
      "Geometry"
    ],
    description: "Multi-digit arithmetic, equivalent fractions, and decimal notation.",
    duration: "Full Year Program"
  },
  {
    grade: "Grade 5",
    level: 6,
    status: "coming-soon",
    color: "bg-indigo-500",
    topics: [
      "Operations and Algebraic Thinking",
      "Number and Operations in Base Ten",
      "Number and Operations—Fractions",
      "Measurement and Data",
      "Geometry"
    ],
    description: "Decimal operations, fraction operations, and coordinate geometry.",
    duration: "Full Year Program"
  }
];

const Courses = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12">
        <section className="px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">
              Courses
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              We are right now offering personalized Math courses but planning to add more courses soon.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="button-gradient text-white" asChild>
                <Link to="/register">
                  <BookOpen className="mr-2 h-5 w-5" />
                  Start Learning Today
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link to="/tutor/search">
                  <Users className="mr-2 h-5 w-5" />
                  Find a Tutor
                </Link>
              </Button>
            </div>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="text-center">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <Calculator className="h-12 w-12 text-rfpurple-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 mb-2">3</h3>
                <p className="text-gray-600">Grades Available</p>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <Users className="h-12 w-12 text-rfpurple-600 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-gray-900 mb-2">Coming Soon</h3>
                <p className="text-gray-600">Students Enrolled</p>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <Star className="h-12 w-12 text-rfpurple-600 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-gray-900 mb-2">Coming Soon</h3>
                <p className="text-gray-600">Average Rating</p>
              </div>
            </div>
          </div>

          {/* Math Curriculum Journey */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Common Core Math Curriculum Journey
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Follow our structured learning path designed to build mathematical foundations
                progressively from Kindergarten through Elementary grades.
              </p>
            </div>

            {/* Journey Path */}
            <div className="relative max-w-4xl mx-auto">
              {/* Connecting Lines */}
              <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-green-200 via-blue-200 to-indigo-200 hidden md:block"></div>

              {/* Grade Nodes */}
              <div className="space-y-8">
                {mathCurriculum.map((grade, index) => (
                  <div key={grade.grade} className={`relative flex items-center ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
                    {/* Node Circle */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 z-10 hidden md:block">
                      <div className={`w-16 h-16 rounded-full ${grade.color} flex items-center justify-center shadow-lg border-4 border-white`}>
                        {grade.status === 'available' ? (
                          <Play className="w-6 h-6 text-white" />
                        ) : (
                          <Lock className="w-6 h-6 text-white" />
                        )}
                      </div>
                    </div>

                    {/* Content Card */}
                    <div className={`w-full md:w-5/12 ${index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'}`}>
                      <Card className={`${grade.status === 'available' ? 'bg-white shadow-lg hover:shadow-xl' : 'bg-gray-50 opacity-75'} transition-all duration-300`}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <div className={`w-8 h-8 rounded-full ${grade.color} flex items-center justify-center md:hidden`}>
                                {grade.status === 'available' ? (
                                  <Play className="w-4 h-4 text-white" />
                                ) : (
                                  <Lock className="w-4 h-4 text-white" />
                                )}
                              </div>
                              <CardTitle className="text-xl font-bold">{grade.grade}</CardTitle>
                            </div>
                            <Badge
                              variant={grade.status === 'available' ? 'default' : 'secondary'}
                              className={grade.status === 'available' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}
                            >
                              {grade.status === 'available' ? (
                                <>
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  Available
                                </>
                              ) : (
                                <>
                                  <Clock className="w-3 h-3 mr-1" />
                                  Coming Soon
                                </>
                              )}
                            </Badge>
                          </div>
                          <CardDescription className="text-gray-600">
                            {grade.description}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="mb-4">
                            <h4 className="font-semibold text-sm text-gray-900 mb-2">Core Standards:</h4>
                            <div className="space-y-1">
                              {grade.topics.map((topic, topicIndex) => (
                                <div key={topicIndex} className="flex items-center text-sm text-gray-600">
                                  <div className="w-1.5 h-1.5 bg-rfpurple-400 rounded-full mr-2"></div>
                                  {topic}
                                </div>
                              ))}
                            </div>
                          </div>

                          <div className="flex items-center justify-between pt-2 mb-4">
                            <div className="text-sm text-gray-500 flex items-center">
                              <Clock size={14} className="mr-1" />
                              {grade.duration}
                            </div>
                            <div className="flex items-center text-sm text-gray-500">
                              <Award size={14} className="mr-1" />
                              Level {grade.level}
                            </div>
                          </div>

                          <Button
                            className={`w-full ${grade.status === 'available' ? 'button-gradient text-white' : 'bg-gray-200 text-gray-500 cursor-not-allowed'}`}
                            disabled={grade.status !== 'available'}
                            asChild={grade.status === 'available'}
                          >
                            {grade.status === 'available' ? (
                              <Link to="/register">
                                Start Learning
                                <ArrowRight className="ml-2 h-4 w-4" />
                              </Link>
                            ) : (
                              <span>
                                Coming Soon
                                <Lock className="ml-2 h-4 w-4" />
                              </span>
                            )}
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-gradient-to-r from-rfpurple-600 to-rfpurple-500 rounded-2xl p-8 md:p-12 text-center text-white">
            <h2 className="text-3xl font-bold mb-4">Ready to Start Your Learning Journey?</h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Begin with our personalized mathematics curriculum and stay tuned for more exciting courses
              coming your way with expert tutors and interactive learning.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-rfpurple-600 hover:bg-gray-100" asChild>
                <Link to="/register">
                  <Target className="mr-2 h-5 w-5" />
                  Get Started Now
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-rfpurple-600 bg-transparent" asChild>
                <Link to="/inquiry">
                  <BookOpen className="mr-2 h-5 w-5" />
                  Learn More
                </Link>
              </Button>
            </div>
          </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Courses;

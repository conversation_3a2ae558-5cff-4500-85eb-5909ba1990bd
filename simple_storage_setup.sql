-- =====================================================
-- SIMPLE STORAGE SETUP FOR PHOTO UPLOADS
-- =====================================================

-- This is a simplified version that should work immediately
-- Run this in your Supabase SQL editor

-- =====================================================
-- STEP 1: CREATE STORAGE BUCKET
-- =====================================================

-- Create student-uploads bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'student-uploads',
    'student-uploads',
    true,
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- =====================================================
-- STEP 2: SIMPLE STORAGE POLICIES
-- =====================================================

-- Remove all existing policies for this bucket
DROP POLICY IF EXISTS "Allow authenticated uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete" ON storage.objects;

-- Policy 1: Allow authenticated users to upload to student-uploads bucket
CREATE POLICY "Allow authenticated uploads" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Policy 2: Allow public read access to all files in student-uploads
CREATE POLICY "Allow public read access" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'student-uploads'
    );

-- Policy 3: Allow authenticated users to update files in student-uploads
CREATE POLICY "Allow authenticated users to update" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Policy 4: Allow authenticated users to delete files in student-uploads
CREATE POLICY "Allow authenticated users to delete" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- =====================================================
-- STEP 3: VERIFICATION
-- =====================================================

-- Check bucket exists
SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'student-uploads';

-- Check policies exist
SELECT 
    policyname,
    cmd as command,
    permissive,
    roles
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects'
AND policyname LIKE '%student%' OR policyname LIKE '%authenticated%' OR policyname LIKE '%public%'
ORDER BY policyname;

-- =====================================================
-- STEP 4: GRANT PERMISSIONS
-- =====================================================

-- Ensure proper permissions are granted
GRANT USAGE ON SCHEMA storage TO authenticated, anon;
GRANT ALL ON storage.objects TO authenticated;
GRANT SELECT ON storage.objects TO anon;
GRANT ALL ON storage.buckets TO authenticated;
GRANT SELECT ON storage.buckets TO anon;

-- =====================================================
-- NOTES
-- =====================================================

/*
This simplified setup:

1. Creates a public bucket called 'student-uploads'
2. Allows any authenticated user to upload files
3. Allows public read access to all files
4. Sets a 10MB file size limit
5. Accepts common image formats

This should resolve the "Unauthorized" and RLS policy errors.

After running this script:
1. Your photo upload should work immediately
2. Files will be publicly accessible (good for profile pictures)
3. Only authenticated users can upload/modify files

If you need more restrictive policies later, you can modify them
after confirming the basic upload functionality works.
*/

-- Meeting Integration Schema Enhancement
-- This schema enhances the existing database to support flexible meeting provider integration
-- Supports Microsoft Teams, other third-party providers, and custom meeting systems

-- =====================================================
-- 1. MEETING PROVIDERS TABLE
-- =====================================================

-- Meeting Providers table - Configure available meeting providers
CREATE TABLE meeting_providers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE, -- 'microsoft_teams', 'zoom', 'google_meet', 'webex', 'custom'
    display_name TEXT NOT NULL, -- 'Microsoft Teams', 'Zoom', 'Google Meet', etc.
    provider_type TEXT NOT NULL CHECK (provider_type IN ('third_party', 'custom', 'embedded')),
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Provider configuration
    api_endpoint TEXT, -- For API-based integrations
    webhook_url TEXT, -- For receiving provider webhooks
    oauth_config JSONB, -- OAuth configuration for provider
    api_config JSONB, -- API keys, secrets, etc. (encrypted)
    
    -- Provider capabilities
    supports_recording BOOLEAN DEFAULT FALSE,
    supports_screen_sharing BOOLEAN DEFAULT FALSE,
    supports_whiteboard BOOLEAN DEFAULT FALSE,
    supports_breakout_rooms BOOLEAN DEFAULT FALSE,
    supports_waiting_room BOOLEAN DEFAULT FALSE,
    supports_chat BOOLEAN DEFAULT FALSE,
    max_participants INTEGER,
    
    -- Provider settings
    default_settings JSONB, -- Default meeting settings for this provider
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    -- Ensure only one default provider
    CONSTRAINT single_default_provider EXCLUDE (is_default WITH =) WHERE (is_default = true)
);

-- =====================================================
-- 2. USER MEETING PREFERENCES TABLE
-- =====================================================

-- User Meeting Preferences table - Per-user meeting provider preferences
CREATE TABLE user_meeting_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) NOT NULL,
    preferred_provider_id UUID REFERENCES meeting_providers(id),
    
    -- User-specific settings for each provider
    provider_settings JSONB, -- User's custom settings for their preferred provider
    
    -- Integration tokens (encrypted)
    provider_tokens JSONB, -- OAuth tokens, API keys specific to this user
    
    -- Preferences
    auto_join_enabled BOOLEAN DEFAULT TRUE,
    recording_preference TEXT CHECK (recording_preference IN ('always', 'never', 'ask')),
    notification_preferences JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    UNIQUE(user_id, preferred_provider_id)
);

-- =====================================================
-- 3. MEETING SESSIONS TABLE
-- =====================================================

-- Meeting Sessions table - Detailed meeting information for each session
CREATE TABLE meeting_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES sessions(id) NOT NULL UNIQUE,
    provider_id UUID REFERENCES meeting_providers(id) NOT NULL,
    
    -- Provider-specific meeting identifiers
    provider_meeting_id TEXT, -- Teams meeting ID, Zoom meeting ID, etc.
    meeting_url TEXT NOT NULL,
    join_url TEXT, -- Different from meeting_url for some providers
    moderator_url TEXT, -- Special URL for meeting moderator
    
    -- Meeting configuration
    meeting_settings JSONB, -- Provider-specific meeting settings
    security_settings JSONB, -- Password, waiting room, etc.
    
    -- Meeting lifecycle
    meeting_status TEXT NOT NULL DEFAULT 'scheduled' CHECK (
        meeting_status IN ('scheduled', 'started', 'ended', 'cancelled', 'failed')
    ),
    
    -- Meeting times (actual vs scheduled)
    scheduled_start_time TIMESTAMP WITH TIME ZONE,
    scheduled_end_time TIMESTAMP WITH TIME ZONE,
    actual_start_time TIMESTAMP WITH TIME ZONE,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    
    -- Recording information
    recording_enabled BOOLEAN DEFAULT FALSE,
    recording_url TEXT,
    recording_status TEXT CHECK (recording_status IN ('not_started', 'recording', 'processing', 'ready', 'failed')),
    
    -- Participant tracking
    max_participants_reached INTEGER DEFAULT 0,
    total_join_attempts INTEGER DEFAULT 0,
    
    -- Provider response data
    provider_response JSONB, -- Full response from provider API
    last_sync_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- =====================================================
-- 4. MEETING PARTICIPANTS TABLE
-- =====================================================

-- Meeting Participants table - Track who joined/left meetings
CREATE TABLE meeting_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    meeting_session_id UUID REFERENCES meeting_sessions(id) NOT NULL,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    
    -- Participation details
    joined_at TIMESTAMP WITH TIME ZONE,
    left_at TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER, -- Calculated duration
    
    -- Participant role and status
    participant_role TEXT CHECK (participant_role IN ('host', 'moderator', 'participant', 'observer')),
    join_method TEXT CHECK (join_method IN ('web', 'mobile', 'desktop', 'phone', 'api')),
    
    -- Provider-specific participant data
    provider_participant_id TEXT, -- Provider's internal participant ID
    participant_data JSONB, -- Additional data from provider
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- =====================================================
-- 5. MEETING EVENTS TABLE
-- =====================================================

-- Meeting Events table - Log all meeting-related events
CREATE TABLE meeting_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    meeting_session_id UUID REFERENCES meeting_sessions(id) NOT NULL,
    user_id UUID REFERENCES profiles(id), -- NULL for system events
    
    -- Event details
    event_type TEXT NOT NULL CHECK (event_type IN (
        'meeting_created', 'meeting_started', 'meeting_ended', 'meeting_cancelled',
        'participant_joined', 'participant_left', 'recording_started', 'recording_stopped',
        'screen_share_started', 'screen_share_stopped', 'chat_message', 'error'
    )),
    event_data JSONB, -- Event-specific data
    
    -- Provider information
    provider_event_id TEXT, -- Provider's event ID if applicable
    provider_timestamp TIMESTAMP WITH TIME ZONE, -- Provider's timestamp
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- =====================================================
-- 6. INDEXES FOR PERFORMANCE
-- =====================================================

-- Meeting providers indexes
CREATE INDEX idx_meeting_providers_active ON meeting_providers(is_active);
CREATE INDEX idx_meeting_providers_default ON meeting_providers(is_default);

-- User preferences indexes
CREATE INDEX idx_user_meeting_preferences_user_id ON user_meeting_preferences(user_id);
CREATE INDEX idx_user_meeting_preferences_provider ON user_meeting_preferences(preferred_provider_id);

-- Meeting sessions indexes
CREATE INDEX idx_meeting_sessions_session_id ON meeting_sessions(session_id);
CREATE INDEX idx_meeting_sessions_provider ON meeting_sessions(provider_id);
CREATE INDEX idx_meeting_sessions_status ON meeting_sessions(meeting_status);
CREATE INDEX idx_meeting_sessions_scheduled_start ON meeting_sessions(scheduled_start_time);

-- Meeting participants indexes
CREATE INDEX idx_meeting_participants_meeting ON meeting_participants(meeting_session_id);
CREATE INDEX idx_meeting_participants_user ON meeting_participants(user_id);
CREATE INDEX idx_meeting_participants_joined_at ON meeting_participants(joined_at);

-- Meeting events indexes
CREATE INDEX idx_meeting_events_meeting ON meeting_events(meeting_session_id);
CREATE INDEX idx_meeting_events_type ON meeting_events(event_type);
CREATE INDEX idx_meeting_events_created_at ON meeting_events(created_at);

-- =====================================================
-- 7. UPDATE EXISTING SESSIONS TABLE
-- =====================================================

-- Add provider reference to existing sessions table
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS meeting_provider_id UUID REFERENCES meeting_providers(id);

-- Add index for the new column
CREATE INDEX IF NOT EXISTS idx_sessions_meeting_provider ON sessions(meeting_provider_id);

-- =====================================================
-- 8. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_meeting_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all meeting tables
CREATE TRIGGER update_meeting_providers_updated_at
    BEFORE UPDATE ON meeting_providers
    FOR EACH ROW EXECUTE FUNCTION update_meeting_updated_at_column();

CREATE TRIGGER update_user_meeting_preferences_updated_at
    BEFORE UPDATE ON user_meeting_preferences
    FOR EACH ROW EXECUTE FUNCTION update_meeting_updated_at_column();

CREATE TRIGGER update_meeting_sessions_updated_at
    BEFORE UPDATE ON meeting_sessions
    FOR EACH ROW EXECUTE FUNCTION update_meeting_updated_at_column();

CREATE TRIGGER update_meeting_participants_updated_at
    BEFORE UPDATE ON meeting_participants
    FOR EACH ROW EXECUTE FUNCTION update_meeting_updated_at_column();

-- =====================================================
-- 9. SAMPLE DATA FOR MICROSOFT TEAMS
-- =====================================================

-- Insert Microsoft Teams as default provider
INSERT INTO meeting_providers (
    name,
    display_name,
    provider_type,
    is_active,
    is_default,
    supports_recording,
    supports_screen_sharing,
    supports_whiteboard,
    supports_chat,
    max_participants,
    default_settings
) VALUES (
    'microsoft_teams',
    'Microsoft Teams',
    'third_party',
    true,
    true,
    true,
    true,
    true,
    true,
    300,
    '{
        "allowAnonymousUsers": false,
        "recordAutomatically": false,
        "lobbyBypassSettings": "organizationAndFederated",
        "allowMeetingChat": true,
        "allowTeamsCameraOn": true,
        "allowTeamsMicOn": true
    }'::jsonb
) ON CONFLICT (name) DO NOTHING;

-- Insert other common providers (inactive by default)
INSERT INTO meeting_providers (
    name,
    display_name,
    provider_type,
    is_active,
    is_default,
    supports_recording,
    supports_screen_sharing,
    supports_chat,
    max_participants
) VALUES
    ('zoom', 'Zoom', 'third_party', false, false, true, true, true, 1000),
    ('google_meet', 'Google Meet', 'third_party', false, false, true, true, true, 250),
    ('webex', 'Cisco Webex', 'third_party', false, false, true, true, true, 200),
    ('custom', 'Custom Meeting System', 'custom', false, false, true, true, true, 50)
ON CONFLICT (name) DO NOTHING;

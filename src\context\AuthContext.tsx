import {
  createContext,
  useContext,
  useEffect,
  useCallback,
  useRef,
  useState,
  ReactNode,
} from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/lib/supabaseClient";
import { useAuthStore } from "@/store/authStore";
import { useProfileStore } from "@/store/profileStore";
import {
  refreshToken,
  getAuthenticatedUser,
  updateUserMetadata,
} from "@/utils/supabase/middleware";
import { handleAuthError } from "@/services/errorHandler";

// Import ProfileData type from our store
import { CombinedProfileData } from "@/store/profileStore";

declare global {
  interface Window {
    _sessionHandlerTimeout?: ReturnType<typeof setTimeout>;
  }
}

// User types
// Import constants and types from separate file to avoid Fast Refresh issues
import { USER_STATUS, USER_TYPE, type UserType, type IsOnboarded, type UserStatus } from '@/constants/auth';

// Re-export for other components to use
export { USER_STATUS, USER_TYPE, type UserType, type IsOnboarded, type UserStatus };

interface UserContextValue {
  // Auth state
  session: Session | null;
  user: User | null;
  loading: boolean;
  isInitialized: boolean;

  // User metadata
  userType: UserType;
  isOnboarded: IsOnboarded;
  userStatus: UserStatus;

  // User profile data from our store
  profileData: CombinedProfileData;

  // Enrollment status
  isEnrolled: boolean;
  refreshEnrollmentStatus: () => Promise<void>;

  // Auth methods
  signUp: (
    email: string,
    password: string,
    metadata?: any
  ) => Promise<{
    error: any;
    data: any;
  }>;
  signIn: (
    email: string,
    password: string
  ) => Promise<{
    error: any;
    data: any;
  }>;
  signOut: () => Promise<{ error: any }>;

  // Guest user methods
  signUpAsGuestTutor: (email: string) => Promise<{
    error: any;
    data: any;
  }>;

  // User status methods
  refreshUserData: () => Promise<void>;
  isUserAdmin: () => boolean;

  // New method to fetch user data by email
  fetchUserDataByEmail: (email: string) => Promise<{
    userStatus: UserStatus;
    userType: UserType;
    isOnboarded: IsOnboarded;
    error: any;
  }>;
}

const AuthContext = createContext<UserContextValue | undefined>(undefined);

// Create a safer useAuth hook
export const useAuth = () => {
  const context = useContext(AuthContext);

  // Return a default object if context is not available
  if (!context) {
    console.warn("useAuth must be used within an AuthProvider");
    return {
      user: null,
      session: null,
      loading: false,
      isInitialized: false,
      userType: null,
      userStatus: null,
      isOnboarded: null,
      isEnrolled: false,
      refreshEnrollmentStatus: async () => {},
      authError: null,
      profileData: {
        firstName: null,
        lastName: null,
        email: "",
        userType: null,
        profilePictureUrl: null,
        timezone: null,
        createdAt: null,
        updatedAt: null,
      },
      signIn: async () => ({ error: new Error("Auth context not available") }),
      signOut: async () => ({ error: new Error("Auth context not available") }),
      signUp: async () => ({ error: new Error("Auth context not available") }),
      resetPassword: async () => ({
        error: new Error("Auth context not available"),
      }),
      updateProfile: async () => ({
        error: new Error("Auth context not available"),
      }),
      refreshUserData: async () => {},
      isUserAdmin: () => false,
      retryAuth: async () => {},
      fetchUserDataByEmail: async () => ({
        userStatus: "new" as UserStatus,
        userType: null as UserType,
        isOnboarded: false as IsOnboarded,
        error: new Error("Auth context not available"),
      }),
      signUpAsGuestTutor: async () => ({
        error: new Error("Auth context not available"),
        data: null,
      }),
    };
  }

  return context;
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const {
    user,
    session,
    userType,
    userStatus,
    isOnboarded,
    isInitialized,
    isLoading: loading,
    setUser,
    setSession,
    setUserData,
    setIsInitialized,
    setIsLoading,
    fetchUserData: storeFetchUserData,
    resetAuthState,
  } = useAuthStore();

  // Use the profileStore instead of useState
  const { profileData, updateProfile, resetProfileData } = useProfileStore();

  // Add enrollment state
  const [isEnrolled, setIsEnrolled] = useState(false);

  // Keep refs for optimization
  const isRefreshing = useRef(false);
  const lastProcessedSessionId = useRef<string | null>(null);
  const isMounted = useRef(true);
  const isProfileFetching = useRef(false);
  const lastProfileFetchId = useRef<string | null>(null);

  // Set up cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Function to preload profile images for better UX
  const preloadProfileImage = useCallback(async (imageUrl: string) => {
    if (!imageUrl) return;

    try {
      // Extract the path from the full URL
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');

      // Find the bucket and path - support all storage buckets
      const supportedBuckets = ['student-uploads', 'tutor-uploads', 'admin-uploads', 'avatars'];
      let bucketIndex = -1;

      for (const bucket of supportedBuckets) {
        bucketIndex = pathParts.findIndex(part => part === bucket);
        if (bucketIndex !== -1) break;
      }

      if (bucketIndex === -1) {
        console.log('Skipping preload for non-storage URL:', imageUrl);
        return;
      }

      const bucket = pathParts[bucketIndex];
      const filePath = pathParts.slice(bucketIndex + 1).join('/');

      // Create signed URL for preloading
      const { data, error } = await supabase.storage
        .from(bucket)
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (error) {
        console.log('Error preloading profile image:', error);
        return;
      }

      if (data?.signedUrl) {
        // Preload the image by creating an Image object
        const img = new Image();
        img.onload = () => console.log('✅ Profile image preloaded successfully');
        img.onerror = () => console.log('❌ Profile image preload failed');
        img.src = data.signedUrl;
      }
    } catch (error) {
      console.log('Error preloading profile image:', error);
    }
  }, []);

  // Function to fetch complete tutor profile data in a single call
  const fetchCompleteTutorProfile = useCallback(async (userId: string, explicitUserType?: string) => {
    const effectiveUserType = explicitUserType || userType;
    console.log('fetchCompleteTutorProfile called for:', userId);

    if (!userId) {
      console.log('No userId provided');
      return null;
    }

    // Prevent duplicate calls for the same user
    if (isProfileFetching.current && lastProfileFetchId.current === userId) {
      console.log('Already fetching complete tutor profile for this user, skipping duplicate call');
      return null;
    }

    try {
      isProfileFetching.current = true;
      lastProfileFetchId.current = userId;

      // Fetch basic profile data
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (profileError) {
        console.error('Error fetching basic profile:', profileError);
        return null;
      }

      // Fetch tutor-specific data from tutors table
      const { data: tutorData, error: tutorError } = await supabase
        .from("tutors")
        .select("*")
        .eq("id", userId)
        .single();

      let candidateTutorData = null;
      if (tutorError) {
        console.log('No tutor record found in tutors table, trying candidate_tutor table:', tutorError);

        // Fallback to candidate_tutor table if tutors table doesn't have the record
        const { data: candidateData, error: candidateError } = await supabase
          .from("candidate_tutor")
          .select("*")
          .eq("id", userId)
          .single();

        if (candidateError) {
          console.error('Error fetching tutor data from both tables:', candidateError);
          // Continue with basic profile data only
        } else {
          console.log('Found tutor data in candidate_tutor table');
          candidateTutorData = candidateData;
        }
      }

      console.log('Complete tutor profile data fetched:', {
        profileData,
        tutorData: tutorData || candidateTutorData
      });

      // Update profile data in the profile store with comprehensive tutor data
      const storeProfileData = {
        firstName: profileData.first_name,
        lastName: profileData.last_name,
        email: profileData.email,
        userType: effectiveUserType, // Use the effective userType (explicit or from context)
        profilePictureUrl: profileData.profile_picture_url,
        timezone: profileData.timezone,
        createdAt: profileData.created_at ? new Date(profileData.created_at) : null,
        updatedAt: profileData.updated_at ? new Date(profileData.updated_at) : null,

        // Tutor-specific data
        educationLevel: (tutorData || candidateTutorData)?.education_level || "",
        hourlyRate: (tutorData || candidateTutorData)?.hourly_rate || 0,
        subjectsTaught: (tutorData || candidateTutorData)?.subjects_taught || "",
        teachingExperience: (tutorData || candidateTutorData)?.teaching_experience || "",
        bio: (tutorData || candidateTutorData)?.bio || "",
        availability: (tutorData || candidateTutorData)?.availability || {},
        verificationStatus: (tutorData || candidateTutorData)?.verification_status || "",
        cvFilePath: (tutorData || candidateTutorData)?.cv_file_path || "",
        rating: (tutorData || candidateTutorData)?.rating || 0,
        dateOfBirth: (tutorData || candidateTutorData)?.date_of_birth || null,

        // Education data (empty for now since tutor_education table doesn't exist)
        education: [],

        // Availability slots (empty for now since tutor_availability table doesn't exist)
        availabilitySlots: [],

        // Additional tutor fields that might be useful
        specializations: (tutorData || candidateTutorData)?.specializations || [],
        subjects: (tutorData || candidateTutorData)?.subjects_taught ?
          (tutorData || candidateTutorData).subjects_taught.split(',').map((s: string) => s.trim()) : [],
      };

      // Update the profile store with complete tutor data
      console.log('fetchCompleteTutorProfile: Setting profile data with userType:', storeProfileData.userType);
      useProfileStore.getState().setProfileData(storeProfileData);

      // Preload profile image if available to improve UX
      if (profileData.profile_picture_url) {
        preloadProfileImage(profileData.profile_picture_url);
      }

      console.log('Updated tutor profile data in store:', storeProfileData);

      return {
        ...profileData,
        ...(tutorData || candidateTutorData)
      };

    } catch (error) {
      console.error('Error in fetchCompleteTutorProfile:', error);
      return null;
    } finally {
      isProfileFetching.current = false;
    }
  }, [userType]);

  // Function to fetch complete student profile data in a single call
  const fetchCompleteStudentProfile = useCallback(async (userId: string, explicitUserType?: string) => {
    const effectiveUserType = explicitUserType || userType;
    console.log('fetchCompleteStudentProfile called for:', userId);

    if (!userId) {
      console.log('No userId provided');
      setIsEnrolled(false);
      return null;
    }

    // Prevent duplicate calls for the same user
    if (isProfileFetching.current && lastProfileFetchId.current === userId) {
      console.log('Already fetching complete profile for this user, skipping duplicate call');
      return null;
    }

    try {
      isProfileFetching.current = true;
      lastProfileFetchId.current = userId;

      // First try the complete profile function
      let { data, error } = await supabase.rpc('get_student_complete_profile', {
        student_id: userId
      });

      // If there's a timestamp error, fall back to basic profile function
      if (error && error.message?.includes('timestamp')) {
        console.log('Timestamp error detected, falling back to basic profile function:', error);

        const { data: basicData, error: basicError } = await supabase.rpc('get_basic_student_profile', {
          student_id: userId
        });

        if (basicError) {
          console.error('Error fetching basic student profile:', basicError);
          setIsEnrolled(false);
          return null;
        }

        // Use basic data and set defaults for missing fields
        data = basicData?.map((profile: any) => ({
          ...profile,
          subjects_of_interest: [],
          learning_goals: [],
          study_preferences: {},
          academic_history: {},
          hobbies: [],
          interests: [],
          location: '',
          date_of_birth: null,
          active_subscriptions_count: 0,
          active_subscriptions: [],
          all_subscriptions: [],
          earliest_subscription_end: null,
          latest_subscription_end: null,
          total_days_remaining: 0
        }));

        error = null; // Clear the error since we handled it
      }

      if (error) {
        console.error('Error fetching complete student profile:', error);
        setIsEnrolled(false);
        return null;
      }

      console.log('Complete student profile data:', data);

      // RPC functions return arrays, so get the first element
      const profileData = data && data.length > 0 ? data[0] : null;

      if (profileData) {
        // Update enrollment status
        setIsEnrolled(profileData.is_enrolled || false);

        // Update profile data in the profile store
        const storeProfileData = {
          firstName: profileData.first_name,
          lastName: profileData.last_name,
          email: profileData.email,
          userType: effectiveUserType, // Use the effective userType (explicit or from context)
          profilePictureUrl: profileData.profile_picture_url,
          timezone: profileData.timezone,
          createdAt: profileData.created_at ? new Date(profileData.created_at) : null,
          updatedAt: profileData.updated_at ? new Date(profileData.updated_at) : null,
          // Student-specific data
          educationLevel: profileData.education_level,
          subjectsOfInterest: profileData.subjects_of_interest || [],
          learningGoals: profileData.learning_goals || [],
          studyPreferences: profileData.study_preferences || {},
          academicHistory: profileData.academic_history || {},
          hobbies: profileData.hobbies || [],
          interests: profileData.interests || [],
          location: profileData.location,
          dateOfBirth: profileData.date_of_birth,
          // Note: profile_completeness and socialLinks are calculated in UI, not stored in DB
          profileCompleteness: 0, // Calculated in UI components
          socialLinks: {}, // Handled in UI components
          // Enrollment data
          isEnrolled: profileData.is_enrolled || false,
          activeSubscriptionsCount: profileData.active_subscriptions_count || 0,
          activeSubscriptions: profileData.active_subscriptions || [],
          allSubscriptions: profileData.all_subscriptions || [],
          totalDaysRemaining: profileData.total_days_remaining || 0
        };

        // Update the profile store with complete data
        console.log('fetchCompleteStudentProfile: Setting profile data with userType:', storeProfileData.userType);
        useProfileStore.getState().setProfileData(storeProfileData);

        // Preload profile image if available to improve UX
        if (profileData.profile_picture_url) {
          preloadProfileImage(profileData.profile_picture_url);
        }

        console.log('Updated profile and enrollment status:', {
          isEnrolled: profileData.is_enrolled,
          activeSubscriptions: profileData.active_subscriptions_count,
          profileCompleteness: profileData.profile_completeness
        });

        return profileData;
      }

      setIsEnrolled(false);
      return null;
    } catch (error) {
      console.error('Error in fetchCompleteStudentProfile:', error);
      setIsEnrolled(false);
      return null;
    } finally {
      isProfileFetching.current = false;
    }
  }, [userType]);

  // Legacy function for backward compatibility - now uses the complete profile fetch
  const checkEnrollmentStatus = useCallback(async (userId: string, _forceCheck = false) => {
    console.log('checkEnrollmentStatus called (legacy) - redirecting to fetchCompleteStudentProfile');
    const profileData = await fetchCompleteStudentProfile(userId, USER_TYPE.STUDENT);
    return profileData?.is_enrolled || false;
  }, [fetchCompleteStudentProfile]);

  // Check enrollment status when userType or user changes
  useEffect(() => {
    if (user?.id && userType === USER_TYPE.STUDENT && isInitialized && isOnboarded) {
      // Only fetch if we're not already in the middle of a profile fetch
      if (!isProfileFetching.current) {
        console.log('UserType changed to student and user is onboarded, fetching complete profile data');
        fetchCompleteStudentProfile(user.id, userType);
      } else {
        console.log('Profile fetch already in progress, skipping useEffect trigger');
      }
    } else if (user?.id && userType === USER_TYPE.STUDENT && isInitialized && !isOnboarded) {
      console.log('Student user is not onboarded, skipping complete profile fetch');
    }
  }, [user?.id, userType, fetchCompleteStudentProfile, isInitialized, isOnboarded]);

  // Function to manually refresh enrollment status
  const refreshEnrollmentStatus = useCallback(async () => {
    if (user?.id && isOnboarded) {
      console.log('Manually refreshing enrollment status for onboarded user:', user.id);
      await fetchCompleteStudentProfile(user.id, USER_TYPE.STUDENT); // This will update enrollment status
    } else if (user?.id && !isOnboarded) {
      console.log('User is not onboarded, skipping enrollment status refresh');
      setIsEnrolled(false); // Not onboarded users are not enrolled
    }
  }, [user?.id, isOnboarded, fetchCompleteStudentProfile]);

  // Fetch user data from database - optimized to reduce API calls
  const fetchUserData = useCallback(
    async (userId: string) => {
      if (!userId) return;

      console.log(`Fetching user data for: ${userId}`);

      // Check if we're already processing this user ID
      if (isRefreshing.current && lastProcessedSessionId.current === userId) {
        console.log(
          "Already fetching data for this user, skipping duplicate call"
        );
        return;
      }

      try {
        isRefreshing.current = true;

        // First fetch basic user data to get userType
        await storeFetchUserData(userId);

        // Get the current userType and onboarding status from the store
        const currentUserType = useAuthStore.getState().userType;
        const currentIsOnboarded = useAuthStore.getState().isOnboarded;

        // If user is a student and onboarded, use the complete profile fetch
        if (currentUserType === USER_TYPE.STUDENT && currentIsOnboarded) {
          console.log('User is a student and onboarded, fetching complete profile data');
          await fetchCompleteStudentProfile(userId, currentUserType);
        } else if (currentUserType === USER_TYPE.STUDENT && !currentIsOnboarded) {
          console.log('User is a student but not onboarded, skipping complete profile fetch');
          await updateProfile(userId);
          setIsEnrolled(false); // Not onboarded students are not enrolled
        } else if (currentUserType === USER_TYPE.TUTOR && currentIsOnboarded) {
          console.log('User is a tutor and onboarded, fetching complete tutor profile data');
          await fetchCompleteTutorProfile(userId, currentUserType);
          setIsEnrolled(false); // Tutors are not enrolled
        } else if (currentUserType === USER_TYPE.TUTOR && !currentIsOnboarded) {
          console.log('User is a tutor but not onboarded, skipping complete profile fetch');
          await updateProfile(userId);
          setIsEnrolled(false); // Not onboarded tutors are not enrolled
        } else {
          // For other user types (admin, guest), just update basic profile data
          await updateProfile(userId);
          setIsEnrolled(false); // Non-students are not enrolled
        }

        // Mark this session as processed
        lastProcessedSessionId.current = userId;
      } finally {
        isRefreshing.current = false;
      }
    },
    [storeFetchUserData, fetchCompleteStudentProfile, fetchCompleteTutorProfile, updateProfile]
  );

  // Refresh user data function
  const refreshUserData = useCallback(async () => {
    if (!user) {
      console.log("No user to refresh data for");
      return;
    }

    try {
      isRefreshing.current = true;

      // First, validate the token and get the authenticated user
      const authenticatedUser = await getAuthenticatedUser();

      if (!authenticatedUser) {
        console.log("User not authenticated, resetting auth state");
        resetAuthState();
        return;
      }

      // Update the user in state if it's different
      if (JSON.stringify(user) !== JSON.stringify(authenticatedUser)) {
        setUser(authenticatedUser);
      }

      console.log("Refreshing user data for:", authenticatedUser.id);

      // Check user metadata first for quick access
      const metadata = authenticatedUser.user_metadata;
      if (metadata?.user_type) {
        setUserData({
          userType: metadata.user_type,
          userStatus,
          isOnboarded,
        });
      }

      // Fetch the latest user data from the database
      const userData = await useAuthStore
        .getState()
        .fetchUserData(authenticatedUser.id);

      if (userData) {
        // Update context state
        setUserData({
          userType: userData.userType,
          userStatus: userData.userStatus || "new",
          isOnboarded: userData.onboarding_completed || false,
        });

        // Also ensure the Zustand store is updated
        useAuthStore.getState().setUserData({
          userType: userData.userType,
          userStatus: userData.userStatus || "new",
          isOnboarded: userData.onboarding_completed || false,
        });

        console.log("User data refreshed successfully:", {
          userType: userData.userType,
          userStatus: userData.userStatus || "new",
          isOnboarded: userData.onboarding_completed || false,
        });

        // For students and tutors, fetch complete profile data only if onboarded
        // Only if we're not already fetching to avoid duplicates
        // Pass the explicit userType to avoid stale closure issues
        if (userData.userType === USER_TYPE.STUDENT && userData.onboarding_completed && !isProfileFetching.current) {
          console.log('Student is onboarded, fetching complete profile data');
          await fetchCompleteStudentProfile(authenticatedUser.id, userData.userType);
        } else if (userData.userType === USER_TYPE.STUDENT && !userData.onboarding_completed) {
          console.log('Student is not onboarded, skipping complete profile fetch');
        } else if (userData.userType === USER_TYPE.TUTOR && userData.onboarding_completed && !isProfileFetching.current) {
          console.log('Tutor is onboarded, fetching complete profile data');
          await fetchCompleteTutorProfile(authenticatedUser.id, userData.userType);
        } else if (userData.userType === USER_TYPE.TUTOR && !userData.onboarding_completed) {
          console.log('Tutor is not onboarded, skipping complete profile fetch');
        }
      }
    } catch (error) {
      console.error("Error refreshing user data:", error);
    } finally {
      isRefreshing.current = false;
    }
  }, [
    user,
    fetchUserData,
    resetAuthState,
    setUserData,
    setUser,
    userStatus,
    isOnboarded,
    checkEnrollmentStatus,
  ]);

  // Handle session changes using Zustand actions
  const handleSession = useCallback(
    async (newSession: Session | null) => {
      // Check if component is still mounted
      if (!isMounted.current) {
        console.log("Skipping session handling - component unmounted");
        return;
      }

      // Prevent infinite loops by checking if we're already processing this session
      const sessionId = newSession?.user?.id || null;
      const currentSessionId = lastProcessedSessionId.current;

      console.log(
        "Handle session called with session ID:",
        sessionId,
        "Current processed ID:",
        currentSessionId
      );

      // Add more debugging to see what's happening during session handling
      console.log("Processing new session state:", {
        hasSession: !!newSession,
        isInitialized,
        currentPath: window.location.pathname,
      });

      // If we're already processing this exact session state, skip
      if (sessionId === currentSessionId && isInitialized) {
        console.log("Skipping duplicate session processing");
        return;
      }

      // Update the last processed session ID immediately to prevent re-entry
      lastProcessedSessionId.current = sessionId;

      // Set loading state
      setIsLoading(true);

      try {
        // Update session in Zustand store
        setSession(newSession);

        // If no session, reset auth state and profile data
        if (!newSession) {
          console.log("No session, resetting auth state");
          resetAuthState();
          resetProfileData();
          setIsEnrolled(false);

          // Clear any persisted process steps modal state on logout
          const { useProcessStepsStore } = await import("@/store/processStepsStore");
          useProcessStepsStore.getState().resetState();

          return;
        }

        // Update user in Zustand store
        setUser(newSession.user);

        // Clear any persisted process steps modal state on login to prevent stale modals
        const { useProcessStepsStore } = await import("@/store/processStepsStore");
        useProcessStepsStore.getState().resetState();

        // Check if this is an OAuth user and handle userType
        const isOAuthUser = newSession.user?.app_metadata?.provider &&
          newSession.user.app_metadata.provider !== 'email';

        if (isOAuthUser) {
          console.log("Detected OAuth user, checking user metadata");

          // Check if user_type already exists in metadata
          const metadata = newSession.user.user_metadata || {};
          const existingUserType = metadata.user_type;

          if (!existingUserType) {
            console.log("No user_type found in metadata, checking URL parameters");

            // Check for oauth_usertype in URL parameters (from OAuth callback)
            const urlParams = new URLSearchParams(window.location.search);
            const oauthUserType = urlParams.get('oauth_usertype');

            // Use the OAuth userType from URL parameter, or default to student
            let userType = oauthUserType || 'student';

            console.log(`Setting user_type to: ${userType} (from ${oauthUserType ? 'URL parameter' : 'default'})`);

            // Update auth metadata with user_type
            const { success, error } = await updateUserMetadata({
              user_type: userType,
              onboarding_completed: false
            });

            if (!success) {
              console.error('Failed to update user metadata during OAuth signup:', error);
            } else {
              console.log(`Successfully set user_type to ${userType} in auth metadata`);
            }
          } else {
            console.log(`User already has user_type in metadata: ${existingUserType}`);
          }
        }

        // Fetch user data if we have a user
        if (newSession.user) {
          console.log("Fetching user data for:", newSession.user.id);
          // fetchUserData already handles calling fetchCompleteStudentProfile for students
          await fetchUserData(newSession.user.id);

          // Also fetch profile data for non-students (students get this via fetchCompleteStudentProfile)
          // Tutors get this via fetchCompleteTutorProfile
          const currentUserType = useAuthStore.getState().userType;
          if (currentUserType !== USER_TYPE.STUDENT && currentUserType !== USER_TYPE.TUTOR) {
            await updateProfile(newSession.user.id);
          }
        }
      } catch (error) {
        console.error("Error in handleSession:", error);
        // Log the error but don't try to set it in state here
        // We'll handle errors at the component level
        console.error("Session handling error:", handleAuthError(error));
      } finally {
        // Always ensure these are set when done
        setIsInitialized(true);
        setIsLoading(false);
      }
    },
    [
      isInitialized,
      setSession,
      resetAuthState,
      resetProfileData,
      setUser,
      fetchUserData,
      updateProfile,
      setIsInitialized,
      setIsLoading,
      checkEnrollmentStatus,
    ]
  );

  // Set up auth state change listener
  useEffect(() => {
    // Subscribe to auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      console.log(
        "Auth state changed:",
        event,
        "Session ID:",
        session?.user?.id
      );

      // Only process certain events to prevent loops
      if (
        ["SIGNED_IN", "SIGNED_OUT", "USER_UPDATED", "TOKEN_REFRESHED", "INITIAL_SESSION"].includes(
          event
        )
      ) {
        try {
          handleSession(session);
        } catch (error) {
          console.error("Error handling session:", error);
          // Update Zustand store
          setIsLoading(false);
          setIsInitialized(true);
        }
      } else {
        console.log("Ignoring auth event:", event);
      }
    });

    // Check initial session
    supabase.auth
      .getSession()
      .then(({ data: { session } }) => {
        if (isMounted.current) {
          console.log("Initial session check, Session ID:", session?.user?.id);
          try {
            handleSession(session);
          } catch (error) {
            console.error("Error handling initial session:", error);
            if (isMounted.current) {
              setIsLoading(false);
              setIsInitialized(true);
            }
          }
        }
      })
      .catch((error) => {
        console.error("Error getting session:", error);
        if (isMounted.current) {
          setIsLoading(false);
          setIsInitialized(true);
        }
      });

    // Cleanup function
    return () => {
      isMounted.current = false;
      subscription.unsubscribe();
    };
  }, [handleSession, setIsLoading, setIsInitialized]);

  // Sign up method
  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      return { data, error };
    } catch (error) {
      console.error("Sign up error:", error);
      return { data: null, error };
    }
  };

  // Sign in method
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) return { data: null, error };

      // If authentication succeeded, refresh user data
      if (data?.user) {
        // refreshUserData will trigger the auth flow which handles all profile fetching
        await refreshUserData();

        if (isOnboarded === false) {
          return {
            data: null,
            error: {
              message:
                "Please complete your onboarding process before logging in.",
              onboardingRequired: true,
              user: data.user,
            },
          };
        }
      }

      return { data, error };
    } catch (error) {
      console.error("Sign in error:", error);
      return { data: null, error };
    }
  };

  // Sign out method
  const signOut = async () => {
    try {
      // First clear local state using the store's setUserData method
      setUserData({
        userType: null,
        userStatus: null,
        isOnboarded: null,
      });

      // Reset profile data
      resetProfileData();

      // Reset enrollment status
      setIsEnrolled(false);

      // Then attempt to sign out from Supabase
      const { error } = await supabase.auth.signOut();

      // Clear all Supabase related items from localStorage
      for (const key in localStorage) {
        if (key.startsWith("supabase.auth.")) {
          localStorage.removeItem(key);
        }
      }

      // Also clear any other auth-related localStorage items
      localStorage.removeItem("pendingTutorEmail");

      // Use the authStore's resetAuthState to ensure Zustand state is also cleared
      if (typeof window !== "undefined") {
        resetAuthState();
      }

      // Force reload to clear any state regardless of error
      window.location.href = "/";

      return { error: error || null };
    } catch (error) {
      console.error("Sign out error:", error);

      // Reset profile data even on error
      resetProfileData();

      // Reset enrollment status
      setIsEnrolled(false);

      // Clear all Supabase related items from localStorage
      for (const key in localStorage) {
        if (key.startsWith("supabase.auth.")) {
          localStorage.removeItem(key);
        }
      }

      // Also clear any other auth-related localStorage items
      localStorage.removeItem("pendingTutorEmail");

      // Use the authStore's resetAuthState to ensure Zustand state is also cleared
      if (typeof window !== "undefined") {
        resetAuthState();
      }

      // Force reload to clear any state
      window.location.href = "/";
      return { error };
    }
  };

  // Guest tutor signup
  const signUpAsGuestTutor = async (email: string) => {
    try {
      // Store email in localStorage for later use
      localStorage.setItem("pendingTutorEmail", email);

      // Send OTP to verify email
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/email-confirmation?source=become-tutor`,
        },
      });

      if (error) throw error;

      // Set user status to tutor using setUserData
      setUserData({
        userType: USER_TYPE.TUTOR as UserType, // Cast to UserType
        userStatus: USER_STATUS.GUEST as UserStatus, // Cast to UserStatus
        isOnboarded: false,
      });

      return { data, error: null };
    } catch (error) {
      console.error("Guest tutor signup error:", error);
      return { data: null, error };
    }
  };

  // Check if user is admin
  const isUserAdmin = () => {
    return userType === USER_TYPE.ADMIN;
  };

  // New function to fetch user data by email
  const fetchUserDataByEmail = async (
    email: string
  ): Promise<{
    userStatus: UserStatus;
    userType: UserType;
    isOnboarded: IsOnboarded;
    error: any;
  }> => {
    try {
      console.log(`Fetching user data for email: ${email}`);

      // Use the RPC function to get user data by email
      const { data, error } = await supabase.rpc("get_user_data_by_email", {
        email_address: email,
      });

      if (error) {
        console.error("Error fetching user data by email:", error);
        return {
          userStatus: "new" as UserStatus,
          userType: null as UserType,
          isOnboarded: false as IsOnboarded,
          error: error,
        };
      }

      return {
        userStatus: (data?.userStatus || "new") as UserStatus,
        userType: (data?.userType || null) as UserType,
        isOnboarded: (data?.onboarding_completed || false) as IsOnboarded,
        error: null,
      };
    } catch (error) {
      console.error("Unexpected error in fetchUserDataByEmail:", error);
      return {
        userStatus: "new" as UserStatus,
        userType: null as UserType,
        isOnboarded: false as IsOnboarded,
        error: error,
      };
    }
  };

  // Set up a timer to periodically check and refresh the token
  useEffect(() => {
    const tokenRefreshInterval = setInterval(async () => {
      if (!isMounted.current) return;

      try {
        // Only refresh if we have a user
        if (user) {
          console.log("Checking token freshness...");
          const refreshedSession = await refreshToken();

          if (refreshedSession) {
            console.log("Token refreshed successfully");
          } else if (user) {
            // We had a user but couldn't refresh the token
            console.log("Token refresh failed, user may be logged out");
            // Update local state to reflect logged out status
            if (isMounted.current) {
              setSession(null);
              setUser(null);
              setUserData({
                userType: null,
                userStatus: null,
                isOnboarded: null,
              });
            }
          }
        }
      } catch (error) {
        console.error("Error in token refresh interval:", error);
      }
    }, 5 * 60 * 1000); // Check every 5 minutes

    return () => {
      clearInterval(tokenRefreshInterval);
    };
  }, [user, setSession, setUser, setUserData]);

  // Context value
  const value: UserContextValue = {
    session,
    user,
    loading,
    isInitialized,
    userType: userType as UserType, // Cast to UserType
    isOnboarded,
    userStatus: userStatus as UserStatus, // Cast to UserStatus for consistency
    profileData,
    isEnrolled,
    refreshEnrollmentStatus,
    signUp,
    signIn,
    signOut,
    signUpAsGuestTutor,
    refreshUserData,
    isUserAdmin,
    fetchUserDataByEmail,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

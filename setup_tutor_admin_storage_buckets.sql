/*
=====================================================
TUTOR AND ADMIN STORAGE BUCKETS SETUP
=====================================================

This script creates storage buckets and policies for tutors and admins,
similar to the existing student-uploads bucket structure.

Bucket Structure:
- tutor-uploads/profiles/{user_id}/{filename} (profile pictures)
- tutor-uploads/cv/{user_id}/{filename} (CV files)
- admin-uploads/profiles/{user_id}/{filename} (profile pictures)
- admin-uploads/cv/{user_id}/{filename} (documents/files)

Run this script in your Supabase SQL editor.
*/

-- =====================================================
-- STEP 1: CREATE STORAGE BUCKETS
-- =====================================================

-- Create tutor-uploads bucket for tutor profile photos and CV files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'tutor-uploads',
    'tutor-uploads',
    true,
    10485760, -- 10MB limit (increased for CV files)
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create admin-uploads bucket for admin profile photos and files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'admin-uploads',
    'admin-uploads',
    true,
    10485760, -- 10MB limit (for documents and files)
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- =====================================================
-- STEP 2: CREATE STORAGE POLICIES FOR TUTOR-UPLOADS
-- =====================================================

-- Policy 1: Allow tutors to upload files to their own profiles folder
CREATE POLICY "Tutors can upload their own profile files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- Policy 1b: Allow tutors to upload CV files to their own cv folder
CREATE POLICY "Tutors can upload their own CV files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'cv' AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- Policy 2: Allow tutors to view their own files (profiles and cv)
CREATE POLICY "Tutors can view their own files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- Policy 3: Allow tutors to update their own files (profiles and cv)
CREATE POLICY "Tutors can update their own files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- Policy 4: Allow tutors to delete their own files (profiles and cv)
CREATE POLICY "Tutors can delete their own files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- Policy 5: Allow public read access to tutor uploads (for profile pictures)
CREATE POLICY "Public can view tutor uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'tutor-uploads'
    );

-- Policy 6: Allow admins to manage all tutor uploads
CREATE POLICY "Admins can manage all tutor uploads" ON storage.objects
    FOR ALL USING (
        bucket_id = 'tutor-uploads' AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- STEP 3: CREATE STORAGE POLICIES FOR ADMIN-UPLOADS
-- =====================================================

-- Policy 1: Allow admins to upload files to their own profiles folder
CREATE POLICY "Admins can upload their own profile files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Policy 1b: Allow admins to upload files to their own cv folder
CREATE POLICY "Admins can upload their own CV files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'cv' AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Policy 2: Allow admins to view their own files (profiles and cv)
CREATE POLICY "Admins can view their own files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Policy 3: Allow admins to update their own files (profiles and cv)
CREATE POLICY "Admins can update their own files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Policy 4: Allow admins to delete their own files (profiles and cv)
CREATE POLICY "Admins can delete their own files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Policy 5: Allow public read access to admin uploads (for profile pictures)
CREATE POLICY "Public can view admin uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'admin-uploads'
    );

-- Policy 6: Allow super admins to manage all admin uploads (if needed)
CREATE POLICY "Super admins can manage all admin uploads" ON storage.objects
    FOR ALL USING (
        bucket_id = 'admin-uploads' AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check if buckets were created successfully
SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id IN ('tutor-uploads', 'admin-uploads');

-- Check if policies were created successfully
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%tutor%' OR policyname LIKE '%admin%'
ORDER BY policyname;

/*
SUMMARY:

This script creates:

1. Two new storage buckets:
   - tutor-uploads: For tutor profile pictures and files
   - admin-uploads: For admin profile pictures and files

2. Comprehensive RLS policies for each bucket that:
   - Allow users to upload files to their own profiles/{user_id}/ and cv/{user_id}/ folders
   - Allow users to manage their own files (view, update, delete) in both folders
   - Allow public read access for profile pictures
   - Allow admins to manage all files in their respective buckets
   - Enforce user_type validation through profiles table

3. Proper file size limits (10MB for documents) and MIME type restrictions
   - Images: JPEG, PNG, WebP
   - Documents: PDF, DOC, DOCX

4. Folder structure:
   - profiles/{user_id}/{filename} (for profile pictures)
   - cv/{user_id}/{filename} (for CV and document files)

To use this:
1. Run this SQL in your Supabase SQL editor
2. Verify the buckets and policies were created using the verification queries
3. Update your application code to use the appropriate bucket based on user type

The policies ensure that:
- Users can only upload to their own folders (profiles/ and cv/)
- File paths follow the patterns:
  - profiles/{user_id}/filename (for profile pictures)
  - cv/{user_id}/filename (for CV and documents)
- Public can view uploaded images (for profile pictures)
- Proper user type validation is enforced
- Separate folder access for different file types
*/

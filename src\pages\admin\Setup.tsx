import { useState, useEffect } from 'react';
import { setUserAsAdmin } from '@/services/authService';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabaseClient';

const AdminSetup = () => {
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const { user } = useAuth();

  // Check if user is already an admin
  useEffect(() => {
    if (user) {
      // Check user metadata for admin role
      const hasAdminRole = user.user_metadata?.role === 'admin';
      
      // Also check profiles table
      const checkProfileAdmin = async () => {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('user_type')
            .eq('id', user.id)
            .single();
            
          if (error) throw error;
          
          const isProfileAdmin = data?.user_type === 'admin';
          setIsAdmin(hasAdminRole || isProfileAdmin);
        } catch (error) {
          console.error('Error checking admin status:', error);
        }
      };
      
      checkProfileAdmin();
    }
  }, [user]);

  const handleSetAdmin = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      const result = await setUserAsAdmin();
      
      if (result.success) {
        setMessage('Successfully set as admin! Please log out and log back in for changes to take effect.');
        setIsAdmin(true);
      } else {
        setMessage(`Error: ${result.error}`);
      }
    } catch (error) {
      setMessage(`Error: ${error.message}`);
      console.error('Admin setup error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Admin Setup</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl mb-4">Current User Info</h2>
        
        {user ? (
          <>
            <div className="mb-4">
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>ID:</strong> {user.id}</p>
              <p><strong>Current Admin Status:</strong> {isAdmin ? 'Admin' : 'Not Admin'}</p>
            </div>
            
            <pre className="bg-gray-100 p-4 rounded mb-4 overflow-auto text-xs">
              {JSON.stringify(user, null, 2)}
            </pre>
            
            {isAdmin ? (
              <div className="bg-green-100 text-green-700 p-4 rounded mb-4">
                This user is already an admin. No further action needed.
              </div>
            ) : (
              <button 
                onClick={handleSetAdmin}
                disabled={loading}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                {loading ? 'Processing...' : 'Set Current User as Admin'}
              </button>
            )}
          </>
        ) : (
          <div className="bg-yellow-100 text-yellow-700 p-4 rounded">
            Please log in to continue with admin setup.
          </div>
        )}
        
        {message && (
          <div className={`mt-4 p-3 rounded ${message.includes('Error') ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
            {message}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminSetup;


import React, { useState } from "react";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import {
  <PERSON><PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/Tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/Dialog";
import { Textarea } from "@/components/ui/TextArea";
import {
  CustomDropdownMenu,
  CustomDropdownMenuContent,
  CustomDropdownMenuItem,
  CustomDropdownMenuTrigger,
} from "@/components/ui/CustomDropdownMenu";
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  ChevronRight,
  Clock,
  Download,
  Edit,
  FileText,
  Info,
  MessageSquare,
  MoreHorizontal,
  Search,
  Upload,
  X,
  Zap,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { Checkbox } from "@/components/ui/Checkbox";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";

// Sample accepted requests data
const acceptedRequestsData = [
  {
    id: "REQ-1001",
    student: {
      id: "STU-1001",
      name: "Alice Johnson",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Alice+Johnson&background=random",
      timezone: "GMT+1",
    },
    topic: "Neural Networks",
    subtopic: "Convolutional Neural Networks",
    sessionType: "One-on-One",
    date: "2023-12-05",
    time: "10:00 AM",
    duration: 60,
    acceptMethod: "manual",
    preparationStatus: {
      materialUploaded: true,
      whiteboardAccess: true,
      studentNotified: true,
      reminderSent: false,
    },
    notes: "Alice requested to focus on CNN architectures and practical implementations.",
    studentPreparation: {
      materialUploaded: true,
      questionsSubmitted: true,
    },
  },
  {
    id: "REQ-1002",
    student: {
      id: "STU-1002",
      name: "Bob Smith",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Bob+Smith&background=random",
      timezone: "GMT-5",
    },
    topic: "Reinforcement Learning",
    subtopic: "Q-Learning",
    sessionType: "One-on-One",
    date: "2023-12-06",
    time: "2:00 PM",
    duration: 60,
    acceptMethod: "auto",
    preparationStatus: {
      materialUploaded: false,
      whiteboardAccess: true,
      studentNotified: true,
      reminderSent: true,
    },
    notes: "Bob is struggling with the mathematical concepts behind Q-learning.",
    studentPreparation: {
      materialUploaded: false,
      questionsSubmitted: true,
    },
  },
  {
    id: "REQ-1003",
    student: {
      id: "STU-1003",
      name: "Charlie Davis",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Charlie+Davis&background=random",
      timezone: "GMT+0",
    },
    topic: "Computer Vision",
    subtopic: "Object Detection",
    sessionType: "Group",
    date: "2023-12-07",
    time: "11:00 AM",
    duration: 90,
    acceptMethod: "manual",
    preparationStatus: {
      materialUploaded: true,
      whiteboardAccess: false,
      studentNotified: true,
      reminderSent: false,
    },
    notes: "Group session with 3 students. Focus on YOLO and R-CNN architectures.",
    studentPreparation: {
      materialUploaded: true,
      questionsSubmitted: false,
    },
  },
  {
    id: "REQ-1004",
    student: {
      id: "STU-1004",
      name: "Diana Wang",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Diana+Wang&background=random",
      timezone: "GMT+8",
    },
    topic: "Natural Language Processing",
    subtopic: "Transformers",
    sessionType: "One-on-One",
    date: "2023-12-08",
    time: "9:00 AM",
    duration: 60,
    acceptMethod: "auto",
    preparationStatus: {
      materialUploaded: false,
      whiteboardAccess: false,
      studentNotified: false,
      reminderSent: false,
    },
    notes: "Diana is new to NLP and needs a comprehensive introduction to transformers.",
    studentPreparation: {
      materialUploaded: false,
      questionsSubmitted: false,
    },
  },
];

// Format date to display in a readable format
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' });
};

// Group sessions by date
const groupSessionsByDate = (sessions: any[]) => {
  const grouped: Record<string, any[]> = {};

  sessions.forEach(session => {
    if (!grouped[session.date]) {
      grouped[session.date] = [];
    }
    grouped[session.date].push(session);
  });

  // Sort sessions within each date by time
  Object.keys(grouped).forEach(date => {
    grouped[date].sort((a, b) => {
      const timeA = new Date(`${a.date} ${a.time}`).getTime();
      const timeB = new Date(`${b.date} ${b.time}`).getTime();
      return timeA - timeB;
    });
  });

  return grouped;
};

const AcceptedRequests = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"timeline" | "calendar">("timeline");
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [showMessageDialog, setShowMessageDialog] = useState(false);
  const [showRescheduleDialog, setShowRescheduleDialog] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [messageText, setMessageText] = useState("");
  const [cancelReason, setCancelReason] = useState("");

  // Filter sessions based on search term
  const filteredSessions = acceptedRequestsData.filter(session => {
    const searchString = searchTerm.toLowerCase();
    return (
      session.student.name.toLowerCase().includes(searchString) ||
      session.topic.toLowerCase().includes(searchString) ||
      session.subtopic.toLowerCase().includes(searchString)
    );
  });

  // Group sessions by date for timeline view
  const groupedSessions = groupSessionsByDate(filteredSessions);
  const sortedDates = Object.keys(groupedSessions).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

  // Get accept method badge
  const getAcceptMethodBadge = (method: string) => {
    if (method === "auto") {
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Auto-Accept</Badge>;
    }
    return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Manual Accept</Badge>;
  };

  // Get preparation status
  const getPreparationStatus = (session: any) => {
    const { preparationStatus, studentPreparation } = session;
    const totalItems = Object.values(preparationStatus).length;
    const completedItems = Object.values(preparationStatus).filter(value => value === true).length;

    return {
      tutor: {
        completed: completedItems,
        total: totalItems,
        percentage: Math.round((completedItems / totalItems) * 100),
      },
      student: {
        materialUploaded: studentPreparation.materialUploaded,
        questionsSubmitted: studentPreparation.questionsSubmitted,
      },
    };
  };

  // Handle message send
  const handleSendMessage = () => {
    // In a real app, this would send a message to the student
    console.log(`Sending message to ${selectedRequest.student.name}: ${messageText}`);
    setMessageText("");
    setShowMessageDialog(false);
  };

  // Handle session cancellation
  const handleCancelSession = () => {
    // In a real app, this would cancel the session
    console.log(`Cancelling session with ${selectedRequest.student.name}. Reason: ${cancelReason}`);
    setCancelReason("");
    setShowCancelDialog(false);
  };

  return (
    <TutorPageLayout
      title="Accepted Requests"
      description="Manage your confirmed teaching sessions and prepare for upcoming classes"
      actions={
        <Badge className="bg-green-100 text-green-800 border-green-200">
          {filteredSessions.length} Confirmed
        </Badge>
      }
    >
      {/* Search and View Toggle */}
      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <div className="relative flex-grow max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search by student, topic, or subtopic..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="timeline" className="w-full sm:w-auto" onValueChange={(value) => setViewMode(value as "timeline" | "calendar")}>
          <TabsList className="grid grid-cols-2 w-full sm:w-auto">
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="calendar">Calendar</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Coming Soon Feature Banner */}
      <Card className="mb-6 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-100">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Zap className="h-6 w-6 text-purple-500 flex-shrink-0 mt-1" />
            <div>
              <h3 className="font-medium text-purple-800">Coming Soon: Smart Preparation Assistant</h3>
              <p className="text-sm text-purple-700 mt-1">
                We're developing AI-generated preparation tips based on session topics and time-zone alignment reminders to help you prepare more effectively.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline View */}
      {viewMode === "timeline" && (
        <div className="space-y-8">
          {sortedDates.length > 0 ? (
            sortedDates.map(date => (
              <div key={date} className="space-y-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-purple-500" />
                  <h3 className="text-lg font-semibold">{formatDate(date)}</h3>
                </div>

                <div className="space-y-4 pl-8 border-l-2 border-gray-100">
                  {groupedSessions[date].map(session => (
                    <Card key={session.id} className="overflow-hidden hover:shadow-md transition-shadow">
                      <CardContent className="p-0">
                        <div className="p-4 sm:p-6">
                          <div className="flex flex-col sm:flex-row justify-between gap-4">
                            <div className="flex items-start gap-4">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={session.student.avatar} alt={session.student.name} />
                                <AvatarFallback>{session.student.name.split(' ').map((n: string) => n[0]).join('')}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="flex items-center gap-2">
                                  <h3 className="font-semibold">{session.student.name}</h3>
                                  {getAcceptMethodBadge(session.acceptMethod)}
                                </div>
                                <p className="text-sm text-gray-500">{session.student.email}</p>
                                <div className="mt-2">
                                  <p className="text-sm font-medium">{session.topic}: {session.subtopic}</p>
                                  <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                                    <Clock size={14} />
                                    <span>{session.time} ({session.duration} min)</span>
                                    {session.student.timezone && (
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger>
                                            <Info size={14} className="text-gray-400" />
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>Student timezone: {session.student.timezone}</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-purple-600 border-purple-200 hover:bg-purple-50"
                                onClick={() => {
                                  setSelectedRequest(session);
                                  setShowMessageDialog(true);
                                }}
                              >
                                <MessageSquare size={14} className="mr-1" />
                                Message
                              </Button>
                              <CustomDropdownMenu>
                                <CustomDropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal size={16} />
                                  </Button>
                                </CustomDropdownMenuTrigger>
                                <CustomDropdownMenuContent align="end">
                                  <CustomDropdownMenuItem
                                    onClick={() => {
                                      setSelectedRequest(session);
                                      setShowRescheduleDialog(true);
                                    }}
                                  >
                                    <Edit size={14} className="mr-2" />
                                    Reschedule
                                  </CustomDropdownMenuItem>
                                  <CustomDropdownMenuItem
                                    onClick={() => {
                                      setSelectedRequest(session);
                                      setShowCancelDialog(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <X size={14} className="mr-2" />
                                    Cancel Session
                                  </CustomDropdownMenuItem>
                                </CustomDropdownMenuContent>
                              </CustomDropdownMenu>
                            </div>
                          </div>

                          {/* Preparation Checklist */}
                          <div className="mt-4 pt-4 border-t border-gray-100">
                            <div className="flex flex-col sm:flex-row justify-between gap-4">
                              <div>
                                <h4 className="text-sm font-medium mb-2">Tutor Preparation</h4>
                                <div className="space-y-2">
                                  <div className="flex items-center">
                                    <Checkbox
                                      id={`material-${session.id}`}
                                      checked={session.preparationStatus.materialUploaded}
                                      className="mr-2"
                                    />
                                    <label htmlFor={`material-${session.id}`} className="text-sm">
                                      Upload teaching materials
                                    </label>
                                  </div>
                                  <div className="flex items-center">
                                    <Checkbox
                                      id={`whiteboard-${session.id}`}
                                      checked={session.preparationStatus.whiteboardAccess}
                                      className="mr-2"
                                    />
                                    <label htmlFor={`whiteboard-${session.id}`} className="text-sm">
                                      Set up whiteboard access
                                    </label>
                                  </div>
                                  <div className="flex items-center">
                                    <Checkbox
                                      id={`notify-${session.id}`}
                                      checked={session.preparationStatus.studentNotified}
                                      className="mr-2"
                                    />
                                    <label htmlFor={`notify-${session.id}`} className="text-sm">
                                      Send session confirmation
                                    </label>
                                  </div>
                                  <div className="flex items-center">
                                    <Checkbox
                                      id={`reminder-${session.id}`}
                                      checked={session.preparationStatus.reminderSent}
                                      className="mr-2"
                                    />
                                    <label htmlFor={`reminder-${session.id}`} className="text-sm">
                                      Send 24h reminder
                                    </label>
                                  </div>
                                </div>
                              </div>

                              <div>
                                <h4 className="text-sm font-medium mb-2">Student Preparation</h4>
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2">
                                    {session.studentPreparation.materialUploaded ? (
                                      <CheckCircle size={16} className="text-green-500" />
                                    ) : (
                                      <AlertCircle size={16} className="text-yellow-500" />
                                    )}
                                    <span className="text-sm">
                                      {session.studentPreparation.materialUploaded
                                        ? "Materials uploaded"
                                        : "Student hasn't uploaded materials yet"}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {session.studentPreparation.questionsSubmitted ? (
                                      <CheckCircle size={16} className="text-green-500" />
                                    ) : (
                                      <AlertCircle size={16} className="text-yellow-500" />
                                    )}
                                    <span className="text-sm">
                                      {session.studentPreparation.questionsSubmitted
                                        ? "Questions submitted"
                                        : "Student hasn't submitted questions yet"}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Session Notes */}
                            {session.notes && (
                              <div className="mt-4 pt-4 border-t border-gray-100">
                                <div className="flex items-start gap-2">
                                  <FileText size={16} className="text-gray-400 mt-0.5" />
                                  <div>
                                    <h4 className="text-sm font-medium">Session Notes</h4>
                                    <p className="text-sm text-gray-600 mt-1">{session.notes}</p>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Action Buttons */}
                            <div className="mt-4 flex justify-end gap-2">
                              <Button variant="outline" size="sm" className="text-gray-600">
                                <Upload size={14} className="mr-1" />
                                Upload Materials
                              </Button>
                              <Button variant="outline" size="sm" className="text-gray-600">
                                <Download size={14} className="mr-1" />
                                Download Info
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900">No confirmed sessions</h3>
              <p className="text-gray-500 mt-2">
                You don't have any confirmed sessions yet. Check your pending requests.
              </p>
            </div>
          )}
        </div>
      )}

      {/* Calendar View */}
      {viewMode === "calendar" && (
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900">Calendar View</h3>
            <p className="text-gray-500 mt-2">
              Calendar view is coming soon. For now, please use the timeline view.
            </p>
          </div>
        </div>
      )}

      {/* Message Dialog */}
      {selectedRequest && (
        <Dialog open={showMessageDialog} onOpenChange={setShowMessageDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Message {selectedRequest.student.name}</DialogTitle>
              <DialogDescription>
                Send a message regarding the session on {formatDate(selectedRequest.date)} at {selectedRequest.time}.
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              <Textarea
                placeholder="Type your message here..."
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                className="min-h-[120px]"
              />
            </div>
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setShowMessageDialog(false)}>Cancel</Button>
              <Button onClick={handleSendMessage}>Send Message</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Reschedule Dialog */}
      {selectedRequest && (
        <Dialog open={showRescheduleDialog} onOpenChange={setShowRescheduleDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reschedule Session</DialogTitle>
              <DialogDescription>
                Reschedule your session with {selectedRequest.student.name} on {formatDate(selectedRequest.date)}.
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4 text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">
                Rescheduling functionality is coming soon.
              </p>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRescheduleDialog(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Cancel Dialog */}
      {selectedRequest && (
        <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Cancel Session</DialogTitle>
              <DialogDescription>
                Are you sure you want to cancel your session with {selectedRequest.student.name} on {formatDate(selectedRequest.date)}?
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason for cancellation
              </label>
              <Textarea
                placeholder="Please provide a reason for cancellation..."
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-sm text-gray-500 mt-2">
                The student will be notified of the cancellation and your reason.
              </p>
            </div>
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
                Keep Session
              </Button>
              <Button variant="destructive" onClick={handleCancelSession}>
                Cancel Session
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </TutorPageLayout>
  );
};

export default AcceptedRequests;

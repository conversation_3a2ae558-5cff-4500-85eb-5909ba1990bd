# Payment Integration Database Schema

This document outlines the database schema changes required for integrating Stripe payment processing into the subscription workflow system.

## 📋 Overview

The payment integration adds comprehensive payment tracking, subscription management, and billing capabilities to support:

- One-time product purchases
- Subscription billing (future enhancement)
- Payment method storage
- Invoice generation
- Revenue analytics
- Payment failure handling

## 🗃️ Database Schema Changes

### 1. Extended Tables

#### `subscription_workflows` (Extended)
New columns added:
- `stripe_payment_intent_id` - Links to Stripe Payment Intent
- `stripe_subscription_id` - For recurring subscriptions
- `stripe_customer_id` - Links to Stripe Customer
- `payment_status` - Current payment status
- `total_amount` - Final calculated amount
- `currency` - Payment currency (default: USD)
- `payment_method_type` - Type of payment method used
- `payment_completed_at` - Timestamp of successful payment

### 2. New Tables

#### `payments`
Tracks all payment attempts and their status:
- Links to workflows and students
- Stores Stripe payment identifiers
- Tracks payment status and metadata
- Records failure information
- Maintains payment timeline

#### `subscriptions`
Manages active subscriptions and access control:
- Links students to purchased products
- Tracks subscription lifecycle
- Manages access periods and expiration
- Supports different billing intervals

#### `invoices`
Stores billing history and invoice data:
- Links to subscriptions
- Tracks payment due dates
- Stores invoice URLs and metadata
- Maintains billing history

#### `payment_methods`
Stores customer payment methods:
- Links to Stripe payment methods
- Stores card information (last 4, brand, expiry)
- Manages default payment methods
- Supports multiple payment methods per customer

#### `payment_events`
Audit trail for payment-related events:
- Tracks all payment state changes
- Stores Stripe webhook events
- Maintains processing status
- Supports event replay and debugging

## 🚀 Installation Instructions

### Step 1: Run Migration Script
```sql
-- Execute the migration script in your Supabase SQL editor
\i payment_migration.sql
```

### Step 2: Apply Functions and Policies
```sql
-- Execute the functions and policies script
\i payment_functions.sql
```

### Step 3: Verify Installation
```sql
-- Check that all tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('payments', 'subscriptions', 'invoices', 'payment_methods', 'payment_events');

-- Verify new columns in subscription_workflows
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'subscription_workflows' 
AND column_name LIKE '%stripe%' OR column_name LIKE '%payment%';
```

## 🔧 Key Functions

### Payment Management
- `create_payment_record()` - Creates new payment record
- `update_payment_status()` - Updates payment status from webhooks
- `activate_subscription_for_workflow()` - Activates subscription after payment

### Subscription Management
- `has_active_subscription()` - Checks if user has active access
- `save_payment_method()` - Stores customer payment methods

### Analytics
- `get_student_payment_summary()` - Student payment overview
- `get_revenue_summary()` - Revenue analytics for date range

## 🔒 Security Features

### Row Level Security (RLS)
All payment tables have RLS enabled with policies:

- **Students**: Can only access their own payment data
- **Admins**: Can access all payment data for support
- **System**: Can insert/update for webhook processing

### Data Protection
- No sensitive payment data stored locally
- All card data handled by Stripe
- Payment methods store only safe metadata
- Audit trail for all payment events

## 📊 Helper Views

### `active_subscriptions_view`
Shows all active subscriptions with product and student details.

### `payment_history_view`
Complete payment history with workflow and product information.

### `revenue_analytics_view`
Daily revenue analytics with success/failure rates.

## 🔄 Workflow Integration

### Payment Flow
1. Student completes product selection (Step 1-3)
2. Payment intent created via Supabase Edge Function
3. Frontend processes payment with Stripe
4. Webhook updates payment status
5. Subscription activated on successful payment
6. Student gains access to purchased product

### Status Tracking
- `pending` - Payment intent created
- `processing` - Payment being processed
- `succeeded` - Payment completed successfully
- `failed` - Payment failed (retry possible)
- `canceled` - Payment canceled by user

## 🧪 Testing

### Test Data Setup
```sql
-- Create test payment record
SELECT create_payment_record(
    'workflow-uuid',
    'student-uuid', 
    'pi_test_123',
    299.99,
    'usd',
    'Test Complete Booster Purchase'
);

-- Simulate successful payment
SELECT update_payment_status(
    'pi_test_123',
    'succeeded',
    'ch_test_123',
    NULL,
    NULL,
    'https://stripe.com/receipt'
);
```

### Verification Queries
```sql
-- Check payment status
SELECT * FROM payments WHERE stripe_payment_intent_id = 'pi_test_123';

-- Check subscription activation
SELECT * FROM subscriptions WHERE workflow_id = 'workflow-uuid';

-- Check access permissions
SELECT has_active_subscription('student-uuid', 'product-uuid');
```

## 📈 Monitoring

### Key Metrics to Track
- Payment success rate
- Average order value
- Revenue by product type
- Failed payment recovery rate
- Subscription churn

### Useful Queries
```sql
-- Daily revenue summary
SELECT * FROM revenue_analytics_view WHERE payment_date >= CURRENT_DATE - INTERVAL '30 days';

-- Failed payments requiring attention
SELECT * FROM payments WHERE status = 'failed' AND created_at >= CURRENT_DATE - INTERVAL '7 days';

-- Active subscriptions by product
SELECT product_name, COUNT(*) as active_count 
FROM active_subscriptions_view 
GROUP BY product_name;
```

## 🚨 Troubleshooting

### Common Issues

1. **Payment stuck in pending**
   - Check webhook delivery in Stripe dashboard
   - Verify webhook endpoint is accessible
   - Check payment_events table for processing errors

2. **Subscription not activated**
   - Verify payment status is 'succeeded'
   - Check activate_subscription_for_workflow function logs
   - Ensure product_id exists in workflow

3. **Access denied after payment**
   - Check subscription status and expiry dates
   - Verify RLS policies are not blocking access
   - Check has_active_subscription function

### Debug Queries
```sql
-- Check payment processing pipeline
SELECT 
    p.stripe_payment_intent_id,
    p.status as payment_status,
    sw.payment_status as workflow_status,
    s.status as subscription_status
FROM payments p
JOIN subscription_workflows sw ON sw.id = p.workflow_id
LEFT JOIN subscriptions s ON s.workflow_id = p.workflow_id
WHERE p.student_id = 'student-uuid';
```

## 🔄 Next Steps

After applying the database schema:

1. **Set up Stripe account** and obtain API keys
2. **Configure environment variables** for Stripe integration
3. **Create Supabase Edge Functions** for payment processing
4. **Implement frontend payment components** using Stripe Elements
5. **Set up webhook endpoints** for payment status updates
6. **Add payment UI** to the subscription workflow
7. **Test payment flow** with Stripe test cards

## 📚 Related Documentation

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)
- [Payment Security Best Practices](https://stripe.com/docs/security)

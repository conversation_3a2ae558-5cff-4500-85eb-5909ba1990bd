/* Custom styles for react-image-crop in dark theme */
.ReactCrop {
  display: inline-block;
  position: relative;
  width: 100%;
  height: 100%;
  background: transparent;
  touch-action: none;
}

.ReactCrop__crop-selection {
  position: absolute;
  top: 0;
  left: 0;
  transform: translate3d(0, 0, 0);
  box-sizing: border-box;
  border: 2px solid rgba(255, 255, 255, 0.8);
  background: transparent;
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.5);
  touch-action: none;
}

.ReactCrop__crop-selection:focus {
  outline: none;
}

.ReactCrop__drag-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  touch-action: none;
}

.ReactCrop__drag-handle::after {
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
  content: '';
  cursor: inherit;
  border: 1px solid rgba(255, 255, 255, 0.7);
  border-radius: 1px;
}

.ReactCrop__drag-handle--n {
  top: -6px;
  left: 50%;
  margin-left: -6px;
  cursor: ns-resize;
}

.ReactCrop__drag-handle--e {
  top: 50%;
  right: -6px;
  margin-top: -6px;
  cursor: ew-resize;
}

.ReactCrop__drag-handle--s {
  bottom: -6px;
  left: 50%;
  margin-left: -6px;
  cursor: ns-resize;
}

.ReactCrop__drag-handle--w {
  top: 50%;
  left: -6px;
  margin-top: -6px;
  cursor: ew-resize;
}

.ReactCrop__drag-handle--ne {
  top: -6px;
  right: -6px;
  cursor: nesw-resize;
}

.ReactCrop__drag-handle--nw {
  top: -6px;
  left: -6px;
  cursor: nwse-resize;
}

.ReactCrop__drag-handle--se {
  bottom: -6px;
  right: -6px;
  cursor: nwse-resize;
}

.ReactCrop__drag-handle--sw {
  bottom: -6px;
  left: -6px;
  cursor: nesw-resize;
}

.ReactCrop__crop-selection {
  border: 2px solid #ffffff;
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.5);
}

.ReactCrop__crop-selection:focus {
  border-color: #ffffff;
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.5);
}

/* Custom styles for the crop modal */
.crop-modal-content {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Ensure scrollable content area */
.crop-modal-content .overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.crop-modal-content .overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.crop-modal-content .overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.crop-modal-content .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.crop-modal-content .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.crop-modal-content .ReactCrop__image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

/* Ensure the crop area is visible */
.ReactCrop__crop-selection {
  border: 2px solid #ffffff !important;
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.5) !important;
}

/* Style the drag handles for better visibility */
.ReactCrop__drag-handle {
  background: #ffffff !important;
  border: 2px solid #3b82f6 !important;
  width: 14px !important;
  height: 14px !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.ReactCrop__drag-handle::after {
  display: none;
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: transparent;
}

.slider::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: transparent;
}

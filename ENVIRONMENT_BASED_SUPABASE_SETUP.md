# Environment-Based Supabase Configuration Setup

## Overview

This setup allows you to use different Supabase configurations for development and production environments, solving the OAuth redirect issue where development was redirecting to production URLs.

## Current Setup

### 1. Environment Configuration Files

**Development (`.env.development`):**
- Uses `http://localhost:8080` as the base URL
- Currently uses the same Supabase project (will be updated)
- Enables debug logging and development features

**Production (`.env.production`):**
- Uses `https://rflearn.com` as the base URL
- Uses the production Supabase project
- Enables analytics and disables debug logging

### 2. Environment Configuration Helper

**File:** `src/config/environment.ts`
- Centralizes all environment-specific configuration
- Provides type-safe configuration objects
- Validates required environment variables
- Supports development, production, and staging environments

### 3. Updated Components

**Supabase Client (`src/lib/supabaseClient.ts`):**
- Uses environment configuration instead of direct env vars
- Validates configuration on startup
- Logs configuration in development mode

**OAuth Flow (`src/components/auth/SocialLoginButtons.tsx`):**
- Uses `config.oauth.redirectBaseUrl` instead of `window.location.origin`
- Ensures consistent redirect URLs across environments

## Next Steps

### Option 1: Update Current Supabase Project (Quick Fix)

**For immediate testing, update the current Supabase project:**

1. **Go to:** https://supabase.com/dashboard/project/yskbkvdoqnbtwtnvgsbz
2. **Navigate to:** Authentication → URL Configuration
3. **Update Site URL to:** `http://localhost:8080`
4. **Ensure Redirect URLs include:**
   ```
   http://localhost:8080/oauth-completion
   http://localhost:8080/onboard-student
   http://localhost:8080/onboard-tutor
   http://localhost:8080/auth/callback
   https://rflearn.com/oauth-completion
   https://rflearn.com/onboard-student
   https://rflearn.com/onboard-tutor
   https://rflearn.com/auth/callback
   ```

**Note:** You'll need to change the Site URL back to `https://rflearn.com` when deploying to production.

### Option 2: Create Separate Development Project (Recommended)

**For a permanent solution, create a dedicated development Supabase project:**

1. **Create New Supabase Project:**
   - Go to https://supabase.com/dashboard
   - Click "New Project"
   - Name: "RF Learn Development" or similar
   - Choose same organization and region

2. **Configure Development Project:**
   - **Site URL:** `http://localhost:8080`
   - **Redirect URLs:**
     ```
     http://localhost:8080/oauth-completion
     http://localhost:8080/onboard-student
     http://localhost:8080/onboard-tutor
     http://localhost:8080/auth/callback
     ```

3. **Copy Database Schema:**
   - Export schema from production project
   - Import into development project
   - Or use Supabase CLI to sync schemas

4. **Update Environment Variables:**
   ```bash
   # Update .env.development with new project credentials
   VITE_SUPABASE_URL=https://[new-project-id].supabase.co
   VITE_SUPABASE_ANON_KEY=[new-anon-key]
   ```

5. **Configure Google OAuth:**
   - Add development Supabase callback URL to Google Cloud Console:
     ```
     https://[new-project-id].supabase.co/auth/v1/callback
     ```

## Testing the Setup

### 1. Development Testing

```bash
# Start development server
npm run dev

# Check console logs for environment configuration
# Should show:
# 🔧 Environment Configuration: { environment: 'development', ... }
# 🔗 Supabase Client Configuration: { url: '...', environment: 'development', ... }
```

### 2. OAuth Flow Testing

1. **Go to registration page**
2. **Click "Sign up with Google"**
3. **Check console logs for:**
   ```
   === OAuth Setup Complete ===
   Environment: development
   OAuth redirect base URL: http://localhost:8080
   OAuth redirect URL: http://localhost:8080/oauth-completion?userType=...
   ```

4. **Verify redirect stays in localhost** (not going to rflearn.com)

### 3. Production Testing

```bash
# Build for production
npm run build

# Serve production build locally (optional)
npm run preview

# Deploy to production and test OAuth flow
```

## Configuration Reference

### Environment Variables

| Variable | Development | Production | Description |
|----------|-------------|------------|-------------|
| `VITE_ENVIRONMENT` | `development` | `production` | Environment identifier |
| `VITE_APP_URL` | `http://localhost:8080` | `https://rflearn.com` | Application base URL |
| `VITE_API_URL` | `http://localhost:8080/api` | `https://rflearn.com/api` | API base URL |
| `VITE_SUPABASE_URL` | Development project URL | Production project URL | Supabase project URL |
| `VITE_SUPABASE_ANON_KEY` | Development anon key | Production anon key | Supabase anonymous key |

### OAuth Redirect URLs

**Development:**
- Base: `http://localhost:8080`
- OAuth completion: `http://localhost:8080/oauth-completion`

**Production:**
- Base: `https://rflearn.com`
- OAuth completion: `https://rflearn.com/oauth-completion`

## Troubleshooting

### Issue: Still redirecting to production
**Solution:** Clear browser cache and check Supabase Site URL configuration

### Issue: Environment config not loading
**Solution:** Restart development server after changing environment files

### Issue: OAuth redirect_uri_mismatch
**Solution:** Ensure all redirect URLs are added to both Supabase and Google Cloud Console

### Issue: Database connection errors
**Solution:** Verify Supabase URL and anon key are correct for the environment

## Benefits of This Setup

1. **Environment Isolation:** Development and production use separate configurations
2. **No Manual Switching:** Automatic environment detection
3. **Type Safety:** TypeScript interfaces for configuration
4. **Validation:** Startup validation of required environment variables
5. **Debugging:** Enhanced logging in development mode
6. **Scalability:** Easy to add staging or other environments

-- Fix subscriptions table RLS policies to resolve infinite recursion error
-- This addresses the "infinite recursion detected in policy for relation 'profiles'" error

-- =====================================================
-- STEP 1: DISABLE RLS TEMPORARILY TO CLEAR CONFLICTS
-- =====================================================

-- Temporarily disable RLS on subscriptions to clear any conflicts
ALTER TABLE subscriptions DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 2: DROP ALL EXISTING POLICIES ON SUBSCRIPTIONS
-- =====================================================

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Students can view their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Students can insert their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "System can update subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Admins can manage subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Service role can manage subscriptions" ON subscriptions;

-- =====================================================
-- STEP 3: CREATE SIMPLE, NON-RECURSIVE POLICIES
-- =====================================================

-- Re-enable RLS
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Policy 1: Students can view their own subscriptions
-- This policy avoids recursion by directly checking auth.uid() without joining profiles
CREATE POLICY "Students can view their own subscriptions" ON subscriptions
    FOR SELECT USING (
        student_id = auth.uid()
    );

-- Policy 2: Admins can view all subscriptions
-- This policy checks user_type directly from profiles but avoids recursion
CREATE POLICY "Admins can view all subscriptions" ON subscriptions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'admin'
        )
    );

-- Policy 3: Students can insert their own subscriptions
CREATE POLICY "Students can insert their own subscriptions" ON subscriptions
    FOR INSERT WITH CHECK (
        student_id = auth.uid()
    );

-- Policy 4: Students and admins can update subscriptions
CREATE POLICY "Students and admins can update subscriptions" ON subscriptions
    FOR UPDATE USING (
        student_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'admin'
        )
    );

-- Policy 5: Service role can manage all subscriptions (for system operations)
CREATE POLICY "Service role can manage subscriptions" ON subscriptions
    FOR ALL USING (
        auth.role() = 'service_role'
    );

-- =====================================================
-- STEP 4: SIMPLE SOLUTION - JUST USE PROPER RLS POLICIES
-- =====================================================

-- No additional functions needed! The simple RLS policies above are sufficient.
-- The get_student_complete_profile function can access subscriptions directly
-- because it runs as SECURITY DEFINER and the policies are non-recursive.

-- =====================================================
-- STEP 5: KEEP THE EXISTING FUNCTION AS-IS
-- =====================================================

-- No need to modify get_student_complete_profile function!
-- The existing function should work fine with the corrected RLS policies.
-- The function is already SECURITY DEFINER, so it can access subscriptions
-- directly without any additional complexity.

-- =====================================================
-- STEP 6: GRANT PERMISSIONS (IF NEEDED)
-- =====================================================

-- The existing get_student_complete_profile function should already have
-- the correct permissions. No additional grants needed.

-- =====================================================
-- STEP 7: VERIFICATION
-- =====================================================

-- Check that RLS is enabled and policies exist
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE tablename = 'subscriptions';

-- List all policies on subscriptions table
SELECT
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE tablename = 'subscriptions'
ORDER BY policyname;

-- =====================================================
-- TESTING FOR INFINITE RECURSION
-- =====================================================

-- Method 1: Direct function test with timeout
-- This will either return results or timeout/error if there's infinite recursion
SELECT 'Testing function for infinite recursion...' as test_status;

-- Uncomment to test (be ready to cancel if it hangs):
-- SELECT * FROM get_student_complete_profile(auth.uid()) LIMIT 1;

-- Method 2: Test with a known student ID (replace with actual student ID)
-- SELECT * FROM get_student_complete_profile('your-student-uuid-here') LIMIT 1;

-- Method 3: Test RLS policies directly on subscriptions table
SELECT 'Testing direct subscriptions access...' as test_status;
-- SELECT COUNT(*) FROM subscriptions; -- Should work without recursion

-- Method 4: Check for recursion errors in PostgreSQL logs
-- Look for errors like: "infinite recursion detected in policy for relation"

-- Method 5: Test with EXPLAIN to see query plan
-- EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM get_student_complete_profile(auth.uid()) LIMIT 1;

-- =====================================================
-- SIGNS OF INFINITE RECURSION:
-- =====================================================
/*
1. Query hangs indefinitely
2. Error message: "infinite recursion detected in policy for relation 'table_name'"
3. High CPU usage in PostgreSQL process
4. Query never returns results
5. PostgreSQL logs show recursion errors

HOW TO TEST SAFELY:
1. Set statement_timeout: SET statement_timeout = '10s';
2. Run the query in a separate session so you can cancel it
3. Monitor PostgreSQL logs for recursion errors
4. Use EXPLAIN to analyze query plan before executing

IF RECURSION OCCURS:
- Press Ctrl+C to cancel the query
- Check PostgreSQL error logs
- Review and simplify RLS policies
- Ensure no circular dependencies in policy conditions
*/

-- =====================================================
-- STEP 8: SUMMARY
-- =====================================================

/*
SIMPLE SOLUTION SUMMARY:

The infinite recursion error was caused by complex RLS policies that created
circular dependencies. The fix is simple:

1. Use simple, non-recursive RLS policies on subscriptions table
2. Keep the existing get_student_complete_profile function unchanged
3. The SECURITY DEFINER function can access subscriptions directly

No additional helper functions or complexity needed!

The key insight: RLS policies should be simple and avoid joins that can
create circular dependencies, especially when accessed by SECURITY DEFINER functions.
*/

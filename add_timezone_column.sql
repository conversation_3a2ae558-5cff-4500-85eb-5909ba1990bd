-- Add timezone column to profiles table
ALTER TABLE public.profiles
ADD COLUMN timezone TEXT NULL;

-- Update existing records with a default timezone (optional)
-- Uncomment and modify the following line if you want to set a default timezone for existing records
-- UPDATE public.profiles SET timezone = 'UTC' WHERE timezone IS NULL;

-- Add a comment to the column for documentation
COMMENT ON COLUMN public.profiles.timezone IS 'User timezone information in IANA format (e.g., America/New_York, Europe/London)';

import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  User,
  Building2,
  GraduationCap,
  Award,
  Target,
  Users,
  Heart,
  Lightbulb,
  TrendingUp,
  Globe,
  Mail,
  Bell,
  Star,
  ArrowRight,
  Sparkles,
  BookOpen,
  Briefcase,
  MapPin,
  Calendar,
  CheckCircle,
  Eye,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Input } from '@/components/ui/Input';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

const About = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleNotifyMe = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      console.log('Subscribed email:', email);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main>
        {/* Hero Section */}
        <section className="relative py-20 px-4 text-center overflow-hidden mt-16 md:mt-20 min-h-screen">
        {/* Content Container */}
        <div className="max-w-6xl mx-auto relative z-10 mb-8">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Heart className="w-4 h-4" />
            Our Story
          </div>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            About <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">rfLearn</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Revolutionizing education through personalized learning experiences, expert tutoring,
            and cutting-edge technology. Founded with a vision to make quality education accessible to everyone.
          </p>
        </div>

        {/* Flowing River Animation */}
        <div className="absolute top-1/2 left-0 w-full h-[45%] transform -translate-y-1/2 overflow-hidden">
          {/* River Container */}
          <div className="water-surface relative w-full h-full bg-gradient-to-b from-cyan-200 via-blue-400 to-blue-800">

            {/* Water Surface Waves */}
            <div className="absolute top-0 w-full h-4 bg-gradient-to-b from-cyan-100 to-transparent opacity-40">
              <div className="wave-1 absolute top-0 w-full h-full"></div>
              <div className="wave-2 absolute top-0 w-full h-full"></div>
              <div className="wave-3 absolute top-0 w-full h-full"></div>
              <div className="wave-4 absolute top-0 w-full h-full"></div>
            </div>

            {/* Flowing Water Effect */}
            <div className="absolute inset-0 opacity-30">
              <div className="flowing-water-1 absolute w-full h-full bg-gradient-to-r from-transparent via-white to-transparent"></div>
              <div className="flowing-water-2 absolute w-full h-full bg-gradient-to-r from-transparent via-blue-300 to-transparent"></div>
              <div className="flowing-water-3 absolute w-full h-full bg-gradient-to-r from-transparent via-white to-transparent"></div>
            </div>

            {/* River Ripples */}
            <div className="absolute inset-0">
              <div className="ripple-1 absolute w-16 h-16 border-2 border-white opacity-30 rounded-full"></div>
              <div className="ripple-2 absolute w-12 h-12 border border-white opacity-25 rounded-full"></div>
              <div className="ripple-3 absolute w-20 h-20 border-2 border-blue-100 opacity-20 rounded-full"></div>
              <div className="ripple-4 absolute w-14 h-14 border border-blue-200 opacity-35 rounded-full"></div>
              <div className="ripple-5 absolute w-18 h-18 border border-white opacity-15 rounded-full"></div>
              <div className="ripple-6 absolute w-10 h-10 border border-blue-100 opacity-40 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* CSS Animations */}
        <style>{`
          @keyframes wave1 {
            0% {
              transform: translateX(-100%);
              background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            }
            50% {
              transform: translateX(0%);
              background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
            }
            100% {
              transform: translateX(100%);
              background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            }
          }

          @keyframes wave2 {
            0% {
              transform: translateX(100%) skewX(-5deg);
              background: linear-gradient(90deg, transparent, rgba(200,230,255,0.5), transparent);
            }
            50% {
              transform: translateX(0%) skewX(0deg);
              background: linear-gradient(90deg, transparent, rgba(200,230,255,0.7), transparent);
            }
            100% {
              transform: translateX(-100%) skewX(5deg);
              background: linear-gradient(90deg, transparent, rgba(200,230,255,0.5), transparent);
            }
          }

          @keyframes wave3 {
            0% {
              transform: translateX(-50%) rotate(1deg);
              background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            }
            50% {
              transform: translateX(50%) rotate(-1deg);
              background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
            }
            100% {
              transform: translateX(150%) rotate(1deg);
              background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            }
          }

          @keyframes wave4 {
            0% {
              transform: translateX(150%);
              background: linear-gradient(90deg, transparent, rgba(180,220,255,0.4), transparent);
            }
            50% {
              transform: translateX(25%);
              background: linear-gradient(90deg, transparent, rgba(180,220,255,0.6), transparent);
            }
            100% {
              transform: translateX(-100%);
              background: linear-gradient(90deg, transparent, rgba(180,220,255,0.4), transparent);
            }
          }

          @keyframes waterSurface {
            0% {
              clip-path: polygon(0% 0%, 10% 1%, 20% 0.5%, 30% 1.5%, 40% 0.5%, 50% 2%, 60% 1%, 70% 1.5%, 80% 0.5%, 90% 1%, 100% 0%, 100% 100%, 0% 100%);
            }
            25% {
              clip-path: polygon(0% 0.5%, 10% 1.5%, 20% 0%, 30% 1%, 40% 2%, 50% 0.5%, 60% 1.5%, 70% 0%, 80% 1%, 90% 2%, 100% 0.5%, 100% 100%, 0% 100%);
            }
            50% {
              clip-path: polygon(0% 1%, 10% 0%, 20% 1.5%, 30% 0.5%, 40% 1%, 50% 0%, 60% 2%, 70% 1%, 80% 1.5%, 90% 0.5%, 100% 1%, 100% 100%, 0% 100%);
            }
            75% {
              clip-path: polygon(0% 0.5%, 10% 2%, 20% 1%, 30% 0%, 40% 1.5%, 50% 1%, 60% 0.5%, 70% 2%, 80% 0%, 90% 1.5%, 100% 0.5%, 100% 100%, 0% 100%);
            }
            100% {
              clip-path: polygon(0% 0%, 10% 1%, 20% 0.5%, 30% 1.5%, 40% 0.5%, 50% 2%, 60% 1%, 70% 1.5%, 80% 0.5%, 90% 1%, 100% 0%, 100% 100%, 0% 100%);
            }
          }

          @keyframes flowRight {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }

          @keyframes flowLeft {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
          }

          @keyframes ripple {
            0% { transform: scale(0.3); opacity: 0; }
            30% { opacity: 1; }
            100% { transform: scale(1.5); opacity: 0; }
          }

          @keyframes rippleFloat {
            0% { transform: scale(0.5) translateY(0px); opacity: 0; }
            50% { opacity: 1; transform: scale(1) translateY(-5px); }
            100% { transform: scale(1.8) translateY(-10px); opacity: 0; }
          }

          .wave-1 {
            animation: wave1 4s ease-in-out infinite;
            animation-delay: 0s;
          }

          .wave-2 {
            animation: wave2 5.5s ease-in-out infinite;
            animation-delay: -1s;
          }

          .wave-3 {
            animation: wave3 3.5s ease-in-out infinite;
            animation-delay: -2s;
          }

          .wave-4 {
            animation: wave4 6s ease-in-out infinite;
            animation-delay: -3s;
          }

          .water-surface {
            animation: waterSurface 3s ease-in-out infinite;
          }

          .flowing-water-1 {
            animation: flowRight 8s linear infinite;
            animation-delay: 0s;
          }

          .flowing-water-2 {
            animation: flowLeft 6s linear infinite;
            animation-delay: -2s;
          }

          .flowing-water-3 {
            animation: flowRight 10s linear infinite;
            animation-delay: -4s;
          }

          .ripple-1 {
            top: 25%;
            left: 15%;
            animation: ripple 3s ease-out infinite;
            animation-delay: 0s;
          }

          .ripple-2 {
            top: 65%;
            left: 75%;
            animation: rippleFloat 4s ease-out infinite;
            animation-delay: -1.5s;
          }

          .ripple-3 {
            top: 45%;
            left: 35%;
            animation: ripple 5s ease-out infinite;
            animation-delay: -2.5s;
          }

          .ripple-4 {
            top: 75%;
            left: 85%;
            animation: rippleFloat 3.5s ease-out infinite;
            animation-delay: -1s;
          }

          .ripple-5 {
            top: 30%;
            left: 60%;
            animation: ripple 4.5s ease-out infinite;
            animation-delay: -3s;
          }

          .ripple-6 {
            top: 55%;
            left: 20%;
            animation: rippleFloat 3.8s ease-out infinite;
            animation-delay: -0.5s;
          }
        `}</style>
      </section>

      {/* Founder Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
              <User className="w-4 h-4" />
              Meet Our Founder
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Visionary Leadership</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Led by industry expertise and a passion for transforming education
            </p>
          </div>

          {/* Founder Profile Card */}
          <div className="max-w-4xl mx-auto">
            <Card className="overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-white to-blue-50">
              <div className="md:flex">
                {/* Profile Image Section */}
                <div className="md:w-1/3 bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center p-8">
                  <div className="text-center">
                    <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
                      <User className="w-16 h-16 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">Mr. Khan</h3>
                    <p className="text-blue-100 font-medium">Founder & CEO</p>
                    <Badge className="mt-3 bg-white/20 text-white border-white/30">
                      Math Researcher
                    </Badge>
                  </div>
                </div>

                {/* Content Section */}
                <div className="md:w-2/3 p-8">
                  <div className="mb-6">
                    <h4 className="text-xl font-semibold text-gray-900 mb-3">Professional Background</h4>
                    <p className="text-gray-600 leading-relaxed mb-4">
                      Mr. Khan brings extensive experience as a Math Researcher, having worked at some of the world's
                      most innovative technology companies. His expertise spans across mathematical research,
                      educational technology, and strategic planning.
                    </p>
                  </div>

                  {/* Experience Highlights */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">Meta</p>
                        <p className="text-sm text-gray-600">Math Researcher</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">Apple</p>
                        <p className="text-sm text-gray-600">Math Researcher</p>
                      </div>
                    </div>
                  </div>

                  {/* Key Achievements */}
                  <div className="space-y-3">
                    <h5 className="font-semibold text-gray-900">Key Expertise:</h5>
                    <div className="flex flex-wrap gap-2">
                      {[
                        "Mathematical Research",
                        "Educational Technology",
                        "Research & Development",
                        "Strategic Planning",
                        "Academic Innovation"
                      ].map((skill, index) => (
                        <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-700">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Vision */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Eye className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">Our Vision</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 leading-relaxed">
                  To create a world where quality education is accessible to everyone, regardless of location or background.
                  We envision a future where personalized learning experiences empower students to achieve their full potential.
                </p>
              </CardContent>
            </Card>

            {/* Mission */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Target className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-2xl">Our Mission</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 leading-relaxed">
                  To revolutionize education by connecting students with expert tutors through innovative technology,
                  providing personalized learning paths, and fostering academic excellence in a supportive environment.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p className="text-xl text-gray-600">The principles that guide everything we do</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <GraduationCap className="w-6 h-6" />,
                title: "Excellence",
                description: "Committed to delivering the highest quality education and learning experiences",
                color: "from-blue-500 to-blue-600"
              },
              {
                icon: <Users className="w-6 h-6" />,
                title: "Accessibility",
                description: "Making quality education available to students everywhere, breaking down barriers",
                color: "from-green-500 to-green-600"
              },
              {
                icon: <Lightbulb className="w-6 h-6" />,
                title: "Innovation",
                description: "Continuously evolving our platform with cutting-edge technology and methodologies",
                color: "from-purple-500 to-purple-600"
              },
              {
                icon: <Heart className="w-6 h-6" />,
                title: "Empowerment",
                description: "Empowering both students and tutors to achieve their goals and make a difference",
                color: "from-orange-500 to-orange-600"
              }
            ].map((value, index) => (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <CardHeader>
                  <div className={`w-12 h-12 bg-gradient-to-r ${value.color} rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                    <div className="text-white">
                      {value.icon}
                    </div>
                  </div>
                  <CardTitle className="text-lg">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Coming Soon Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-4xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-orange-100 text-orange-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            More Coming Soon
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Story Continues</h2>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            We're working on sharing more details about our journey, team members, company milestones,
            and the exciting future we're building together. Stay tuned for updates!
          </p>

          {/* Notify Me Form */}
          <div className="max-w-md mx-auto mb-8">
            {!isSubscribed ? (
              <form onSubmit={handleNotifyMe} className="flex gap-2">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="flex-1"
                />
                <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <Bell className="w-4 h-4 mr-2" />
                  Notify Me
                </Button>
              </form>
            ) : (
              <div className="bg-green-100 text-green-800 px-6 py-3 rounded-lg flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                <span>Thanks! We'll keep you updated on our journey.</span>
              </div>
            )}
            <p className="text-sm text-gray-500 mt-2">
              Be the first to know about company updates and milestones
            </p>
          </div>

          {/* Preview of upcoming content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                icon: <Users className="w-5 h-5" />,
                title: "Team Profiles",
                description: "Meet the talented individuals behind RFLearn"
              },
              {
                icon: <Calendar className="w-5 h-5" />,
                title: "Company Timeline",
                description: "Key milestones and achievements in our journey"
              },
              {
                icon: <Award className="w-5 h-5" />,
                title: "Recognition",
                description: "Awards and recognition we've received"
              }
            ].map((item, index) => (
              <Card key={index} className="border-dashed border-2 border-gray-300 bg-white/50">
                <CardHeader className="text-center">
                  <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center mx-auto mb-2">
                    {item.icon}
                  </div>
                  <CardTitle className="text-sm text-gray-600">{item.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-xs text-gray-500">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6">Join Our Mission</h2>
          <p className="text-xl mb-8 opacity-90">
            Be part of the educational revolution. Whether you're a student seeking knowledge or a tutor sharing expertise,
            we welcome you to our community.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
              <Link to="/register" className="flex items-center gap-2">
                <GraduationCap className="w-5 h-5" />
                Start Learning
              </Link>
            </Button>
            <Button size="lg" className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 transition-colors">
              <Link to="/become-tutor" className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Become a Tutor
              </Link>
            </Button>
          </div>
        </div>
      </section>
      </main>
      <Footer />
    </div>
  );
};

export default About;

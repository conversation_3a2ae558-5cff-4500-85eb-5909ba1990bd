-- Test script to detect infinite recursion in PostgreSQL functions and RLS policies
-- Run this script to safely test for infinite recursion issues

-- =====================================================
-- SETUP: CONFIGURE SAFE TESTING ENVIRONMENT
-- =====================================================

-- Set a timeout to prevent hanging queries (10 seconds)
SET statement_timeout = '10s';

-- Enable detailed logging for debugging
SET log_statement = 'all';
SET log_duration = on;

-- =====================================================
-- TEST 1: CHECK CURRENT RLS POLICIES
-- =====================================================

SELECT '=== CHECKING RLS POLICIES ===' as test_section;

-- Check if RLS is enabled on subscriptions table
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE 
        WHEN rowsecurity THEN 'RLS is ENABLED'
        ELSE 'RLS is DISABLED'
    END as status
FROM pg_tables 
WHERE tablename = 'subscriptions';

-- List all policies on subscriptions table
SELECT 
    '=== SUBSCRIPTIONS TABLE POLICIES ===' as info,
    policyname,
    permissive,
    roles,
    cmd as command,
    qual as policy_condition
FROM pg_policies 
WHERE tablename = 'subscriptions'
ORDER BY policyname;

-- =====================================================
-- TEST 2: BASIC TABLE ACCESS TEST
-- =====================================================

SELECT '=== TESTING BASIC TABLE ACCESS ===' as test_section;

-- Test 1: Simple count query on subscriptions (should not recurse)
BEGIN;
    SELECT 'Testing basic subscriptions count...' as test_name;
    -- Uncomment the next line to test:
    -- SELECT COUNT(*) as subscription_count FROM subscriptions;
ROLLBACK;

-- Test 2: Simple profiles access
BEGIN;
    SELECT 'Testing basic profiles access...' as test_name;
    -- Uncomment the next line to test:
    -- SELECT COUNT(*) as profile_count FROM profiles;
ROLLBACK;

-- =====================================================
-- TEST 3: FUNCTION EXISTENCE AND SIGNATURE CHECK
-- =====================================================

SELECT '=== CHECKING FUNCTION EXISTENCE ===' as test_section;

-- Check if the function exists and its properties
SELECT 
    routine_name,
    routine_type,
    security_type,
    'Function exists' as status
FROM information_schema.routines 
WHERE routine_name = 'get_student_complete_profile'
AND routine_schema = 'public';

-- =====================================================
-- TEST 4: SAFE FUNCTION TESTING WITH TIMEOUT
-- =====================================================

SELECT '=== TESTING FUNCTION FOR INFINITE RECURSION ===' as test_section;

-- Test with a transaction that can be rolled back
BEGIN;
    SELECT 'Starting function test with timeout...' as test_status;
    
    -- UNCOMMENT ONE OF THESE LINES TO TEST:
    
    -- Option 1: Test with current user (if you're logged in)
    -- SELECT * FROM get_student_complete_profile(auth.uid()) LIMIT 1;
    
    -- Option 2: Test with a specific student ID (replace with actual ID)
    -- SELECT * FROM get_student_complete_profile('your-student-uuid-here') LIMIT 1;
    
    -- Option 3: Test with a non-existent ID (should return empty, not recurse)
    -- SELECT * FROM get_student_complete_profile('00000000-0000-0000-0000-000000000000') LIMIT 1;
    
    SELECT 'Function test completed without recursion!' as result;
ROLLBACK;

-- =====================================================
-- TEST 5: EXPLAIN PLAN ANALYSIS
-- =====================================================

SELECT '=== ANALYZING QUERY EXECUTION PLAN ===' as test_section;

-- Use EXPLAIN to see the query plan without executing
-- UNCOMMENT TO TEST:
-- EXPLAIN (ANALYZE false, BUFFERS false, VERBOSE true) 
-- SELECT * FROM get_student_complete_profile(auth.uid()) LIMIT 1;

-- =====================================================
-- TEST 6: STEP-BY-STEP COMPONENT TESTING
-- =====================================================

SELECT '=== TESTING INDIVIDUAL COMPONENTS ===' as test_section;

-- Test 1: Profiles table access
BEGIN;
    SELECT 'Testing profiles table...' as test_name;
    -- SELECT id, first_name, user_type FROM profiles WHERE user_type = 'student' LIMIT 1;
ROLLBACK;

-- Test 2: Students table access
BEGIN;
    SELECT 'Testing students table...' as test_name;
    -- SELECT id, education_level FROM students LIMIT 1;
ROLLBACK;

-- Test 3: Subscriptions table with specific conditions
BEGIN;
    SELECT 'Testing subscriptions with conditions...' as test_name;
    -- SELECT id, student_id, status FROM subscriptions WHERE status = 'active' LIMIT 1;
ROLLBACK;

-- =====================================================
-- TEST 7: MONITOR FOR SPECIFIC ERROR PATTERNS
-- =====================================================

SELECT '=== MONITORING FOR RECURSION ERRORS ===' as test_section;

-- Check PostgreSQL logs for recursion errors
-- (This query checks the current session for any logged errors)
SELECT 
    'Check PostgreSQL logs for these error patterns:' as instruction,
    'ERROR: infinite recursion detected in policy for relation' as error_pattern_1,
    'ERROR: stack depth limit exceeded' as error_pattern_2,
    'ERROR: maximum recursion depth exceeded' as error_pattern_3;

-- =====================================================
-- INSTRUCTIONS FOR MANUAL TESTING
-- =====================================================

SELECT '=== MANUAL TESTING INSTRUCTIONS ===' as section;

/*
TO TEST FOR INFINITE RECURSION:

1. BEFORE TESTING:
   - Set statement_timeout: SET statement_timeout = '10s';
   - Open a separate database session for monitoring
   - Have Ctrl+C ready to cancel queries

2. UNCOMMENT ONE TEST AT A TIME:
   - Start with basic table access tests
   - Then try the function with a non-existent ID
   - Finally test with real data

3. SIGNS OF INFINITE RECURSION:
   - Query hangs for more than a few seconds
   - Error: "infinite recursion detected in policy for relation"
   - High CPU usage in PostgreSQL process
   - Query never returns results

4. IF RECURSION OCCURS:
   - Press Ctrl+C immediately to cancel
   - Check PostgreSQL error logs
   - Review RLS policies for circular dependencies
   - Simplify policies to avoid complex joins

5. SAFE TESTING APPROACH:
   - Always use transactions (BEGIN/ROLLBACK)
   - Test with EXPLAIN first (doesn't execute)
   - Use timeouts
   - Test components individually

6. MONITORING:
   - Watch PostgreSQL logs: tail -f /var/log/postgresql/postgresql.log
   - Monitor CPU usage: top or htop
   - Use pg_stat_activity to see running queries
*/

-- =====================================================
-- RESET SETTINGS
-- =====================================================

-- Reset timeout to default
RESET statement_timeout;
RESET log_statement;
RESET log_duration;

SELECT 'Testing script completed. Review results above.' as final_status;

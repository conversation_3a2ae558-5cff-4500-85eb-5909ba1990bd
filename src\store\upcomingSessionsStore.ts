import { create } from "zustand";

// Define the session interface
export interface SessionStudent {
  name: string;
  email: string;
  country: string;
}

export interface SessionDateTime {
  date: string;
  time: string;
  timestamp: Date;
}

export interface Session {
  id: string;
  externalId: string;
  student: SessionStudent;
  topic: string;
  subtopic: string;
  sessionType: string;
  dateTime: SessionDateTime;
  status: string;
  actions: string[];
}

// Define the store interface
interface UpcomingSessionsState {
  // Data
  sessions: Session[];
  filteredSessions: Session[];
  
  // UI State
  searchTerm: string;
  visibleColumns: {
    id: boolean;
    externalId: boolean;
    student: boolean;
    country: boolean;
    topic: boolean;
    subtopic: boolean;
    sessionType: boolean;
    dateTime: boolean;
    status: boolean;
    actions: boolean;
  };
  isCalendarView: boolean;
  isReminderModalOpen: boolean;
  selectedSession: Session | null;
  
  // Actions
  setSearchTerm: (term: string) => void;
  setVisibleColumns: (columns: Partial<UpcomingSessionsState['visibleColumns']>) => void;
  setIsCalendarView: (isCalendarView: boolean) => void;
  setIsReminderModalOpen: (isOpen: boolean) => void;
  setSelectedSession: (session: Session | null) => void;
  filterSessions: () => void;
}

// Sample data
const upcomingSessionsData: Session[] = [
  {
    id: "S-1092",
    externalId: "EXT-1092",
    student: {
      name: "Emma Johnson",
      email: "<EMAIL>",
      country: "United States",
    },
    topic: "Mathematics",
    subtopic: "Calculus: Integrals Techniques",
    sessionType: "One-on-One",
    dateTime: {
      date: "Today",
      time: "2:00 PM - 3:30 PM",
      timestamp: new Date(new Date().setHours(14, 0, 0, 0)),
    },
    status: "Scheduled",
    actions: ["Reschedule", "Join", "Send Reminder"],
  },
  {
    id: "S-1093",
    externalId: "EXT-1093",
    student: {
      name: "James Wilson",
      email: "<EMAIL>",
      country: "United Kingdom",
    },
    topic: "Physics",
    subtopic: "Electromagnetism: Theory",
    sessionType: "One-on-One",
    dateTime: {
      date: "Today",
      time: "5:30 PM - 6:30 PM",
      timestamp: new Date(new Date().setHours(17, 30, 0, 0)),
    },
    status: "Scheduled",
    actions: ["Reschedule", "Join", "Send Reminder", "Cancel Session"],
  },
  {
    id: "S-1095",
    externalId: "EXT-1095",
    student: {
      name: "Study Group",
      email: "<EMAIL>",
      country: "Multiple",
    },
    topic: "Computer Science",
    subtopic: "Data Structures & Algorithms",
    sessionType: "Group",
    dateTime: {
      date: "Tomorrow",
      time: "10:00 AM - 12:00 PM",
      timestamp: new Date(new Date().setDate(new Date().getDate() + 1)),
    },
    status: "Pending Confirmations",
    actions: ["Reschedule", "Join"],
  },
  {
    id: "S-1098",
    externalId: "EXT-1098",
    student: {
      name: "Sophia Chen",
      email: "<EMAIL>",
      country: "Canada",
    },
    topic: "Chemistry",
    subtopic: "Organic Chemistry",
    sessionType: "One-on-One",
    dateTime: {
      date: "Dec 15, 2023",
      time: "3:00 PM - 4:00 PM",
      timestamp: new Date("2023-12-15T15:00:00"),
    },
    status: "Scheduled",
    actions: ["Reschedule", "Join"],
  },
];

// Create the store
export const useUpcomingSessionsStore = create<UpcomingSessionsState>((set, get) => ({
  // Initial data
  sessions: upcomingSessionsData,
  filteredSessions: upcomingSessionsData,
  
  // Initial UI state
  searchTerm: "",
  visibleColumns: {
    id: true,
    externalId: false,
    student: true,
    country: true,
    topic: true,
    subtopic: false,
    sessionType: true,
    dateTime: true,
    status: true,
    actions: true,
  },
  isCalendarView: false,
  isReminderModalOpen: false,
  selectedSession: null,
  
  // Actions
  setSearchTerm: (term) => {
    set({ searchTerm: term });
    get().filterSessions();
  },
  
  setVisibleColumns: (columns) => {
    set({ visibleColumns: { ...get().visibleColumns, ...columns } });
  },
  
  setIsCalendarView: (isCalendarView) => {
    set({ isCalendarView });
  },
  
  setIsReminderModalOpen: (isOpen) => {
    set({ isReminderModalOpen: isOpen });
  },
  
  setSelectedSession: (session) => {
    set({ selectedSession: session });
  },
  
  filterSessions: () => {
    const { sessions, searchTerm } = get();
    const searchLower = searchTerm.toLowerCase();
    
    const filtered = sessions.filter((session) => {
      return (
        session.id.toLowerCase().includes(searchLower) ||
        session.student.name.toLowerCase().includes(searchLower) ||
        session.topic.toLowerCase().includes(searchLower) ||
        session.subtopic.toLowerCase().includes(searchLower) ||
        session.sessionType.toLowerCase().includes(searchLower) ||
        session.status.toLowerCase().includes(searchLower)
      );
    });
    
    set({ filteredSessions: filtered });
  },
}));

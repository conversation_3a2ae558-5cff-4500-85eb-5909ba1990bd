import { create } from "zustand";

interface RegisterState {
  formData: {
    email: string;
    password: string;
  };
  setFormData: (data: Partial<RegisterState["formData"]>) => void;
  userType: string;
  setUserType: (type: string) => void;
  acceptTerms: boolean;
  setAcceptTerms: (accepted: boolean) => void;
  formErrors: {
    email?: string;
    password?: string;
    userType?: string;
    acceptTerms?: string;
  };
  setFormErrors: (errors: Partial<RegisterState["formErrors"]>) => void;
  clearFormErrors: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  registrationError: string | null;
  setRegistrationError: (error: string | null) => void;
  registeredEmail: string;
  setRegisteredEmail: (email: string) => void;
  isCheckingStatus: boolean;
  setIsCheckingStatus: (checking: boolean) => void;
  isRegistered: boolean;
  setIsRegistered: (registered: boolean) => void;
  existingUserType: string | undefined;
  setExistingUserType: (type: string | undefined) => void;
}

export const useRegisterStore = create<RegisterState>((set) => ({
  formData: {
    email: "",
    password: "",
  },
  setFormData: (data) =>
    set((state) => ({ formData: { ...state.formData, ...data } })),
  userType: "student",
  setUserType: (type) => set({ userType: type }),
  acceptTerms: false,
  setAcceptTerms: (accepted) => set({ acceptTerms: accepted }),
  formErrors: {},
  setFormErrors: (errors) =>
    set((state) => ({ formErrors: { ...state.formErrors, ...errors } })),
  clearFormErrors: () => set({ formErrors: {} }),
  isLoading: false,
  setIsLoading: (loading) => set({ isLoading: loading }),
  registrationError: null,
  setRegistrationError: (error) => set({ registrationError: error }),
  registeredEmail: "",
  setRegisteredEmail: (email) => set({ registeredEmail: email }),
  isCheckingStatus: false,
  setIsCheckingStatus: (checking) => set({ isCheckingStatus: checking }),
  isRegistered: false,
  setIsRegistered: (registered) => set({ isRegistered: registered }),
  existingUserType: undefined,
  setExistingUserType: (type) => set({ existingUserType: type }),
}));

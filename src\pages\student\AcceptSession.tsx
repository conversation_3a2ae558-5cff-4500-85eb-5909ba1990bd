import React, { useEffect } from "react";
import { useProfileData } from "@/hooks/useProfileData";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card, CardContent } from "@/components/ui/Card";
import { Input } from "@/components/ui/Input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { Textarea } from "@/components/ui/TextArea";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/Avatar";
import {
  Search,
  Clock,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star,
  DollarSign,
  User,
  BookOpen,
} from "lucide-react";
import { useStudentSessionRequestsStore, StudentSessionRequest } from "@/store/studentSessionRequestsStore";
import { useSessionRequestCardStore } from "@/store/sessionRequestCardStore";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";

// Request Card Component
const SessionRequestCard: React.FC<{ request: StudentSessionRequest }> = ({ request }) => {

  const {
    showAcceptDialog,
    showDeclineDialog,
    showProfileDialog,
    message,
    selectedRequestId,
    setShowAcceptDialog,
    setShowDeclineDialog,
    setShowProfileDialog,
    setMessage
  } = useSessionRequestCardStore();

  const { acceptRequest, declineRequest } = useStudentSessionRequestsStore();

  // Check if this card's request is the selected one
  const isSelected = selectedRequestId === request.id;

  const getUrgencyBadge = (urgency: string) => {
    switch (urgency) {
      case "high":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <Clock className="h-3 w-3" /> Within 24 hrs
          </Badge>
        );
      case "medium":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-orange-100 text-orange-800 hover:bg-orange-200">
            <Clock className="h-3 w-3" /> Within 48 hrs
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" /> Upcoming
          </Badge>
        );
    }
  };

  return (
    <Card className="mb-4 hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Tutor Info */}
          <div className="md:w-1/4 flex items-start gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={request.tutorImage || undefined} />
              <AvatarFallback className="bg-purple-100 text-purple-600">
                {request.tutorName.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{request.tutorName}</div>
              <div className="flex items-center text-sm text-gray-500">
                <Star className="h-3 w-3 text-yellow-500 fill-yellow-500 mr-1" />
                {request.tutorRating}
                {request.tutorHistory > 0 && (
                  <span className="ml-2">• {request.tutorHistory} sessions</span>
                )}
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {request.tutorSpecializations.slice(0, 2).map((spec, index) => (
                  <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                    {spec}
                  </Badge>
                ))}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs px-2 py-0 h-6 mt-1 text-purple-600"
                onClick={() => setShowProfileDialog(true, request.id)}
              >
                View Profile
              </Button>
            </div>
          </div>

          {/* Session Details */}
          <div className="md:w-2/4">
            <div className="flex flex-wrap gap-2 mb-2">
              {getUrgencyBadge(request.urgency)}
              {request.autoAccept && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Auto-Accept
                </Badge>
              )}
              {request.conflictsWith && (
                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" /> Conflict
                </Badge>
              )}
            </div>

            <h3 className="font-medium">{request.topic}: {request.subtopic}</h3>
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <Calendar className="h-4 w-4 mr-1" />
              {request.requestedDate} at {request.requestedTime} • {request.sessionType}
            </div>
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <Clock className="h-4 w-4 mr-1" />
              {request.duration} minutes
              <DollarSign className="h-4 w-4 ml-3 mr-1" />
              ${request.price}
            </div>

            {request.notes && (
              <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                <strong>Tutor's message:</strong> {request.notes}
              </div>
            )}

            {request.conflictsWith && (
              <div className="mt-2 p-2 bg-red-50 rounded text-sm text-red-700">
                <strong>Scheduling conflict:</strong> {request.conflictsWith}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="md:w-1/4 flex md:flex-col md:items-end gap-2 mt-2 md:mt-0">
            <Button
              className="bg-green-600 hover:bg-green-700 flex-1 md:flex-none w-full md:w-auto"
              onClick={() => setShowAcceptDialog(true, request.id)}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Accept
            </Button>
            <Button
              variant="outline"
              className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 flex-1 md:flex-none w-full md:w-auto"
              onClick={() => setShowDeclineDialog(true, request.id)}
            >
              <XCircle className="h-4 w-4 mr-2" />
              Decline
            </Button>
          </div>
        </div>
      </CardContent>

      {/* Accept Dialog - Only render if this request is selected */}
      {isSelected && showAcceptDialog && (
        <Dialog open={showAcceptDialog} onOpenChange={(open) => setShowAcceptDialog(open)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Accept Session Request</DialogTitle>
              <DialogDescription>
                You're accepting a session request from {request.tutorName} for {request.topic}: {request.subtopic}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="bg-gray-50 p-3 rounded-md">
                <h4 className="font-medium mb-2">Session Details</h4>
                <p className="text-sm text-gray-600">
                  <strong>Date & Time:</strong> {request.requestedDate} at {request.requestedTime}<br />
                  <strong>Duration:</strong> {request.duration} minutes<br />
                  <strong>Price:</strong> ${request.price}<br />
                  <strong>Type:</strong> {request.sessionType}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium">Message to tutor (optional)</label>
                <Textarea
                  placeholder="Looking forward to learning from you!"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAcceptDialog(false)}>
                Cancel
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={async () => {
                  await acceptRequest(request.id, message);
                  setShowAcceptDialog(false);
                }}
              >
                Confirm Accept
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Decline Dialog - Only render if this request is selected */}
      {isSelected && showDeclineDialog && (
        <Dialog open={showDeclineDialog} onOpenChange={(open) => setShowDeclineDialog(open)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Decline Session Request</DialogTitle>
              <DialogDescription>
                You're declining a session request from {request.tutorName} for {request.topic}: {request.subtopic}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Reason for declining (optional)</label>
                <Textarea
                  placeholder="I'm not available at this time. Could we reschedule for another day?"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeclineDialog(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={async () => {
                  await declineRequest(request.id, message);
                  setShowDeclineDialog(false);
                }}
              >
                Confirm Decline
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Tutor Profile Dialog - Only render if this request is selected */}
      {isSelected && showProfileDialog && (
        <Dialog open={showProfileDialog} onOpenChange={(open) => setShowProfileDialog(false)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Tutor Profile</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={request.tutorImage || undefined} />
                  <AvatarFallback className="bg-purple-100 text-purple-600 text-lg">
                    {request.tutorName.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium text-lg">{request.tutorName}</h3>
                  <div className="flex items-center text-sm text-gray-500">
                    <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                    {request.tutorRating} rating • {request.tutorHistory} sessions taught
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Specializations
                </h4>
                <div className="flex flex-wrap gap-1">
                  {request.tutorSpecializations.map((spec, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {spec}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md">
                <h4 className="font-medium mb-2">About This Session</h4>
                <p className="text-sm text-gray-600">
                  {request.notes || "No additional notes provided by the tutor."}
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setShowProfileDialog(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
};

const AcceptSession: React.FC = () => {
  const {
    requests,
    filter,
    searchQuery,
    isLoading,
    setFilter,
    setSearchQuery,
    fetchRequests
  } = useStudentSessionRequestsStore();
  const profileData = useProfileData();

  // Fetch requests on component mount
  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  // Filter requests based on current filter and search query
  const filteredRequests = requests.filter(request => {
    // Apply search filter
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = 
        request.tutorName.toLowerCase().includes(searchLower) ||
        request.topic.toLowerCase().includes(searchLower) ||
        request.subtopic.toLowerCase().includes(searchLower) ||
        request.tutorSpecializations.some(spec => spec.toLowerCase().includes(searchLower));
      
      if (!matchesSearch) return false;
    }

    // Apply category filter
    switch (filter) {
      case 'urgent':
        return request.urgency === 'high';
      case 'auto-accept':
        return request.autoAccept;
      case 'conflicts':
        return !!request.conflictsWith;
      default:
        return true;
    }
  });

  return (
    <StudentPageLayout
      title="Accept Sessions"
      description="Review and respond to session requests from tutors"
      actions={
        <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
          {filteredRequests.length} request{filteredRequests.length !== 1 ? 's' : ''}
        </span>
      }
    >
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search by tutor, topic, subtopic..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-full sm:w-auto" onValueChange={setFilter}>
          <TabsList className="grid grid-cols-4 w-full sm:w-auto">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="urgent">Urgent</TabsTrigger>
            <TabsTrigger value="auto-accept">Auto-Accept</TabsTrigger>
            <TabsTrigger value="conflicts">Conflicts</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Requests List */}
      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      ) : filteredRequests.length === 0 ? (
        <div className="text-center py-8">
          <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No session requests</h3>
          <p className="text-gray-500">
            {searchQuery || filter !== 'all' 
              ? "No requests match your current filters." 
              : "You don't have any pending session requests from tutors."}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredRequests.map((request) => (
            <SessionRequestCard key={request.id} request={request} />
          ))}
        </div>
      )}
    </StudentPageLayout>
  );
};

export default AcceptSession;

import { create } from "zustand";

// Types for student data
export interface Student {
  id: string;
  // These fields come from the profiles table
  first_name?: string;
  last_name?: string;
  name?: string;
  email?: string;
  profile_picture_url?: string;

  // Fields from the students table
  education_level?: string;
  subjects_of_interest?: string[]; // Now an array in the DB
  learning_goals?: string[]; // Now an array in the DB
  study_preferences?: {
    preferred_time?: string;
    preferred_days?: string[];
    learning_style?: string;
    communication_preference?: string;
  };
  academic_history?: {
    school_name?: string;
    achievements?: string[];
    favorite_subjects?: string[];
  };
  hobbies?: string[]; // This is a text[] in the DB
  interests?: string[]; // This is a text[] in the DB
  location?: string;
  date_of_birth?: string; // Added this field from the DB schema
  created_at: string;
  updated_at: string;
}

// Student store state and actions
interface StudentStore {
  // State
  students: Student[];
  selectedStudent: Student | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchStudents: () => Promise<void>;
  fetchStudentById: (id: string) => Promise<Student | null>;
  createStudent: (student: Omit<Student, "id" | "created_at" | "updated_at">) => Promise<Student | null>;
  updateStudent: (id: string, updates: Partial<Student>) => Promise<boolean>;
  deleteStudent: (id: string) => Promise<boolean>;
  setSelectedStudent: (student: Student | null) => void;
  searchStudents: (query: string) => Student[];
}

// Create the store
export const useStudentStore = create<StudentStore>((set, get) => ({
  // Initial state
  students: [],
  selectedStudent: null,
  isLoading: false,
  error: null,

  // Actions
  fetchStudents: async () => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      console.log("Fetching students...");

      // First, let's try to get all profiles with user_type = 'student'
      // This approach is more reliable than joining with a potentially missing students table
      const { data: profilesData, error: profilesError } = await supabase
        .from("profiles")
        .select(`
          id,
          first_name,
          last_name,
          email,
          profile_picture_url,
          created_at,
          updated_at
        `)
        .eq('user_type', 'student');

      console.log("Profiles query result:", { profilesData, profilesError });

      if (profilesError) throw profilesError;

      if (!profilesData || profilesData.length === 0) {
        console.log("No student profiles found");
        set({ students: [], isLoading: false });
        return;
      }

      // Now try to get student-specific data for each profile
      const studentIds = profilesData.map(profile => profile.id);

      const { data: studentsData, error: studentsError } = await supabase
        .from("students")
        .select(`
          id,
          education_level,
          subjects_of_interest,
          learning_goals,
          study_preferences,
          academic_history,
          hobbies,
          interests,
          location,
          date_of_birth
        `)
        .in('id', studentIds);

      console.log("Students data query result:", { studentsData, studentsError });

      // If students table doesn't exist or query fails, we'll still show the profiles
      const studentsMap = new Map();
      if (studentsData && !studentsError) {
        studentsData.forEach(student => {
          studentsMap.set(student.id, student);
        });
      }

      // Transform the data to match our Student interface
      const transformedData = profilesData.map(profile => {
        const studentData = studentsMap.get(profile.id) || {};

        console.log("Processing student:", { profile, studentData });

        // Create a combined student object
        return {
          id: profile.id,
          first_name: profile.first_name,
          last_name: profile.last_name,
          name: profile.first_name && profile.last_name ?
                `${profile.first_name} ${profile.last_name}` : undefined,
          email: profile.email,
          profile_picture_url: profile.profile_picture_url,
          education_level: studentData.education_level,
          subjects_of_interest: studentData.subjects_of_interest || [],
          learning_goals: studentData.learning_goals || [],
          study_preferences: studentData.study_preferences,
          academic_history: studentData.academic_history,
          hobbies: studentData.hobbies || [],
          interests: studentData.interests || [],
          location: studentData.location,
          date_of_birth: studentData.date_of_birth,
          created_at: profile.created_at,
          updated_at: profile.updated_at
        } as Student;
      });

      console.log("Transformed students data:", transformedData);

      set({ students: transformedData, isLoading: false });
    } catch (error) {
      console.error("Error fetching students:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load students",
        isLoading: false
      });
    }
  },

  fetchStudentById: async (id) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Join the students table with profiles to get all the data we need
      const { data, error } = await supabase
        .from("students")
        .select(`
          id,
          education_level,
          subjects_of_interest,
          learning_goals,
          study_preferences,
          academic_history,
          hobbies,
          interests,
          location,
          date_of_birth,
          created_at,
          updated_at,
          profiles:id (
            first_name,
            last_name,
            email,
            profile_picture_url
          )
        `)
        .eq("id", id)
        .single();

      if (error) throw error;

      // Use type assertion to handle the nested profile data
      const studentData = data as any;
      const profile = studentData.profiles || {};

      // Create a combined student object
      const transformedData: Student = {
        id: data.id,
        first_name: profile.first_name,
        last_name: profile.last_name,
        name: profile.first_name && profile.last_name ?
              `${profile.first_name} ${profile.last_name}` : undefined,
        email: profile.email,
        profile_picture_url: profile.profile_picture_url,
        education_level: data.education_level,
        subjects_of_interest: data.subjects_of_interest,
        learning_goals: data.learning_goals,
        study_preferences: data.study_preferences,
        academic_history: data.academic_history,
        hobbies: data.hobbies,
        interests: data.interests,
        location: data.location,
        date_of_birth: data.date_of_birth,
        created_at: data.created_at,
        updated_at: data.updated_at
      };

      set({ selectedStudent: transformedData, isLoading: false });
      return transformedData;
    } catch (error) {
      console.error(`Error fetching student with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to load student with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  createStudent: async (studentInput) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      const userId = user.id;

      // Extract profile data and student data
      const profileData = {
        id: userId,
        first_name: studentInput.first_name,
        last_name: studentInput.last_name,
        email: studentInput.email,
        profile_picture_url: studentInput.profile_picture_url,
        user_type: 'student'
      };

      // Ensure arrays are properly formatted
      const ensureArray = (value: any) => {
        if (!value) return [];
        if (Array.isArray(value)) return value;
        // If it's a string, try to parse it as JSON
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed) ? parsed : [value];
          } catch (e) {
            // If parsing fails, treat it as a single item array
            return [value];
          }
        }
        // For any other type, wrap in array
        return [value];
      };

      const studentData = {
        id: userId, // This should be the auth.uid
        education_level: studentInput.education_level,
        subjects_of_interest: ensureArray(studentInput.subjects_of_interest),
        learning_goals: ensureArray(studentInput.learning_goals),
        study_preferences: studentInput.study_preferences,
        academic_history: studentInput.academic_history,
        hobbies: ensureArray(studentInput.hobbies),
        interests: ensureArray(studentInput.interests),
        location: studentInput.location,
        date_of_birth: studentInput.date_of_birth
      };

      // First, insert/update the profile
      const { error: profileError } = await supabase
        .from("profiles")
        .upsert(profileData);

      if (profileError) throw profileError;

      // Then, insert the student data
      const { data, error } = await supabase
        .from("students")
        .insert(studentData)
        .select(`
          id,
          education_level,
          subjects_of_interest,
          learning_goals,
          study_preferences,
          academic_history,
          hobbies,
          interests,
          location,
          date_of_birth,
          created_at,
          updated_at
        `)
        .single();

      if (error) throw error;

      // Combine the data for the return value
      const combinedData: Student = {
        ...data,
        first_name: profileData.first_name,
        last_name: profileData.last_name,
        email: profileData.email,
        profile_picture_url: profileData.profile_picture_url
      };

      // Update the students list with the new student
      set(state => ({
        students: [...state.students, combinedData],
        isLoading: false
      }));

      return combinedData;
    } catch (error) {
      console.error("Error creating student:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to create student",
        isLoading: false
      });
      return null;
    }
  },

  updateStudent: async (id, updates) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Ensure arrays are properly formatted
      const ensureArray = (value: any) => {
        if (!value) return [];
        if (Array.isArray(value)) return value;
        // If it's a string, try to parse it as JSON
        if (typeof value === 'string') {
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed) ? parsed : [value];
          } catch (e) {
            // If parsing fails, treat it as a single item array
            return [value];
          }
        }
        // For any other type, wrap in array
        return [value];
      };

      // Split updates into profile updates and student updates
      const profileUpdates: any = {};
      const studentUpdates: any = {};

      // Profile fields
      if (updates.first_name !== undefined) profileUpdates.first_name = updates.first_name;
      if (updates.last_name !== undefined) profileUpdates.last_name = updates.last_name;
      if (updates.email !== undefined) profileUpdates.email = updates.email;
      if (updates.profile_picture_url !== undefined) profileUpdates.profile_picture_url = updates.profile_picture_url;

      // Student fields
      if (updates.education_level !== undefined) studentUpdates.education_level = updates.education_level;
      if (updates.subjects_of_interest !== undefined) studentUpdates.subjects_of_interest = ensureArray(updates.subjects_of_interest);
      if (updates.learning_goals !== undefined) studentUpdates.learning_goals = ensureArray(updates.learning_goals);
      if (updates.study_preferences !== undefined) studentUpdates.study_preferences = updates.study_preferences;
      if (updates.academic_history !== undefined) studentUpdates.academic_history = updates.academic_history;
      if (updates.hobbies !== undefined) studentUpdates.hobbies = ensureArray(updates.hobbies);
      if (updates.interests !== undefined) studentUpdates.interests = ensureArray(updates.interests);
      if (updates.location !== undefined) studentUpdates.location = updates.location;
      if (updates.date_of_birth !== undefined) studentUpdates.date_of_birth = updates.date_of_birth;

      // Update profile if there are profile updates
      if (Object.keys(profileUpdates).length > 0) {
        const { error: profileError } = await supabase
          .from("profiles")
          .update(profileUpdates)
          .eq("id", id);

        if (profileError) throw profileError;
      }

      // Update student if there are student updates
      if (Object.keys(studentUpdates).length > 0) {
        const { error } = await supabase
          .from("students")
          .update(studentUpdates)
          .eq("id", id);

        if (error) throw error;
      }

      // Update the students list with the updated student
      set(state => ({
        students: state.students.map(student =>
          student.id === id ? { ...student, ...updates } : student
        ),
        selectedStudent: state.selectedStudent?.id === id
          ? { ...state.selectedStudent, ...updates }
          : state.selectedStudent,
        isLoading: false
      }));

      return true;
    } catch (error) {
      console.error(`Error updating student with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update student with ID ${id}`,
        isLoading: false
      });
      return false;
    }
  },

  deleteStudent: async (id) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Note: Due to the foreign key constraint with ON DELETE CASCADE,
      // deleting from the students table will automatically delete the corresponding
      // record from the profiles table
      const { error } = await supabase
        .from("students")
        .delete()
        .eq("id", id);

      if (error) throw error;

      // Remove the deleted student from the students list
      set(state => ({
        students: state.students.filter(student => student.id !== id),
        selectedStudent: state.selectedStudent?.id === id ? null : state.selectedStudent,
        isLoading: false
      }));

      return true;
    } catch (error) {
      console.error(`Error deleting student with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete student with ID ${id}`,
        isLoading: false
      });
      return false;
    }
  },

  setSelectedStudent: (student) => {
    set({ selectedStudent: student });
  },

  searchStudents: (query) => {
    const { students } = get();
    const lowercaseQuery = query.toLowerCase();

    return students.filter(student => {
      // Handle potentially undefined fields
      const firstName = student.first_name || '';
      const lastName = student.last_name || '';
      const email = student.email || '';
      const educationLevel = student.education_level || '';

      return firstName.toLowerCase().includes(lowercaseQuery) ||
        lastName.toLowerCase().includes(lowercaseQuery) ||
        email.toLowerCase().includes(lowercaseQuery) ||
        educationLevel.toLowerCase().includes(lowercaseQuery) ||
        // Also search in subjects_of_interest and learning_goals arrays
        (student.subjects_of_interest &&
          student.subjects_of_interest.some(subject =>
            subject.toLowerCase().includes(lowercaseQuery)
          )) ||
        (student.learning_goals &&
          student.learning_goals.some(goal =>
            goal.toLowerCase().includes(lowercaseQuery)
          ));
    });
  }
}));

import { create } from 'zustand';

// Define the types for the store
export interface Tu<PERSON> {
  id: string;
  name: string;
  rating: number;
  image: string | null;
  sessionHistory: number;
  specializations: string[];
}

export interface StudentSessionRequest {
  id: string;
  tutorId: string;
  tutorName: string;
  tutorRating: number;
  tutorImage: string | null;
  tutorSpecializations: string[];
  requestedDate: string;
  requestedTime: string;
  topic: string;
  subtopic: string;
  sessionType: string;
  notes: string | null;
  urgency: 'high' | 'medium' | 'low';
  autoAccept: boolean;
  conflictsWith: string | null;
  tutorHistory: number;
  createdAt: string;
  duration: number; // in minutes
  price: number; // session price
}

interface StudentSessionRequestsState {
  requests: StudentSessionRequest[];
  isLoading: boolean;
  filter: string;
  searchQuery: string;
  
  // Actions
  setFilter: (filter: string) => void;
  setSearchQuery: (searchQuery: string) => void;
  fetchRequests: () => Promise<void>;
  acceptRequest: (requestId: string, message?: string) => Promise<void>;
  declineRequest: (requestId: string, reason?: string) => Promise<void>;
}

// Mock data for student session requests (from tutors)
const MOCK_STUDENT_REQUESTS: StudentSessionRequest[] = [
  {
    id: "req-001",
    tutorId: "tutor-001",
    tutorName: "Dr. Sarah Johnson",
    tutorRating: 4.9,
    tutorImage: null,
    tutorSpecializations: ["Machine Learning", "Data Science", "Python"],
    requestedDate: "2023-07-15",
    requestedTime: "14:00",
    topic: "Machine Learning",
    subtopic: "Neural Networks",
    sessionType: "One-on-one",
    notes: "I can help you understand backpropagation algorithms with practical examples.",
    urgency: "high", // within 24 hours
    autoAccept: false,
    conflictsWith: null,
    tutorHistory: 150, // sessions taught
    createdAt: "2023-07-14T10:30:00Z",
    duration: 60,
    price: 45.00,
  },
  {
    id: "req-002",
    tutorId: "tutor-002",
    tutorName: "Prof. Michael Chen",
    tutorRating: 4.7,
    tutorImage: null,
    tutorSpecializations: ["Data Structures", "Algorithms", "Java"],
    requestedDate: "2023-07-16",
    requestedTime: "10:30",
    topic: "Data Structures",
    subtopic: "Binary Trees",
    sessionType: "One-on-one",
    notes: "I'll help you master tree traversal methods with coding practice.",
    urgency: "medium",
    autoAccept: true,
    conflictsWith: "Another session at 11:00",
    tutorHistory: 89,
    createdAt: "2023-07-14T09:15:00Z",
    duration: 90,
    price: 60.00,
  },
  {
    id: "req-003",
    tutorId: "tutor-003",
    tutorName: "Emma Rodriguez",
    tutorRating: 4.8,
    tutorImage: null,
    tutorSpecializations: ["Web Development", "React", "JavaScript"],
    requestedDate: "2023-07-17",
    requestedTime: "16:00",
    topic: "Web Development",
    subtopic: "React Hooks",
    sessionType: "One-on-one",
    notes: "Let's dive deep into useState, useEffect, and custom hooks.",
    urgency: "low",
    autoAccept: false,
    conflictsWith: null,
    tutorHistory: 67,
    createdAt: "2023-07-13T14:20:00Z",
    duration: 75,
    price: 50.00,
  },
  {
    id: "req-004",
    tutorId: "tutor-004",
    tutorName: "David Kim",
    tutorRating: 4.6,
    tutorImage: null,
    tutorSpecializations: ["Database Design", "SQL", "MongoDB"],
    requestedDate: "2023-07-18",
    requestedTime: "13:30",
    topic: "Database Design",
    subtopic: "Normalization",
    sessionType: "One-on-one",
    notes: "I'll explain database normalization with real-world examples.",
    urgency: "medium",
    autoAccept: false,
    conflictsWith: null,
    tutorHistory: 112,
    createdAt: "2023-07-13T11:45:00Z",
    duration: 60,
    price: 40.00,
  },
];

// Create the store
export const useStudentSessionRequestsStore = create<StudentSessionRequestsState>((set, get) => ({
  requests: MOCK_STUDENT_REQUESTS,
  isLoading: false,
  filter: 'all',
  searchQuery: '',

  // Set the filter
  setFilter: (filter) => set({ filter }),

  // Set the search query
  setSearchQuery: (searchQuery) => set({ searchQuery }),

  // Fetch requests from the API
  fetchRequests: async () => {
    set({ isLoading: true });

    try {
      // In a real app, this would be an API call
      // const response = await fetch('/api/student/session-requests');
      // const data = await response.json();

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Use mock data for now
      set({
        requests: MOCK_STUDENT_REQUESTS,
        isLoading: false
      });
    } catch (error) {
      console.error('Error fetching student session requests:', error);
      set({ isLoading: false });
    }
  },

  // Accept a request
  acceptRequest: async (requestId, message) => {
    set({ isLoading: true });

    try {
      // In a real app, this would be an API call
      // await fetch(`/api/student/session-requests/${requestId}/accept`, {
      //   method: 'POST',
      //   body: JSON.stringify({ message }),
      // });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update local state
      set(state => ({
        requests: state.requests.filter(req => req.id !== requestId),
        isLoading: false
      }));

      console.log(`Accepted session request ${requestId} with message: ${message || 'None'}`);
    } catch (error) {
      console.error('Error accepting session request:', error);
      set({ isLoading: false });
    }
  },

  // Decline a request
  declineRequest: async (requestId, reason) => {
    set({ isLoading: true });

    try {
      // In a real app, this would be an API call
      // await fetch(`/api/student/session-requests/${requestId}/decline`, {
      //   method: 'POST',
      //   body: JSON.stringify({ reason }),
      // });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update local state
      set(state => ({
        requests: state.requests.filter(req => req.id !== requestId),
        isLoading: false
      }));

      console.log(`Declined session request ${requestId} with reason: ${reason || 'None'}`);
    } catch (error) {
      console.error('Error declining session request:', error);
      set({ isLoading: false });
    }
  },
}));

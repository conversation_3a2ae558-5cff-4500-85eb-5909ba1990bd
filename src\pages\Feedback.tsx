import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import FeedbackForm from "@/components/FeedbackForm";
import useScrollToTop from "@/hooks/useScrollToTop";

const Feedback = () => {
  // Use the scroll to top hook
  useScrollToTop();

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-4 sm:py-6 md:py-8 lg:py-12">
        <div className="max-w-7xl mx-2 sm:mx-4 md:mx-9 px-2 sm:px-2 lg:px-0">
          {/* Flex container matching Inquiry page structure */}
          <div className="flex flex-col lg:flex-row items-start gap-8 lg:gap-16">
            <div className="lg:max-w-lg">
              <p className="text-xl text-gray-500 mt-2 sm:mt-4 md:mt-2 lg:mt-0">
                Your feedback helps us improve our platform and provide better
                learning experiences.
              </p>
            </div>

            <div className="w-full lg:flex-1">
              <FeedbackForm />
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Feedback;

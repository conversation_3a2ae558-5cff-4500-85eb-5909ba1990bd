
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { ROUTES } from "@/routes/RouteConfig";
import AuthenticatedImage from "@/components/ui/AuthenticatedImage";
import { useMemo, useState, useCallback, useRef, useEffect } from "react";
import {
  CustomDropdownMenu as DropdownMenu,
  CustomDropdownMenuContent as DropdownMenuContent,
  CustomDropdownMenuItem as DropdownMenuItem,
  CustomDropdownMenuLabel as DropdownMenuLabel,
  CustomDropdownMenuSeparator as DropdownMenuSeparator,
  CustomDropdownMenuTrigger as DropdownMenuTrigger,
} from "@/components/ui/CustomDropdownMenu";
import { Button } from "@/components/ui/Button";
import {
  LogOut,
  Settings,
  Lightbulb,
  Command,
  ChevronDown,
  Shield,
  Bell,
} from "lucide-react";

interface UserProfileMenuProps {
  isAdmin?: boolean;
  isAdminPage?: boolean;
  className?: string;
}

const UserProfileMenu = ({
  isAdmin = false,
  isAdminPage = false,
  className = "",
}: UserProfileMenuProps) => {
  const navigate = useNavigate();
  const { signOut, user, profileData, userType, isUserAdmin } = useAuth();

  // State for image loading to prevent flickering
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Memoize profile data to prevent unnecessary re-renders
  const memoizedProfileData = useMemo(() => {
    const displayName = `${profileData.firstName || ""} ${profileData.lastName || ""}`.trim() ||
                       (user?.email ? user.email.split('@')[0] : "User");
    const email = profileData.email || user?.email || "";
    const photoUrl = profileData.profilePictureUrl ||
                     (user?.user_metadata?.profile_picture_url as string) ||
                     (user?.user_metadata?.avatar_url as string) || "";

    return { displayName, email, photoUrl };
  }, [
    profileData.firstName,
    profileData.lastName,
    profileData.email,
    profileData.profilePictureUrl,
    user?.email,
    user?.user_metadata?.profile_picture_url,
    user?.user_metadata?.avatar_url
  ]);

  // Reset image states when photoUrl changes
  const prevPhotoUrl = useRef<string>('');
  useEffect(() => {
    if (memoizedProfileData.photoUrl !== prevPhotoUrl.current) {
      setImageLoaded(false);
      setImageError(false);
      prevPhotoUrl.current = memoizedProfileData.photoUrl;
      console.log('Profile photo URL changed:', {
        from: prevPhotoUrl.current,
        to: memoizedProfileData.photoUrl
      });
    }
  }, [memoizedProfileData.photoUrl]);

  const { displayName, email, photoUrl } = memoizedProfileData;

  // Use the actual admin status from auth context if not explicitly provided
  const actualIsAdmin = isAdmin || isUserAdmin();

  // Get initials from display name for avatar fallback
  const getInitials = useCallback(() => {
    if (!displayName) return "U";
    return displayName
      .split(" ")
      .map((n: string) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  }, [displayName]);

  // Handle image load success
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
  }, []);

  // Handle image load error
  const handleImageError = useCallback(() => {
    setImageLoaded(false);
    setImageError(true);
  }, []);

  const handleSignOut = async () => {
    // The signOut function will handle navigation via window.location.href
    const { error } = await signOut();
    if (error) {
      console.error("Error signing out:", error);
    }
    // No need for navigate("/") as the page will reload
  };



  // Create a stable fallback component to prevent re-renders
  const avatarFallback = useMemo(() => (
    <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-600 text-sm font-medium">
      {getInitials()}
    </div>
  ), [getInitials]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className={className}>
        <Button variant="ghost" className="flex items-center gap-2 h-auto py-2">
          <div className="h-8 w-8 rounded-full overflow-hidden flex-shrink-0 relative">
            {photoUrl && !imageError ? (
              <>
                {/* Show fallback while loading to prevent empty space */}
                {!imageLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-200 text-gray-600 text-sm font-medium z-10">
                    {getInitials()}
                  </div>
                )}
                <AuthenticatedImage
                  key={photoUrl} // Force re-render when photoUrl changes
                  src={photoUrl}
                  alt={displayName}
                  className={`w-full h-full object-cover transition-opacity duration-300 ${
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                  }`}
                  fallback={avatarFallback}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              </>
            ) : (
              avatarFallback
            )}
          </div>
          <div className="flex flex-col items-start text-sm">
            <span className="font-medium">{displayName || "User"}</span>
            {email && (
              <span className="text-xs text-muted-foreground">{email}</span>
            )}
          </div>
          <ChevronDown className="h-4 w-4 ml-1 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium">{displayName || "User"}</p>
            {email && <p className="text-xs text-muted-foreground">{email}</p>}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={() => {
          // Navigate to appropriate notifications page based on user type
          const notificationsPath = userType === 'tutor'
            ? ROUTES.TUTOR_NOTIFICATIONS.path
            : ROUTES.STUDENT_NOTIFICATIONS.path;
          navigate(notificationsPath);
        }}>
          <Bell className="mr-2 h-4 w-4" />
          <span>Notifications</span>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => {
          // Navigate to appropriate account preferences based on user type
          const accountPreferencesPath = userType === 'tutor'
            ? ROUTES.TUTOR_ACCOUNT_PREFERENCES.path
            : ROUTES.STUDENT_ACCOUNT_PREFERENCES.path;
          navigate(accountPreferencesPath);
        }}>
          <Settings className="mr-2 h-4 w-4" />
          <span>Account preferences</span>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => navigate("/feature-previews")}>
          <Lightbulb className="mr-2 h-4 w-4" />
          <span>Feature previews</span>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={() => navigate("/command-menu")}>
          <Command className="mr-2 h-4 w-4" />
          <span>Command menu</span>
        </DropdownMenuItem>



        {actualIsAdmin && (
          <>
            <DropdownMenuSeparator />
            {!isAdminPage && (
              <DropdownMenuItem onClick={() => navigate("/admin-dashboard")}>
                <Shield className="mr-2 h-4 w-4" />
                <span>Admin Dashboard</span>
              </DropdownMenuItem>
            )}
          </>
        )}

        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserProfileMenu;



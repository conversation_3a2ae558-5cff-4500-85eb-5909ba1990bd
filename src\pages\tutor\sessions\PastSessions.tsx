import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import { Badge } from "@/components/ui/Badge";
import { Checkbox } from "@/components/ui/Checkbox";
import {
  Search,
  Filter,
  ArrowUpDown,
  Columns,
  Clipboard,
  Check,
  Download,
  AlertTriangle,
  FileText,
  Info,
  Calendar,
  BarChart3,
  MessageSquare,
  Pencil,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import { useProfileData } from "@/hooks/useProfileData";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";
import { Card, CardContent } from "@/components/ui/Card";
import { DatePickerWithRange } from "@/components/ui/DatePickerWithRange";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/Popover";

// Sample past sessions data
const pastSessionsData = [
  {
    id: "S-1001",
    externalId: "EXT-917",
    studentName: "Alice Johnson",
    studentEmail: "<EMAIL>",
    country: "United States",
    topic: "Reinforcement Learning",
    subtopic: "Q-Learning vs SARSA",
    sessionType: "One-on-One",
    status: "Completed",
    date: "2023-11-15",
    time: "10:00 AM",
    duration: 60,
    scheduledDuration: 60,
    tutorTalkTime: 38,
    whiteboardInteractions: 12,
    messagesSent: 15,
    messagesReceived: 22,
    participationScore: 8.2,
    participationLevel: "Highly Engaged",
    notes: "Alice was very engaged and asked great questions about Q-learning algorithms.",
    feedbackRating: 4.8,
  },
  {
    id: "S-1005",
    externalId: "EXT-342",
    studentName: "Bob Smith",
    studentEmail: "<EMAIL>",
    country: "Canada",
    topic: "Neural Networks",
    subtopic: "Convolutional Networks",
    sessionType: "One-on-One",
    status: "Completed",
    date: "2023-11-10",
    time: "2:00 PM",
    duration: 45,
    scheduledDuration: 60,
    tutorTalkTime: 35,
    whiteboardInteractions: 5,
    messagesSent: 12,
    messagesReceived: 8,
    participationScore: 6.5,
    participationLevel: "Moderate",
    notes: "Bob seemed distracted at times. We covered the basics of CNNs but should revisit in a future session.",
    feedbackRating: 4.0,
  },
  {
    id: "S-1008",
    externalId: "EXT-519",
    studentName: "Charlie Davis",
    studentEmail: "<EMAIL>",
    country: "UK",
    topic: "Deep Learning",
    subtopic: "Recurrent Neural Networks",
    sessionType: "Group",
    status: "Completed",
    date: "2023-11-05",
    time: "11:30 AM",
    duration: 90,
    scheduledDuration: 90,
    tutorTalkTime: 65,
    whiteboardInteractions: 18,
    messagesSent: 25,
    messagesReceived: 32,
    participationScore: 9.0,
    participationLevel: "Highly Engaged",
    notes: "Excellent group session with lots of interaction. Charlie led many of the discussions.",
    feedbackRating: 5.0,
  },
  {
    id: "S-1012",
    externalId: "EXT-721",
    studentName: "Diana Wang",
    studentEmail: "<EMAIL>",
    country: "Singapore",
    topic: "Machine Learning",
    subtopic: "Support Vector Machines",
    sessionType: "One-on-One",
    status: "Completed",
    date: "2023-10-28",
    time: "9:00 AM",
    duration: 30,
    scheduledDuration: 60,
    tutorTalkTime: 25,
    whiteboardInteractions: 2,
    messagesSent: 8,
    messagesReceived: 5,
    participationScore: 4.5,
    participationLevel: "Passive",
    notes: "Diana had connectivity issues and seemed unprepared. Session ended early.",
    feedbackRating: 3.5,
  },
];

// Basic structure for the Past Sessions page
const PastSessions = () => {
  const profileData = useProfileData();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSessions, setSelectedSessions] = useState<string[]>([]);
  const [visibleColumns, setVisibleColumns] = useState({
    id: true,
    student: true,
    topic: true,
    subtopic: true,
    tutorTalkTime: true,
    whiteboardInteractions: true,
    messagesSent: false,
    messagesReceived: false,
    participationScore: true,
    date: true,
    notes: true,
  });

  // Calculate summary statistics
  const totalSessions = pastSessionsData.length;
  const avgParticipationScore = (pastSessionsData.reduce((sum, session) => sum + session.participationScore, 0) / totalSessions).toFixed(1);
  const avgDuration = Math.round(pastSessionsData.reduce((sum, session) => sum + session.duration, 0) / totalSessions);
  const avgScheduledDuration = Math.round(pastSessionsData.reduce((sum, session) => sum + session.scheduledDuration, 0) / totalSessions);
  const tutorTalkTimeRatio = Math.round((pastSessionsData.reduce((sum, session) => sum + session.tutorTalkTime, 0) / pastSessionsData.reduce((sum, session) => sum + session.duration, 0)) * 100);
  const whiteboardUsagePercent = Math.round((pastSessionsData.filter(session => session.whiteboardInteractions > 0).length / totalSessions) * 100);
  const avgFeedbackRating = (pastSessionsData.reduce((sum, session) => sum + session.feedbackRating, 0) / totalSessions).toFixed(1);

  // Filter sessions based on search term
  const filteredSessions = pastSessionsData.filter((session) => {
    const searchString = searchTerm.toLowerCase();
    return (
      session.id.toLowerCase().includes(searchString) ||
      session.studentName.toLowerCase().includes(searchString) ||
      session.topic.toLowerCase().includes(searchString) ||
      session.subtopic.toLowerCase().includes(searchString)
    );
  });

  // Get participation level badge
  const getParticipationBadge = (level: string) => {
    switch (level) {
      case "Highly Engaged":
        return <Badge className="bg-green-100 text-green-800">⭐ Highly Engaged</Badge>;
      case "Moderate":
        return <Badge className="bg-blue-100 text-blue-800">Moderate</Badge>;
      case "Passive":
        return <Badge className="bg-yellow-100 text-yellow-800">Passive</Badge>;
      default:
        return <Badge>{level}</Badge>;
    }
  };

  return (
    <TutorPageLayout
      title="Past Sessions"
      profileData={profileData}
      description="Review your completed sessions, engagement metrics, and teaching trends"
    >
      {/* Summary Analytics Panel */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Session Stats</h3>
                <div className="mt-2 grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-2xl font-bold">{totalSessions}</p>
                    <p className="text-sm text-gray-500">Total Sessions</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{avgDuration} mins</p>
                    <p className="text-sm text-gray-500">Avg. Duration</p>
                  </div>
                </div>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Engagement Metrics</h3>
                <div className="mt-2 grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-2xl font-bold">{avgParticipationScore}/10</p>
                    <p className="text-sm text-gray-500">Avg. Participation</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{tutorTalkTimeRatio}%</p>
                    <p className="text-sm text-gray-500">Tutor Talk Ratio</p>
                  </div>
                </div>
              </div>
              <MessageSquare className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Feedback & Tools</h3>
                <div className="mt-2 grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-2xl font-bold">{avgFeedbackRating}/5</p>
                    <p className="text-sm text-gray-500">Avg. Rating</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{whiteboardUsagePercent}%</p>
                    <p className="text-sm text-gray-500">Whiteboard Usage</p>
                  </div>
                </div>
              </div>
              <Pencil className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main content will be added in the next step */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-4 border-b flex flex-wrap items-center justify-between gap-4">
          <div className="relative flex-grow max-w-md">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={18}
            />
            <Input
              placeholder="Search by student, topic, or subtopic..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <Filter size={16} className="mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {/* Filter options would go here */}
                <DropdownMenuCheckboxItem checked>
                  All Sessions
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem>
                  High Engagement
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem>
                  Low Engagement
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem>
                  Used Whiteboard
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <Calendar size={16} className="mr-2" />
                  Date Range
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                {/* Date picker would go here */}
                <div className="p-4">
                  <p className="text-sm text-gray-500 mb-2">Select date range</p>
                </div>
              </PopoverContent>
            </Popover>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <Columns size={16} className="mr-2" />
                  Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.id}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      id: !!checked,
                    })
                  }
                >
                  Session ID
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.student}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      student: !!checked,
                    })
                  }
                >
                  Student
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.topic}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      topic: !!checked,
                    })
                  }
                >
                  Topic
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.subtopic}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      subtopic: !!checked,
                    })
                  }
                >
                  Subtopic
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.tutorTalkTime}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      tutorTalkTime: !!checked,
                    })
                  }
                >
                  Tutor Talk Time
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.whiteboardInteractions}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      whiteboardInteractions: !!checked,
                    })
                  }
                >
                  Whiteboard Interactions
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.participationScore}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      participationScore: !!checked,
                    })
                  }
                >
                  Participation Score
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.date}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      date: !!checked,
                    })
                  }
                >
                  Date & Time
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.notes}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      notes: !!checked,
                    })
                  }
                >
                  Session Notes
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
            >
              <Download size={16} className="mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="w-12">
                  <Checkbox
                    checked={
                      selectedSessions.length === filteredSessions.length &&
                      filteredSessions.length > 0
                    }
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedSessions(filteredSessions.map((session) => session.id));
                      } else {
                        setSelectedSessions([]);
                      }
                    }}
                    aria-label="Select all"
                  />
                </TableHead>
                {visibleColumns.id && (
                  <TableHead>Session ID</TableHead>
                )}
                {visibleColumns.student && (
                  <TableHead>Student</TableHead>
                )}
                {visibleColumns.topic && (
                  <TableHead>Topic</TableHead>
                )}
                {visibleColumns.subtopic && (
                  <TableHead>Subtopic</TableHead>
                )}
                {visibleColumns.tutorTalkTime && (
                  <TableHead>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="flex items-center">
                          Tutor Talk Time <Info size={14} className="ml-1 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Amount of time the tutor spent speaking during the session compared to the total session duration.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableHead>
                )}
                {visibleColumns.whiteboardInteractions && (
                  <TableHead>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="flex items-center">
                          Whiteboard <Info size={14} className="ml-1 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Number of collaborative annotations, drawings, or text entries on the whiteboard.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableHead>
                )}
                {visibleColumns.participationScore && (
                  <TableHead>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="flex items-center">
                          Participation <Info size={14} className="ml-1 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Student engagement score (0-10) based on messages, whiteboard activity, and camera/audio participation.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableHead>
                )}
                {visibleColumns.date && (
                  <TableHead>Date & Time</TableHead>
                )}
                {visibleColumns.notes && (
                  <TableHead>Notes</TableHead>
                )}
                <TableHead className="w-20">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSessions.map((session) => (
                <TableRow
                  key={session.id}
                  className={`hover:bg-gray-50 ${
                    session.tutorTalkTime / session.duration > 0.8 ? "bg-yellow-50" : ""
                  }`}
                >
                  <TableCell>
                    <Checkbox
                      checked={selectedSessions.includes(session.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedSessions([...selectedSessions, session.id]);
                        } else {
                          setSelectedSessions(selectedSessions.filter(id => id !== session.id));
                        }
                      }}
                      aria-label={`Select session ${session.id}`}
                    />
                  </TableCell>
                  {visibleColumns.id && (
                    <TableCell className="font-medium">{session.id}</TableCell>
                  )}
                  {visibleColumns.student && (
                    <TableCell>
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded-full"
                            src={`https://ui-avatars.com/api/?name=${session.studentName}&background=random`}
                            alt={session.studentName}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {session.studentName}
                          </div>
                          <div className="text-gray-500 text-sm">
                            {session.studentEmail}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.topic && (
                    <TableCell>{session.topic}</TableCell>
                  )}
                  {visibleColumns.subtopic && (
                    <TableCell>{session.subtopic}</TableCell>
                  )}
                  {visibleColumns.tutorTalkTime && (
                    <TableCell>
                      <div className={`${
                        session.tutorTalkTime / session.duration > 0.8 ? "text-yellow-700" : ""
                      }`}>
                        {session.tutorTalkTime} min / {session.duration} min
                        <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                          <div
                            className={`h-1.5 rounded-full ${
                              session.tutorTalkTime / session.duration > 0.8
                                ? "bg-yellow-500"
                                : "bg-green-500"
                            }`}
                            style={{ width: `${(session.tutorTalkTime / session.duration) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.whiteboardInteractions && (
                    <TableCell>
                      <div className="flex items-center">
                        <span className="font-medium">{session.whiteboardInteractions}</span>
                        <span className="text-gray-500 text-sm ml-1">interactions</span>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.participationScore && (
                    <TableCell>
                      <div>
                        <div className="font-medium">{session.participationScore.toFixed(1)}/10</div>
                        <div>{getParticipationBadge(session.participationLevel)}</div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.date && (
                    <TableCell>
                      <div>
                        <div>{formatDate(session.date)}</div>
                        <div className="text-gray-500 text-sm">
                          {session.time} ({session.duration} min)
                        </div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.notes && (
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <FileText className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">{session.notes}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  )}
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Download className="h-4 w-4 text-gray-500" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Export session data</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <AlertTriangle className="h-4 w-4 text-gray-500" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Flag session for review</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </TutorPageLayout>
  );
};

export default PastSessions;

import React from "react";
import { Input } from "@/components/ui/input";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

interface RateFieldProps {
  form: any;
  name?: string;
  label?: string;
  placeholder?: string;
}

const RateField: React.FC<RateFieldProps> = ({
  form,
  name = "hourlyRate",
  label = "Hourly Rate ($)",
  placeholder = "40",
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input type="number" placeholder={placeholder} {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default RateField;

// src/store/batchCreationStore.ts
import { create } from "zustand";
import { useAdminBatchStore, EnrolledStudentForBatch } from "./adminBatchStore";
import { Subscription } from "./billingStore";

interface BatchCreationState {
  // Form state
  selectedStudent: string;
  selectedTutor: string;
  selectedSubscription: string;
  enrolledStudents: EnrolledStudentForBatch[];
  selectedStudentData: EnrolledStudentForBatch | null;
  isLoadingStudents: boolean;
  isLoading: boolean;
  error: string | null;
  success: boolean;

  // Actions
  setSelectedStudent: (studentId: string) => void;
  setSelectedTutor: (tutorId: string) => void;
  setSelectedSubscription: (subscriptionId: string) => void;
  setEnrolledStudents: (students: EnrolledStudentForBatch[]) => void;
  setSelectedStudentData: (student: EnrolledStudentForBatch | null) => void;
  setIsLoadingStudents: (isLoading: boolean) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setSuccess: (success: boolean) => void;
  resetForm: () => void;

  // Async actions
  fetchEnrolledStudents: () => Promise<void>;
  handleStudentSelection: (studentId: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<boolean>;
}

export const useBatchCreationStore = create<BatchCreationState>((set, get) => ({
  // Initial state
  selectedStudent: "",
  selectedTutor: "none",
  selectedSubscription: "",
  enrolledStudents: [],
  selectedStudentData: null,
  isLoadingStudents: false,
  isLoading: false,
  error: null,
  success: false,

  // Actions
  setSelectedStudent: (studentId) => set({ selectedStudent: studentId }),
  setSelectedTutor: (tutorId) => set({ selectedTutor: tutorId }),
  setSelectedSubscription: (subscriptionId) => set({ selectedSubscription: subscriptionId }),
  setEnrolledStudents: (students) => set({ enrolledStudents: students }),
  setSelectedStudentData: (student) => set({ selectedStudentData: student }),
  setIsLoadingStudents: (isLoading) => set({ isLoadingStudents: isLoading }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setSuccess: (success) => set({ success }),
  resetForm: () => set({
    selectedStudent: "",
    selectedTutor: "none",
    selectedSubscription: "",
    selectedStudentData: null,
    error: null,
    success: false
  }),

  // Async actions
  fetchEnrolledStudents: async () => {
    set({ isLoadingStudents: true, error: null });

    try {
      const { getEnrolledStudentsForBatch } = useAdminBatchStore.getState();
      const students = await getEnrolledStudentsForBatch();

      set({
        enrolledStudents: students,
        isLoadingStudents: false
      });
    } catch (error) {
      console.error("Error fetching enrolled students:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to fetch enrolled students",
        isLoadingStudents: false
      });
    }
  },

  handleStudentSelection: (studentId) => {
    const { enrolledStudents } = get();
    const selectedStudent = enrolledStudents.find(student => student.id === studentId);

    set({
      selectedStudent: studentId,
      selectedStudentData: selectedStudent || null,
      selectedSubscription: "" // Reset subscription selection
    });
  },

  handleSubmit: async (e) => {
    e.preventDefault();

    const { selectedStudent, selectedSubscription, selectedStudentData } = get();

    // Validate form
    if (!selectedStudent || !selectedSubscription) {
      set({ error: "Please fill in all required fields" });
      return false;
    }

    if (!selectedStudentData) {
      set({ error: "Invalid student selected", isLoading: false });
      return false;
    }

    set({ isLoading: true, error: null });

    // Get subscription data from the selected student
    const subscription = selectedStudentData.subscriptions.find(sub => sub.id === selectedSubscription);

    if (!subscription) {
      set({ error: "Invalid subscription selected", isLoading: false });
      return false;
    }

    try {
      // Create batch
      const { createBatch } = useAdminBatchStore.getState();
      const result = await createBatch({
        student_id: selectedStudent,
        package_type: subscription.product_type,
        package_name: subscription.product_name,
        default_tutor_id: get().selectedTutor === "none" ? null : get().selectedTutor || null,
        subscription_id: selectedSubscription,
        workflow_id: subscription.workflow_id
      });

      set({ isLoading: false });

      if (result.success) {
        set({ success: true });
        return true;
      } else {
        set({ error: result.error || "Failed to create batch" });
        return false;
      }
    } catch (error) {
      console.error("Error creating batch:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to create batch",
        isLoading: false
      });
      return false;
    }
  }
}));

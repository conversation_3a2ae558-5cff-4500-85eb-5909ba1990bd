import { UserType } from "@/context/AuthContext";

/**
 * Utility functions for determining tutor application state and UI presentation
 * based on authentication status
 */

/**
 * Determines if a user should be shown tutor application options
 * @param userStatus The user's status
 * @returns boolean indicating if tutor application options should be shown
 */
export const shouldShowTutorApplicationOptions = (userStatus: string | null): boolean => {
  return userStatus === "new";
};

/**
 * Determines if a user needs to complete signup process
 * @param userStatus The user's status
 * @returns boolean indicating if user needs to complete signup
 */
export const needsCompleteSignup = (userStatus: string | null): boolean => {
  return userStatus === "guest";
};

/**
 * Determines if a user needs to continue to onboarding
 * @param userStatus The user's status
 * @param userType The user's type
 * @param isOnboarded Whether the user has completed onboarding
 * @returns boolean indicating if user needs to continue to onboarding
 */
export const needsContinueToOnboarding = (
  userStatus: string | null,
  userType: UserType,
  isOnboarded: boolean
): boolean => {
  return userStatus === "registered" && userType === "tutor" && !isOnboarded;
};
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import RegisterForm from "@/components/auth/RegisterForm";
import { Info, AlertCircle } from "lucide-react";
import useScrollToTop from "@/hooks/useScrollToTop";
import { getPostRegistrationRedirect } from "@/routes/RouteConfig";
import { useRegisterStore } from "@/store/registerStore";
import { useUserStatusCheck } from "@/hooks/useUserStatusCheck";
import { handleAuthError } from "@/services/errorHandler";
import {
  registrationSchema,
  isEmailRegisteredSchema,
} from "@/services/errorHandler";

const Register = () => {
  const { signUp } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const {
    isLoading,
    setIsLoading,
    setRegisteredEmail,
    registrationError,
    setRegistrationError,
    isRegistered,
    existingUserType,
  } = useRegisterStore();

  useScrollToTop();

  // Get state from location if available
  const locationState = location.state as {
    email?: string;
    source?: string;
    isGuestUpgrade?: boolean;
  } | null;

  // Check if this is a guest upgrade
  const isGuestUpgrade = locationState?.isGuestUpgrade === true;
  const source = locationState?.source || "";
  const prefilledEmail = locationState?.email || "";

  // Add this near the top of the component
  const { checkUserStatus } = useUserStatusCheck();

  // Set prefilled email in store if provided
  useEffect(() => {
    if (prefilledEmail) {
      useRegisterStore.getState().setFormData({ email: prefilledEmail });
    }
  }, [prefilledEmail]);

  const handleRegister = async (
    email: string,
    password: string,
    userType: string
  ) => {
    setIsLoading(true);
    setRegistrationError(null);

    try {
      // Skip email registration check for guest upgrades
      if (!isGuestUpgrade) {
        // Check if the email is already registered
        const { isRegistered, userType: existingUserType } =
          await checkUserStatus(email);

        // Validate using the email registration schema
        try {
          isEmailRegisteredSchema(isRegistered, existingUserType).parse({
            email,
          });
        } catch (validationError: any) {
          const formattedErrors = validationError.format
            ? validationError.format()
            : {};
          const errorMessage =
            formattedErrors.email?._errors[0] || "Email validation failed";
          setRegistrationError(errorMessage);
          setIsLoading(false);
          return;
        }
      }

      // Validate the rest of the registration data
      try {
        registrationSchema.parse({
          email,
          password,
          userType,
          acceptTerms: true, // Assuming this has already been checked in the form
        });
      } catch (validationError: any) {
        const formattedErrors = validationError.format
          ? validationError.format()
          : {};
        const errorMessage =
          formattedErrors.email?._errors[0] ||
          formattedErrors.password?._errors[0] ||
          formattedErrors.userType?._errors[0] ||
          "Validation failed";

        setRegistrationError(errorMessage);
        setIsLoading(false);
        return;
      }

      // Add additional metadata for guest upgrades
      const metadata: any = {
        user_type: userType,
        onboarding_completed: false,
      };

      // If this is a guest upgrade, add that information
      if (isGuestUpgrade) {
        metadata.is_guest_upgrade = true;
        metadata.previous_source = source;
      }

      const { error} = await signUp(email, password, metadata);

      if (error) {
        console.error("SignUp returned error:", error);
        throw error;
      }

      // Store the email for reference
      setRegisteredEmail(email);

      // Get the redirect path from our centralized routing config
      const redirectPath = getPostRegistrationRedirect(
        email,
        userType,
        isGuestUpgrade
      );

      // Redirect to the email confirmation page
      navigate(redirectPath, {
        state: {
          email: email,
          userType: userType,
          isGuestUpgrade: isGuestUpgrade,
        },
      });
    } catch (error: any) {
      console.error("Registration error:", error);
      const errorMessage = handleAuthError(error);
      setRegistrationError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {isGuestUpgrade && (
            <div className="flex items-start gap-2 text-sm text-blue-700 bg-blue-50 p-4 rounded-md mb-4">
              <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
              <span>
                You're upgrading your guest account to a full account. Complete
                the form below to set up your password.
              </span>
            </div>
          )}

          {registrationError && (
            <div className="flex items-start gap-2 text-sm text-red-700 bg-red-50 p-4 rounded-md mb-4">
              <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
              <span>{registrationError}</span>
            </div>
          )}

          <RegisterForm
            onSubmit={handleRegister}
            isLoading={isLoading}
            prefilledEmail={prefilledEmail}
            isGuestUpgrade={isGuestUpgrade}
            isRegistered={isRegistered}
            existingUserType={existingUserType}
          />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Register;

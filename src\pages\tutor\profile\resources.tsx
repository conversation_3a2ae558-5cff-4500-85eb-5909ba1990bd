import React from "react";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { BookOpen, Construction } from "lucide-react";

const ResourcesPage: React.FC = () => {
  return (
    <TutorPageLayout
      title="Resources"
      description="Access teaching materials and resources for your tutoring sessions."
    >
      <Card className="border-2 border-dashed border-gray-300 bg-white">
        <CardHeader className="text-center pb-2">
          <CardTitle className="flex items-center justify-center gap-2 text-2xl font-bold text-gray-700">
            <BookOpen className="h-6 w-6 text-rfpurple-500" />
            Tutor Resources
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-10">
          <div className="mx-auto max-w-md">
            <Construction className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Coming Soon</h3>
            <p className="text-gray-600 mb-6">
              We're currently developing this section to provide you with valuable teaching resources,
              curriculum materials, and session templates to enhance your tutoring experience.
            </p>
            <p className="text-gray-500 text-sm mb-6">
              Soon you'll be able to access, upload, and organize teaching materials
              to help you deliver exceptional tutoring sessions.
            </p>
            <Button
              onClick={() => window.history.back()}
              className="bg-rfpurple-600 hover:bg-rfpurple-700"
            >
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </TutorPageLayout>
  );
};

export default ResourcesPage;

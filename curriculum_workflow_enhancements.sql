/*
=====================================================
CURRICULUM WORKFLOW ENHANCEMENTS
=====================================================

This script provides three key enhancements to better support the 
curriculum configuration workflow:

1. Explicit product-subject relationships
2. Enhanced quiz/test configuration  
3. Clear subscription-to-batch relationship

Run this script after the existing schema is in place.
*/

-- =====================================================
-- 1. EXPLICIT PRODUCT-SUBJECT RELATIONSHIPS
-- =====================================================

-- Create products table if it doesn't exist (for reference)
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    price NUMERIC(10,2),
    duration_days INTEGER,
    type TEXT NOT NULL CHECK (type IN ('booster', 'custom', 'preparation')),
    features JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Product-Subject relationship table
-- Defines which subjects are available for each product type
CREATE TABLE IF NOT EXISTS product_subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    subject_id UUID REFERENCES subjects(id) ON DELETE CASCADE,
    
    -- Configuration options
    is_default BOOLEAN DEFAULT FALSE, -- Auto-selected for this product
    is_required BOOLEAN DEFAULT FALSE, -- Must be included (for booster products)
    max_topics_allowed INTEGER, -- Limit topics for custom/prep products
    estimated_sessions_per_subject INTEGER, -- For pricing calculations
    
    -- Pricing overrides (if subject has different pricing)
    price_override NUMERIC(10,2), -- Override default product price for this subject
    price_per_session_override NUMERIC(10,2), -- Override session pricing
    
    -- Display and ordering
    display_order INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE, -- Highlight this subject for the product
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    UNIQUE(product_id, subject_id)
);

-- Product-specific topic configurations
-- Allows fine-grained control over which topics are available per product
CREATE TABLE IF NOT EXISTS product_topic_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_subject_id UUID REFERENCES product_subjects(id) ON DELETE CASCADE,
    topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
    
    -- Topic-level configuration
    is_available BOOLEAN DEFAULT TRUE, -- Can be selected for this product
    is_recommended BOOLEAN DEFAULT FALSE, -- Recommended for this product type
    estimated_sessions INTEGER DEFAULT 1, -- Sessions needed for this topic
    difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    prerequisites TEXT[], -- Array of prerequisite topic IDs
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    UNIQUE(product_subject_id, topic_id)
);

-- =====================================================
-- 2. ENHANCED QUIZ/TEST CONFIGURATION
-- =====================================================

-- Extend resources table for better quiz/test support
ALTER TABLE resources 
ADD COLUMN IF NOT EXISTS is_required BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS estimated_duration_minutes INTEGER,
ADD COLUMN IF NOT EXISTS difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
ADD COLUMN IF NOT EXISTS max_attempts INTEGER DEFAULT 3,
ADD COLUMN IF NOT EXISTS passing_score NUMERIC(5,2), -- Percentage required to pass
ADD COLUMN IF NOT EXISTS is_practice BOOLEAN DEFAULT TRUE, -- Practice vs. assessment
ADD COLUMN IF NOT EXISTS prerequisites JSONB DEFAULT '[]', -- Array of prerequisite resource IDs
ADD COLUMN IF NOT EXISTS tags JSONB DEFAULT '[]'; -- Array of tags for categorization

-- Quiz/Test configuration table for curriculum planning
CREATE TABLE IF NOT EXISTS curriculum_assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Relationship to curriculum structure
    subject_id UUID REFERENCES subjects(id),
    topic_id UUID REFERENCES topics(id),
    subtopic_id UUID REFERENCES subtopics(id),
    
    -- Assessment details
    name TEXT NOT NULL,
    description TEXT,
    assessment_type TEXT NOT NULL CHECK (assessment_type IN ('quiz', 'test', 'practice', 'final_exam', 'diagnostic')),
    
    -- Configuration
    question_count INTEGER,
    time_limit_minutes INTEGER,
    difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    passing_score NUMERIC(5,2) DEFAULT 70.0, -- Percentage
    max_attempts INTEGER DEFAULT 3,
    
    -- Curriculum integration
    is_required_for_completion BOOLEAN DEFAULT FALSE,
    unlock_requirements JSONB DEFAULT '[]', -- What must be completed first
    completion_rewards JSONB DEFAULT '{}', -- Points, badges, etc.
    
    -- Scheduling
    recommended_timing TEXT, -- 'after_topic', 'mid_topic', 'before_topic'
    auto_schedule BOOLEAN DEFAULT FALSE, -- Auto-add to student schedule
    
    -- Content reference
    resource_id UUID REFERENCES resources(id), -- Link to actual quiz content
    
    -- Metadata
    created_by UUID REFERENCES profiles(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Assessment prerequisites (what must be completed before this assessment)
CREATE TABLE IF NOT EXISTS assessment_prerequisites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    assessment_id UUID REFERENCES curriculum_assessments(id) ON DELETE CASCADE,
    prerequisite_type TEXT NOT NULL CHECK (prerequisite_type IN ('topic', 'subtopic', 'assessment', 'resource')),
    prerequisite_id UUID NOT NULL, -- ID of the prerequisite item
    is_required BOOLEAN DEFAULT TRUE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    UNIQUE(assessment_id, prerequisite_type, prerequisite_id)
);

-- =====================================================
-- 3. CLEAR SUBSCRIPTION-TO-BATCH RELATIONSHIP
-- =====================================================

-- Add subscription relationship to batches table
ALTER TABLE batches 
ADD COLUMN IF NOT EXISTS subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS auto_created BOOLEAN DEFAULT FALSE, -- Was this batch auto-created from subscription?
ADD COLUMN IF NOT EXISTS curriculum_locked BOOLEAN DEFAULT FALSE; -- Prevent changes to curriculum after creation

-- Subscription-Batch mapping table (for cases where one subscription creates multiple batches)
CREATE TABLE IF NOT EXISTS subscription_batches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    batch_id UUID REFERENCES batches(id) ON DELETE CASCADE,
    
    -- Batch configuration from subscription
    subject_id UUID REFERENCES subjects(id), -- Which subject this batch covers
    curriculum_source TEXT NOT NULL CHECK (curriculum_source IN ('subscription_workflow', 'admin_configured', 'student_customized')),
    
    -- Status tracking
    is_primary_batch BOOLEAN DEFAULT FALSE, -- Main batch for the subscription
    activation_date TIMESTAMP WITH TIME ZONE, -- When this batch becomes active
    completion_target_date TIMESTAMP WITH TIME ZONE, -- Expected completion
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    UNIQUE(subscription_id, batch_id)
);

-- Batch curriculum mapping (links batch to specific curriculum selections)
CREATE TABLE IF NOT EXISTS batch_curriculum_mapping (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID REFERENCES batches(id) ON DELETE CASCADE,
    workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE SET NULL,
    
    -- Curriculum content (copied from subscription_curriculum_selections)
    subject_id UUID REFERENCES subjects(id),
    topic_ids JSONB DEFAULT '[]', -- Array of selected topic IDs
    subtopic_ids JSONB DEFAULT '[]', -- Array of selected subtopic IDs
    assessment_ids JSONB DEFAULT '[]', -- Array of selected assessment IDs
    
    -- Batch-specific configuration
    sequence_order INTEGER DEFAULT 0, -- Order of topics/subtopics in the batch
    is_completed BOOLEAN DEFAULT FALSE,
    completion_date TIMESTAMP WITH TIME ZONE,
    
    -- Session allocation
    allocated_sessions INTEGER, -- How many sessions allocated to this curriculum part
    completed_sessions INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Product-Subject relationships
CREATE INDEX IF NOT EXISTS idx_product_subjects_product_id ON product_subjects(product_id);
CREATE INDEX IF NOT EXISTS idx_product_subjects_subject_id ON product_subjects(subject_id);
CREATE INDEX IF NOT EXISTS idx_product_subjects_is_default ON product_subjects(is_default) WHERE is_default = TRUE;

-- Product-Topic configurations
CREATE INDEX IF NOT EXISTS idx_product_topic_config_product_subject ON product_topic_configurations(product_subject_id);
CREATE INDEX IF NOT EXISTS idx_product_topic_config_topic ON product_topic_configurations(topic_id);

-- Assessment configurations
CREATE INDEX IF NOT EXISTS idx_curriculum_assessments_subject ON curriculum_assessments(subject_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_assessments_topic ON curriculum_assessments(topic_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_assessments_subtopic ON curriculum_assessments(subtopic_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_assessments_type ON curriculum_assessments(assessment_type);

-- Subscription-Batch relationships
CREATE INDEX IF NOT EXISTS idx_batches_subscription_id ON batches(subscription_id);
CREATE INDEX IF NOT EXISTS idx_batches_workflow_id ON batches(workflow_id);
CREATE INDEX IF NOT EXISTS idx_subscription_batches_subscription ON subscription_batches(subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscription_batches_batch ON subscription_batches(batch_id);
CREATE INDEX IF NOT EXISTS idx_batch_curriculum_mapping_batch ON batch_curriculum_mapping(batch_id);
CREATE INDEX IF NOT EXISTS idx_batch_curriculum_mapping_workflow ON batch_curriculum_mapping(workflow_id);

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Sample products (if not already exist)
INSERT INTO products (name, description, type, price, duration_days, features) VALUES
('Complete Math Booster', 'Comprehensive mathematics curriculum covering all grade levels', 'booster', 299.99, 365, '{"subjects": ["mathematics"], "sessions": 50, "assessments": true}'),
('Custom Learning Plan', 'Personalized learning plan with selected topics', 'custom', 199.99, 180, '{"flexible_curriculum": true, "sessions": 30, "assessments": true}'),
('SAT Preparation', 'Comprehensive SAT test preparation program', 'preparation', 399.99, 120, '{"test_prep": true, "practice_tests": 10, "sessions": 40}')
ON CONFLICT DO NOTHING;

-- =====================================================
-- RLS POLICIES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE product_subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_topic_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE curriculum_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_prerequisites ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE batch_curriculum_mapping ENABLE ROW LEVEL SECURITY;

-- Product-Subject policies (public read, admin write)
CREATE POLICY "Public can view product subjects" ON product_subjects
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage product subjects" ON product_subjects
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Product-Topic configuration policies
CREATE POLICY "Public can view product topic configurations" ON product_topic_configurations
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage product topic configurations" ON product_topic_configurations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Curriculum assessments policies
CREATE POLICY "Public can view curriculum assessments" ON curriculum_assessments
    FOR SELECT USING (true);

CREATE POLICY "Admins and tutors can manage curriculum assessments" ON curriculum_assessments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type IN ('admin', 'tutor')
        )
    );

-- Assessment prerequisites policies
CREATE POLICY "Public can view assessment prerequisites" ON assessment_prerequisites
    FOR SELECT USING (true);

CREATE POLICY "Admins and tutors can manage assessment prerequisites" ON assessment_prerequisites
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type IN ('admin', 'tutor')
        )
    );

-- Subscription-Batch policies
CREATE POLICY "Students can view their subscription batches" ON subscription_batches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM subscriptions s
            WHERE s.id = subscription_batches.subscription_id
            AND s.student_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all subscription batches" ON subscription_batches
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Batch curriculum mapping policies
CREATE POLICY "Students can view their batch curriculum" ON batch_curriculum_mapping
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM batches b
            WHERE b.id = batch_curriculum_mapping.batch_id
            AND b.student_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all batch curriculum mappings" ON batch_curriculum_mapping
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- BATCHES TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on batches table
ALTER TABLE batches ENABLE ROW LEVEL SECURITY;

-- Students can view their own batches
CREATE POLICY "Students can view their own batches" ON batches
    FOR SELECT USING (
        student_id = auth.uid()
    );

-- Tutors can view batches where they are assigned as default tutor
CREATE POLICY "Tutors can view assigned batches" ON batches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'tutor'
            AND (
                -- Tutor is assigned as default tutor
                auth.uid() = batches.default_tutor_id
                OR
                -- Tutor has sessions with this batch (future enhancement)
                EXISTS (
                    SELECT 1 FROM sessions s
                    WHERE s.batch_id = batches.id
                    AND s.tutor_id = auth.uid()
                )
            )
        )
    );

-- Admins can view all batches
CREATE POLICY "Admins can view all batches" ON batches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Only admins can create batches
CREATE POLICY "Admins can create batches" ON batches
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Admins can update all batches
CREATE POLICY "Admins can update all batches" ON batches
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Tutors can update their assigned batches (basic access control)
CREATE POLICY "Tutors can update assigned batches" ON batches
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'tutor'
            AND auth.uid() = batches.default_tutor_id
        )
    );

-- Students can update their own batches (basic access control)
CREATE POLICY "Students can update own batches" ON batches
    FOR UPDATE USING (
        student_id = auth.uid()
    );

-- Only admins can delete batches
CREATE POLICY "Admins can delete batches" ON batches
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- BATCH UPDATE FUNCTIONS WITH FIELD RESTRICTIONS
-- =====================================================

-- Function for tutors to update batch progress (restricted fields)
CREATE OR REPLACE FUNCTION tutor_update_batch_progress(
    batch_uuid UUID,
    new_status TEXT DEFAULT NULL,
    new_remaining_sessions INTEGER DEFAULT NULL,
    new_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user is tutor and assigned to this batch
    IF NOT EXISTS (
        SELECT 1 FROM batches b
        JOIN profiles p ON p.id = auth.uid()
        WHERE b.id = batch_uuid
        AND p.user_type = 'tutor'
        AND b.default_tutor_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied. You are not assigned to this batch.';
    END IF;

    -- Validate status if provided
    IF new_status IS NOT NULL AND new_status NOT IN ('active', 'completed', 'paused', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status. Must be one of: active, completed, paused, cancelled';
    END IF;

    -- Validate remaining sessions if provided
    IF new_remaining_sessions IS NOT NULL AND new_remaining_sessions < 0 THEN
        RAISE EXCEPTION 'Remaining sessions cannot be negative';
    END IF;

    -- Update only allowed fields
    UPDATE batches
    SET
        status = COALESCE(new_status, status),
        remaining_sessions = COALESCE(new_remaining_sessions, remaining_sessions),
        end_date = COALESCE(new_end_date, end_date),
        updated_at = NOW()
    WHERE id = batch_uuid;

    RETURN TRUE;
END;
$$;

-- Function for students to update batch name (restricted fields)
CREATE OR REPLACE FUNCTION student_update_batch_name(
    batch_uuid UUID,
    new_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user is the student for this batch
    IF NOT EXISTS (
        SELECT 1 FROM batches
        WHERE id = batch_uuid AND student_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied. This is not your batch.';
    END IF;

    -- Validate name
    IF new_name IS NULL OR LENGTH(TRIM(new_name)) = 0 THEN
        RAISE EXCEPTION 'Batch name cannot be empty';
    END IF;

    -- Update only the name field
    UPDATE batches
    SET
        name = new_name,
        updated_at = NOW()
    WHERE id = batch_uuid;

    RETURN TRUE;
END;
$$;

-- Function for admins to create batches with validation
CREATE OR REPLACE FUNCTION admin_create_batch(
    student_uuid UUID,
    batch_name TEXT,
    product_type_val TEXT,
    product_name_val TEXT,
    subscription_uuid UUID DEFAULT NULL,
    workflow_uuid UUID DEFAULT NULL,
    tutor_uuid UUID DEFAULT NULL,
    total_sessions_val INTEGER DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_batch_id UUID;
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND user_type = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    -- Validate student exists
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = student_uuid AND user_type = 'student'
    ) THEN
        RAISE EXCEPTION 'Invalid student ID or student does not exist.';
    END IF;

    -- Validate tutor if provided
    IF tutor_uuid IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = tutor_uuid AND user_type = 'tutor'
    ) THEN
        RAISE EXCEPTION 'Invalid tutor ID or tutor does not exist.';
    END IF;

    -- Validate subscription if provided
    IF subscription_uuid IS NOT NULL AND NOT EXISTS (
        SELECT 1 FROM subscriptions
        WHERE id = subscription_uuid AND student_id = student_uuid
    ) THEN
        RAISE EXCEPTION 'Invalid subscription ID or subscription does not belong to this student.';
    END IF;

    -- Check if subscription already has a batch
    IF subscription_uuid IS NOT NULL AND EXISTS (
        SELECT 1 FROM batches
        WHERE subscription_id = subscription_uuid
    ) THEN
        RAISE EXCEPTION 'This subscription already has a batch created.';
    END IF;

    -- Create the batch
    INSERT INTO batches (
        name,
        student_id,
        product_type,
        product_name,
        default_tutor_id,
        status,
        subscription_id,
        workflow_id,
        total_sessions
    ) VALUES (
        batch_name,
        student_uuid,
        product_type_val,
        product_name_val,
        tutor_uuid,
        'active',
        subscription_uuid,
        workflow_uuid,
        total_sessions_val
    )
    RETURNING id INTO new_batch_id;

    RETURN new_batch_id;
END;
$$;

-- =====================================================
-- BATCH-SPECIFIC HELPER FUNCTIONS
-- =====================================================

-- Function for admins to get all batches with student and tutor info
CREATE OR REPLACE FUNCTION admin_get_all_batches()
RETURNS TABLE (
    batch_id UUID,
    batch_name TEXT,
    student_id UUID,
    student_name TEXT,
    student_email TEXT,
    tutor_id UUID,
    tutor_name TEXT,
    product_type TEXT,
    product_name TEXT,
    status TEXT,
    total_sessions INTEGER,
    remaining_sessions INTEGER,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    subscription_id UUID,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND user_type = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    RETURN QUERY
    SELECT
        b.id,
        b.name,
        b.student_id,
        CONCAT(sp.first_name, ' ', sp.last_name) as student_name,
        sp.email,
        b.default_tutor_id,
        CONCAT(tp.first_name, ' ', tp.last_name) as tutor_name,
        b.product_type,
        b.product_name,
        b.status,
        b.total_sessions,
        b.remaining_sessions,
        b.start_date,
        b.end_date,
        b.subscription_id,
        b.created_at
    FROM batches b
    LEFT JOIN profiles sp ON b.student_id = sp.id
    LEFT JOIN profiles tp ON b.default_tutor_id = tp.id
    ORDER BY b.created_at DESC;
END;
$$;

-- Function to get batches for a specific student
CREATE OR REPLACE FUNCTION get_student_batches(student_uuid UUID DEFAULT NULL)
RETURNS TABLE (
    batch_id UUID,
    batch_name TEXT,
    tutor_name TEXT,
    product_type TEXT,
    product_name TEXT,
    status TEXT,
    total_sessions INTEGER,
    remaining_sessions INTEGER,
    progress_percentage NUMERIC,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    days_remaining INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    target_student_id UUID;
BEGIN
    -- Determine target student
    IF student_uuid IS NULL THEN
        target_student_id := auth.uid();
    ELSE
        target_student_id := student_uuid;
    END IF;

    -- Check access permissions
    IF target_student_id != auth.uid() THEN
        IF NOT EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type IN ('admin', 'tutor')
        ) THEN
            RAISE EXCEPTION 'Access denied. Cannot view other students batches.';
        END IF;
    END IF;

    RETURN QUERY
    SELECT
        b.id,
        b.name,
        CONCAT(tp.first_name, ' ', tp.last_name) as tutor_name,
        b.product_type,
        b.product_name,
        b.status,
        b.total_sessions,
        b.remaining_sessions,
        CASE
            WHEN b.total_sessions > 0 THEN
                ROUND(((b.total_sessions - COALESCE(b.remaining_sessions, b.total_sessions))::NUMERIC / b.total_sessions::NUMERIC) * 100, 2)
            ELSE 0
        END as progress_percentage,
        b.start_date,
        b.end_date,
        CASE
            WHEN b.end_date IS NOT NULL THEN
                GREATEST(0, EXTRACT(DAY FROM (b.end_date - NOW()))::INTEGER)
            ELSE NULL
        END as days_remaining
    FROM batches b
    LEFT JOIN profiles tp ON b.default_tutor_id = tp.id
    WHERE b.student_id = target_student_id
    ORDER BY
        CASE b.status
            WHEN 'active' THEN 1
            WHEN 'paused' THEN 2
            WHEN 'completed' THEN 3
            WHEN 'cancelled' THEN 4
        END,
        b.created_at DESC;
END;
$$;

-- Function to get batches for a tutor
CREATE OR REPLACE FUNCTION get_tutor_batches()
RETURNS TABLE (
    batch_id UUID,
    batch_name TEXT,
    student_name TEXT,
    student_email TEXT,
    product_type TEXT,
    product_name TEXT,
    status TEXT,
    total_sessions INTEGER,
    remaining_sessions INTEGER,
    progress_percentage NUMERIC,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user is tutor
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND user_type = 'tutor'
    ) THEN
        RAISE EXCEPTION 'Access denied. Tutor privileges required.';
    END IF;

    RETURN QUERY
    SELECT
        b.id,
        b.name,
        CONCAT(sp.first_name, ' ', sp.last_name) as student_name,
        sp.email,
        b.product_type,
        b.product_name,
        b.status,
        b.total_sessions,
        b.remaining_sessions,
        CASE
            WHEN b.total_sessions > 0 THEN
                ROUND(((b.total_sessions - COALESCE(b.remaining_sessions, b.total_sessions))::NUMERIC / b.total_sessions::NUMERIC) * 100, 2)
            ELSE 0
        END as progress_percentage,
        b.start_date,
        b.end_date
    FROM batches b
    LEFT JOIN profiles sp ON b.student_id = sp.id
    WHERE b.default_tutor_id = auth.uid()
    ORDER BY
        CASE b.status
            WHEN 'active' THEN 1
            WHEN 'paused' THEN 2
            WHEN 'completed' THEN 3
            WHEN 'cancelled' THEN 4
        END,
        b.created_at DESC;
END;
$$;

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get available subjects for a product
CREATE OR REPLACE FUNCTION get_product_subjects(product_uuid UUID)
RETURNS TABLE (
    subject_id UUID,
    subject_name TEXT,
    is_default BOOLEAN,
    is_required BOOLEAN,
    estimated_sessions INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.name,
        ps.is_default,
        ps.is_required,
        ps.estimated_sessions_per_subject
    FROM subjects s
    JOIN product_subjects ps ON s.id = ps.subject_id
    WHERE ps.product_id = product_uuid
    AND ps.is_active = TRUE
    ORDER BY ps.display_order, s.name;
END;
$$;

-- Function to get available topics for a product-subject combination
CREATE OR REPLACE FUNCTION get_product_topics(product_uuid UUID, subject_uuid UUID)
RETURNS TABLE (
    topic_id UUID,
    topic_name TEXT,
    is_recommended BOOLEAN,
    estimated_sessions INTEGER,
    difficulty_level TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.id,
        t.name,
        ptc.is_recommended,
        ptc.estimated_sessions,
        ptc.difficulty_level
    FROM topics t
    JOIN product_topic_configurations ptc ON t.id = ptc.topic_id
    JOIN product_subjects ps ON ptc.product_subject_id = ps.id
    WHERE ps.product_id = product_uuid
    AND ps.subject_id = subject_uuid
    AND ptc.is_available = TRUE
    ORDER BY t.display_order, t.name;
END;
$$;

-- Function to calculate estimated sessions for curriculum selection
CREATE OR REPLACE FUNCTION calculate_curriculum_sessions(
    product_uuid UUID,
    selected_subjects JSONB DEFAULT '[]',
    selected_topics JSONB DEFAULT '[]',
    selected_subtopics JSONB DEFAULT '[]'
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_sessions INTEGER := 0;
    subject_sessions INTEGER;
    topic_sessions INTEGER;
BEGIN
    -- Calculate sessions for complete subjects
    IF jsonb_array_length(selected_subjects) > 0 THEN
        SELECT COALESCE(SUM(ps.estimated_sessions_per_subject), 0)
        INTO subject_sessions
        FROM product_subjects ps
        WHERE ps.product_id = product_uuid
        AND ps.subject_id::text = ANY(SELECT jsonb_array_elements_text(selected_subjects));

        total_sessions := total_sessions + COALESCE(subject_sessions, 0);
    END IF;

    -- Calculate sessions for selected topics
    IF jsonb_array_length(selected_topics) > 0 THEN
        SELECT COALESCE(SUM(ptc.estimated_sessions), 0)
        INTO topic_sessions
        FROM product_topic_configurations ptc
        JOIN product_subjects ps ON ptc.product_subject_id = ps.id
        WHERE ps.product_id = product_uuid
        AND ptc.topic_id::text = ANY(SELECT jsonb_array_elements_text(selected_topics));

        total_sessions := total_sessions + COALESCE(topic_sessions, 0);
    END IF;

    -- For subtopics, use a default session count (can be enhanced later)
    IF jsonb_array_length(selected_subtopics) > 0 THEN
        total_sessions := total_sessions + jsonb_array_length(selected_subtopics);
    END IF;

    RETURN GREATEST(total_sessions, 1); -- Minimum 1 session
END;
$$;

-- Function to check if a subscription already has a batch
CREATE OR REPLACE FUNCTION subscription_has_batch(subscription_uuid UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM batches
        WHERE subscription_id = subscription_uuid
    );
END;
$$;

-- Function to get subscriptions without batches (for admin batch creation)
CREATE OR REPLACE FUNCTION admin_get_subscriptions_without_batches()
RETURNS TABLE (
    subscription_id UUID,
    student_id UUID,
    student_name TEXT,
    student_email TEXT,
    product_id UUID,
    product_name TEXT,
    product_type TEXT,
    workflow_id UUID,
    current_period_end TIMESTAMP WITH TIME ZONE,
    days_remaining INTEGER,
    admin_assistance_required BOOLEAN,
    admin_assistance_notes TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND user_type = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    RETURN QUERY
    SELECT
        s.id,
        s.student_id,
        CONCAT(p.first_name, ' ', p.last_name) as student_name,
        p.email,
        s.product_id,
        pr.name as product_name,
        pr.type as product_type,
        s.workflow_id,
        s.current_period_end,
        GREATEST(0, EXTRACT(DAY FROM (s.current_period_end - NOW()))::INTEGER) as days_remaining,
        COALESCE(sw.admin_assistance_requested, false) as admin_assistance_required,
        sw.admin_assistance_message as admin_assistance_notes
    FROM subscriptions s
    LEFT JOIN profiles p ON s.student_id = p.id
    LEFT JOIN products pr ON s.product_id = pr.id
    LEFT JOIN subscription_workflows sw ON s.workflow_id = sw.id
    WHERE s.status = 'active'
    AND s.current_period_end > NOW() -- Not expired
    AND NOT EXISTS (
        SELECT 1 FROM batches b
        WHERE b.subscription_id = s.id
    )
    ORDER BY s.created_at DESC;
END;
$$;

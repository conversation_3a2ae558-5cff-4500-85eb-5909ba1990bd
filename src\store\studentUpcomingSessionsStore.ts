import { create } from "zustand";

// Define the session interface for student perspective
export interface SessionTutor {
  name: string;
  email: string;
  country: string;
}

export interface SessionDateTime {
  date: string;
  time: string;
  timestamp: Date;
}

export interface StudentSession {
  id: string;
  externalId: string;
  tutor: SessionTutor;
  subject: string;
  topic: string;
  subtopic: string;
  sessionType: string;
  dateTime: SessionDateTime;
  status: string;
  actions: string[];
}

// Define the store interface
interface StudentUpcomingSessionsState {
  // Data
  sessions: StudentSession[];
  filteredSessions: StudentSession[];
  
  // UI State
  searchTerm: string;
  visibleColumns: {
    id: boolean;
    externalId: boolean;
    tutor: boolean;
    country: boolean;
    subject: boolean;
    topic: boolean;
    subtopic: boolean;
    sessionType: boolean;
    dateTime: boolean;
    status: boolean;
    actions: boolean;
  };
  isCalendarView: boolean;
  isReminderModalOpen: boolean;
  selectedSession: StudentSession | null;
  
  // Actions
  setSearchTerm: (term: string) => void;
  setVisibleColumns: (columns: Partial<StudentUpcomingSessionsState['visibleColumns']>) => void;
  setIsCalendarView: (isCalendarView: boolean) => void;
  setIsReminderModalOpen: (isOpen: boolean) => void;
  setSelectedSession: (session: StudentSession | null) => void;
  filterSessions: () => void;
}

// Sample data for student upcoming sessions
const studentUpcomingSessionsData: StudentSession[] = [
  {
    id: "S-1092",
    externalId: "EXT-1092",
    tutor: {
      name: "Dr. Sarah Johnson",
      email: "<EMAIL>",
      country: "United States",
    },
    subject: "Mathematics",
    topic: "Addition and Subtraction",
    subtopic: "Subtraction of Two-Digit Numbers",
    sessionType: "One-on-One",
    dateTime: {
      date: "Today",
      time: "2:00 PM - 3:30 PM",
      timestamp: new Date(new Date().setHours(14, 0, 0, 0)),
    },
    status: "Scheduled",
    actions: ["Reschedule", "Join", "Cancel"],
  },
  {
    id: "S-1093",
    externalId: "EXT-1093",
    tutor: {
      name: "Prof. Michael Wilson",
      email: "<EMAIL>",
      country: "United Kingdom",
    },
    subject: "Mathematics",
    topic: "An Introduction to Multiplication",
    subtopic: "Multiplication Tables (2, 5, 10)",
    sessionType: "One-on-One",
    dateTime: {
      date: "Today",
      time: "5:30 PM - 6:30 PM",
      timestamp: new Date(new Date().setHours(17, 30, 0, 0)),
    },
    status: "Scheduled",
    actions: ["Reschedule", "Join", "Cancel"],
  },
  {
    id: "S-1095",
    externalId: "EXT-1095",
    tutor: {
      name: "Dr. Emily Chen",
      email: "<EMAIL>",
      country: "Canada",
    },
    subject: "Mathematics",
    topic: "Number Systems",
    subtopic: "Place Value and Number Patterns",
    sessionType: "Group",
    dateTime: {
      date: "Tomorrow",
      time: "10:00 AM - 12:00 PM",
      timestamp: new Date(new Date().setDate(new Date().getDate() + 1)),
    },
    status: "Pending Confirmation",
    actions: ["Reschedule", "Join"],
  },
  {
    id: "S-1098",
    externalId: "EXT-1098",
    tutor: {
      name: "Dr. James Rodriguez",
      email: "<EMAIL>",
      country: "Spain",
    },
    subject: "Mathematics",
    topic: "Addition and Subtraction",
    subtopic: "Word Problems with Addition",
    sessionType: "One-on-One",
    dateTime: {
      date: "Dec 15, 2023",
      time: "3:00 PM - 4:00 PM",
      timestamp: new Date("2023-12-15T15:00:00"),
    },
    status: "Scheduled",
    actions: ["Reschedule", "Join"],
  },
];

// Create the store
export const useStudentUpcomingSessionsStore = create<StudentUpcomingSessionsState>((set, get) => ({
  // Initial data
  sessions: studentUpcomingSessionsData,
  filteredSessions: studentUpcomingSessionsData,
  
  // Initial UI state
  searchTerm: "",
  visibleColumns: {
    id: true,
    externalId: false,
    tutor: true,
    country: true,
    subject: true,
    topic: true,
    subtopic: false,
    sessionType: true,
    dateTime: true,
    status: true,
    actions: true,
  },
  isCalendarView: false,
  isReminderModalOpen: false,
  selectedSession: null,
  
  // Actions
  setSearchTerm: (term) => {
    set({ searchTerm: term });
    get().filterSessions();
  },
  
  setVisibleColumns: (columns) => {
    set({ visibleColumns: { ...get().visibleColumns, ...columns } });
  },
  
  setIsCalendarView: (isCalendarView) => {
    set({ isCalendarView });
  },
  
  setIsReminderModalOpen: (isOpen) => {
    set({ isReminderModalOpen: isOpen });
  },
  
  setSelectedSession: (session) => {
    set({ selectedSession: session });
  },
  
  filterSessions: () => {
    const { sessions, searchTerm } = get();
    const searchLower = searchTerm.toLowerCase();

    const filtered = sessions.filter((session) => {
      return (
        session.id.toLowerCase().includes(searchLower) ||
        session.tutor.name.toLowerCase().includes(searchLower) ||
        session.subject.toLowerCase().includes(searchLower) ||
        session.topic.toLowerCase().includes(searchLower) ||
        session.subtopic.toLowerCase().includes(searchLower) ||
        session.sessionType.toLowerCase().includes(searchLower) ||
        session.status.toLowerCase().includes(searchLower)
      );
    });

    set({ filteredSessions: filtered });
  },
}));

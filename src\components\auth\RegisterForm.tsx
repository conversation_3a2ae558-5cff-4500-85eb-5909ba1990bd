import { useEffect } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Link } from "react-router-dom";
import { Checkbox } from "@/components/ui/Checkbox";
import SocialLoginButtons from "./SocialLoginButtons";
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";
import { ROUTES } from "@/routes/RouteConfig";
import { useRegisterStore } from "@/store/registerStore";
import { fieldErrors } from "@/constants/errorMessages";
import { useUserStatusCheck } from "@/hooks/useUserStatusCheck";
import { registrationSchema } from "@/services/errorHandler";
import { Loader2 } from "lucide-react";

interface RegisterFormProps {
  onSubmit: (
    email: string,
    password: string,
    userType: string
  ) => Promise<void>;
  isLoading: boolean;
  prefilledEmail?: string;
  isGuestUpgrade?: boolean;
  isRegistered?: boolean;
  existingUserType?: string;
}

// Add email checking functionality
const EmailField = ({
  value,
  onChange,
  error,
}: {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
}) => {
  const { checkUserStatus } = useUserStatusCheck();
  const { isCheckingStatus } = useRegisterStore();

  // Check email status when it changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (value && value.includes("@")) {
        checkUserStatus(value);
      }
    }, 500); // Debounce

    return () => clearTimeout(timer);
  }, [value]);

  return (
    <div>
      <label
        htmlFor="email"
        className="block text-sm font-medium text-gray-700"
      >
        Email address
      </label>
      <div className="relative">
        <input
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          value={value}
          onChange={onChange}
          placeholder="<EMAIL>"
          className={`mt-1 block w-full px-3 py-2 border ${
            error ? "border-red-300" : "border-gray-300"
          } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm`}
        />
        {isCheckingStatus && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center">
            <Loader2 className="h-4 w-4 text-gray-400 animate-spin mr-2" />
            <span className="text-xs text-gray-500">Checking email...</span>
          </div>
        )}
      </div>
      {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
    </div>
  );
};

const RegisterForm = ({
  onSubmit,
  isLoading,
  prefilledEmail = "",
  isGuestUpgrade = false,
  isRegistered = false,
  existingUserType,
}: RegisterFormProps) => {
  console.log("RegisterForm rendering", { prefilledEmail, isGuestUpgrade }); // Debug log

  const {
    formData,
    setFormData,
    userType,
    setUserType,
    acceptTerms,
    setAcceptTerms,
    formErrors,
    setFormErrors,
  } = useRegisterStore();

  // If prefilled email is provided, update the store
  useEffect(() => {
    // Only set the prefilled email once when the component mounts
    if (prefilledEmail && formData.email === "") {
      setFormData({ ...formData, email: prefilledEmail });
    }
  }, [prefilledEmail]); // Remove formData from dependencies to prevent re-running

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({ [name]: undefined });
    }
  };

  const validateForm = () => {
    try {
      console.log("Validating form with data:", {
        email: formData.email,
        password: formData.password,
        userType,
        acceptTerms,
      });

      // Use only the registration schema without the email registration check
      registrationSchema.parse({
        email: formData.email,
        password: formData.password,
        userType,
        acceptTerms,
      });
      return true;
    } catch (error: any) {
      console.log("Validation error:", error);
      const formattedErrors = error.format ? error.format() : {};
      console.log("Formatted errors:", formattedErrors);

      setFormErrors({
        email: formattedErrors.email?._errors[0],
        password: formattedErrors.password?._errors[0],
        userType: formattedErrors.userType?._errors[0],
        acceptTerms: formattedErrors.acceptTerms?._errors[0],
      });
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await onSubmit(formData.email, formData.password, userType);
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md">
      <div className="text-center mb-6">
        <h2 className="text-3xl font-extrabold text-gray-900">
          {isGuestUpgrade ? "Complete Your Registration" : "Create an Account"}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {isGuestUpgrade
            ? "Set up a password to access your account"
            : "Join our community of learners and tutors"}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <EmailField
          value={formData.email}
          onChange={handleChange}
          error={formErrors.email}
        />

        <div className="space-y-2 mb-6">
          <Label htmlFor="password">Password</Label>
          <Input
            id="password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleChange}
            placeholder="••••••••"
            className={`placeholder:text-gray-300 ${
              formErrors.password ? "border-red-300" : ""
            }`}
            required
          />
          {formErrors.password && (
            <p className="mt-2 text-sm text-red-600">{formErrors.password}</p>
          )}
          <p className="text-xs text-gray-500 mt-1">
            {fieldErrors.password.complexity}
          </p>
        </div>

        <div className="flex items-center space-x-4 mb-6">
          <Label className="whitespace-nowrap">I am a</Label>
          <RadioGroup
            value={userType}
            onValueChange={setUserType}
            className="flex space-x-8"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="student" id="student" />
              <Label htmlFor="student" className="font-normal">
                student
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="tutor" id="tutor" />
              <Label htmlFor="tutor" className="font-normal">
                tutor
              </Label>
            </div>
          </RadioGroup>
          {formErrors.userType && (
            <p className="text-sm text-red-600">{formErrors.userType}</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="terms"
            checked={acceptTerms}
            onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
          />
          <label
            htmlFor="terms"
            className="text-sm text-gray-500 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            I agree to the{" "}
            <Link
              to={ROUTES.TERMS.path}
              className="text-rfpurple-600 hover:text-rfpurple-800"
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              to={ROUTES.PRIVACY.path}
              className="text-rfpurple-600 hover:text-rfpurple-800"
            >
              Privacy Policy
            </Link>
          </label>
        </div>
        {formErrors.acceptTerms && (
          <p className="text-sm text-red-600">{formErrors.acceptTerms}</p>
        )}

        <Button
          type="submit"
          className="w-full button-gradient text-white mt-6"
          disabled={isLoading || !acceptTerms}
        >
          {isLoading ? "Creating account..." : "Create Account"}
        </Button>
      </form>

      <div className="relative my-5">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-300"></span>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">Or sign up with</span>
        </div>
      </div>

      <SocialLoginButtons isSubmitting={isLoading} userType={userType} />
    </div>
  );
};

export default RegisterForm;

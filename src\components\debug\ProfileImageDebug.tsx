import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import AuthenticatedImage from '@/components/ui/AuthenticatedImage';
import { getCachedImageUrl } from '@/utils/imageCache';

/**
 * Debug component to help troubleshoot profile image loading issues
 * This component can be temporarily added to the Dashboard to debug the profile picture issue
 */
const ProfileImageDebug: React.FC = () => {
  const { user, profileData } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    const photoUrl = profileData.profilePictureUrl ||
                     (user?.user_metadata?.profile_picture_url as string) ||
                     (user?.user_metadata?.avatar_url as string) || "";

    const cachedUrl = getCachedImageUrl(photoUrl);

    setDebugInfo({
      userId: user?.id,
      profileDataLoaded: !!profileData.firstName || !!profileData.lastName,
      photoUrl,
      cachedUrl,
      userMetadata: user?.user_metadata,
      profileData: {
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        email: profileData.email,
        profilePictureUrl: profileData.profilePictureUrl
      }
    });
  }, [user, profileData]);

  if (!user) {
    return <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">No user logged in</div>;
  }

  return (
    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg space-y-4">
      <h3 className="font-bold text-lg">Profile Image Debug Info</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold mb-2">Profile Data:</h4>
          <pre className="text-xs bg-white p-2 rounded border overflow-auto">
            {JSON.stringify(debugInfo.profileData, null, 2)}
          </pre>
        </div>
        
        <div>
          <h4 className="font-semibold mb-2">Debug Info:</h4>
          <pre className="text-xs bg-white p-2 rounded border overflow-auto">
            {JSON.stringify({
              userId: debugInfo.userId,
              profileDataLoaded: debugInfo.profileDataLoaded,
              photoUrl: debugInfo.photoUrl,
              cachedUrl: debugInfo.cachedUrl ? 'Available' : 'Not cached'
            }, null, 2)}
          </pre>
        </div>
      </div>

      {debugInfo.photoUrl && (
        <div className="space-y-2">
          <h4 className="font-semibold">Profile Image Test:</h4>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 border border-gray-300 rounded-full overflow-hidden">
              <AuthenticatedImage
                src={debugInfo.photoUrl}
                alt="Profile"
                className="w-full h-full object-cover"
                fallback={
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center text-xs">
                    No Image
                  </div>
                }
                onLoad={() => console.log('Debug: Profile image loaded successfully')}
                onError={(error) => console.log('Debug: Profile image failed to load', error)}
              />
            </div>
            <div className="text-sm">
              <p><strong>URL:</strong> {debugInfo.photoUrl}</p>
              <p><strong>Cached:</strong> {debugInfo.cachedUrl ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileImageDebug;

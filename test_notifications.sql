-- Test script to create sample notifications for testing
-- Run this after setting up the RLS policies

-- First, let's check if we have any users in the profiles table
SELECT 'Current users in profiles table:' as info;
SELECT id, user_type, first_name, last_name, email FROM profiles LIMIT 5;

-- Create some test notifications for existing users
-- Replace the user_id values with actual user IDs from your profiles table

-- Example test notifications (update user_id values as needed)
INSERT INTO notifications (user_id, title, message, type) VALUES
-- Billing notifications
('********-0000-0000-0000-************', 'Payment Successful', 'Your payment for Advanced AI Course has been processed successfully. Your subscription is now active.', 'billing'),
('********-0000-0000-0000-************', 'Subscription Expiring Soon', 'Your Premium Learning subscription will expire in 7 days. Renew now to continue your learning journey.', 'billing'),

-- Academic notifications  
('********-0000-0000-0000-************', 'Session Scheduled', 'Your session with Dr. <PERSON> has been scheduled for tomorrow at 2:00 PM.', 'academic'),
('********-0000-0000-0000-************', 'New Learning Material Available', 'New material "Advanced Neural Networks" has been added to your Machine Learning course. Check it out now!', 'academic'),
('********-0000-0000-0000-************', 'Assignment Due Soon', 'Your assignment "Linear Algebra Problem Set" is due in 2 days. Make sure to submit it on time.', 'academic'),

-- System notifications
('********-0000-0000-0000-************', 'Welcome to rfLearn!', 'Your account has been successfully verified. Welcome to our learning platform!', 'system'),
('********-0000-0000-0000-************', 'New Feature Available', 'We have added a new feature: Interactive Code Editor. Check it out and let us know what you think!', 'system');

-- Check the created notifications
SELECT 'Created test notifications:' as info;
SELECT id, title, type, is_read, created_at FROM notifications ORDER BY created_at DESC;

-- Test the notification functions
SELECT 'Testing get_unread_notification_count function:' as info;
-- SELECT get_unread_notification_count('your-user-id-here');

-- Test creating a notification using the function
SELECT 'Testing create_notification function:' as info;
-- SELECT create_notification('your-user-id-here', 'Test Notification', 'This is a test notification created using the function.', 'system');

-- Instructions for manual testing:
SELECT '
MANUAL TESTING INSTRUCTIONS:
1. Replace the user_id values above with actual user IDs from your profiles table
2. Run the setup_notifications_rls.sql script first to create the RLS policies
3. Run this script to create test notifications
4. Log in to the application and check the notification dropdown in the dashboard
5. Navigate to /student/notifications to see the full notifications page
6. Test marking notifications as read and deleting them
' as instructions;

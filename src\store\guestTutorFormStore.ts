import { create } from "zustand";
import { z } from "zod";
import {
  firstNameSchema,
  lastNameSchema,
  emailSchema,
  phoneNumberSchema,
  subjectsSchema,
  cvFileSchema,
} from "@/services/errorHandler";

// Define a generic field state type
interface FieldState {
  value: string;
  isValid: boolean;
  error: string | null;
  touched: boolean; // Add touched state
}

// Define a specific type for subjects array
interface SubjectsFieldState {
  value: string[];
  isValid: boolean;
  error: string | null;
  touched: boolean; // Add touched state
}

// Define a specific type for file upload
interface FileFieldState {
  value: string;
  isValid: boolean;
  error: string | null;
  touched: boolean; // Add touched state
}

interface GuestTutorFormState {
  // Field states
  firstName: FieldState;
  lastName: FieldState;
  email: FieldState;
  phoneNumber: FieldState;
  subjects: SubjectsFieldState;
  cvFile: FileFieldState;

  // Form-wide state
  isFormValid: boolean;
  formErrors: Record<string, string>;

  // Actions
  setField: (
    fieldName: keyof Omit<
      GuestTutorFormState,
      | "isFormValid"
      | "formErrors"
      | "setField"
      | "validateField"
      | "validateForm"
      | "setFormErrors"
      | "subjects"
      | "cvFile"
      | "setSubjects"
      | "setCvFile"
    >,
    value: string
  ) => void;
  validateField: (
    fieldName: string,
    value: string | string[]
  ) => { isValid: boolean; error: string | null };
  validateForm: () => void;
  setFormErrors: (errors: Record<string, string>) => void;

  // Individual field setters
  setFirstName: (value: string, isValid: boolean, error: string | null) => void;
  setLastName: (value: string, isValid: boolean, error: string | null) => void;
  setEmail: (value: string, isValid: boolean, error: string | null) => void;
  setPhoneNumber: (
    value: string,
    isValid: boolean,
    error: string | null
  ) => void;

  // New setters for subjects and cvFile
  setSubjects: (
    value: string[],
    isValid: boolean,
    error: string | null
  ) => void;
  setCvFile: (value: string, isValid: boolean, error: string | null) => void;

  // Add customSubjectInput to the interface
  customSubjectInput: string;

  // Add this new action
  setCustomSubjectInput: (value: string) => void;

  // Add touch field actions
  touchField: (fieldName: string) => void;
  touchAllFields: () => void;
}

export const useGuestTutorFormStore = create<GuestTutorFormState>(
  (set, get) => ({
    // Initial states
    firstName: { value: "", isValid: false, error: null, touched: false },
    lastName: { value: "", isValid: false, error: null, touched: false },
    email: { value: "", isValid: false, error: null, touched: false },
    phoneNumber: { value: "", isValid: false, error: null, touched: false },
    subjects: { value: [], isValid: false, error: null, touched: false },
    cvFile: { value: "", isValid: false, error: null, touched: false },

    isFormValid: false,
    formErrors: {},

    // Add initial state for custom subject input
    customSubjectInput: "",

    // Generic field validator
    validateField: (fieldName, value) => {
      try {
        switch (fieldName) {
          case "firstName":
            firstNameSchema.parse(value);
            return { isValid: true, error: null };
          case "lastName":
            lastNameSchema.parse(value);
            return { isValid: true, error: null };
          case "email":
            emailSchema.parse(value);
            return { isValid: true, error: null };
          case "phoneNumber":
            phoneNumberSchema.parse(value);
            return { isValid: true, error: null };
          case "subjects":
            subjectsSchema.parse(value);
            return { isValid: true, error: null };
          case "cvFile":
            cvFileSchema.parse(value);
            return { isValid: true, error: null };
          default:
            return { isValid: false, error: "Unknown field" };
        }
      } catch (error) {
        const zodError = error as z.ZodError;
        const errorMessage =
          zodError.errors[0]?.message || `Invalid ${fieldName}`;
        return { isValid: false, error: errorMessage };
      }
    },

    // Generic field setter
    setField: (fieldName, value) => {
      const { isValid, error } = get().validateField(fieldName, value);

      set((state) => ({
        [fieldName]: {
          value,
          isValid,
          error,
        },
      }));

      // Validate the form after updating the field
      get().validateForm();
    },

    // Form validator
    validateForm: () =>
      set((state) => {
        const isValid =
          state.firstName.isValid &&
          state.lastName.isValid &&
          state.email.isValid &&
          state.phoneNumber.isValid &&
          state.subjects.isValid &&
          state.cvFile.isValid;

        return { isFormValid: isValid };
      }),

    // Add setter for custom subject input
    setCustomSubjectInput: (value) => {
      set({ customSubjectInput: value });
    },

    // Add method to set form errors
    setFormErrors: (errors) => set({ formErrors: errors }),

    setFirstName: (value, isValid, error) => {
      set((state) => ({
        firstName: {
          value,
          isValid,
          error,
          touched: true,
        },
      }));
      get().validateForm();
    },

    setLastName: (value, isValid, error) => {
      set((state) => ({
        lastName: {
          value,
          isValid,
          error,
          touched: true,
        },
      }));
      get().validateForm();
    },

    setEmail: (value, isValid, error) => {
      set((state) => ({
        email: {
          value,
          isValid,
          error,
          touched: true,
        },
      }));
      get().validateForm();
    },

    setPhoneNumber: (value, isValid, error) => {
      set((state) => ({
        phoneNumber: {
          value,
          isValid,
          error,
          touched: true,
        },
      }));
      get().validateForm();
    },

    // New setters for subjects and cvFile
    setSubjects: (value, isValid, error) => {
      set((state) => ({
        subjects: {
          value,
          isValid,
          error,
          touched: true,
        },
      }));
      get().validateForm();
    },

    setCvFile: (value, isValid, error) => {
      set((state) => ({
        cvFile: {
          value,
          isValid,
          error,
          touched: true,
        },
      }));
      get().validateForm();
    },

    // Add touch field action
    touchField: (fieldName) => {
      set((state) => {
        // Handle each field type specifically to avoid type errors
        if (fieldName === "firstName") {
          return { firstName: { ...state.firstName, touched: true } };
        } else if (fieldName === "lastName") {
          return { lastName: { ...state.lastName, touched: true } };
        } else if (fieldName === "email") {
          return { email: { ...state.email, touched: true } };
        } else if (fieldName === "phoneNumber") {
          return { phoneNumber: { ...state.phoneNumber, touched: true } };
        } else if (fieldName === "subjects") {
          return { subjects: { ...state.subjects, touched: true } };
        } else if (fieldName === "cvFile") {
          return { cvFile: { ...state.cvFile, touched: true } };
        }
        return {};
      });
    },

    // Add touch all fields action
    touchAllFields: () => {
      set((state) => ({
        firstName: { ...state.firstName, touched: true },
        lastName: { ...state.lastName, touched: true },
        email: { ...state.email, touched: true },
        phoneNumber: { ...state.phoneNumber, touched: true },
        subjects: { ...state.subjects, touched: true },
        cvFile: { ...state.cvFile, touched: true },
      }));
    },
  })
);

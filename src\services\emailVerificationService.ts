import { supabase } from "@/lib/supabaseClient";
import { isDisposableEmail } from "@/api/disposableCheck";
import { handleEmailVerificationError } from "@/services/errorHandler";
import config from "@/api/config";

// Configuration object that maps sources to specific behaviors and messages
export const verificationConfig = {
  default: {
    redirectPath: "/email-confirmation",
    successMessage: "Your email has been successfully verified!",
    pendingMessage:
      "Please check your inbox and follow the link to verify your email",
    failureMessage: "Failed to send verification email. Please try again.",
    countdownSeconds: 60,
    redirectAfterSend: "/confirm-email",
  },
  "become-tutor": {
    redirectPath: "/email-confirmation",
    successMessage:
      "Your email has been verified! You can now complete your tutor application.",
    pendingMessage:
      "Please check your inbox and follow the link to verify your email address",
    failureMessage:
      "Failed to send verification email. Please try again or use a different email.",
    countdownSeconds: 60,
    redirectAfterSend: "/confirm-email",
  },
  signup: {
    redirectPath: "/email-confirmation",
    successMessage:
      "Your email has been verified! You can now log in to your account.",
    pendingMessage: "Please check your inbox for a verification link",
    failureMessage:
      "Could not send verification email. Please check your email address.",
    countdownSeconds: 60,
    redirectAfterSend: "/confirm-email",
  },
  "password-reset": {
    redirectPath: "/reset-password",
    successMessage:
      "Your email has been verified. You can now reset your password.",
    pendingMessage: "Please check your inbox for a password reset link",
    failureMessage:
      "Could not send password reset email. Please try again later.",
    countdownSeconds: 120,
    redirectAfterSend: "/check-email",
  },
};

/**
 * Gets configuration for a specific verification source
 * @param source The source identifier
 * @returns Configuration object for the specified source
 */
export const getVerificationConfig = (source: string = "default") => {
  return verificationConfig[source] || verificationConfig["default"];
};

/**
 * Sends an email verification OTP to the provided email address
 * @param email The email address to verify
 * @param source The source identifier for the verification (e.g., 'become-tutor', 'signup', etc.)
 * @param redirectUrl Optional custom redirect URL (overrides the source-based redirect)
 * @returns Object containing success status and error if any
 */
export const sendVerificationEmail = async (
  email: string,
  source: string = "default",
  redirectUrl?: string
) => {
  try {
    // Validate email format (basic check)
    if (!email || !email.includes("@")) {
      throw new Error("Please enter a valid email address");
    }

    // Check if email is disposable (optional security measure)
    if (await isDisposableEmail(email)) {
      throw new Error("Please use a valid non-disposable email address");
    }

    // Get configuration for this source
    const sourceConfig = getVerificationConfig(source);

    // Use Supabase's OTP sign-in method
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo:
          redirectUrl ||
          `${config.appUrl}${sourceConfig.redirectPath}?source=${source}`,
      },
    });

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: sourceConfig.pendingMessage,
      countdownSeconds: sourceConfig.countdownSeconds,
      redirectPath: sourceConfig.redirectAfterSend,
    };
  } catch (error) {
    console.error("Error sending verification email:", error);
    const sourceConfig = getVerificationConfig(source);
    return {
      success: false,
      error: handleEmailVerificationError(error) || sourceConfig.failureMessage,
    };
  }
};

/**
 * Checks if the user's email is verified
 * @param source The source identifier for contextual messages
 * @returns Object containing verification status and user email if available
 */
export const checkEmailVerification = async (source: string = "default") => {
  try {
    const sourceConfig = getVerificationConfig(source);

    // First check if we have a verification flag in localStorage
    const emailConfirmed = localStorage.getItem("emailConfirmed") === "true";
    const confirmationMessage = localStorage.getItem("confirmationMessage");

    if (emailConfirmed) {
      // Clear the flag to prevent issues on future visits
      localStorage.removeItem("emailConfirmed");
      localStorage.removeItem("confirmationMessage");

      return {
        verified: true,
        message: confirmationMessage || sourceConfig.successMessage,
      };
    }

    // If not in localStorage, check if the user has a verified email in their session
    const { data } = await supabase.auth.getSession();
    if (data.session?.user?.email_confirmed_at) {
      return {
        verified: true,
        email: data.session.user.email || "",
        message: sourceConfig.successMessage,
      };
    } else if (data.session) {
      // User is logged in but email not confirmed
      return {
        verified: false,
        email: data.session.user.email || "",
      };
    }

    return { verified: false };
  } catch (error) {
    console.error("Error checking email verification:", error);
    return { verified: false, error };
  }
};

/**
 * Sets up a verification countdown timer
 * @param initialSeconds Initial countdown time in seconds
 * @param onTick Callback function called on each tick with remaining seconds
 * @param onComplete Callback function called when countdown reaches zero
 * @returns Object with start and stop functions
 */
export const setupVerificationCountdown = (
  initialSeconds: number,
  onTick: (seconds: number) => void,
  onComplete: () => void
) => {
  let timerId: number | null = null;

  const start = () => {
    let seconds = initialSeconds;
    onTick(seconds);

    timerId = window.setInterval(() => {
      seconds -= 1;
      onTick(seconds);

      if (seconds <= 0) {
        stop();
        onComplete();
      }
    }, 1000);
  };

  const stop = () => {
    if (timerId !== null) {
      clearInterval(timerId);
      timerId = null;
    }
  };

  return { start, stop };
};

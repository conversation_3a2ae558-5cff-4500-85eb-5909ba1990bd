import { create } from "zustand";
import { persist } from "zustand/middleware";

interface SidebarState {
  isCollapsed: boolean;
  expandedSections: number[];
  toggleCollapse: () => void;
  setCollapsed: (collapsed: boolean) => void;
  toggleSection: (sectionIndex: number) => void;
  isSectionExpanded: (sectionIndex: number) => boolean;
}

export const useSidebarStore = create<SidebarState>()(
  persist(
    (set, get) => ({
      isCollapsed: false,
      expandedSections: [],
      toggleCollapse: () => set((state) => ({ isCollapsed: !state.isCollapsed })),
      setCollapsed: (collapsed) => set({ isCollapsed: collapsed }),
      toggleSection: (sectionIndex) => set((state) => {
        const isCurrentlyExpanded = state.expandedSections.includes(sectionIndex);
        return {
          expandedSections: isCurrentlyExpanded
            ? state.expandedSections.filter(i => i !== sectionIndex)
            : [...state.expandedSections, sectionIndex]
        };
      }),
      isSectionExpanded: (sectionIndex) => get().expandedSections.includes(sectionIndex),
    }),
    {
      name: "sidebar-storage",
    }
  )
);


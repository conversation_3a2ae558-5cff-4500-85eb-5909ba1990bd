import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
  <PERSON>,
  <PERSON><PERSON>he<PERSON>,
  <PERSON>geChe<PERSON>,
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  User,
  Award,
  ThumbsUp,
  Eye,
} from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Progress } from "@/components/ui/Progress";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/Avatar";
import TutorProfileModal from "@/components/tutor/ProfileModal";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { useProfileData } from "@/hooks/useProfileData";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";

// No longer need the UI state store as TutorPageLayout handles the sidebar

// --- Sample Data (same as before) ---
const sampleTutor = {
  id: "tutor-123",
  firstName: "<PERSON>. <PERSON>",
  lastName: "Doe",
  tagline: "PhD SoftwareDev - Stats, Math, CompSci, Writing",
  bio: `I'm a passionate educator with 10+ years of experience helping students excel in programming, statistics, and writing. My approach is patient, tailored, and focused on building confidence. I hold a PhD in Computer Science from MIT and have worked with students of all backgrounds. Let's unlock your potential together!`,
  hourlyRate: 80,
  totalHours: 1200,
  rating: 4.9,
  reviewCount: 87,
  profilePictureUrl: "/images/tutors/jane-doe.jpg",
  specializations: ["Python", "C++", "Data Science", "Statistics", "Writing"],
  achievements: [
    "Published 15+ research papers in top AI conferences",
    "Developed ML algorithms used by 3 Fortune 500 companies",
    "Google AI Research Award 2021",
    "Stanford Excellence in Teaching Award",
  ],
  education: [
    { degree: "PhD Computer Science", institution: "MIT", year: "2017" },
    {
      degree: "MSc Statistics",
      institution: "Stanford University",
      year: "2013",
    },
    { degree: "BSc Mathematics", institution: "UCLA", year: "2011" },
  ],
  subjects: ["Python", "C++", "Data Science", "Statistics", "Writing"],
  availability: [
    {
      day: "Monday",
      slots: [
        { start: "09:00", end: "12:00" },
        { start: "14:00", end: "17:00" },
      ],
    },
    { day: "Tuesday", slots: [{ start: "10:00", end: "13:00" }] },
    {
      day: "Wednesday",
      slots: [
        { start: "09:00", end: "12:00" },
        { start: "15:00", end: "18:00" },
      ],
    },
    { day: "Thursday", slots: [{ start: "11:00", end: "16:00" }] },
    { day: "Friday", slots: [{ start: "09:00", end: "12:00" }] },
    { day: "Saturday", slots: [] },
    { day: "Sunday", slots: [] },
  ],
  policies: {
    cancellation:
      "Free cancellation up to 24 hours before session. 50% fee within 24 hours.",
    backgroundCheck: true,
    responseTime: "Typically responds within 2 hours",
  },
  reviews: [
    {
      id: "r1",
      studentName: "Alex S.",
      rating: 5,
      comment:
        "Dr. Jane is the best tutor I've ever had! She explains complex topics so clearly.",
      date: "2024-05-10",
      tutorResponse: "Thank you, Alex! It was a pleasure working with you.",
    },
    {
      id: "r2",
      studentName: "Maria L.",
      rating: 5,
      comment:
        "Very patient and knowledgeable. Highly recommend for anyone struggling with stats.",
      date: "2024-04-22",
      tutorResponse: "Glad I could help, Maria!",
    },
    {
      id: "r3",
      studentName: "Ben T.",
      rating: 4,
      comment: "Great sessions, but sometimes hard to book last minute.",
      date: "2024-03-15",
      tutorResponse:
        "Thanks for the feedback, Ben! I'll try to open more slots.",
    },
    // ...more reviews
  ],
  expertiseExamples: [
    {
      id: "q1",
      title: "How to implement a binary search in Python?",
      link: "#",
    },
    {
      id: "q2",
      title: "Best practices for statistical hypothesis testing",
      link: "#",
    },
    { id: "q3", title: "Tips for writing technical essays", link: "#" },
  ],
};

// --- Components (same as before, except AboutSection) ---

const ProfileHeader = ({ tutor, onPreviewClick }: { tutor: any; onPreviewClick: (tutor: any) => void }) => (
  <section className="flex flex-col md:flex-row items-center md:items-start gap-6 p-6 bg-white rounded-lg shadow relative group">
    {/* Preview button with tooltip */}
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={() => onPreviewClick(tutor)}
            className="absolute top-4 right-4 p-2 rounded-full bg-rfpurple-100 text-rfpurple-600 hover:bg-rfpurple-200 transition-colors"
            aria-label="Preview profile"
          >
            <Eye size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          This is what students will see on your public profile.
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>

    <Avatar className="h-16 w-16 cursor-pointer hover:ring-2 hover:ring-rfpurple-300 transition-all" onClick={() => onPreviewClick(tutor)}>
      <AvatarImage src={tutor.profilePictureUrl} alt="Profile picture" />
      <AvatarFallback>{tutor.firstName?.charAt(0) || "U"}</AvatarFallback>
    </Avatar>
    <div className="flex-1 flex flex-col gap-2">
      <h1
        className="text-2xl font-bold text-gray-900 cursor-pointer hover:text-rfpurple-600 transition-colors"
        onClick={() => onPreviewClick(tutor)}
      >
        {tutor.firstName} {tutor.lastName}
      </h1>
      <div className="text-rfpurple-600 font-medium">{tutor.tagline}</div>
      <div className="flex items-center gap-3 mt-1">
        <span className="flex items-center gap-1 text-yellow-500 font-semibold">
          <Star size={18} fill="#facc15" /> {tutor.rating}
        </span>
        <span className="text-gray-500 text-sm">
          ({tutor.reviewCount} reviews)
        </span>
        <span className="ml-4 flex items-center gap-1 text-gray-600">
          <Clock size={16} /> {tutor.totalHours} hrs
        </span>
      </div>
      <div className="flex items-center gap-2 mt-2">
        <BadgeCheck
          size={18}
          className="text-green-500"
          aria-label="Good Fit Guarantee"
        />
        <span className="text-xs text-green-700 font-semibold">
          Good Fit Guarantee
        </span>
        <ShieldCheck
          size={18}
          className="text-blue-500 ml-3"
          aria-label="Background Checked"
        />
        <span className="text-xs text-blue-700 font-semibold">
          {tutor.policies.backgroundCheck
            ? "Background Checked"
            : "No Background Check"}
        </span>
      </div>
    </div>
    <div className="flex flex-col items-center gap-2 min-w-[160px]">
      <div className="text-xl font-bold text-rfpurple-600">
        ${tutor.hourlyRate}/hr
      </div>
      <Button variant="default" className="w-full">
        <MessageSquare size={18} className="mr-2" /> Contact
      </Button>
    </div>
  </section>
);

// --- Achievements Section ---
const AchievementsSection = ({ achievements }) => {
  return (
    <Card className="p-5 mb-4">
      <h2 className="text-lg font-bold mb-2 flex items-center">
        <Award size={18} className="text-amber-500 mr-2" />
        Achievements
      </h2>
      <ul className="space-y-2">
        {achievements.map((achievement, idx) => (
          <li key={idx} className="flex items-start">
            <ThumbsUp size={16} className="text-amber-500 mr-2 mt-1" />
            <span className="text-gray-700">{achievement}</span>
          </li>
        ))}
      </ul>
    </Card>
  );
};

const EducationSection = ({ education }) => (
  <Card className="p-5 mb-4">
    <h2 className="text-lg font-bold mb-2">Education</h2>
    <ul className="list-disc pl-5">
      {education.map((ed, idx) => (
        <li key={idx} className="mb-1">
          <span className="font-semibold">{ed.degree}</span>, {ed.institution}{" "}
          <span className="text-gray-400">({ed.year})</span>
        </li>
      ))}
    </ul>
  </Card>
);

const PoliciesSection = ({ policies, hourlyRate }) => (
  <Card className="p-5 mb-4">
    <h2 className="text-lg font-bold mb-2">Policies</h2>
    <div className="flex flex-col gap-2">
      <div>
        <span className="font-semibold">Hourly Rate:</span> ${hourlyRate}/hr
      </div>
      <div>
        <span className="font-semibold">Cancellation:</span>{" "}
        {policies.cancellation}
      </div>
      <div className="flex items-center gap-2">
        <span className="font-semibold">Background Check:</span>
        {policies.backgroundCheck ? (
          <CheckCircle size={16} className="text-green-500" />
        ) : (
          <XCircle size={16} className="text-red-500" />
        )}
      </div>
      <div>
        <span className="font-semibold">Response Time:</span>{" "}
        {policies.responseTime}
      </div>
    </div>
  </Card>
);

const AvailabilitySection = ({ availability }) => (
  <Card className="p-5 mb-4">
    <h2 className="text-lg font-bold mb-2">Availability</h2>
    <div className="overflow-x-auto">
      <table className="min-w-full text-sm border">
        <thead>
          <tr>
            <th className="p-2 border-b text-left">Day</th>
            <th className="p-2 border-b text-left">Available Slots</th>
          </tr>
        </thead>
        <tbody>
          {availability.map((day, idx) => (
            <tr key={idx} className={idx % 2 === 0 ? "bg-gray-50" : ""}>
              <td className="p-2 border-b font-semibold">{day.day}</td>
              <td className="p-2 border-b">
                {day.slots.length === 0 ? (
                  <span className="text-gray-400">Not available</span>
                ) : (
                  day.slots.map((slot, i) => (
                    <span
                      key={i}
                      className="inline-block bg-rfpurple-100 text-rfpurple-700 rounded px-2 py-1 mr-2 mb-1"
                    >
                      {slot.start} - {slot.end}
                    </span>
                  ))
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </Card>
);

const SubjectsSection = ({ subjects }) => (
  <Card className="p-5 mb-4">
    <h2 className="text-lg font-bold mb-2">Approved Subjects</h2>
    <div className="flex flex-wrap gap-2">
      {subjects.map((subj, idx) => (
        <span
          key={idx}
          className="bg-rfpurple-100 text-rfpurple-700 px-3 py-1 rounded-full text-sm font-medium"
        >
          {subj}
        </span>
      ))}
    </div>
  </Card>
);

const ExpertiseExamplesSection = ({ examples }) => (
  <Card className="p-5 mb-4">
    <h2 className="text-lg font-bold mb-2">Examples of Expertise</h2>
    <ul className="list-disc pl-5">
      {examples.map((ex) => (
        <li key={ex.id}>
          <a
            href={ex.link}
            className="text-rfpurple-600 underline hover:text-rfpurple-800"
          >
            {ex.title}
          </a>
        </li>
      ))}
    </ul>
  </Card>
);

const RatingsBreakdown = ({ reviews }) => {
  const total = reviews.length;
  const counts = [5, 4, 3, 2, 1].map(
    (star) => reviews.filter((r) => r.rating === star).length
  );
  return (
    <div className="mb-4">
      <h3 className="font-semibold mb-2">Review Breakdown</h3>
      {counts.map((count, idx) => (
        <div key={idx} className="flex items-center gap-2 mb-1">
          <span className="w-8 text-right">
            {5 - idx} <Star size={14} className="inline text-yellow-500" />
          </span>
          <Progress
            value={total ? (count / total) * 100 : 0}
            className="flex-1 h-2 bg-gray-200"
          />
          <span className="w-8 text-gray-600 text-xs">{count}</span>
        </div>
      ))}
    </div>
  );
};

const ReviewsSection = ({ reviews }) => (
  <Card className="p-5 mb-4">
    <h2 className="text-lg font-bold mb-2">Ratings & Reviews</h2>
    <RatingsBreakdown reviews={reviews} />
    <div className="divide-y">
      {reviews.slice(0, 5).map((review) => (
        <div key={review.id} className="py-3">
          <div className="flex items-center gap-2 mb-1">
            <User size={16} className="text-gray-400" />
            <span className="font-semibold">{review.studentName}</span>
            <span className="flex items-center gap-1 text-yellow-500 ml-2">
              {[...Array(review.rating)].map((_, i) => (
                <Star key={i} size={14} fill="#facc15" />
              ))}
            </span>
            <span className="text-xs text-gray-400 ml-2">{review.date}</span>
          </div>
          <div className="text-gray-700 mb-1">{review.comment}</div>
          {review.tutorResponse && (
            <div className="text-xs text-gray-600 border-l-2 border-rfpurple-200 pl-3 mt-1">
              <span className="font-semibold text-rfpurple-600">
                Tutor Response:
              </span>{" "}
              {review.tutorResponse}
            </div>
          )}
        </div>
      ))}
      {reviews.length > 5 && (
        <div className="text-center mt-3">
          <Button variant="ghost" className="text-rfpurple-600">
            View all reviews
          </Button>
        </div>
      )}
    </div>
  </Card>
);

const TutorProfilePage: React.FC = () => {
  // In real usage, fetch data from Zustand store or API
  const tutor = sampleTutor;
  const location = useLocation();

  // Get profile data for the UserNavbar
  const profileData = useProfileData();

  // State for profile modal
  const [selectedTutor, setSelectedTutor] = useState(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [hasTriedToOpenModal, setHasTriedToOpenModal] = useState(false);

  // Handle opening the tutor profile modal
  const handleOpenTutorProfile = (tutorData: any) => {
    // Make sure we're passing all the necessary data to the modal
    // Map the tutor data to match the expected format in the modal
    const modalTutorData = {
      ...tutorData,
      name: `${tutorData.firstName} ${tutorData.lastName}`,
      photo: tutorData.profilePictureUrl,
      rate: tutorData.hourlyRate,
      specialties: tutorData.specializations,
      // Add any other mappings needed
    };

    setSelectedTutor(modalTutorData);
    setShowProfileModal(true);
    setHasTriedToOpenModal(true);
  };

  // Check if we're navigating to this page directly (not already on it)
  // If so, open the modal automatically
  useEffect(() => {
    // If we're on the profile page and haven't tried to open the modal yet, open it
    if (location.pathname === "/tutor/profile" && !hasTriedToOpenModal && tutor) {
      // Mark that we've tried to open the modal
      setHasTriedToOpenModal(true);

      // Small delay to ensure the component is fully mounted
      const timer = setTimeout(() => {
        handleOpenTutorProfile(tutor);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [location.pathname, hasTriedToOpenModal, tutor]);

  // Reset the hasTriedToOpenModal flag when the user navigates away from the page
  useEffect(() => {
    return () => {
      setHasTriedToOpenModal(false);
    };
  }, []);

  return (
    <TutorPageLayout
      title="My Profile"
      description="View and manage your tutor profile information."
    >
      <div>
        <ProfileHeader tutor={tutor} onPreviewClick={handleOpenTutorProfile} />
        <div className="grid md:grid-cols-2 gap-6 mt-6">
          <div>
            <AchievementsSection achievements={tutor.achievements} />
            <EducationSection education={tutor.education} />
            <PoliciesSection
              policies={tutor.policies}
              hourlyRate={tutor.hourlyRate}
            />
            <AvailabilitySection availability={tutor.availability} />
          </div>
          <div>
            <SubjectsSection subjects={tutor.subjects} />
            <ExpertiseExamplesSection examples={tutor.expertiseExamples} />
            <ReviewsSection reviews={tutor.reviews} />
          </div>
        </div>
      </div>

      {/* Add the TutorProfileModal component */}
      {selectedTutor && (
        <TutorProfileModal
          tutor={selectedTutor}
          isOpen={showProfileModal}
          onClose={() => {
            // Close the modal
            setShowProfileModal(false);
            // Set a small timeout to prevent the useEffect from immediately reopening it
            setTimeout(() => {
              setHasTriedToOpenModal(true);
            }, 50);
          }}
        />
      )}
    </TutorPageLayout>
  );
};

export default TutorProfilePage;

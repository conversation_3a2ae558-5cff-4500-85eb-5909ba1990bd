// src/components/student/dashboard/BillingSummary.tsx
import React from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { ArrowRight, Calendar, Clock, CreditCard } from "lucide-react";
import { Subscription } from "@/store/billingStore";
import { format } from "date-fns";
import { Progress } from "@/components/ui/Progress";
import { useNavigate } from "react-router-dom";

interface BillingSummaryProps {
  activeSubscriptions: Subscription[];
  recentInvoices: any[];
}

const BillingSummary: React.FC<BillingSummaryProps> = ({ activeSubscriptions, recentInvoices }) => {
  const navigate = useNavigate();

  // Format date
  const formatDate = (date: Date) => {
    return format(date, "MMM d, yyyy");
  };

  // Get the most recent subscription
  const mostRecentSubscription = activeSubscriptions.length > 0
    ? activeSubscriptions.sort((a, b) => new Date(b.start_date).getTime() - new Date(a.start_date).getTime())[0]
    : null;

  // Calculate progress percentage for the most recent subscription
  const calculateProgress = (subscription: Subscription) => {
    const startDate = new Date(subscription.start_date);
    const endDate = new Date(subscription.end_date);
    const now = new Date();

    const totalDuration = endDate.getTime() - startDate.getTime();
    const elapsed = now.getTime() - startDate.getTime();

    return Math.min(100, Math.max(0, Math.floor((elapsed / totalDuration) * 100)));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing Summary</CardTitle>
        <CardDescription>
          Your subscription and billing information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {mostRecentSubscription ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">{mostRecentSubscription.product_name}</h3>
                <p className="text-sm text-gray-500">Active Subscription</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('/student/subscriptions')}
              >
                Manage
              </Button>
            </div>

            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="text-sm">
                  {formatDate(mostRecentSubscription.start_date)} - {formatDate(mostRecentSubscription.end_date)}
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="text-sm">
                  {mostRecentSubscription.days_remaining} days remaining
                </p>
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Subscription Progress</span>
                <span>{calculateProgress(mostRecentSubscription)}%</span>
              </div>
              <Progress value={calculateProgress(mostRecentSubscription)} className="h-2" />
            </div>
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-gray-500 mb-2">No active subscriptions</p>
            <Button
              size="sm"
              onClick={() => navigate('/student/products')}
            >
              Browse Products
            </Button>
          </div>
        )}

        {/* Recent Invoices */}
        {recentInvoices.length > 0 && (
          <div className="mt-6">
            <h3 className="font-medium mb-2">Recent Invoices</h3>
            <div className="space-y-2">
              {recentInvoices.slice(0, 3).map((invoice) => (
                <div key={invoice.id} className="flex justify-between items-center p-2 bg-gray-50 rounded-md">
                  <div className="flex items-center">
                    <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">
                        Invoice #{invoice.id.substring(0, 8)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {format(new Date(invoice.created_at), "MMM d, yyyy")}
                      </p>
                    </div>
                  </div>
                  <p className="font-medium">${invoice.amount.toFixed(2)}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => navigate('/student/billing-history')}
        >
          View Billing History
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default BillingSummary;
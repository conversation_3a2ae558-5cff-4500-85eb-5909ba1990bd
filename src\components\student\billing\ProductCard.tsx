// src/components/student/billing/ProductCard.tsx
import React from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Check, Info } from "lucide-react";
import { Product } from "@/store/billingStore";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";

interface ProductCardProps {
  product: Product;
  isSubscribed: boolean;
  onPurchase: (productId: string) => void;
  isProcessing?: boolean;
  purchaseStep?: 'idle' | 'processing' | 'success' | 'error';
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  isSubscribed,
  onPurchase,
  isProcessing = false,
  purchaseStep = 'idle'
}) => {
  // Format features for display
  const formatFeatureKey = (key: string) => {
    return key.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <Card className="flex flex-col h-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle>{product.name}</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 text-gray-400 cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">{product.description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <CardDescription>{product.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="mb-6">
          <p className="text-3xl font-bold">${product.price.toFixed(2)}</p>
          <p className="text-sm text-gray-500">Duration: {product.duration_days} days</p>
        </div>

        <div className="space-y-2">
          {Object.entries(product.features || {}).map(([key, value]) => (
            <div key={key} className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
              <span className="text-sm">
                {formatFeatureKey(key)}: {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        {isSubscribed ? (
          <Button disabled className="w-full bg-green-600 hover:bg-green-700">
            ✅ Already Subscribed
          </Button>
        ) : (
          <Button
            className="w-full min-h-[44px]"
            onClick={() => onPurchase(product.id)}
            disabled={isProcessing || purchaseStep === 'processing'}
          >
            {isProcessing && purchaseStep === 'processing' ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Processing...
              </div>
            ) : purchaseStep === 'success' ? (
              <div className="flex items-center gap-2">
                <span>✅</span>
                Purchased!
              </div>
            ) : purchaseStep === 'error' ? (
              <div className="flex items-center gap-2">
                <span>⚠️</span>
                Try Again
              </div>
            ) : (
              'Purchase'
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default ProductCard;
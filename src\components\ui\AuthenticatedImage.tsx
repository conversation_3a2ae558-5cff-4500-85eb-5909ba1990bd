import React, { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { useAuth } from '@/context/AuthContext';
import { getSupportedBucketsForUserType, isStorageUrl, parseStorageUrl } from '@/utils/storageUtils';

interface AuthenticatedImageProps {
  src: string;
  alt: string;
  className?: string;
  fallback?: React.ReactNode;
  onLoad?: () => void;
  onError?: (error: any) => void;
}

// Cache for signed URLs with expiration tracking
interface CachedUrl {
  signedUrl: string;
  expiresAt: number;
}

const urlCache = new Map<string, CachedUrl>();

// Clean up expired URLs from cache periodically
const cleanupCache = () => {
  const now = Date.now();
  for (const [key, value] of urlCache.entries()) {
    if (value.expiresAt <= now) {
      urlCache.delete(key);
    }
  }
};

// Run cleanup every 10 minutes
setInterval(cleanupCache, 10 * 60 * 1000);

const AuthenticatedImage: React.FC<AuthenticatedImageProps> = ({
  src,
  alt,
  className,
  fallback,
  onLoad,
  onError
}) => {
  const { userType, user, session } = useAuth();
  const [signedUrl, setSignedUrl] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use refs to track the current request and prevent race conditions
  const currentSrcRef = useRef<string>('');
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastSuccessfulUrlRef = useRef<string>('');



  // Memoized function to get signed URL
  const getSignedUrl = useCallback(async (srcUrl: string) => {
    // Skip if this is the same URL we already successfully loaded
    if (srcUrl === lastSuccessfulUrlRef.current && signedUrl) {
      setLoading(false);
      return;
    }

    // Abort any previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    // Update current src ref
    currentSrcRef.current = srcUrl;

    if (!srcUrl) {
      setLoading(false);
      return;
    }

    // If the src is not a storage URL (e.g., external URL or data URL), use it directly
    if (!isStorageUrl(srcUrl)) {
      console.log('Using direct URL (not storage):', srcUrl);
      if (signal.aborted) return;
      setSignedUrl(srcUrl);
      lastSuccessfulUrlRef.current = srcUrl; // Track successful load
      setLoading(false);
      return;
    }

    // Only the avatars bucket should have public access - all others are private
    const publicBuckets = ['avatars'];
    const privateBuckets = ['student-uploads', 'tutor-uploads', 'admin-uploads'];
    const url = new URL(srcUrl);
    const pathParts = url.pathname.split('/');

    // Check if this URL contains a truly public bucket
    const isPublicBucket = publicBuckets.some(bucket =>
      pathParts.includes(bucket)
    );

    if (isPublicBucket) {
      console.log('Using direct URL for public bucket:', srcUrl);
      if (signal.aborted) return;
      setSignedUrl(srcUrl);
      lastSuccessfulUrlRef.current = srcUrl; // Track successful load
      setLoading(false);
      return;
    }

    // Check if this is a private bucket that requires authentication
    const isPrivateBucket = privateBuckets.some(bucket =>
      pathParts.includes(bucket)
    );

    if (isPrivateBucket && (!user || !session)) {
      console.log('Private bucket requires authentication, but user not authenticated');
      if (signal.aborted) return;
      setError('Authentication required');
      setLoading(false);
      return;
    }

      // For private buckets with authenticated users, we need signed URLs
      // This ensures proper access control while allowing authenticated users
      // to view profile pictures of other users



    try {
      // Check cache first
      const cached = urlCache.get(srcUrl);
      const now = Date.now();

      // Use cached URL if it exists and hasn't expired (with 5 minute buffer)
      if (cached && cached.expiresAt > now + 5 * 60 * 1000) {
        console.log('✅ Using cached signed URL for:', srcUrl);
        if (signal.aborted) return;
        setSignedUrl(cached.signedUrl);
        lastSuccessfulUrlRef.current = srcUrl; // Track successful load
        setLoading(false);
        return;
      }

        // Extract the path from the full URL
        // URL format: https://[project].supabase.co/storage/v1/object/public/bucket/path
        // or: https://[project].supabase.co/storage/v1/object/sign/bucket/path
        const url = new URL(src);
        const pathParts = url.pathname.split('/');

        // Find the bucket and path - use user-type specific buckets
        const supportedBuckets = getSupportedBucketsForUserType(userType);
        let bucketIndex = -1;

        for (const bucket of supportedBuckets) {
          bucketIndex = pathParts.findIndex(part => part === bucket);
          if (bucketIndex !== -1) {
            break;
          }
        }

        if (bucketIndex === -1) {
          console.warn(`No supported bucket found in URL for user type ${userType}. URL: ${src}`);
          console.warn(`Supported buckets: ${supportedBuckets.join(', ')}`);
          // Fallback to direct URL usage instead of failing
          console.log('Falling back to direct URL usage');
          setSignedUrl(src);
          lastSuccessfulUrlRef.current = src; // Track successful load
          setLoading(false);
          return;
        }

        const bucket = pathParts[bucketIndex];
        const filePath = pathParts.slice(bucketIndex + 1).join('/');

        console.log('Getting signed URL for:', { bucket, filePath });

        // Get a signed URL that includes authentication
        const { data, error: signError } = await supabase.storage
          .from(bucket)
          .createSignedUrl(filePath, 3600); // 1 hour expiry

        if (signError) {
          console.error('Supabase storage error:', signError);

          // Handle specific error types
          if (signError.message?.includes('not found')) {
            console.log('File not found in storage');
            setError('File not found');
          } else if (signError.message?.includes('unauthorized') || signError.message?.includes('permission')) {
            console.log('Unauthorized access to storage');
            setError('Access denied');
          } else {
            console.log('Storage error:', signError.message);
            setError('Storage error');
          }

          setLoading(false);
          return;
        }

        if (data?.signedUrl) {
          // Cache the signed URL with expiration time
          const expiresAt = now + 3600 * 1000; // 1 hour from now
          urlCache.set(src, {
            signedUrl: data.signedUrl,
            expiresAt
          });

          setSignedUrl(data.signedUrl);
          lastSuccessfulUrlRef.current = src; // Track successful load
          console.log('✅ Signed URL created and cached successfully');
        } else {
          throw new Error('No signed URL returned');
        }
      } catch (err) {
        console.error('❌ Error creating signed URL:', err);
        setError(err instanceof Error ? err.message : 'Failed to load image');
        onError?.(err);
        setLoading(false);
      }
    }, [userType, user?.id, session?.access_token]); // Only depend on stable values

    useEffect(() => {
      getSignedUrl(src);
    }, [src, getSignedUrl]);

  const handleImageLoad = () => {
    console.log('✅ Authenticated image loaded successfully');
    onLoad?.();
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error('❌ Authenticated image failed to load:', signedUrl);
    setError('Failed to load image');
    onError?.(e);
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error || !signedUrl) {
    return fallback ? (
      <>{fallback}</>
    ) : (
      <div className={`${className} flex items-center justify-center bg-gray-100 text-gray-500`}>
        <span className="text-sm">
          {error === 'Authentication required' ? 'Login required' :
           error === 'Access denied' ? 'Access denied' :
           error === 'File not found' ? 'Image not found' : 'Failed to load'}
        </span>
      </div>
    );
  }

  return (
    <img
      src={signedUrl}
      alt={alt}
      className={className}
      onLoad={handleImageLoad}
      onError={handleImageError}
    />
  );
};

export default AuthenticatedImage;

import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/Button";
import { useAuth } from "@/context/AuthContext";
import {
  Menu,
  X,
  MessageSquare,
  LayoutDashboard,
  Shield,
  LogIn,
} from "lucide-react";
import { useNavbarStore } from "@/store/navbarStore";
import { ROUTES } from "@/routes/RouteConfig";
import UserProfileMenu from "@/components/UserProfileMenu";

// Add isAdminPage prop with default value
interface NavbarProps {
  isAdminPage?: boolean;
}

// Define navigation items for reuse
const mainNavItems = [
  { path: ROUTES.HOME.path, label: "Home" },
  { path: ROUTES.COURSES.path, label: "Courses" },
  { path: ROUTES.TUTORS.path, label: "Tutors" },
  { path: ROUTES.PRICING.path, label: "Pricing" },
  { path: "/how-it-works", label: "How it Works" },
];

const adminNavItems = [
  {
    path: ROUTES.ADMIN_DASHBOARD.path,
    label: "Dashboard",
    icon: <LayoutDashboard size={18} className="mr-1" />,
  },
  {
    path: "/admin/inquiries",
    label: "Inquiries",
    icon: <MessageSquare size={18} className="mr-1" />,
  },
];

const Navbar = ({ isAdminPage = false }: NavbarProps) => {
  const { isMenuOpen, toggleMenu, setIsMenuOpen } = useNavbarStore();
  const { user, isUserAdmin } = useAuth();
  const location = useLocation();

  // Function to check if a path is active
  const isActivePath = (path: string): boolean => {
    // Exact match for home page
    if (path === ROUTES.HOME.path) {
      return location.pathname === ROUTES.HOME.path;
    }
    // For other paths, check if the current path starts with the given path
    return location.pathname.startsWith(path);
  };

  return (
    <nav
      className={`sticky top-5 z-50 ${
        isAdminPage ? "bg-rfpurple-50/95" : "bg-[#f0e9ff]/75"
      } backdrop-blur-sm rounded-lg mx-2 sm:mx-4 md:mx-9 mt-5`}
    >
      <div className="w-full px-2">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Left side - Logo */}
          <div className="flex-shrink-0 ml-2">
            <Link
              to={ROUTES.HOME.path}
              className="flex items-center focus:outline-none focus:ring-0 border-0 no-underline"
              style={{ border: 'none', outline: 'none' }}
            >
              <div className="flex items-center gap-1">
                <img
                  src="/logo.png"
                  alt="rfLearn logo"
                  className="h-8 w-auto object-contain max-w-[200px] border-0"
                  style={{ border: 'none', outline: 'none' }}
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                    if (nextElement) {
                      nextElement.style.display = 'flex';
                    }
                  }}
                />
                <div className="hidden items-center gap-1">
                  <span
                    className="text-rfpurple-600 text-2xl md:text-3xl font-bold"
                    style={{ fontFamily: "'Comic Sans MS', cursive" }}
                  >
                    rf
                  </span>
                  <span
                    className="text-2xl md:text-3xl font-bold text-[#ff7f52]"
                    style={{ fontFamily: "'Comic Sans MS', cursive" }}
                  >
                    Learn
                  </span>
                </div>
              </div>
              {isAdminPage && (
                <span className="ml-2 bg-rfpurple-100 text-rfpurple-800 text-xs font-medium px-2.5 py-0.5 rounded-full flex items-center">
                  <Shield size={12} className="mr-1" />
                  Admin
                </span>
              )}
            </Link>
          </div>

          {/* Center - Navigation Items */}
          <div className="hidden lg:flex lg:items-center lg:justify-center lg:flex-1 lg:space-x-8">
            {isAdminPage ? (
              // Admin-specific navigation items
              <>
                {adminNavItems.map((item) => (
                  <div
                    key={item.path}
                    className="relative flex flex-col items-center"
                  >
                    <Link
                      to={item.path}
                      className={`flex items-center hover:text-rfpurple-600 px-3 py-2 rounded-md text-base tracking-wide font-roboto ${
                        isActivePath(item.path)
                          ? "text-purple-900 font-medium"
                          : "text-gray-700 font-normal"
                      }`}
                    >
                      {item.icon}
                      {item.label}
                    </Link>
                    {isActivePath(item.path) && (
                      <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 h-1.5 w-1.5 rounded-full bg-[#ff7f52] bg-opacity-80"></div>
                    )}
                  </div>
                ))}
              </>
            ) : (
              // Regular navigation items
              <>
                {mainNavItems.map((item) => (
                  <div
                    key={item.path}
                    className="relative flex flex-col items-center"
                  >
                    <Link
                      to={item.path}
                      className={`flex items-center hover:text-rfpurple-600 px-3 py-2 rounded-md text-base tracking-wide font-roboto ${
                        isActivePath(item.path)
                          ? "text-purple-900 font-medium"
                          : "text-purple-900 font-normal"
                      }`}
                    >
                      {item.label}
                    </Link>
                    {isActivePath(item.path) && (
                      <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 h-1.5 w-1.5 rounded-full bg-[#ff7f52] bg-opacity-80"></div>
                    )}
                  </div>
                ))}
              </>
            )}
          </div>

          {/* Right side - auth buttons (ONLY VISIBLE ON LARGE SCREENS) */}
          <div className="hidden lg:flex items-center flex-shrink-0 mr-2">
            {user ? (
              <UserProfileMenu
                isAdmin={isUserAdmin()}
                isAdminPage={isAdminPage}
              />
            ) : (
              // Desktop login/signup buttons
              <div className="flex items-center space-x-1">
                <Button
                  className="bg-transparent border-0 text-[#ff7f52] hover:bg-[#ff7f52] hover:text-white px-4"
                  asChild
                >
                  <Link to={ROUTES.LOGIN.path}>Login</Link>
                </Button>
                <Button
                  className="bg-transparent border-2 border-[#9548ef] text-[#9548ef] hover:bg-[#9548ef] hover:text-white px-6"
                  asChild
                >
                  <Link to={ROUTES.REGISTER.path}>Signup</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile menu button - VISIBLE ON MOBILE AND MEDIUM DEVICES */}
          <div className="flex lg:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-rfpurple-500"
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">
                {isMenuOpen ? "Close main menu" : "Open main menu"}
              </span>
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation - use isMenuOpen from Zustand */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200 animate-fade-in">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {isAdminPage ? (
              // Admin-specific mobile navigation
              <>
                {adminNavItems.map((item) => (
                  <div key={item.path} className="relative">
                    <Link
                      to={item.path}
                      className={`flex items-center hover:bg-gray-50 px-3 py-2 rounded-md text-base font-roboto ${
                        isActivePath(item.path)
                          ? "text-purple-900 font-medium"
                          : "text-gray-700 font-normal"
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.icon}
                      {item.label}
                      {isActivePath(item.path) && (
                        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 h-1.5 w-1.5 rounded-full bg-[#ff7f52] bg-opacity-80"></div>
                      )}
                    </Link>
                  </div>
                ))}
              </>
            ) : (
              <>
                {/* Regular navigation items */}
                {mainNavItems.map((item) => (
                  <div key={item.path} className="relative">
                    <Link
                      to={item.path}
                      className={`flex items-center hover:bg-gray-50 px-3 py-2 rounded-md text-base font-roboto ${
                        isActivePath(item.path)
                          ? "text-purple-900 font-medium"
                          : "text-gray-700 font-normal"
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.label}
                      {isActivePath(item.path) && (
                        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 h-1.5 w-1.5 rounded-full bg-[#ff7f52] bg-opacity-80"></div>
                      )}
                    </Link>
                  </div>
                ))}

                {isUserAdmin() && !isAdminPage && (
                  <Link
                    to={ROUTES.ADMIN_DASHBOARD.path}
                    className="flex items-center text-gray-700 hover:text-rfpurple-600 hover:bg-gray-50 px-3 py-2 rounded-md text-base font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Shield size={18} className="mr-2" />
                    Admin Dashboard
                  </Link>
                )}
              </>
            )}

            {user && !isAdminPage && (
              <Link
                to="/dashboard"
                className="flex items-center text-gray-700 hover:text-rfpurple-600 hover:bg-gray-50 px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                <LayoutDashboard size={18} className="mr-2" />
                Dashboard
              </Link>
            )}

            <div className="flex flex-col space-y-2 pt-2">
              {user ? (
                <div className="w-full">
                  <UserProfileMenu
                    isAdmin={isUserAdmin()}
                    isAdminPage={isAdminPage}
                    className="w-full justify-start"
                  />
                </div>
              ) : (
                <>
                  <Button
                    className="w-full bg-transparent border-2 border-[#ff7f52] text-[#ff7f52] hover:bg-[#ff7f52] hover:text-white"
                    asChild
                  >
                    <Link
                      to={ROUTES.LOGIN.path}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <LogIn size={18} className="mr-2" />
                      Login
                    </Link>
                  </Button>
                  <Button
                    className="w-full bg-transparent border-2 border-[#9548ef] text-[#9548ef] hover:bg-[#9548ef] hover:text-white"
                    asChild
                  >
                    <Link
                      to={ROUTES.REGISTER.path}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Signup
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;

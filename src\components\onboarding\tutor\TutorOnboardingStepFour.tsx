import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/TextArea";
import { Label } from "@/components/ui/Label";

interface TutorOnboardingStepFourProps {
  firstName: string;
  setFirstName: (name: string) => void;
  lastName: string;
  setLastName: (name: string) => void;
  bio: string;
  setBio: (bio: string) => void;
  hourlyRate: string;
  setHourlyRate: (rate: string) => void;
}

const TutorOnboardingStepFour: React.FC<TutorOnboardingStepFourProps> = ({
  firstName,
  setFirstName,
  lastName,
  setLastName,
  bio,
  setBio,
  hourlyRate,
  setHourlyRate,
}) => {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold mb-2">
        Tell us about yourself
      </h2>
      <p className="text-gray-600 mb-6">
        This information will be displayed on your public profile.
      </p>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            placeholder="John"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            placeholder="Doe"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="bio">Bio</Label>
        <Textarea
          id="bio"
          value={bio}
          onChange={(e) => setBio(e.target.value)}
          placeholder="Tell students about your background, teaching style, and expertise..."
          className="min-h-[120px]"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="hourlyRate">Hourly Rate ($)</Label>
        <Input
          id="hourlyRate"
          type="number"
          value={hourlyRate}
          onChange={(e) => setHourlyRate(e.target.value)}
          placeholder="45"
          className="max-w-[200px]"
        />
      </div>
    </div>
  );
};

export default TutorOnboardingStepFour;
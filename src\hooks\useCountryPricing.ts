import { useMemo } from 'react';
import { useTimezone } from './useTimezone';
import {
  getCountryByTimezone,
  getCountryConfig,
  calculateLocalizedPrice,
  formatPrice,
  hasDiscount,
  getDiscountPercentage,
  BASE_PRICING_PLANS,
  CountryConfig,
  PricingPlan
} from '@/config/countryPricing';

export interface LocalizedPricingPlan extends Omit<PricingPlan, 'basePriceUSD' | 'originalPriceUSD'> {
  price: number;
  originalPrice?: number;
  formattedPrice: string;
  formattedOriginalPrice?: string;
  currency: {
    code: string;
    symbol: string;
    name: string;
  };
  countryCode: string;
  countryName: string;
}

export interface UseCountryPricingReturn {
  countryCode: string;
  countryConfig: CountryConfig;
  localizedPlans: LocalizedPricingPlan[];
  formatLocalPrice: (priceUSD: number) => string;
  calculateLocalPrice: (priceUSD: number) => number;
  isDetected: boolean;
  detectedTimezone: string;
  hasDiscount: boolean;
  discountPercentage: number;
}

/**
 * Hook for country-based pricing using timezone detection
 */
export const useCountryPricing = (): UseCountryPricingReturn => {
  const { detectedTimezone, currentTimezone } = useTimezone();
  
  // Use current timezone (from profile) or detected timezone
  const activeTimezone = currentTimezone || detectedTimezone;
  
  // Determine country based on timezone
  const countryCode = useMemo(() => {
    return getCountryByTimezone(activeTimezone);
  }, [activeTimezone]);
  
  // Get country configuration
  const countryConfig = useMemo(() => {
    return getCountryConfig(countryCode);
  }, [countryCode]);
  
  // Calculate localized pricing plans
  const localizedPlans = useMemo(() => {
    return BASE_PRICING_PLANS.map((plan): LocalizedPricingPlan => {
      const localPrice = calculateLocalizedPrice(plan.basePriceUSD, countryCode);
      const localOriginalPrice = plan.originalPriceUSD 
        ? calculateLocalizedPrice(plan.originalPriceUSD, countryCode)
        : undefined;
      
      return {
        ...plan,
        price: localPrice,
        originalPrice: localOriginalPrice,
        formattedPrice: formatPrice(localPrice, countryCode),
        formattedOriginalPrice: localOriginalPrice 
          ? formatPrice(localOriginalPrice, countryCode)
          : undefined,
        currency: countryConfig.currency,
        countryCode,
        countryName: countryConfig.name
      };
    });
  }, [countryCode, countryConfig]);
  
  // Helper function to format any USD price to local currency
  const formatLocalPrice = useMemo(() => {
    return (priceUSD: number): string => {
      const localPrice = calculateLocalizedPrice(priceUSD, countryCode);
      return formatPrice(localPrice, countryCode);
    };
  }, [countryCode]);
  
  // Helper function to calculate local price from USD
  const calculateLocalPrice = useMemo(() => {
    return (priceUSD: number): number => {
      return calculateLocalizedPrice(priceUSD, countryCode);
    };
  }, [countryCode]);
  
  return {
    countryCode,
    countryConfig,
    localizedPlans,
    formatLocalPrice,
    calculateLocalPrice,
    isDetected: !!detectedTimezone,
    detectedTimezone,
    hasDiscount: hasDiscount(countryCode),
    discountPercentage: getDiscountPercentage(countryCode)
  };
};

/**
 * Hook for getting pricing for a specific country (useful for admin/testing)
 */
export const useSpecificCountryPricing = (targetCountryCode: string): UseCountryPricingReturn => {
  const { detectedTimezone } = useTimezone();
  
  // Get country configuration
  const countryConfig = useMemo(() => {
    return getCountryConfig(targetCountryCode);
  }, [targetCountryCode]);
  
  // Calculate localized pricing plans
  const localizedPlans = useMemo(() => {
    return BASE_PRICING_PLANS.map((plan): LocalizedPricingPlan => {
      const localPrice = calculateLocalizedPrice(plan.basePriceUSD, targetCountryCode);
      const localOriginalPrice = plan.originalPriceUSD 
        ? calculateLocalizedPrice(plan.originalPriceUSD, targetCountryCode)
        : undefined;
      
      return {
        ...plan,
        price: localPrice,
        originalPrice: localOriginalPrice,
        formattedPrice: formatPrice(localPrice, targetCountryCode),
        formattedOriginalPrice: localOriginalPrice 
          ? formatPrice(localOriginalPrice, targetCountryCode)
          : undefined,
        currency: countryConfig.currency,
        countryCode: targetCountryCode,
        countryName: countryConfig.name
      };
    });
  }, [targetCountryCode, countryConfig]);
  
  // Helper function to format any USD price to local currency
  const formatLocalPrice = useMemo(() => {
    return (priceUSD: number): string => {
      const localPrice = calculateLocalizedPrice(priceUSD, targetCountryCode);
      return formatPrice(localPrice, targetCountryCode);
    };
  }, [targetCountryCode]);
  
  // Helper function to calculate local price from USD
  const calculateLocalPrice = useMemo(() => {
    return (priceUSD: number): number => {
      return calculateLocalizedPrice(priceUSD, targetCountryCode);
    };
  }, [targetCountryCode]);
  
  return {
    countryCode: targetCountryCode,
    countryConfig,
    localizedPlans,
    formatLocalPrice,
    calculateLocalPrice,
    isDetected: !!detectedTimezone,
    detectedTimezone,
    hasDiscount: hasDiscount(targetCountryCode),
    discountPercentage: getDiscountPercentage(targetCountryCode)
  };
};

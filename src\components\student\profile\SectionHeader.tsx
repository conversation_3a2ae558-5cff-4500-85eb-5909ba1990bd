import React from "react";
import { <PERSON>, CardHeader, CardTitle } from "@/components/ui/Card";
import { Pencil } from "lucide-react";

interface SectionHeaderProps {
  title: string;
  isModified?: boolean;
  onEdit: () => void;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({ 
  title, 
  isModified = false, 
  onEdit 
}) => {
  return (
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <div className="flex items-center">
        <CardTitle className="text-xl">{title}</CardTitle>
        {isModified && (
          <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
            Modified
          </span>
        )}
      </div>
      <button
        onClick={onEdit}
        className="p-1 rounded-full hover:bg-gray-100 transition-colors"
        aria-label={`Edit ${title}`}
      >
        <Pencil className="h-4 w-4 text-gray-500" />
      </button>
    </CardHeader>
  );
};

export default SectionHeader;

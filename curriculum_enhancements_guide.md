# Curriculum Workflow Enhancements Guide

This document explains the three key enhancements to support your curriculum configuration workflow for Complete Booster, Customized Learning, and Exam Preparation products.

## 🎯 Overview

The enhancements address three critical areas:
1. **Explicit Product-Subject Relationships** - Define which subjects are available for each product
2. **Enhanced Quiz/Test Configuration** - Better support for assessments in curriculum planning
3. **Clear Subscription-to-Batch Relationship** - Proper linking between subscriptions and learning batches

---

## 1. 📚 Explicit Product-Subject Relationships

### Problem Solved
Previously, there was no clear definition of which subjects were available for each product type (Complete Booster, Customized Learning, Exam Preparation).

### New Tables

#### `product_subjects`
Defines which subjects are available for each product with configuration options:

| Field | Type | Purpose |
|-------|------|---------|
| `product_id` | UUID | Links to products table |
| `subject_id` | UUID | Links to subjects table |
| `is_default` | BOOLEAN | Auto-selected for this product |
| `is_required` | BOOLEAN | Must be included (for booster products) |
| `max_topics_allowed` | INTEGER | Limit topics for custom/prep products |
| `estimated_sessions_per_subject` | INTEGER | For pricing calculations |
| `price_override` | NUMERIC | Override default product price |
| `display_order` | INTEGER | Display ordering |

#### `product_topic_configurations`
Fine-grained control over which topics are available per product:

| Field | Type | Purpose |
|-------|------|---------|
| `product_subject_id` | UUID | Links to product_subjects |
| `topic_id` | UUID | Links to topics table |
| `is_available` | BOOLEAN | Can be selected for this product |
| `is_recommended` | BOOLEAN | Recommended for this product type |
| `estimated_sessions` | INTEGER | Sessions needed for this topic |
| `difficulty_level` | TEXT | beginner/intermediate/advanced |
| `prerequisites` | TEXT[] | Array of prerequisite topic IDs |

### Use Cases

#### Complete Booster Product
```sql
-- Math Booster includes all math subjects by default
INSERT INTO product_subjects (product_id, subject_id, is_default, is_required, estimated_sessions_per_subject)
VALUES ('booster-product-id', 'math-subject-id', TRUE, TRUE, 50);
```

#### Custom Learning Product
```sql
-- Custom learning allows selection from multiple subjects
INSERT INTO product_subjects (product_id, subject_id, is_default, max_topics_allowed, estimated_sessions_per_subject)
VALUES ('custom-product-id', 'math-subject-id', FALSE, 10, 30);
```

#### Exam Preparation Product
```sql
-- SAT prep focuses on specific topics
INSERT INTO product_topic_configurations (product_subject_id, topic_id, is_recommended, estimated_sessions)
VALUES ('sat-math-id', 'algebra-topic-id', TRUE, 8);
```

---

## 2. 📝 Enhanced Quiz/Test Configuration

### Problem Solved
Better support for quizzes and tests in curriculum planning with proper assessment configuration.

### Enhanced `resources` Table
Added new columns for assessment configuration:

| New Field | Type | Purpose |
|-----------|------|---------|
| `is_required` | BOOLEAN | Required for curriculum completion |
| `estimated_duration_minutes` | INTEGER | Time needed for assessment |
| `difficulty_level` | TEXT | beginner/intermediate/advanced |
| `max_attempts` | INTEGER | Maximum attempts allowed |
| `passing_score` | NUMERIC | Percentage required to pass |
| `is_practice` | BOOLEAN | Practice vs. formal assessment |
| `prerequisites` | JSONB | Array of prerequisite resource IDs |
| `tags` | JSONB | Array of tags for categorization |

### New `curriculum_assessments` Table
Comprehensive assessment configuration for curriculum planning:

| Field | Type | Purpose |
|-------|------|---------|
| `subject_id` | UUID | Links to subjects |
| `topic_id` | UUID | Links to topics |
| `subtopic_id` | UUID | Links to subtopics |
| `assessment_type` | TEXT | quiz/test/practice/final_exam/diagnostic |
| `question_count` | INTEGER | Number of questions |
| `time_limit_minutes` | INTEGER | Time limit for assessment |
| `passing_score` | NUMERIC | Required percentage to pass |
| `is_required_for_completion` | BOOLEAN | Must complete to finish curriculum |
| `unlock_requirements` | JSONB | What must be completed first |
| `recommended_timing` | TEXT | When to take (after_topic/mid_topic/before_topic) |
| `auto_schedule` | BOOLEAN | Auto-add to student schedule |

### New `assessment_prerequisites` Table
Defines what must be completed before taking an assessment:

| Field | Type | Purpose |
|-------|------|---------|
| `assessment_id` | UUID | Links to curriculum_assessments |
| `prerequisite_type` | TEXT | topic/subtopic/assessment/resource |
| `prerequisite_id` | UUID | ID of the prerequisite item |
| `is_required` | BOOLEAN | Must be completed |

### Use Cases

#### Topic-Level Quiz
```sql
INSERT INTO curriculum_assessments (
    topic_id, name, assessment_type, question_count, 
    time_limit_minutes, passing_score, is_required_for_completion
) VALUES (
    'algebra-topic-id', 'Algebra Basics Quiz', 'quiz', 10, 
    15, 70.0, TRUE
);
```

#### Final Exam with Prerequisites
```sql
-- Create final exam
INSERT INTO curriculum_assessments (
    subject_id, name, assessment_type, question_count,
    time_limit_minutes, passing_score, recommended_timing
) VALUES (
    'math-subject-id', 'Mathematics Final Exam', 'final_exam', 50,
    120, 80.0, 'after_topic'
);

-- Add prerequisites
INSERT INTO assessment_prerequisites (
    assessment_id, prerequisite_type, prerequisite_id, is_required
) VALUES (
    'final-exam-id', 'topic', 'algebra-topic-id', TRUE
);
```

---

## 3. 🔗 Clear Subscription-to-Batch Relationship

### Problem Solved
Previously, there was no clear relationship between subscriptions (payment/enrollment) and batches (learning packages). This enhancement creates proper linking.

### Enhanced `batches` Table
Added new columns to link batches with subscriptions:

| New Field | Type | Purpose |
|-----------|------|---------|
| `subscription_id` | UUID | Links to subscriptions table |
| `workflow_id` | UUID | Links to subscription_workflows |
| `auto_created` | BOOLEAN | Was batch auto-created from subscription? |
| `curriculum_locked` | BOOLEAN | Prevent changes after creation |

### New `subscription_batches` Table
Handles cases where one subscription creates multiple batches:

| Field | Type | Purpose |
|-------|------|---------|
| `subscription_id` | UUID | Links to subscriptions |
| `batch_id` | UUID | Links to batches |
| `subject_id` | UUID | Which subject this batch covers |
| `curriculum_source` | TEXT | subscription_workflow/admin_configured/student_customized |
| `is_primary_batch` | BOOLEAN | Main batch for the subscription |
| `activation_date` | TIMESTAMP | When this batch becomes active |
| `completion_target_date` | TIMESTAMP | Expected completion |

### New `batch_curriculum_mapping` Table
Links batch to specific curriculum selections from the subscription workflow:

| Field | Type | Purpose |
|-------|------|---------|
| `batch_id` | UUID | Links to batches |
| `workflow_id` | UUID | Links to subscription_workflows |
| `subject_id` | UUID | Subject for this curriculum part |
| `topic_ids` | JSONB | Array of selected topic IDs |
| `subtopic_ids` | JSONB | Array of selected subtopic IDs |
| `assessment_ids` | JSONB | Array of selected assessment IDs |
| `sequence_order` | INTEGER | Order in the batch |
| `allocated_sessions` | INTEGER | Sessions allocated to this part |
| `completed_sessions` | INTEGER | Sessions completed |

### Use Cases

#### Single Subject Subscription → Single Batch
```sql
-- Create batch linked to subscription
INSERT INTO batches (name, student_id, subscription_id, package_type)
VALUES ('Math Booster Batch', 'student-id', 'subscription-id', 'complete_booster');

-- Map curriculum from subscription workflow
INSERT INTO batch_curriculum_mapping (
    batch_id, workflow_id, subject_id, topic_ids, allocated_sessions
) VALUES (
    'batch-id', 'workflow-id', 'math-subject-id', 
    '["algebra-id", "geometry-id"]', 25
);
```

#### Multi-Subject Subscription → Multiple Batches
```sql
-- Create multiple batches for different subjects
INSERT INTO subscription_batches (subscription_id, batch_id, subject_id, is_primary_batch)
VALUES 
    ('subscription-id', 'math-batch-id', 'math-subject-id', TRUE),
    ('subscription-id', 'science-batch-id', 'science-subject-id', FALSE);
```

---

## 🛠️ Helper Functions

### `get_product_subjects(product_uuid)`
Returns available subjects for a product with configuration details.

### `get_product_topics(product_uuid, subject_uuid)`
Returns available topics for a product-subject combination.

### `calculate_curriculum_sessions(product_uuid, selected_subjects, selected_topics, selected_subtopics)`
Calculates estimated sessions needed for a curriculum selection.

---

## 🔐 Security & Permissions

### RLS Policies
- **Public Read**: Students can view product configurations and assessments
- **Admin Write**: Only admins can modify product configurations
- **Student Access**: Students can only view their own subscription-batch mappings
- **Tutor Access**: Tutors can manage curriculum assessments

### Data Integrity
- Foreign key constraints ensure referential integrity
- Unique constraints prevent duplicate configurations
- Check constraints validate enum values
- Indexes optimize query performance

---

## 🚀 Implementation Benefits

1. **Flexible Product Configuration**: Admins can easily configure which subjects/topics are available for each product
2. **Accurate Pricing**: Session estimation based on actual curriculum selections
3. **Assessment Integration**: Proper quiz/test configuration in curriculum planning
4. **Clear Data Flow**: Subscription → Curriculum Selection → Batch Creation → Learning Delivery
5. **Admin Control**: Fine-grained control over curriculum availability and pricing
6. **Student Experience**: Clear understanding of what's included in each product

This enhancement provides the foundation for implementing the detailed curriculum configuration workflow you described.

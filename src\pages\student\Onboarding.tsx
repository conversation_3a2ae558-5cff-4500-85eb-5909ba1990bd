import { useNavigate } from "react-router-dom";
import { OnboardingProvider, useOnboarding } from "@/context/OnboardingContext";
import OnboardingLayout from "@/components/onboarding/OnboardingLayout";
import SubjectSelection from "@/components/onboarding/student/SubjectSelection";
import EducationLevel from "@/components/onboarding/student/EducationLevel";
import LearningGoals from "@/components/onboarding/student/LearningGoals";
import PersonalInfo from "@/components/onboarding/student/PersonalInfo";
import { ProtectedRouteWrapper, ROUTES } from "@/routes/RouteConfig";
import useScrollToTop from "@/hooks/useScrollToTop";
import ProcessStepsModal from "@/components/ui/ProcessStepsModal";
import { useProcessStepsStore } from "@/store/processStepsStore";
import { useAuth } from "@/context/AuthContext";

// Wrapper component that uses the onboarding context
const OnboardingContent = () => {
  const navigate = useNavigate();
  const { refreshUserData } = useAuth();
  const {
    currentStep,
    totalSteps,
    nextStep,
    prevStep,
    completeOnboarding,
    selectedSubjects,
    setSelectedSubjects,
    educationLevel,
    setEducationLevel,
    learningGoals,
    setLearningGoals,
    firstName,
    setFirstName,
    lastName,
    setLastName,
    dateOfBirth,
    setDateOfBirth,
  } = useOnboarding();

  // Get actions from the process steps store
  const { startProcess, updateStep, close } = useProcessStepsStore();

  // Determine if the next button should be disabled
  const isNextDisabled = () => {
    if (currentStep === 1 && selectedSubjects.length === 0) return true;
    if (currentStep === 2 && !educationLevel) return true;
    if (currentStep === 3 && learningGoals.length === 0) return true;
    if (currentStep === 4 && (!firstName || !lastName || !dateOfBirth))
      return true;
    return false;
  };

  // Handle the next button click
  const handleNext = () => {
    if (currentStep < totalSteps) {
      nextStep();
    } else {
      // Initialize the process steps modal with our steps
      startProcess("Onboarding Status", [
        { id: "saving", label: "Saving details", status: "pending" },
        { id: "onboarding", label: "Onboarding completed", status: "pending" },
        { id: "redirect", label: "Redirect to dashboard", status: "pending" },
      ]);

      // Update first step to loading
      updateStep("saving", "loading");

      // Call the completeOnboarding function from context
      completeOnboarding()
        .then(async () => {
          updateStep("saving", "complete");
          updateStep("onboarding", "complete");
          updateStep("redirect", "loading");

          // Longer delay to allow database trigger to complete and profile to be created
          setTimeout(async () => {
            // Refresh user data to get updated profile and onboarding status
            await refreshUserData();

            updateStep("redirect", "complete");
            // Navigate to dashboard after the process is complete
            navigate(ROUTES.STUDENT_DASHBOARD.path);
          }, 2000); // Increased delay to 2 seconds
        })
        .catch((error) => {
          console.error("Error during onboarding completion:", error);
          updateStep("saving", "error");
          // Close the modal after a short delay
          setTimeout(() => {
            close();
          }, 3000);
        });
    }
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <SubjectSelection
            selectedSubjects={selectedSubjects}
            setSelectedSubjects={setSelectedSubjects}
          />
        );
      case 2:
        return (
          <EducationLevel
            educationLevel={educationLevel}
            setEducationLevel={setEducationLevel}
          />
        );
      case 3:
        return (
          <LearningGoals
            learningGoals={learningGoals}
            setLearningGoals={setLearningGoals}
          />
        );
      case 4:
        return (
          <PersonalInfo
            firstName={firstName}
            setFirstName={setFirstName}
            lastName={lastName}
            setLastName={setLastName}
            dateOfBirth={dateOfBirth}
            setDateOfBirth={setDateOfBirth}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <OnboardingLayout
        currentStep={currentStep}
        totalSteps={totalSteps}
        nextStep={handleNext}
        prevStep={prevStep}
        isNextDisabled={isNextDisabled()}
        nextButtonText={currentStep === totalSteps ? "Complete" : "Next"}
      >
        {renderStep()}
      </OnboardingLayout>

      {/* Process Steps Modal - no props needed as it uses the Zustand store */}
      <ProcessStepsModal />
    </>
  );
};

// Main component with provider
const StudentOnboarding = () => {
  useScrollToTop();

  return (
    <ProtectedRouteWrapper routeConfig={ROUTES.STUDENT_ONBOARDING}>
      <OnboardingProvider>
        <OnboardingContent />
      </OnboardingProvider>
    </ProtectedRouteWrapper>
  );
};

export default StudentOnboarding;


// src/hooks/useToast.ts
import { useState } from 'react';

interface ToastOptions {
  title: string;
  description?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

interface Toast extends ToastOptions {
  id: string;
}

export const useToast = () => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = (options: ToastOptions) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast: Toast = {
      id,
      title: options.title,
      description: options.description,
      type: options.type || 'info',
      duration: options.duration || 5000,
    };

    setToasts((prevToasts) => [...prevToasts, newToast]);

    // Auto-dismiss toast after duration
    setTimeout(() => {
      dismissToast(id);
    }, newToast.duration);

    return id;
  };

  const dismissToast = (id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  };

  // Convenience methods for different toast types
  const success = (title: string, description?: string, duration?: number) => {
    return toast({ title, description, type: 'success', duration });
  };

  const error = (title: string, description?: string, duration?: number) => {
    return toast({ title, description, type: 'error', duration });
  };

  const warning = (title: string, description?: string, duration?: number) => {
    return toast({ title, description, type: 'warning', duration });
  };

  const info = (title: string, description?: string, duration?: number) => {
    return toast({ title, description, type: 'info', duration });
  };

  return {
    toasts,
    toast,
    dismissToast,
    success,
    error,
    warning,
    info,
  };
};

export default useToast;

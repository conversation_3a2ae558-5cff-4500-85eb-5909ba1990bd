import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Edit, Trophy } from "lucide-react";
import EditSectionModal, { FormInput } from "@/components/student/profile/EditSectionModal";
import { StudentExtendedProfileData } from "@/pages/student/Profile";

interface AchievementsSectionProps {
  profileData: StudentExtendedProfileData;
  editData: Partial<StudentExtendedProfileData>;
  activeSectionEdit: string | null;
  setActiveSectionEdit: (section: string | null) => void;
  updateEditData: (field: string, value: any) => void;
  updateProfile: (data: Partial<StudentExtendedProfileData>) => Promise<void>;
}

const AchievementsSection: React.FC<AchievementsSectionProps> = ({
  profileData,
  editData,
  activeSectionEdit,
  setActiveSectionEdit,
  updateEditData,
  updateProfile
}) => {
  return (
    <>
      {/* Achievements Section */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Achievements</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setActiveSectionEdit('achievements')}
          >
            <Edit className="h-3.5 w-3.5 mr-1" />
            Edit
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {profileData.academic_history?.achievements?.length > 0 ? (
              profileData.academic_history.achievements.map((achievement, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center">
                    <Trophy className="h-6 w-6 text-amber-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">{achievement}</h3>
                    <p className="text-sm text-gray-500">{profileData.academic_history?.school_name || "School not specified"}</p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500">No academic achievements specified</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Achievements Edit Modal */}
      <EditSectionModal
        isOpen={activeSectionEdit === 'achievements'}
        onClose={() => setActiveSectionEdit(null)}
        title="Edit Achievements"
        onSubmit={() => {
          // Save changes
          updateProfile(editData);
          setActiveSectionEdit(null);
        }}
      >
        <FormInput
          label="Achievements"
          id="achievements"
          placeholder="Add achievements separated by commas"
          helpText="Enter your academic achievements, awards, or recognitions"
          value={editData.academic_history?.achievements?.join(', ') || ''}
          onChange={(e) => {
            const achievementsArray = e.target.value.split(',').map(item => item.trim()).filter(Boolean);
            updateEditData("academic_history", {
              ...editData.academic_history,
              achievements: achievementsArray
            });
          }}
        />
      </EditSectionModal>
    </>
  );
};

export default AchievementsSection;

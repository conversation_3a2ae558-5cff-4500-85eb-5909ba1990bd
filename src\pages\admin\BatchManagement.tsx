// src/pages/admin/BatchManagement.tsx
import React from "react";
import { Link } from "react-router-dom";
import AdminPageLayout from "@/components/layouts/AdminPageLayout";
import { useProfileData } from "@/hooks/useProfileData";
import { PlusCircle, List, Edit, Trash2 } from "lucide-react";
import { ROUTES } from "@/routes/RouteConfig";

const BatchManagement: React.FC = () => {
  const profileData = useProfileData();

  return (
    <AdminPageLayout
      title="Batch Management"
      description="Create and manage student batches"
      profileData={profileData}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
        {/* Create Batch */}
        <Link
          to={ROUTES.ADMIN_CREATE_BATCH.path}
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
        >
          <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
            <PlusCircle className="text-rfpurple-600" size={20} />
          </div>
          <h3 className="font-semibold text-lg mb-2">Create Batch</h3>
          <p className="text-sm text-gray-500">
            Create new batches and assign students, subjects, and tutors.
          </p>
        </Link>

        {/* View Batches */}
        <Link
          to={ROUTES.ADMIN_VIEW_BATCHES.path}
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
        >
          <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
            <List className="text-rfpurple-600" size={20} />
          </div>
          <h3 className="font-semibold text-lg mb-2">View Batches</h3>
          <p className="text-sm text-gray-500">
            Browse and search through all batches and their details.
          </p>
        </Link>

        {/* Edit Batch */}
        <Link
          to={ROUTES.ADMIN_EDIT_BATCH.path}
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
        >
          <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
            <Edit className="text-rfpurple-600" size={20} />
          </div>
          <h3 className="font-semibold text-lg mb-2">Edit Batch</h3>
          <p className="text-sm text-gray-500">
            Modify batch details, change assigned tutors, or update subjects.
          </p>
        </Link>

        {/* Delete Batch */}
        <Link
          to={ROUTES.ADMIN_DELETE_BATCH.path}
          className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
        >
          <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
            <Trash2 className="text-rfpurple-600" size={20} />
          </div>
          <h3 className="font-semibold text-lg mb-2">Delete Batch</h3>
          <p className="text-sm text-gray-500">
            Remove batches that are no longer needed from the system.
          </p>
        </Link>
      </div>
    </AdminPageLayout>
  );
};

export default BatchManagement;
-- Database Schema for Learning Platform

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (handled by Supa<PERSON> Auth)
-- This table is managed by Supabase Auth and contains basic user authentication info
-- Note: We'll reference this table but not create it as it's managed by Supabase

-- Profiles table - Extended user information
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    first_name TEXT,
    last_name TEXT,
    user_type TEXT NOT NULL CHECK (user_type IN ('student', 'tutor', 'admin')),
    email TEXT NOT NULL,
    avatar_url TEXT,
    timezone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Subjects table - Main subject areas
CREATE TABLE subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Topics table - Categories within subjects
CREATE TABLE topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subject_id UUID REFERENCES subjects(id) NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    display_order INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(subject_id, name)
);

-- Subtopics table - Specific learning units within topics (also called standards)
CREATE TABLE subtopics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    topic_id UUID REFERENCES topics(id) NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    state_standard TEXT, -- Reference to external educational standards
    display_order INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(topic_id, name)
);

-- Resources table - Learning materials associated with subtopics
CREATE TABLE resources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subtopic_id UUID REFERENCES subtopics(id) NOT NULL,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('document', 'video', 'quiz', 'exercise', 'other')),
    url TEXT,
    content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Batches table - Student learning packages
CREATE TABLE batches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    student_id UUID REFERENCES profiles(id) NOT NULL,
    package_type TEXT NOT NULL CHECK (package_type IN ('complete_booster', 'preparation', 'customized')),
    package_name TEXT NOT NULL,  -- e.g., "Common Core", "SAT", "ESAP", etc.
    default_tutor_id UUID REFERENCES profiles(id),
    status TEXT NOT NULL CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    total_sessions INTEGER,
    remaining_sessions INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,

    -- Valid date range constraint
    CONSTRAINT valid_date_range CHECK (
        end_date IS NULL OR start_date IS NULL OR end_date > start_date
    )
);

-- Batch Topics table - Topics included in a batch with optional custom tutor assignment
CREATE TABLE batch_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID REFERENCES batches(id) NOT NULL,
    topic_id UUID REFERENCES topics(id) NOT NULL,
    custom_tutor_id UUID REFERENCES profiles(id),
    status TEXT NOT NULL CHECK (status IN ('not_started', 'in_progress', 'completed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(batch_id, topic_id)
);

-- Batch Subtopics table - Subtopics included in a batch with optional custom tutor assignment
CREATE TABLE batch_subtopics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_topic_id UUID REFERENCES batch_topics(id) NOT NULL,
    subtopic_id UUID REFERENCES subtopics(id) NOT NULL,
    custom_tutor_id UUID REFERENCES profiles(id),
    status TEXT NOT NULL CHECK (status IN ('not_started', 'in_progress', 'completed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(batch_topic_id, subtopic_id)
);

-- Tutor Availability table - Tutor availability slots
CREATE TABLE tutor_availability (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tutor_id UUID REFERENCES profiles(id) NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday, 6 = Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('available', 'auto_accept', 'manual_approval')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- Tutor Auto Accept Rules table - Rules for automatic session acceptance
CREATE TABLE tutor_auto_accept_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tutor_id UUID REFERENCES profiles(id) NOT NULL,
    name TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    existing_students_only BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Rule Topics table - Topics that a rule applies to
CREATE TABLE rule_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_id UUID REFERENCES tutor_auto_accept_rules(id) NOT NULL,
    topic_id UUID REFERENCES topics(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(rule_id, topic_id)
);

-- Rule Time Ranges table - Time ranges that a rule applies to
CREATE TABLE rule_time_ranges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_id UUID REFERENCES tutor_auto_accept_rules(id) NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6),
    start_time TIME,
    end_time TIME,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    CONSTRAINT valid_time_range CHECK (end_time IS NULL OR start_time IS NULL OR end_time > start_time)
);

-- Sessions table - Individual learning sessions
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID REFERENCES batches(id) NOT NULL,
    topic_id UUID REFERENCES topics(id) NOT NULL,
    subtopic_id UUID REFERENCES subtopics(id),
    tutor_id UUID REFERENCES profiles(id) NOT NULL,
    student_id UUID REFERENCES profiles(id) NOT NULL,
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_min INTEGER NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('scheduled', 'completed', 'cancelled', 'rescheduled')),
    mode TEXT NOT NULL CHECK (mode IN ('video', 'audio', 'quiz', 'hybrid')),
    session_type TEXT NOT NULL CHECK (session_type IN ('regular', 'demo', 'makeup', 'assessment', 'consultation')),
    meeting_url TEXT,
    location TEXT, -- For physical locations or virtual meeting identifiers
    has_conflict BOOLEAN DEFAULT FALSE,
    urgency_level SMALLINT CHECK (urgency_level BETWEEN 1 AND 5),
    rescheduled_from UUID REFERENCES sessions(id),
    created_by TEXT NOT NULL CHECK (created_by IN ('admin', 'tutor', 'student')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Session Details table - Analytics and session metrics
CREATE TABLE session_details (
    session_id UUID PRIMARY KEY REFERENCES sessions(id),
    description TEXT,
    materials JSONB, -- Links to study materials
    recording_url TEXT,
    student_attended BOOLEAN DEFAULT FALSE,
    tutor_attended BOOLEAN DEFAULT FALSE,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    actual_start_time TIMESTAMP WITH TIME ZONE,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    tutor_talk_time INTEGER, -- in minutes
    student_talk_time INTEGER, -- in minutes
    whiteboard_interactions INTEGER,
    messages_sent INTEGER,
    messages_received INTEGER,
    participation_score DECIMAL(3,1),
    participation_level TEXT,
    cancellation_reason TEXT,
    notes TEXT, -- Internal notes about the session
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Session Feedback table - Feedback for completed sessions
CREATE TABLE session_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES sessions(id) NOT NULL,
    submitted_by UUID REFERENCES profiles(id) NOT NULL, -- Can be either student or tutor
    user_role TEXT NOT NULL CHECK (user_role IN ('student', 'tutor')),
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    comments TEXT,
    areas_of_improvement TEXT[], -- Specific areas that need improvement
    strengths TEXT[], -- Specific strengths noted
    follow_up_requested BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(session_id, submitted_by)
);


-- Session Requests table - Requests for new sessions
CREATE TABLE session_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID REFERENCES batches(id) NOT NULL,
    topic_id UUID REFERENCES topics(id) NOT NULL,
    subtopic_id UUID REFERENCES subtopics(id),
    tutor_id UUID REFERENCES profiles(id), -- Requested or assigned tutor
    student_id UUID REFERENCES profiles(id) NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL, -- Combined date and time for better constraint management
    end_time TIMESTAMP WITH TIME ZONE NOT NULL, -- Calculated from start_time + duration
    duration_min INTEGER NOT NULL,
    mode TEXT NOT NULL CHECK (mode IN ('video', 'audio', 'quiz', 'hybrid')),
    location TEXT, -- Meeting URL or physical location
    notes TEXT,
    
    -- Request metadata
    requested_by TEXT NOT NULL CHECK (requested_by IN ('student', 'tutor', 'admin')),
    request_type TEXT NOT NULL CHECK (request_type IN ('new', 'reschedule', 'cancellation')),
    original_session_id UUID REFERENCES sessions(id), -- For reschedule/cancellation requests
    
    -- Approval workflow
    status TEXT NOT NULL CHECK (status IN ('pending', 'approved_by_tutor', 'approved_by_student', 'approved_by_admin', 'rejected_by_tutor', 'rejected_by_student', 'rejected_by_admin', 'cancelled')),
    student_approval TEXT CHECK (student_approval IN ('pending', 'approved', 'rejected')),
    tutor_approval TEXT CHECK (tutor_approval IN ('pending', 'approved', 'rejected')),
    admin_approval TEXT CHECK (admin_approval IN ('pending', 'approved', 'rejected')),
    
    -- Additional flags
    urgency TEXT CHECK (urgency IN ('high', 'medium', 'low')),
    has_conflict BOOLEAN DEFAULT false,
    conflict_details JSONB, -- Store details about the conflict
    auto_accepted BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    -- Constraints to prevent overlapping sessions
    CONSTRAINT no_student_overlap EXCLUDE USING gist (
        student_id WITH =,
        tstzrange(start_time, end_time) WITH &&
    ),
    CONSTRAINT no_tutor_overlap EXCLUDE USING gist (
        tutor_id WITH =,
        tstzrange(start_time, end_time) WITH &&
    )
);

-- Index for efficient querying
CREATE INDEX idx_session_requests_student_id ON session_requests(student_id);
CREATE INDEX idx_session_requests_tutor_id ON session_requests(tutor_id);
CREATE INDEX idx_session_requests_status ON session_requests(status);
CREATE INDEX idx_session_requests_start_time ON session_requests(start_time);

-- Create a function to get the assigned tutor for a session
CREATE OR REPLACE FUNCTION get_assigned_tutor_for_session(
    p_batch_id UUID,
    p_topic_id UUID,
    p_subtopic_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    tutor_id UUID;
    batch_topic_id UUID;
BEGIN
    -- If a subtopic is specified, check if there's a custom tutor at the subtopic level
    IF p_subtopic_id IS NOT NULL THEN
        -- Find the batch_topic_id first
        SELECT bt.id INTO batch_topic_id
        FROM batch_topics bt
        WHERE bt.batch_id = p_batch_id AND bt.topic_id = p_topic_id;

        -- Then check for a custom tutor at the subtopic level
        SELECT bs.custom_tutor_id INTO tutor_id
        FROM batch_subtopics bs
        WHERE bs.batch_topic_id = batch_topic_id AND bs.subtopic_id = p_subtopic_id;

        IF tutor_id IS NOT NULL THEN
            RETURN tutor_id;
        END IF;
    END IF;

    -- Check if there's a custom tutor at the topic level
    SELECT custom_tutor_id INTO tutor_id
    FROM batch_topics
    WHERE batch_id = p_batch_id AND topic_id = p_topic_id;

    IF tutor_id IS NOT NULL THEN
        RETURN tutor_id;
    END IF;

    -- Finally, fall back to the default tutor for the batch
    SELECT default_tutor_id INTO tutor_id
    FROM batches
    WHERE id = p_batch_id;

    RETURN tutor_id;
END;
$$ LANGUAGE plpgsql;

-- Note: This function determines the assigned tutor based on the hierarchy. The actual tutor assignment
-- for a session is stored directly in the sessions table and can be different
-- from the assigned tutor based on availability, admin decisions, etc.

-- Create triggers to update timestamps
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to all tables with updated_at
CREATE TRIGGER update_profiles_timestamp BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_subjects_timestamp BEFORE UPDATE ON subjects FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_topics_timestamp BEFORE UPDATE ON topics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_subtopics_timestamp BEFORE UPDATE ON subtopics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_resources_timestamp BEFORE UPDATE ON resources FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batches_timestamp BEFORE UPDATE ON batches FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batch_topics_timestamp BEFORE UPDATE ON batch_topics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batch_subtopics_timestamp BEFORE UPDATE ON batch_subtopics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_sessions_timestamp BEFORE UPDATE ON sessions FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_requests_timestamp BEFORE UPDATE ON session_requests FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_details_timestamp BEFORE UPDATE ON session_details FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_feedback_timestamp BEFORE UPDATE ON session_feedback FOR EACH ROW EXECUTE FUNCTION update_timestamp();

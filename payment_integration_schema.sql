-- Payment Integration Database Schema Updates
-- This file contains all the database schema changes needed for Stripe payment integration

-- =====================================================
-- 1. EXTEND EXISTING TABLES
-- =====================================================

-- Add payment-related columns to subscription_workflows table
ALTER TABLE subscription_workflows
ADD COLUMN IF NOT EXISTS stripe_payment_intent_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
ADD COLUMN IF NOT EXISTS payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action')),
ADD COLUMN IF NOT EXISTS total_amount DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'usd',
ADD COLUMN IF NOT EXISTS payment_method_type TEXT, -- 'card', 'bank_transfer', etc.
ADD COLUMN IF NOT EXISTS payment_completed_at TIMESTAMP;

-- Add indexes for payment-related queries
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_stripe_payment_intent
ON subscription_workflows(stripe_payment_intent_id);

CREATE INDEX IF NOT EXISTS idx_subscription_workflows_stripe_customer
ON subscription_workflows(stripe_customer_id);

CREATE INDEX IF NOT EXISTS idx_subscription_workflows_payment_status
ON subscription_workflows(payment_status);

-- =====================================================
-- 2. PAYMENTS TABLE
-- =====================================================

-- Main payments tracking table
CREATE TABLE IF NOT EXISTS payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE CASCADE,
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,

    -- Stripe identifiers
    stripe_payment_intent_id TEXT UNIQUE NOT NULL,
    stripe_charge_id TEXT,
    stripe_customer_id TEXT,

    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd' NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action')),
    payment_method_type TEXT, -- 'card', 'bank_transfer', etc.

    -- Payment metadata
    description TEXT,
    receipt_email TEXT,
    receipt_url TEXT,

    -- Failure information
    failure_code TEXT,
    failure_message TEXT,

    -- Timestamps
    stripe_created_at TIMESTAMP,
    succeeded_at TIMESTAMP,
    failed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for payments table
CREATE INDEX IF NOT EXISTS idx_payments_workflow_id ON payments(workflow_id);
CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);
CREATE INDEX IF NOT EXISTS idx_payments_stripe_payment_intent ON payments(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- =====================================================
-- 3. SUBSCRIPTIONS TABLE (RENAMED FROM student_subscriptions)
-- =====================================================

-- Migration: Rename student_subscriptions to subscriptions and add new columns
-- First, rename the existing table if it exists
DO $$
BEGIN
    -- Check if student_subscriptions table exists and subscriptions doesn't
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'student_subscriptions')
       AND NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions') THEN

        -- Rename the table
        ALTER TABLE student_subscriptions RENAME TO subscriptions;

        -- Add new columns for Stripe integration
        ALTER TABLE subscriptions
        ADD COLUMN IF NOT EXISTS workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE SET NULL,
        ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT UNIQUE,
        ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
        ADD COLUMN IF NOT EXISTS stripe_price_id TEXT,
        ADD COLUMN IF NOT EXISTS current_period_start TIMESTAMP,
        ADD COLUMN IF NOT EXISTS current_period_end TIMESTAMP,
        ADD COLUMN IF NOT EXISTS trial_start TIMESTAMP,
        ADD COLUMN IF NOT EXISTS trial_end TIMESTAMP,
        ADD COLUMN IF NOT EXISTS canceled_at TIMESTAMP,
        ADD COLUMN IF NOT EXISTS ended_at TIMESTAMP,
        ADD COLUMN IF NOT EXISTS amount DECIMAL(10,2),
        ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'usd',
        ADD COLUMN IF NOT EXISTS interval_type TEXT CHECK (interval_type IN ('one_time', 'monthly', 'yearly')),
        ADD COLUMN IF NOT EXISTS access_granted_at TIMESTAMP,
        ADD COLUMN IF NOT EXISTS access_expires_at TIMESTAMP;

        -- Update status check constraint to include new values
        ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS student_subscriptions_status_check;
        ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_status_check
        CHECK (status IN ('active', 'expired', 'cancelled', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid'));

        -- Rename columns to match new schema
        ALTER TABLE subscriptions RENAME COLUMN start_date TO current_period_start;
        ALTER TABLE subscriptions RENAME COLUMN end_date TO current_period_end;

    END IF;
END $$;

-- Create subscriptions table if it doesn't exist (for fresh installations)
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE RESTRICT,
    invoice_id UUID REFERENCES invoices(id),
    workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE SET NULL,

    -- Stripe identifiers
    stripe_subscription_id TEXT UNIQUE,
    stripe_customer_id TEXT,
    stripe_price_id TEXT,

    -- Subscription details
    status TEXT NOT NULL CHECK (status IN ('active', 'expired', 'cancelled', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid')),
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    trial_start TIMESTAMP,
    trial_end TIMESTAMP,
    canceled_at TIMESTAMP,
    ended_at TIMESTAMP,

    -- Billing details
    amount DECIMAL(10,2),
    currency TEXT DEFAULT 'usd',
    interval_type TEXT CHECK (interval_type IN ('one_time', 'monthly', 'yearly')),

    -- Access control
    access_granted_at TIMESTAMP,
    access_expires_at TIMESTAMP,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Indexes for subscriptions table
CREATE INDEX IF NOT EXISTS idx_subscriptions_student_id ON subscriptions(student_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_product_id ON subscriptions(product_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_current_period ON subscriptions(current_period_start, current_period_end);

-- =====================================================
-- 4. INVOICES TABLE MIGRATION
-- =====================================================

-- Migrate existing invoices table to support Stripe integration
-- The existing table has: id, student_id, amount, status, payment_method, payment_id,
-- created_at, updated_at, actual_amount, workflow_id

DO $$
BEGIN
    -- Check if invoices table exists and add missing columns
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoices') THEN

        RAISE NOTICE 'Migrating existing invoices table to support Stripe integration...';

        -- Add new columns for Stripe integration
        ALTER TABLE invoices
        ADD COLUMN IF NOT EXISTS subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
        ADD COLUMN IF NOT EXISTS stripe_invoice_id TEXT UNIQUE,
        ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
        ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT,
        ADD COLUMN IF NOT EXISTS invoice_number TEXT,
        ADD COLUMN IF NOT EXISTS amount_due DECIMAL(10,2),
        ADD COLUMN IF NOT EXISTS amount_paid DECIMAL(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS amount_remaining DECIMAL(10,2) DEFAULT 0,
        ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'usd',
        ADD COLUMN IF NOT EXISTS due_date TIMESTAMP,
        ADD COLUMN IF NOT EXISTS paid_at TIMESTAMP,
        ADD COLUMN IF NOT EXISTS invoice_pdf_url TEXT,
        ADD COLUMN IF NOT EXISTS hosted_invoice_url TEXT,
        ADD COLUMN IF NOT EXISTS receipt_number TEXT,
        ADD COLUMN IF NOT EXISTS stripe_created_at TIMESTAMP;

        -- Update existing data to populate new columns
        UPDATE invoices
        SET
            amount_due = amount,
            amount_paid = CASE WHEN status = 'paid' THEN amount ELSE 0 END,
            amount_remaining = CASE WHEN status = 'paid' THEN 0 ELSE amount END,
            currency = 'usd',
            paid_at = CASE WHEN status = 'paid' THEN updated_at END
        WHERE amount_due IS NULL;

        -- Update status check constraint to include both old and new statuses
        ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_status_check;
        ALTER TABLE invoices ADD CONSTRAINT invoices_status_check
        CHECK (status IN ('pending', 'paid', 'failed', 'refunded', 'draft', 'open', 'uncollectible', 'void'));

        RAISE NOTICE 'Invoices table migration completed.';

    ELSE
        -- Create new invoices table if it doesn't exist
        CREATE TABLE invoices (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
            workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE SET NULL,
            subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,

            -- Stripe identifiers
            stripe_invoice_id TEXT UNIQUE,
            stripe_customer_id TEXT,
            stripe_subscription_id TEXT,

            -- Invoice details
            invoice_number TEXT,
            amount DECIMAL(10,2) NOT NULL,
            actual_amount DECIMAL(10,2),
            amount_due DECIMAL(10,2),
            amount_paid DECIMAL(10,2) DEFAULT 0,
            amount_remaining DECIMAL(10,2) DEFAULT 0,
            currency TEXT DEFAULT 'usd',

            -- Payment details
            payment_method TEXT,
            payment_id TEXT,

            -- Status and dates
            status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed', 'refunded', 'draft', 'open', 'uncollectible', 'void')),
            due_date TIMESTAMP,
            paid_at TIMESTAMP,

            -- URLs and metadata
            invoice_pdf_url TEXT,
            hosted_invoice_url TEXT,
            receipt_number TEXT,

            -- Timestamps
            stripe_created_at TIMESTAMP,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        RAISE NOTICE 'New invoices table created.';
    END IF;
END $$;

-- Indexes for invoices table
CREATE INDEX IF NOT EXISTS idx_invoices_subscription_id ON invoices(subscription_id);
CREATE INDEX IF NOT EXISTS idx_invoices_student_id ON invoices(student_id);
CREATE INDEX IF NOT EXISTS idx_invoices_stripe_invoice ON invoices(stripe_invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);

-- =====================================================
-- 5. PAYMENT METHODS TABLE
-- =====================================================

-- Store customer payment methods for future use
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,

    -- Stripe identifiers
    stripe_payment_method_id TEXT UNIQUE NOT NULL,
    stripe_customer_id TEXT NOT NULL,

    -- Payment method details
    type TEXT NOT NULL, -- 'card', 'bank_account', etc.
    card_brand TEXT, -- 'visa', 'mastercard', etc.
    card_last4 TEXT,
    card_exp_month INTEGER,
    card_exp_year INTEGER,

    -- Status
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,

    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for payment_methods table
CREATE INDEX IF NOT EXISTS idx_payment_methods_student_id ON payment_methods(student_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_stripe_payment_method ON payment_methods(stripe_payment_method_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_stripe_customer ON payment_methods(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_default ON payment_methods(student_id, is_default) WHERE is_default = TRUE;

-- =====================================================
-- 6. PAYMENT EVENTS TABLE
-- =====================================================

-- Track all payment-related events for auditing
CREATE TABLE IF NOT EXISTS payment_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID REFERENCES payments(id) ON DELETE CASCADE,
    workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE CASCADE,

    -- Event details
    event_type TEXT NOT NULL, -- 'payment_created', 'payment_succeeded', 'payment_failed', etc.
    stripe_event_id TEXT,

    -- Event data
    event_data JSONB,
    processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT,

    -- Timestamps
    stripe_created_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP
);

-- Indexes for payment_events table
CREATE INDEX IF NOT EXISTS idx_payment_events_payment_id ON payment_events(payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_workflow_id ON payment_events(workflow_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_type ON payment_events(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_events_stripe_event ON payment_events(stripe_event_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_processed ON payment_events(processed);
CREATE INDEX IF NOT EXISTS idx_payment_events_created_at ON payment_events(created_at);

// logger.ts
import { supabase } from "@/lib/supabaseClient";
import { LogLevel, LogEntry, LoggerConfig } from "./types.ts";

// Default configuration
const DEFAULT_CONFIG: LoggerConfig = {
  supabaseEnabled: false,
  consoleEnabled: false,
  throttleMs: 5000, // Throttle duplicate logs
  batchIntervalMs: 10000, // Batch logs every 10s
};

class SupabaseLogger {
  private queue: LogEntry[] = [];
  private config: LoggerConfig;
  private lastLogTimes: Record<string, number> = {}; // Track last log time per message

  constructor(initialConfig?: Partial<LoggerConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...initialConfig };
    this.initBatchProcessing();
    this.loadConfig();
  }

  // Initialize
  private async loadConfig() {
    try {
      const localStorageConfig = localStorage.getItem("loggerConfig");
      if (localStorageConfig) {
        this.config = { ...this.config, ...JSON.parse(localStorageConfig) };
      } else {
        const { data, error } = await supabase
          .from("config")
          .select("value")
          .eq("key", "logger_config")
          .single();
        if (data?.value) this.config = { ...this.config, ...data.value };
        if (error) console.error("Error loading logger config:", error);
      }
    } catch (err) {
      console.error("Failed to load logger config:", err);
    }
  }

  // Batch processing
  private initBatchProcessing() {
    setInterval(() => this.flush(), this.config.batchIntervalMs);
  }

  // Throttling check
  private shouldThrottle(message: string): boolean {
    const now = Date.now();
    const lastTime = this.lastLogTimes[message] || 0;
    if (now - lastTime < this.config.throttleMs) return true;
    this.lastLogTimes[message] = now;
    return false;
  }

  // Get current user ID safely
  private async getCurrentUserId(): Promise<string | undefined> {
    try {
      const { data } = await supabase.auth.getSession();
      return data.session?.user?.id;
    } catch (err) {
      console.error("Error getting user ID:", err);
      return undefined;
    }
  }

  // Add log to queue
  private async enqueue(level: LogLevel, message: string, data?: unknown) {
    if (this.shouldThrottle(message)) return;

    try {
      const userId = await this.getCurrentUserId();
      
      this.queue.push({
        level,
        message: String(message).slice(0, 1000),
        data: data ? JSON.parse(JSON.stringify(data)) : undefined,
        timestamp: new Date(),
        user_id: userId,
      });

      if (this.config.consoleEnabled) {
        console[level](message, data);
      }
    } catch (err) {
      console.error("Error enqueueing log:", err);
    }
  }

  // Flush logs to Supabase
  private async flush() {
    if (!this.config.supabaseEnabled || this.queue.length === 0) return;

    const logsToSend = [...this.queue];
    this.queue = []; // Clear queue

    try {
      const { error } = await supabase.from("logs").insert(logsToSend);
      if (error) console.error("Supabase log error:", error);
    } catch (err) {
      console.error("Failed to flush logs:", err);
      // Re-add logs to queue if they failed to send
      this.queue = [...this.queue, ...logsToSend];
    }
  }

  // Public methods
  public async error(message: string, data?: unknown) {
    await this.enqueue("error", message, data);
    if (this.config.supabaseEnabled) await this.flush(); // Immediate flush for errors
  }

  public async warn(message: string, data?: unknown) {
    await this.enqueue("warn", message, data);
  }

  public async info(message: string, data?: unknown) {
    await this.enqueue("info", message, data);
  }

  public async debug(message: string, data?: unknown) {
    await this.enqueue("debug", message, data);
  }

  public updateConfig(newConfig: Partial<LoggerConfig>) {
    this.config = { ...this.config, ...newConfig };
    localStorage.setItem("loggerConfig", JSON.stringify(this.config));
  }
}

// Singleton instance
export const logger = new SupabaseLogger();

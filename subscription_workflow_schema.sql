-- Four-Step Subscription Workflow Schema
-- This schema supports the complete 4-step subscription process with save/resume capability

-- Subscription Workflows table - Track the 4-step process
CREATE TABLE subscription_workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) NOT NULL,
    product_type TEXT NOT NULL CHECK (product_type IN ('booster', 'custom', 'preparation')),
    product_id UUID REFERENCES products(id), -- Set in step 1 for booster, calculated for custom/prep
    
    -- Step completion tracking
    step_1_completed BOOLEAN DEFAULT FALSE, -- Product selection
    step_2_completed BOOLEAN DEFAULT FALSE, -- Curriculum configuration (required for custom/prep)
    step_3_completed BOOLEAN DEFAULT FALSE, -- Price calculation
    step_4_completed BOOLEAN DEFAULT FALSE, -- Purchase completion
    
    -- Step completion timestamps
    step_1_completed_at TIMESTAMP WITH TIME ZONE,
    step_2_completed_at TIMESTAMP WITH TIME ZONE,
    step_3_completed_at TIMESTAMP WITH TIME ZONE,
    step_4_completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Current step (1-4)
    current_step INTEGER DEFAULT 1 CHECK (current_step BETWEEN 1 AND 4),
    
    -- Workflow status
    status TEXT NOT NULL DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'cancelled', 'admin_assistance_requested')),
    
    -- Admin assistance
    admin_assistance_requested BOOLEAN DEFAULT FALSE,
    admin_assistance_message TEXT,
    assigned_admin_id UUID REFERENCES profiles(id),
    admin_configured BOOLEAN DEFAULT FALSE,
    student_confirmation_required BOOLEAN DEFAULT FALSE,
    student_confirmed BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Ensure only one active workflow per student
    CONSTRAINT unique_active_workflow UNIQUE (student_id) DEFERRABLE INITIALLY DEFERRED
);

-- Subscription Curriculum Selections table - Store curriculum selections for custom/prep
CREATE TABLE subscription_curriculum_selections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES subscription_workflows(id) NOT NULL,
    
    -- Selection type
    selection_type TEXT NOT NULL CHECK (selection_type IN ('complete_subjects', 'selected_topics', 'selected_subtopics')),
    
    -- Selected items (stored as JSON arrays for flexibility)
    selected_subjects JSONB DEFAULT '[]'::jsonb, -- Array of subject IDs for booster
    selected_topics JSONB DEFAULT '[]'::jsonb,   -- Array of topic IDs for custom/prep
    selected_subtopics JSONB DEFAULT '[]'::jsonb, -- Array of subtopic IDs for custom/prep
    
    -- Estimated sessions needed (calculated based on selections)
    estimated_sessions INTEGER,
    
    -- Configuration metadata
    configured_by UUID REFERENCES profiles(id) NOT NULL, -- Student or admin who configured
    configured_by_role TEXT NOT NULL CHECK (configured_by_role IN ('student', 'admin')),
    configuration_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    UNIQUE(workflow_id)
);

-- Subscription Pricing table - Store pricing calculations and admin overrides
CREATE TABLE subscription_pricing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES subscription_workflows(id) NOT NULL,
    
    -- Base pricing calculation
    base_price NUMERIC(10, 2) NOT NULL, -- Calculated price based on sessions/curriculum
    sessions_count INTEGER NOT NULL, -- Number of sessions included
    price_per_session NUMERIC(10, 2), -- Price per session for custom/prep
    
    -- Admin overrides
    admin_override_applied BOOLEAN DEFAULT FALSE,
    admin_override_price NUMERIC(10, 2), -- Admin can override the final price
    admin_override_reason TEXT, -- Reason for override (discount, special case, etc.)
    admin_override_by UUID REFERENCES profiles(id),
    admin_override_at TIMESTAMP WITH TIME ZONE,
    
    -- Final pricing
    final_price NUMERIC(10, 2) NOT NULL, -- Final price (base_price or admin_override_price)
    discount_amount NUMERIC(10, 2) DEFAULT 0, -- Discount applied
    discount_percentage NUMERIC(5, 2) DEFAULT 0, -- Discount percentage
    
    -- Pricing calculation metadata
    calculation_method TEXT NOT NULL CHECK (calculation_method IN ('fixed_product_price', 'dynamic_session_based')),
    calculation_details JSONB, -- Store detailed calculation breakdown
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    UNIQUE(workflow_id)
);

-- Update invoices table to include actual amount paid
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS actual_amount NUMERIC(10, 2);
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS workflow_id UUID REFERENCES subscription_workflows(id);

-- Add indexes for better performance
CREATE INDEX idx_subscription_workflows_student_id ON subscription_workflows(student_id);
CREATE INDEX idx_subscription_workflows_status ON subscription_workflows(status);
CREATE INDEX idx_subscription_workflows_current_step ON subscription_workflows(current_step);
CREATE INDEX idx_subscription_workflows_admin_assistance ON subscription_workflows(admin_assistance_requested);
CREATE INDEX idx_subscription_curriculum_selections_workflow_id ON subscription_curriculum_selections(workflow_id);
CREATE INDEX idx_subscription_pricing_workflow_id ON subscription_pricing(workflow_id);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_subscription_workflows_updated_at 
    BEFORE UPDATE ON subscription_workflows 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_curriculum_selections_updated_at 
    BEFORE UPDATE ON subscription_curriculum_selections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_pricing_updated_at 
    BEFORE UPDATE ON subscription_pricing 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to get complete workflow data
CREATE OR REPLACE FUNCTION get_subscription_workflow_data(p_workflow_id UUID)
RETURNS TABLE (
    workflow_id UUID,
    student_id UUID,
    product_type TEXT,
    product_id UUID,
    current_step INTEGER,
    status TEXT,
    step_completions JSONB,
    admin_assistance JSONB,
    curriculum_selections JSONB,
    pricing_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sw.id as workflow_id,
        sw.student_id,
        sw.product_type,
        sw.product_id,
        sw.current_step,
        sw.status,
        jsonb_build_object(
            'step_1_completed', sw.step_1_completed,
            'step_2_completed', sw.step_2_completed,
            'step_3_completed', sw.step_3_completed,
            'step_4_completed', sw.step_4_completed,
            'step_1_completed_at', sw.step_1_completed_at,
            'step_2_completed_at', sw.step_2_completed_at,
            'step_3_completed_at', sw.step_3_completed_at,
            'step_4_completed_at', sw.step_4_completed_at
        ) as step_completions,
        jsonb_build_object(
            'admin_assistance_requested', sw.admin_assistance_requested,
            'admin_assistance_message', sw.admin_assistance_message,
            'assigned_admin_id', sw.assigned_admin_id,
            'admin_configured', sw.admin_configured,
            'student_confirmation_required', sw.student_confirmation_required,
            'student_confirmed', sw.student_confirmed
        ) as admin_assistance,
        COALESCE(
            (SELECT row_to_json(scs.*) FROM subscription_curriculum_selections scs WHERE scs.workflow_id = sw.id),
            '{}'::json
        )::jsonb as curriculum_selections,
        COALESCE(
            (SELECT row_to_json(sp.*) FROM subscription_pricing sp WHERE sp.workflow_id = sw.id),
            '{}'::json
        )::jsonb as pricing_data,
        sw.created_at,
        sw.updated_at
    FROM subscription_workflows sw
    WHERE sw.id = p_workflow_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get active workflow for a student
CREATE OR REPLACE FUNCTION get_active_workflow_for_student(p_student_id UUID)
RETURNS TABLE (
    workflow_id UUID,
    product_type TEXT,
    current_step INTEGER,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sw.id as workflow_id,
        sw.product_type,
        sw.current_step,
        sw.status
    FROM subscription_workflows sw
    WHERE sw.student_id = p_student_id 
    AND sw.status IN ('in_progress', 'admin_assistance_requested')
    ORDER BY sw.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate estimated sessions for curriculum selection
CREATE OR REPLACE FUNCTION calculate_estimated_sessions(
    p_selected_topics JSONB DEFAULT '[]'::jsonb,
    p_selected_subtopics JSONB DEFAULT '[]'::jsonb
)
RETURNS INTEGER AS $$
DECLARE
    topic_count INTEGER;
    subtopic_count INTEGER;
    estimated_sessions INTEGER;
BEGIN
    -- Count selected topics and subtopics
    topic_count := jsonb_array_length(p_selected_topics);
    subtopic_count := jsonb_array_length(p_selected_subtopics);
    
    -- Basic estimation: 2 sessions per topic, 1 session per subtopic
    -- This can be made more sophisticated based on actual curriculum data
    estimated_sessions := (topic_count * 2) + subtopic_count;
    
    -- Minimum of 1 session
    IF estimated_sessions < 1 THEN
        estimated_sessions := 1;
    END IF;
    
    RETURN estimated_sessions;
END;
$$ LANGUAGE plpgsql;

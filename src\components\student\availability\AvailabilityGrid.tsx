import React, { useRef, useState } from "react";
import { useStudentAvailabilityStore } from "@/store/studentAvailabilityStore";
import { Button } from "@/components/ui/Button";
import { Clock, Calendar, Check, X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";

// Days of the week
const daysOfWeek = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

// Generate time slots from 8 AM to 10 PM
const generateTimeSlots = (startHour: string, endHour: string) => {
  const start = parseInt(startHour);
  const end = parseInt(endHour);
  const slots = [];

  for (let hour = start; hour < end; hour++) {
    const hourFormatted = hour.toString().padStart(2, "0");
    slots.push(`${hourFormatted}:00`);
    slots.push(`${hourFormatted}:30`);
  }

  return slots;
};

// Helper to format time for display
const formatTimeForDisplay = (time: string) => {
  const [hours, minutes] = time.split(":");
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? "PM" : "AM";
  const hour12 = hour % 12 || 12;
  return `${hour12}:${minutes} ${ampm}`;
};

const AvailabilityGrid: React.FC = () => {
  // Create a ref to track newly created slots to prevent immediate status cycling
  const newlyCreatedSlotRef = useRef<string | null>(null);
  const [hoveredCell, setHoveredCell] = useState<{ day: string; time: string } | null>(null);

  // Get all the state and actions from the Zustand store
  const {
    availabilitySlots,
    updateAvailabilitySlot,
    deleteAvailabilitySlot,
    addAvailabilitySlot,
    startHour,
    endHour,
    isDragging,
    dragStart,
    dragEnd,
    slotStatus,
    setStartHour,
    setEndHour,
    setIsDragging,
    setDragStart,
    setDragEnd,
    setSlotStatus,
    handleCreateSlot,
  } = useStudentAvailabilityStore();

  // Generate time slots based on start and end hours
  const timeSlots = generateTimeSlots(startHour, endHour);

  // Filter time slots to show only those within the selected range
  const filteredTimeSlots = timeSlots;

  // Handle mouse down on a cell
  const handleMouseDown = async (day: string, time: string) => {
    // Check if there's an existing slot at this position
    const existingSlot = availabilitySlots.find(
      (slot) =>
        slot.day === day &&
        time >= slot.startTime &&
        time < slot.endTime
    );

    if (existingSlot) {
      // If there's an existing slot, cycle its status
      const nextStatus = getNextStatus(existingSlot.status);
      try {
        await updateAvailabilitySlot(existingSlot.id, { status: nextStatus });
      } catch (error) {
        console.error("Error updating slot status:", error);
      }
    } else {
      // Otherwise, start dragging to create a new slot
      setIsDragging(true);
      setDragStart({ day, time });
      setDragEnd({ day, time });
    }
  };

  // Handle mouse move over a cell
  const handleMouseMove = (day: string, time: string) => {
    setHoveredCell({ day, time });

    if (isDragging && dragStart && dragStart.day === day) {
      setDragEnd({ day, time });
    }
  };

  // Handle mouse up to complete the drag
  const handleMouseUp = () => {
    if (isDragging) {
      handleCreateSlot();
    }
  };

  // Handle click on a cell (for single-cell selection)
  const handleClick = async (day: string, time: string) => {
    // Check if there's an existing slot at this position
    const existingSlot = availabilitySlots.find(
      (slot) =>
        slot.day === day &&
        time >= slot.startTime &&
        time < slot.endTime
    );

    // If there's no existing slot and we're not dragging (just a simple click),
    // create a new slot for this single cell
    if (!existingSlot && !isDragging) {
      // Calculate the end time (30 minutes after the start time)
      const [hours, minutes] = time.split(':').map(Number);
      let endHours = hours;
      let endMinutes = minutes + 30;

      if (endMinutes >= 60) {
        endHours += 1;
        endMinutes -= 60;
      }

      const endTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;

      // Add the new slot
      try {
        await addAvailabilitySlot({
          day,
          startTime: time,
          endTime,
          status: slotStatus,
        });
      } catch (error) {
        console.error("Error creating slot:", error);
      }
    }
  };

  // Get the next status in the cycle
  const getNextStatus = (
    currentStatus: "available" | "preferred" | "unavailable"
  ): "available" | "preferred" | "unavailable" => {
    switch (currentStatus) {
      case "available":
        return "preferred";
      case "preferred":
        return "unavailable";
      case "unavailable":
        return "available";
      default:
        return "available";
    }
  };

  // Get the color class for a status
  const getStatusColorClass = (status: "available" | "preferred" | "unavailable") => {
    switch (status) {
      case "available":
        return "bg-green-100 border-green-200 text-green-800";
      case "preferred":
        return "bg-yellow-100 border-yellow-200 text-yellow-800";
      case "unavailable":
        return "bg-red-100 border-red-200 text-red-800";
      default:
        return "bg-gray-100 border-gray-200 text-gray-800";
    }
  };

  // Check if a cell is within a drag selection
  const isInDragSelection = (day: string, time: string) => {
    if (!isDragging || !dragStart || !dragEnd || dragStart.day !== day) {
      return false;
    }

    // Ensure start time is before end time
    const startTime = dragStart.time <= dragEnd.time ? dragStart.time : dragEnd.time;
    const endTime = dragStart.time <= dragEnd.time ? dragEnd.time : dragStart.time;

    // Use inclusive end time check for visual feedback to match what the user sees
    return time >= startTime && time <= endTime;
  };

  // Check if a cell is part of an existing slot
  const getExistingSlot = (day: string, time: string) => {
    return availabilitySlots.find(
      (slot) =>
        slot.day === day &&
        time >= slot.startTime &&
        time < slot.endTime
    );
  };

  // Check if this is the first cell of a slot (where we should show the content)
  const isFirstCellOfSlot = (slot: any, time: string) => {
    return time === slot.startTime;
  };

  // Get status icon based on slot status
  const getStatusIcon = (status: "available" | "preferred" | "unavailable") => {
    switch (status) {
      case "available":
        return <Check className="h-3 w-3 text-green-600" />;
      case "preferred":
        return <Clock className="h-3 w-3 text-yellow-600" />;
      case "unavailable":
        return <X className="h-3 w-3 text-red-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4 items-center mb-4">
        <div className="flex items-center space-x-2">
          <label htmlFor="startHour" className="text-sm font-medium">
            Start Hour:
          </label>
          <Select
            value={startHour}
            onValueChange={setStartHour}
          >
            <SelectTrigger className="w-24">
              <SelectValue placeholder="Start" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                <SelectItem
                  key={hour}
                  value={hour.toString().padStart(2, "0")}
                  disabled={hour >= parseInt(endHour)}
                >
                  {hour}:00
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <label htmlFor="endHour" className="text-sm font-medium">
            End Hour:
          </label>
          <Select
            value={endHour}
            onValueChange={setEndHour}
          >
            <SelectTrigger className="w-24">
              <SelectValue placeholder="End" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 25 }, (_, i) => i).map((hour) => (
                <SelectItem
                  key={hour}
                  value={hour.toString().padStart(2, "0")}
                  disabled={hour <= parseInt(startHour)}
                >
                  {hour === 24 ? "24:00" : `${hour}:00`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">Status:</span>
          <div className="flex space-x-1">
            <Button
              size="sm"
              variant="outline"
              className={
                slotStatus === "available"
                  ? "bg-green-100 border-green-200 text-green-800 hover:bg-green-200"
                  : "border-green-200 text-green-700 hover:bg-green-50"
              }
              onClick={() => setSlotStatus("available")}
            >
              <Check className="h-4 w-4 mr-1" /> Available
            </Button>
            <Button
              size="sm"
              variant="outline"
              className={
                slotStatus === "preferred"
                  ? "bg-yellow-100 border-yellow-200 text-yellow-800 hover:bg-yellow-200"
                  : "border-yellow-200 text-yellow-700 hover:bg-yellow-50"
              }
              onClick={() => setSlotStatus("preferred")}
            >
              <Clock className="h-4 w-4 mr-1" /> Preferred
            </Button>
            <Button
              size="sm"
              variant="outline"
              className={
                slotStatus === "unavailable"
                  ? "bg-red-100 border-red-200 text-red-800 hover:bg-red-200"
                  : "border-red-200 text-red-700 hover:bg-red-50"
              }
              onClick={() => setSlotStatus("unavailable")}
            >
              <X className="h-4 w-4 mr-1" /> Unavailable
            </Button>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-max">
          <div className="grid grid-cols-[100px_repeat(7,1fr)]">
            {/* Header row with days */}
            <div className="bg-gray-100 p-2 font-medium border-b border-r"></div>
            {daysOfWeek.map((day) => (
              <div
                key={day}
                className="bg-gray-100 p-2 font-medium text-center border-b border-r"
              >
                {day}
              </div>
            ))}

            {/* Time slots rows */}
            {filteredTimeSlots.map((time) => (
              <React.Fragment key={time}>
                {/* Time label */}
                <div className="p-2 text-xs text-gray-500 border-b border-r">
                  {formatTimeForDisplay(time)}
                </div>

                {/* Day cells */}
                {daysOfWeek.map((day) => {
                  const existingSlot = getExistingSlot(day, time);
                  const isInDrag = isInDragSelection(day, time);
                  const isHovered = hoveredCell?.day === day && hoveredCell?.time === time;

                  return (
                    <div
                      key={`${day}-${time}`}
                      className={`
                        border-b border-r h-8 cursor-pointer
                        ${existingSlot ? getStatusColorClass(existingSlot.status) : ""}
                        ${isInDrag ? getStatusColorClass(slotStatus) : ""}
                        ${isHovered && !existingSlot && !isInDrag ? "bg-gray-50" : ""}
                      `}
                      onMouseDown={() => handleMouseDown(day, time)}
                      onMouseMove={() => handleMouseMove(day, time)}
                      onMouseUp={handleMouseUp}
                      onClick={() => handleClick(day, time)}
                    >
                      {existingSlot && isFirstCellOfSlot(existingSlot, time) && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div
                                className="h-full w-full flex items-center justify-center text-xs group relative"
                                onClick={(e) => {
                                  // Only allow status cycling if not clicking on the delete button
                                  const target = e.target as HTMLElement;
                                  if (target.closest('button')) {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    return;
                                  }
                                }}
                              >
                                <div className="flex items-center space-x-1">
                                  {getStatusIcon(existingSlot.status)}
                                  <span className="text-xs">
                                    {formatTimeForDisplay(existingSlot.startTime)} - {formatTimeForDisplay(existingSlot.endTime)}
                                  </span>
                                </div>
                                <button
                                  className="absolute right-0 top-0 bg-white rounded-full p-0.5 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity"
                                  onClick={async (e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    try {
                                      await deleteAvailabilitySlot(existingSlot.id);
                                    } catch (error) {
                                      console.error("Error deleting slot:", error);
                                    }
                                  }}
                                  onMouseDown={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                  }}
                                >
                                  <X className="h-3 w-3 text-gray-500 hover:text-red-500" />
                                </button>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                {day} {formatTimeForDisplay(existingSlot.startTime)} - {formatTimeForDisplay(existingSlot.endTime)}
                              </p>
                              <p className="text-xs capitalize">Status: {existingSlot.status}</p>
                              <p className="text-xs text-gray-500 mt-1">Click to change status, hover for delete button</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  );
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvailabilityGrid;

import React from "react";
import { Textarea } from "@/components/ui/TextArea";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

interface BioFieldProps {
  form: any;
  name?: string;
  label?: string;
  placeholder?: string;
}

const BioField: React.FC<BioFieldProps> = ({
  form,
  name = "bio",
  label = "Bio",
  placeholder = "Share your background, experience, and approach to tutoring...",
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Textarea
              placeholder={placeholder}
              className="min-h-[200px]"
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default BioField;

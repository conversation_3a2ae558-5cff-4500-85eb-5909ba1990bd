-- Test script for the fixed set_pending_oauth_metadata function
-- This script tests the function with the corrected parameter order

-- =====================================================
-- TEST 1: Test with session_id (no email)
-- =====================================================

-- Test the function with session_id
SELECT set_pending_oauth_metadata(
    'tutor',                    -- p_user_type (required, no default)
    NULL,                       -- p_email (default NULL)
    'test_session_123',         -- p_session_id (default NULL)
    'google',                   -- p_provider (default 'google')
    10                          -- p_expires_minutes (default 10)
) AS pending_id_1;

-- =====================================================
-- TEST 2: Test with email (no session_id)
-- =====================================================

-- Test the function with email
SELECT set_pending_oauth_metadata(
    'student',                  -- p_user_type (required, no default)
    '<EMAIL>',         -- p_email (default NULL)
    NULL,                       -- p_session_id (default NULL)
    'google',                   -- p_provider (default 'google')
    15                          -- p_expires_minutes (default 10)
) AS pending_id_2;

-- =====================================================
-- TEST 3: Test with minimal parameters (using defaults)
-- =====================================================

-- Test the function with only required parameter
SELECT set_pending_oauth_metadata(
    'tutor'                     -- p_user_type (required, no default)
    -- All other parameters will use their defaults
) AS pending_id_3;

-- =====================================================
-- TEST 4: Test with named parameters (recommended approach)
-- =====================================================

-- Test using named parameters for clarity
SELECT set_pending_oauth_metadata(
    p_user_type := 'student',
    p_session_id := 'named_session_456',
    p_provider := 'microsoft',
    p_expires_minutes := 20
) AS pending_id_4;

-- =====================================================
-- VERIFY THE RESULTS
-- =====================================================

-- Check all the test records we created
SELECT 
    id,
    email,
    session_id,
    user_type,
    provider,
    expires_at,
    processed,
    created_at
FROM oauth_pending_metadata 
WHERE session_id LIKE 'test_%' 
   OR session_id LIKE 'named_%'
   OR email = '<EMAIL>'
ORDER BY created_at DESC;

-- =====================================================
-- TEST THE RETRIEVAL FUNCTION
-- =====================================================

-- Test getting metadata by session_id
SELECT * FROM get_pending_oauth_metadata(
    p_email := NULL,
    p_session_id := 'test_session_123'
);

-- Test getting metadata by email
SELECT * FROM get_pending_oauth_metadata(
    p_email := '<EMAIL>',
    p_session_id := NULL
);

-- =====================================================
-- TEST THE SYSTEM TEST FUNCTION
-- =====================================================

-- Run the built-in system test
SELECT test_oauth_pending_system() AS system_test_result;

-- =====================================================
-- CLEANUP TEST DATA
-- =====================================================

-- Clean up the test records
DELETE FROM oauth_pending_metadata 
WHERE session_id LIKE 'test_%' 
   OR session_id LIKE 'named_%'
   OR email = '<EMAIL>';

-- Verify cleanup
SELECT COUNT(*) AS remaining_test_records
FROM oauth_pending_metadata 
WHERE session_id LIKE 'test_%' 
   OR session_id LIKE 'named_%'
   OR email = '<EMAIL>';

-- =====================================================
-- FUNCTION SIGNATURE VERIFICATION
-- =====================================================

-- Check the function signature to confirm parameter order
SELECT 
    routine_name,
    parameter_name,
    ordinal_position,
    parameter_default,
    data_type
FROM information_schema.parameters 
WHERE routine_name = 'set_pending_oauth_metadata'
ORDER BY ordinal_position;

-- =====================================================
-- EXPECTED RESULTS
-- =====================================================

/*
Expected function signature:
1. p_user_type (TEXT, no default)
2. p_email (TEXT, default NULL)
3. p_session_id (TEXT, default NULL)
4. p_provider (TEXT, default 'google')
5. p_expires_minutes (INTEGER, default 10)

All tests should complete successfully without parameter errors.
The function should return UUID values for each successful call.
*/

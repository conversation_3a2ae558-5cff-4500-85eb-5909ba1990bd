import React, { ReactNode, useEffect } from "react";
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import LoadingSpinner from "@/components/LoadingSpinner";
import { UserType } from "@/context/AuthContext";
import { useAuthStore } from "@/store/authStore";

// Define route configuration by user type and onboarding status
export interface RouteConfig {
  path: string;
  requiresAuth: boolean;
  allowedUserTypes?: UserType[];
  requiresOnboarding?: boolean;
  onboardingPath?: string;
  fallbackPath?: string;
}

// Central route configuration
export const ROUTES: Record<string, RouteConfig> = {
  HOME: {
    path: "/",
    requiresAuth: false,
  },
  LOGIN: {
    path: "/login",
    requiresAuth: false,
  },
  REGISTER: {
    path: "/register",
    requiresAuth: false,
  },
  STUDENT_DASHBOARD: {
    path: "/student/dashboard",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    onboardingPath: "onboard-student",
    fallbackPath: "/",
  },
  TUTOR_DASHBOARD: {
    path: "/tutor/dashboard",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    onboardingPath: "/onboard-tutor",
    fallbackPath: "/",
  },
  TUTOR_SCHEDULE: {
    path: "/tutor/schedule",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_PENDING_REQUESTS: {
    path: "/tutor/requests/pending",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_ACCEPTED_REQUESTS: {
    path: "/tutor/requests/accepted",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_REJECTION_REQUESTS: {
    path: "/tutor/requests/rejections",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_PAST_SESSIONS: {
    path: "/tutor/sessions/history",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_STUDENT_OVERVIEW: {
    path: "/tutor/students/overview",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_STUDENT_PERFORMANCE: {
    path: "/tutor/students/performance",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  ADMIN_DASHBOARD: {
    path: "/admin-dashboard",
    requiresAuth: true,
    allowedUserTypes: ["admin"],
    fallbackPath: "/",
  },
  STUDENT_ONBOARDING: {
    path: "/onboard-student",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: false,
    fallbackPath: "/",
  },
  TUTOR_ONBOARDING: {
    path: "/onboard-tutor",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: false,
    fallbackPath: "/",
  },
  BECOME_TUTOR: {
    path: "/become-tutor",
    requiresAuth: false,
  },
  CONFIRM_EMAIL: {
    path: "/confirm-email",
    requiresAuth: false,
  },
  TERMS: {
    path: "/terms",
    requiresAuth: false,
  },
  PRIVACY: {
    path: "/privacy",
    requiresAuth: false,
  },
  HOW_IT_WORKS: {
    path: "/how-it-works",
    requiresAuth: false,
  },
  RESOURCES: {
    path: "/resources",
    requiresAuth: false,
  },
  ABOUT: {
    path: "/about",
    requiresAuth: false,
  },
  COURSES: {
    path: "/courses",
    requiresAuth: false,
  },
  CAREERS: {
    path: "/careers",
    requiresAuth: false,
  },
  PRICING: {
    path: "/pricing",
    requiresAuth: false,
  },
  TUTORS: {
    path: "/tutors",
    requiresAuth: false,
  },
  CONTACT: {
    path: "/contact",
    requiresAuth: false,
  },
  TUTOR_SEARCH: {
    path: "/tutor/search",
    requiresAuth: false,
    // Add any other required properties
  },
  STUDENT_PROFILE: {
    path: "/student/profile",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/",
  },
  STUDENT_SCHEDULE: {
    path: "/student/schedule",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_JOURNEY: {
    path: "/student/journey",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_MATERIALS: {
    path: "/student/materials",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_REQUEST_BOOKING: {
    path: "/student/request-booking",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    //requiresEnrollment: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_ACCEPT_SESSION: {
    path: "/student/accept-session",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
    TUTOR_PROFILE: {
    path: "/tutor/profile",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/",
  },
  STUDENT_PRODUCTS: {
    path: "/student/products",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_BILLING_HISTORY: {
    path: "/student/billing-history",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_SUBSCRIPTIONS: {
    path: "/student/subscriptions",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_NEW_SUBSCRIPTION: {
    path: "/student/subscriptions/new",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_NEW_SUBSCRIPTION_SELECT: {
    path: "/student/subscriptions/new/select-package",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_NEW_SUBSCRIPTION_CONFIGURE: {
    path: "/student/subscriptions/new/configure",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_NEW_SUBSCRIPTION_PRICING: {
    path: "/student/subscriptions/new/pricing",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_NEW_SUBSCRIPTION_PURCHASE: {
    path: "/student/subscriptions/new/purchase",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_ACCOUNT_PREFERENCES: {
    path: "/student/account-preferences",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_NOTIFICATIONS: {
    path: "/student/notifications",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  JOIN_SESSION: {
    path: "/join-session/:sessionId",
    requiresAuth: true,
    allowedUserTypes: ["student", "tutor", "admin"],
    requiresOnboarding: true,
    fallbackPath: "/",
  },
  STUDENT_BOOK_SESSIONS: {
    path: "/student/book-sessions",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_UPCOMING_SESSIONS: {
    path: "/student/sessions/upcoming",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  STUDENT_PAST_SESSIONS: {
    path: "/student/sessions/history",
    requiresAuth: true,
    allowedUserTypes: ["student"],
    requiresOnboarding: true,
    fallbackPath: "/student/dashboard",
  },
  TUTOR_BOOK_SESSIONS: {
    path: "/tutor/book-sessions",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_ACCOUNT_PREFERENCES: {
    path: "/tutor/account-preferences",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  TUTOR_NOTIFICATIONS: {
    path: "/tutor/notifications",
    requiresAuth: true,
    allowedUserTypes: ["tutor"],
    requiresOnboarding: true,
    fallbackPath: "/tutor/dashboard",
  },
  ADMIN_BATCHES: {
    path: "/admin/batches",
    requiresAuth: true,
    allowedUserTypes: ["admin"],
    fallbackPath: "/admin-dashboard",
  },
  ADMIN_CREATE_BATCH: {
    path: "/admin/batches/create",
    requiresAuth: true,
    allowedUserTypes: ["admin"],
    fallbackPath: "/admin-dashboard",
  },
  ADMIN_VIEW_BATCHES: {
    path: "/admin/batches/view",
    requiresAuth: true,
    allowedUserTypes: ["admin"],
    fallbackPath: "/admin-dashboard",
  },
  ADMIN_EDIT_BATCH: {
    path: "/admin/batches/edit",
    requiresAuth: true,
    allowedUserTypes: ["admin"],
    fallbackPath: "/admin-dashboard",
  },
  ADMIN_DELETE_BATCH: {
    path: "/admin/batches/delete",
    requiresAuth: true,
    allowedUserTypes: ["admin"],
    fallbackPath: "/admin-dashboard",
  },
};

// Helper function to get the appropriate route based on user state
export const getRouteForUser = (
  userType: UserType,
  isOnboarded: boolean,
  userStatus: string | null = "registered",
  source: string | null = null
): string => {
  console.log("getRouteForUser called with:", {
    userType,
    isOnboarded,
    userStatus,
    source,
  });

  // Handle special case for tutor application flow
  if (source === "become-tutor") {
    // If user is a tutor with incomplete onboarding, send to tutor onboarding
    if (userStatus === "registered" && userType === "tutor" && !isOnboarded) {
      console.log("Routing to tutor onboarding (from become-tutor flow)");
      return ROUTES.TUTOR_ONBOARDING.path;
    }

    // If user is a guest, they need to complete signup
    if (userStatus === "guest") {
      console.log("Routing to register (guest in become-tutor flow)");
      return "/register";
    }

    // If user is new, they can proceed with the tutor application
    if (userStatus === "new") {
      console.log("Routing to become-tutor page (new user)");
      return ROUTES.BECOME_TUTOR.path;
    }
  }

  // Standard routing logic
  if (!userType) return ROUTES.LOGIN.path;

  if (userType === "admin") {
    console.log("Admin user, routing to admin dashboard");
    return ROUTES.ADMIN_DASHBOARD.path;
  }

  if (isOnboarded === false) {
    if (userType === "tutor") {
      console.log("Tutor needs onboarding, routing to tutor onboarding");
      return ROUTES.TUTOR_ONBOARDING.path;
    } else {
      console.log("Student needs onboarding, routing to student onboarding");
      return ROUTES.STUDENT_ONBOARDING.path;
    }
  }

  if (userType === "tutor") {
    console.log("Onboarded tutor, routing to tutor dashboard");
    return ROUTES.TUTOR_DASHBOARD.path;
  } else {
    console.log("Onboarded student, routing to student dashboard");
    return ROUTES.STUDENT_DASHBOARD.path;
  }
};

// Helper function to handle email confirmation redirects
export const getEmailConfirmationRedirect = (
  source: string | null,
  userType: UserType,
  isOnboarded: boolean
): string => {
  // For tutor application flow
  if (source === "become-tutor") {
    // If user is a tutor with incomplete onboarding, send to tutor onboarding
    if (userType === "tutor" && !isOnboarded) {
      return ROUTES.TUTOR_ONBOARDING.path;
    }
    // Otherwise send to become-tutor page
    return ROUTES.BECOME_TUTOR.path;
  }

  // For regular signup flow, send to login
  return ROUTES.LOGIN.path;
};

// Helper function to get post-registration redirect path
export const getPostRegistrationRedirect = (
  email: string,
  userType: string,
  isGuestUpgrade: boolean = false
): string => {
  // Always redirect to confirm-email page with appropriate state
  return ROUTES.CONFIRM_EMAIL.path;
};

// Route protection wrapper component
export const ProtectedRouteWrapper = ({
  children,
  routeConfig,
}: {
  children: React.ReactNode;
  routeConfig: RouteConfig;
}) => {
  const { user, userType, isOnboarded, isInitialized, loading } = useAuth();

  // Also check Zustand store for auth state
  const storeUser = useAuthStore((state) => state.user);
  const storeUserType = useAuthStore((state) => state.userType);
  const storeIsOnboarded = useAuthStore((state) => state.isOnboarded);

  const location = useLocation();
  const navigate = useNavigate();

  // Use either context or store data, preferring context
  const effectiveUser = user || storeUser;
  // Cast to UserType to fix type error
  const effectiveUserType = (userType || storeUserType) as UserType;
  const effectiveIsOnboarded =
    isOnboarded !== null ? isOnboarded : storeIsOnboarded;

  // Debug logging for user type issues
  console.log("ProtectedRouteWrapper user type debug:", {
    userType,
    storeUserType,
    effectiveUserType,
    userTypeType: typeof userType,
    storeUserTypeType: typeof storeUserType,
    effectiveUserTypeType: typeof effectiveUserType,
  });

  useEffect(() => {
    if (!isInitialized || loading) {
      console.log("Auth not initialized yet, waiting...");
      return;
    }

    console.log("ProtectedRouteWrapper evaluating access for:", {
      path: routeConfig.path,
      requiresAuth: routeConfig.requiresAuth,
      effectiveUser: effectiveUser?.id,
      effectiveUserType,
      effectiveIsOnboarded,
      allowedUserTypes: routeConfig.allowedUserTypes,
      requiresOnboarding: routeConfig.requiresOnboarding,
    });

    // Check if route requires authentication
    if (routeConfig.requiresAuth && !effectiveUser) {
      console.log(
        "Authentication required but no user found, redirecting to login"
      );
      navigate(ROUTES.LOGIN.path, { state: { from: location } });
      return;
    }

    // If user exists, check user type restrictions
    if (
      effectiveUser &&
      routeConfig.allowedUserTypes &&
      routeConfig.allowedUserTypes.length > 0 &&
      effectiveUserType &&
      !routeConfig.allowedUserTypes.includes(effectiveUserType)
    ) {
      console.log(
        `User type ${effectiveUserType} not allowed, redirecting to fallback`
      );
      navigate(routeConfig.fallbackPath || "/", { replace: true });
      return;
    }

    // Check onboarding requirements
    if (
      effectiveUser &&
      routeConfig.requiresOnboarding &&
      !effectiveIsOnboarded
    ) {
      console.log("Onboarding required but not completed");

      // Redirect to appropriate onboarding page based on user type
      if (effectiveUserType === "student") {
        navigate(ROUTES.STUDENT_ONBOARDING.path, { replace: true });
        return;
      } else if (effectiveUserType === "tutor") {
        navigate(ROUTES.TUTOR_ONBOARDING.path, { replace: true });
        return;
      }

      // Fallback if no specific onboarding path
      navigate(routeConfig.fallbackPath || "/", { replace: true });
      return;
    }
  }, [
    isInitialized,
    loading,
    effectiveUser,
    effectiveUserType,
    effectiveIsOnboarded,
    location,
    navigate,
    routeConfig,
  ]);

  // Show loading state while auth is initializing
  if (!isInitialized || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
        <p className="ml-2">Checking authentication...</p>
      </div>
    );
  }

  return <>{children}</>;
};

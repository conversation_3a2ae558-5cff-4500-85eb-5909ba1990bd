-- Add Meeting Metadata Column to Session Details Table
-- This migration adds a dedicated column for meeting metadata while preserving 
-- the materials column for its intended purpose (recordings, whiteboards, resources)

-- =====================================================
-- 1. ADD MEETING_METADATA COLUMN
-- =====================================================

-- Add meeting_metadata column to session_details table
ALTER TABLE session_details ADD COLUMN IF NOT EXISTS meeting_metadata JSONB;

-- Add comment to clarify the purpose of each JSONB column
COMMENT ON COLUMN session_details.materials IS 'Links to study materials, recordings, whiteboards, and other resources shared during the session';
COMMENT ON COLUMN session_details.meeting_metadata IS 'Meeting provider-specific metadata including meeting IDs, settings, and provider configuration';

-- =====================================================
-- 2. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Create GIN index for efficient querying of meeting metadata
CREATE INDEX IF NOT EXISTS idx_session_details_meeting_metadata 
ON session_details USING GIN (meeting_metadata);

-- Create specific indexes for common meeting metadata queries
CREATE INDEX IF NOT EXISTS idx_session_details_meeting_provider 
ON session_details ((meeting_metadata->>'provider'));

CREATE INDEX IF NOT EXISTS idx_session_details_meeting_id 
ON session_details ((meeting_metadata->>'meeting_id'));

-- =====================================================
-- 3. SAMPLE DATA STRUCTURE EXAMPLES
-- =====================================================

-- Example 1: Microsoft Teams meeting metadata
/*
{
  "provider": "microsoft_teams",
  "meeting_id": "19:meeting_abc123def456@thread.v2",
  "join_url": "https://teams.microsoft.com/l/meetup-join/...",
  "conference_id": "123456789",
  "organizer_id": "user-uuid-here",
  "settings": {
    "allowAnonymousUsers": false,
    "recordAutomatically": true,
    "lobbyBypassSettings": "organizationAndFederated",
    "allowMeetingChat": true,
    "allowTeamsCameraOn": true,
    "allowTeamsMicOn": true
  },
  "created_at": "2024-01-15T10:00:00Z",
  "status": "scheduled"
}
*/

-- Example 2: Zoom meeting metadata (for future use)
/*
{
  "provider": "zoom",
  "meeting_id": "123456789",
  "join_url": "https://zoom.us/j/123456789",
  "password": "encrypted_password",
  "settings": {
    "waiting_room": true,
    "auto_recording": "cloud",
    "mute_upon_entry": true,
    "allow_multiple_devices": false
  },
  "created_at": "2024-01-15T10:00:00Z",
  "status": "scheduled"
}
*/

-- Example 3: Materials column usage (unchanged)
/*
{
  "recording": {
    "url": "https://teams.microsoft.com/recording/...",
    "duration": "45 minutes",
    "size": "125 MB",
    "format": "mp4"
  },
  "whiteboard": {
    "url": "https://whiteboard.microsoft.com/...",
    "title": "Math Session - Algebra",
    "last_modified": "2024-01-15T10:45:00Z"
  },
  "shared_files": [
    {
      "name": "homework_assignment.pdf",
      "url": "https://storage.example.com/files/...",
      "size": "2.3 MB"
    }
  ],
  "chat_transcript": {
    "url": "https://storage.example.com/chats/...",
    "message_count": 15
  }
}
*/

-- =====================================================
-- 4. HELPER FUNCTIONS
-- =====================================================

-- Function to get meeting provider for a session
CREATE OR REPLACE FUNCTION get_session_meeting_provider(p_session_id UUID)
RETURNS TEXT AS $$
BEGIN
    SET search_path TO public;
    
    RETURN (
        SELECT meeting_metadata->>'provider'
        FROM session_details
        WHERE session_id = p_session_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get meeting join URL for a session
CREATE OR REPLACE FUNCTION get_session_meeting_url(p_session_id UUID)
RETURNS TEXT AS $$
BEGIN
    SET search_path TO public;
    
    -- First try to get from meeting_metadata (new approach)
    DECLARE
        metadata_url TEXT;
        session_url TEXT;
    BEGIN
        SELECT meeting_metadata->>'join_url' INTO metadata_url
        FROM session_details
        WHERE session_id = p_session_id;
        
        -- Fallback to sessions.meeting_url if metadata doesn't exist
        IF metadata_url IS NULL THEN
            SELECT meeting_url INTO session_url
            FROM sessions
            WHERE id = p_session_id;
            
            RETURN session_url;
        END IF;
        
        RETURN metadata_url;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update meeting status in metadata
CREATE OR REPLACE FUNCTION update_meeting_status(
    p_session_id UUID,
    p_status TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    SET search_path TO public;
    
    UPDATE session_details
    SET meeting_metadata = jsonb_set(
        COALESCE(meeting_metadata, '{}'),
        '{status}',
        to_jsonb(p_status)
    )
    WHERE session_id = p_session_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. VALIDATION FUNCTIONS
-- =====================================================

-- Function to validate meeting metadata structure
CREATE OR REPLACE FUNCTION validate_meeting_metadata(metadata JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if required fields exist
    IF metadata IS NULL THEN
        RETURN TRUE; -- Allow NULL metadata
    END IF;
    
    -- Validate provider field exists
    IF NOT (metadata ? 'provider') THEN
        RAISE EXCEPTION 'Meeting metadata must include provider field';
    END IF;
    
    -- Validate provider is a known type
    IF NOT (metadata->>'provider' IN ('microsoft_teams', 'zoom', 'google_meet', 'webex', 'custom')) THEN
        RAISE EXCEPTION 'Unknown meeting provider: %', metadata->>'provider';
    END IF;
    
    -- Validate meeting_id exists for non-custom providers
    IF metadata->>'provider' != 'custom' AND NOT (metadata ? 'meeting_id') THEN
        RAISE EXCEPTION 'Meeting metadata must include meeting_id for provider: %', metadata->>'provider';
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add constraint to validate meeting metadata
ALTER TABLE session_details 
ADD CONSTRAINT valid_meeting_metadata 
CHECK (validate_meeting_metadata(meeting_metadata));

-- =====================================================
-- 6. MIGRATION VERIFICATION
-- =====================================================

-- Verify the column was added successfully
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'session_details' 
AND column_name = 'meeting_metadata';

-- Verify indexes were created
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'session_details' 
AND indexname LIKE '%meeting%';

-- Show the updated table structure
\d session_details;

-- =====================================================
-- 7. USAGE EXAMPLES
-- =====================================================

-- Example: Insert Teams meeting metadata
/*
INSERT INTO session_details (session_id, meeting_metadata)
VALUES (
    'your-session-uuid',
    '{
        "provider": "microsoft_teams",
        "meeting_id": "19:meeting_abc123@thread.v2",
        "join_url": "https://teams.microsoft.com/l/meetup-join/...",
        "conference_id": "123456789",
        "organizer_id": "user-uuid",
        "settings": {
            "allowAnonymousUsers": true,
            "recordAutomatically": true
        },
        "status": "scheduled"
    }'::jsonb
);
*/

-- Example: Query sessions by meeting provider
/*
SELECT s.id, s.scheduled_at, sd.meeting_metadata->>'provider' as provider
FROM sessions s
JOIN session_details sd ON s.id = sd.session_id
WHERE sd.meeting_metadata->>'provider' = 'microsoft_teams';
*/

-- Example: Get all Teams meetings with their join URLs
/*
SELECT 
    s.id,
    s.scheduled_at,
    sd.meeting_metadata->>'join_url' as teams_url,
    sd.meeting_metadata->>'meeting_id' as teams_meeting_id
FROM sessions s
JOIN session_details sd ON s.id = sd.session_id
WHERE sd.meeting_metadata->>'provider' = 'microsoft_teams';
*/

import { create } from 'zustand';
import { supabase } from '@/lib/supabaseClient';

interface TutorAccountPreferencesState {
  // Timezone state
  selectedTimezone: string;
  originalTimezone: string;
  hasChanges: boolean;
  isSaving: boolean;
  isInitialized: boolean;

  // UI state
  autoDetect: boolean;
  showMultipleTimezones: boolean;
  timezoneNotifications: boolean;
  lastUpdated: Date;
  isRefreshing: boolean;
  updateTrigger: number; // For forcing re-renders

  // Actions
  setSelectedTimezone: (timezone: string) => void;
  setHasChanges: (hasChanges: boolean) => void;
  setIsSaving: (isSaving: boolean) => void;
  setIsInitialized: (isInitialized: boolean) => void;
  initializeTimezone: (currentTimezone: string) => void;
  saveTimezoneSettings: (userId: string, timezone: string, refreshProfileData?: () => Promise<void>) => Promise<{ success: boolean; error?: string }>;
  resetChanges: () => void;
  resetStore: () => void;

  // UI actions
  setAutoDetect: (autoDetect: boolean) => void;
  setShowMultipleTimezones: (showMultipleTimezones: boolean) => void;
  setTimezoneNotifications: (timezoneNotifications: boolean) => void;
  setLastUpdated: (lastUpdated: Date) => void;
  setIsRefreshing: (isRefreshing: boolean) => void;
  triggerUpdate: () => void;

  // Future preference categories can be added here
  // notifications: NotificationPreferences;
  // language: LanguagePreferences;
  // theme: ThemePreferences;
}

/**
 * Zustand store for managing tutor account preferences
 * Handles timezone settings and can be extended for other preferences
 */
export const useTutorAccountPreferencesStore = create<TutorAccountPreferencesState>((set, get) => ({
  // Initial state
  selectedTimezone: '',
  originalTimezone: '',
  hasChanges: false,
  isSaving: false,
  isInitialized: false,

  // UI state initial values
  autoDetect: true,
  showMultipleTimezones: false,
  timezoneNotifications: true,
  lastUpdated: new Date(),
  isRefreshing: false,
  updateTrigger: 0,

  // Actions
  setSelectedTimezone: (timezone: string) => {
    const state = get();
    set({
      selectedTimezone: timezone,
      hasChanges: state.isInitialized && timezone !== state.originalTimezone
    });
  },

  setHasChanges: (hasChanges: boolean) => set({ hasChanges }),

  setIsSaving: (isSaving: boolean) => set({ isSaving }),

  setIsInitialized: (isInitialized: boolean) => set({ isInitialized }),

  initializeTimezone: (currentTimezone: string) => {
    if (currentTimezone) {
      set({
        selectedTimezone: currentTimezone,
        originalTimezone: currentTimezone,
        hasChanges: false,
        isInitialized: true
      });
    }
  },

  saveTimezoneSettings: async (userId: string, timezone: string, refreshProfileData?: () => Promise<void>) => {
    if (!userId) {
      return { success: false, error: 'User not found. Please try logging in again.' };
    }

    set({ isSaving: true });

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ timezone })
        .eq('id', userId);

      if (error) throw error;

      // Refresh the complete profile data in AuthContext to ensure consistency
      if (refreshProfileData) {
        try {
          await refreshProfileData();
          console.log('Profile data refreshed after timezone update');
        } catch (refreshError) {
          console.error('Error refreshing profile data after timezone update:', refreshError);
          // Don't fail the save operation if refresh fails
        }
      }

      set({
        originalTimezone: timezone,
        hasChanges: false,
        isSaving: false
      });

      return { success: true };
    } catch (error) {
      console.error('Error saving timezone:', error);
      set({ isSaving: false });

      return {
        success: false,
        error: 'Failed to save timezone settings. Please try again.'
      };
    }
  },

  resetChanges: () => {
    set({ hasChanges: false });
  },

  resetStore: () => {
    set({
      selectedTimezone: '',
      originalTimezone: '',
      hasChanges: false,
      isSaving: false,
      isInitialized: false,
      autoDetect: true,
      showMultipleTimezones: false,
      timezoneNotifications: true,
      lastUpdated: new Date(),
      isRefreshing: false,
      updateTrigger: 0,
    });
  },

  // UI actions
  setAutoDetect: (autoDetect: boolean) => set({ autoDetect }),

  setShowMultipleTimezones: (showMultipleTimezones: boolean) => set({ showMultipleTimezones }),

  setTimezoneNotifications: (timezoneNotifications: boolean) => set({ timezoneNotifications }),

  setLastUpdated: (lastUpdated: Date) => set({ lastUpdated }),

  setIsRefreshing: (isRefreshing: boolean) => set({ isRefreshing }),

  triggerUpdate: () => {
    const state = get();
    set({ updateTrigger: state.updateTrigger + 1 });
  },

  // Future methods for other preferences can be added here
  // updateNotificationPreferences: async (preferences) => { ... },
  // updateLanguagePreferences: async (language) => { ... },
  // updateThemePreferences: async (theme) => { ... },
}));

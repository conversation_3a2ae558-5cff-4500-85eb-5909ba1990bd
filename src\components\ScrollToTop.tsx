import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Component that scrolls to top whenever the route changes
 * This should be placed in the App component to work globally
 */
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    const scrollToTop = () => {
      // Use multiple methods to ensure cross-browser compatibility
      try {
        // Method 1: Simple scrollTo
        window.scrollTo(0, 0);
        
        // Method 2: Direct DOM manipulation
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
        
        // Method 3: Modern scrollTo with options
        if (window.scrollTo) {
          window.scrollTo({
            top: 0,
            left: 0,
            behavior: 'auto'
          });
        }
        
        // Method 4: Force scroll using requestAnimationFrame
        requestAnimationFrame(() => {
          window.scrollTo(0, 0);
        });
      } catch (error) {
        console.warn('ScrollToTop failed:', error);
      }
    };

    // Execute immediately
    scrollToTop();

    // Execute with multiple delays to handle different loading scenarios
    const timeouts = [
      setTimeout(scrollToTop, 0),
      setTimeout(scrollToTop, 10),
      setTimeout(scrollToTop, 50),
      setTimeout(scrollToTop, 100),
      setTimeout(scrollToTop, 200)
    ];

    // Cleanup timeouts
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [pathname]);

  return null; // This component doesn't render anything
};

export default ScrollToTop;

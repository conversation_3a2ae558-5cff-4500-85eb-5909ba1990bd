import React from "react";
import { useStudentUpcomingSessionsStore } from "@/store/studentUpcomingSessionsStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import { Badge } from "@/components/ui/Badge";
import {
  Search,
  Filter,
  Columns,
  Calendar,
  MoreHorizontal,
  Video,
  RefreshCw,
  Clock,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";

const StudentUpcomingSessions = () => {
  // Use the Zustand store
  const {
    searchTerm,
    setSearchTerm,
    visibleColumns,
    setVisibleColumns,
    isReminderModalOpen,
    setIsReminderModalOpen,
    selectedSession,
    setSelectedSession,
    isCalendarView,
    setIsCalendarView,
    filteredSessions
  } = useStudentUpcomingSessionsStore();

  // Get profile data for the UserNavbar
  const profileData = useProfileData();

  const handleJoinSession = (session: any) => {
    console.log(`Joining session ${session.id}`);
    // In a real app, this would open the session room
  };

  const handleActionsClick = (session: any, action: string) => {
    if (action === "Join") {
      handleJoinSession(session);
    } else if (action === "Reschedule") {
      console.log(`Rescheduling session ${session.id}`);
    } else if (action === "Cancel") {
      console.log(`Cancelling session ${session.id}`);
    } else {
      console.log(`Action ${action} clicked for session ${session.id}`);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Scheduled": { color: "bg-green-100 text-green-800", icon: <Clock size={12} /> },
      "Pending Confirmation": { color: "bg-yellow-100 text-yellow-800", icon: <Clock size={12} /> },
      "Cancelled": { color: "bg-red-100 text-red-800", icon: null },
      "Completed": { color: "bg-blue-100 text-blue-800", icon: null },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      color: "bg-gray-100 text-gray-800", 
      icon: null 
    };

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        {config.icon}
        {status}
      </Badge>
    );
  };

  const getActionButtons = (session: any) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {session.actions.map((action: string) => (
            <button
              key={action}
              className="flex w-full items-center px-2 py-1.5 text-sm hover:bg-gray-100 rounded"
              onClick={() => handleActionsClick(session, action)}
            >
              {action === "Join" && <Video className="mr-2 h-4 w-4" />}
              {action}
            </button>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  return (
    <StudentPageLayout
      title="Upcoming Sessions"
      description="View and manage your scheduled learning sessions"
    >
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-4 border-b border-gray-200 flex flex-wrap justify-between items-center gap-4">
          <div className="relative flex-grow max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by tutor, subject, or topic"
              className="pl-10 pr-4 py-2 w-full border-gray-200 rounded-md focus:border-rfpurple-500 focus:ring focus:ring-rfpurple-200 focus:ring-opacity-50 transition-colors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter size={16} />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <div className="p-2 text-sm font-medium text-gray-700 border-b">
                  Filter Options
                </div>
                <div className="p-2 text-sm text-gray-500">
                  Coming soon...
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Columns size={16} />
                  Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <div className="p-2 text-sm font-medium text-gray-700 border-b">
                  Toggle Columns
                </div>
                {Object.entries(visibleColumns).map(([key, value]) => (
                  <DropdownMenuCheckboxItem
                    key={key}
                    checked={value}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ [key]: checked })
                    }
                    className="capitalize"
                  >
                    {key === "dateTime" ? "Date & Time" : key}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsCalendarView(!isCalendarView)}
              className="flex items-center gap-2"
            >
              <Calendar size={16} />
              {isCalendarView ? "Table View" : "Calendar View"}
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw size={16} />
              Refresh
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {visibleColumns.id && <TableHead>Session ID</TableHead>}
                {visibleColumns.externalId && <TableHead>External ID</TableHead>}
                {visibleColumns.tutor && <TableHead>Tutor</TableHead>}
                {visibleColumns.country && <TableHead>Country</TableHead>}
                {visibleColumns.subject && <TableHead>Subject</TableHead>}
                {visibleColumns.topic && <TableHead>Topic</TableHead>}
                {visibleColumns.subtopic && <TableHead>Subtopic</TableHead>}
                {visibleColumns.sessionType && <TableHead>Type</TableHead>}
                {visibleColumns.dateTime && <TableHead>Date & Time</TableHead>}
                {visibleColumns.status && <TableHead>Status</TableHead>}
                {visibleColumns.actions && <TableHead>Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSessions.map((session) => (
                <TableRow key={session.id}>
                  {visibleColumns.id && (
                    <TableCell className="font-medium">{session.id}</TableCell>
                  )}
                  {visibleColumns.externalId && (
                    <TableCell>{session.externalId}</TableCell>
                  )}
                  {visibleColumns.tutor && (
                    <TableCell>
                      <div>
                        <div className="font-medium">{session.tutor.name}</div>
                        <div className="text-sm text-gray-500">{session.tutor.email}</div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.country && (
                    <TableCell>{session.tutor.country}</TableCell>
                  )}
                  {visibleColumns.subject && (
                    <TableCell>{session.subject}</TableCell>
                  )}
                  {visibleColumns.topic && (
                    <TableCell>{session.topic}</TableCell>
                  )}
                  {visibleColumns.subtopic && (
                    <TableCell>{session.subtopic}</TableCell>
                  )}
                  {visibleColumns.sessionType && (
                    <TableCell>{session.sessionType}</TableCell>
                  )}
                  {visibleColumns.dateTime && (
                    <TableCell>
                      <div>
                        <div className="font-medium">{session.dateTime.date}</div>
                        <div className="text-sm text-gray-500">{session.dateTime.time}</div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.status && (
                    <TableCell>{getStatusBadge(session.status)}</TableCell>
                  )}
                  {visibleColumns.actions && (
                    <TableCell>{getActionButtons(session)}</TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {filteredSessions.length === 0 && (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 mx-auto text-gray-300 mb-2" />
            <p className="text-gray-500">No upcoming sessions found.</p>
          </div>
        )}
      </div>

      {/* Reminder Modal */}
      <Dialog open={isReminderModalOpen} onOpenChange={setIsReminderModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Session Reminder</DialogTitle>
            <DialogDescription>
              {selectedSession && (
                <>
                  Session with {selectedSession.tutor.name} on {selectedSession.dateTime.date} at {selectedSession.dateTime.time}
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReminderModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setIsReminderModalOpen(false)}>
              Set Reminder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </StudentPageLayout>
  );
};

export default StudentUpcomingSessions;

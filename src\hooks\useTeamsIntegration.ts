import { useState, useEffect, useCallback } from 'react';
import { teamsService, TeamsSessionData, TeamsMeetingResponse } from '@/services/teamsServiceBackend';
import { useToast } from '@/hooks/use-toast';
import { config } from '@/config/environment';

interface UseTeamsIntegrationReturn {
  isAuthenticated: boolean;
  isInitializing: boolean;
  isCreatingMeeting: boolean;
  authenticate: () => Promise<boolean>;
  createMeeting: (sessionData: TeamsSessionData) => Promise<TeamsMeetingResponse | null>;
  createAndStoreMeeting: (sessionId: string, sessionData: TeamsSessionData) => Promise<boolean>;
  signOut: () => Promise<void>;
  isTeamsEnabled: boolean;
}

export const useTeamsIntegration = (): UseTeamsIntegrationReturn => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [isCreatingMeeting, setIsCreatingMeeting] = useState(false);
  const { toast } = useToast();

  // Check if Teams is enabled in configuration
  const isTeamsEnabled = config.meeting.providers.microsoft_teams.enabled;

  /**
   * Initialize Teams service on component mount
   */
  useEffect(() => {
    const initializeTeams = async () => {
      if (!isTeamsEnabled) {
        setIsInitializing(false);
        return;
      }

      try {
        const isAuth = await teamsService.isAuthenticated();
        setIsAuthenticated(isAuth);
      } catch (error) {
        console.error('Failed to initialize Teams service:', error);
        toast({
          title: "Teams Integration Error",
          description: "Failed to initialize Microsoft Teams integration.",
          variant: "destructive",
        });
      } finally {
        setIsInitializing(false);
      }
    };

    initializeTeams();
  }, [isTeamsEnabled, toast]);

  /**
   * Handle Teams authentication callback from URL parameters
   */
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const teamsSuccess = urlParams.get('teams_success');
    const teamsError = urlParams.get('teams_error');

    if (teamsSuccess === 'true') {
      setIsAuthenticated(true);
      toast({
        title: "Teams Connected",
        description: "Successfully connected to Microsoft Teams.",
      });

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('teams_success');
      window.history.replaceState({}, '', newUrl.toString());
    }

    if (teamsError) {
      toast({
        title: "Authentication Failed",
        description: `Failed to connect to Teams: ${teamsError.replace(/_/g, ' ')}`,
        variant: "destructive",
      });

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('teams_error');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [toast]);

  /**
   * Authenticate with Microsoft Teams
   */
  const authenticate = useCallback(async (): Promise<boolean> => {
    if (!isTeamsEnabled) {
      toast({
        title: "Teams Not Enabled",
        description: "Microsoft Teams integration is not enabled.",
        variant: "destructive",
      });
      return false;
    }

    try {
      const currentUrl = window.location.href;
      const success = await teamsService.authenticate(currentUrl);

      // Note: For backend auth, the user will be redirected to Microsoft
      // and then back to the app, so we don't update state here
      if (!success) {
        toast({
          title: "Authentication Failed",
          description: "Failed to initiate Teams authentication.",
          variant: "destructive",
        });
      }

      return success;
    } catch (error) {
      console.error('Teams authentication error:', error);
      toast({
        title: "Authentication Error",
        description: "An error occurred during Teams authentication.",
        variant: "destructive",
      });
      return false;
    }
  }, [isTeamsEnabled, toast]);

  /**
   * Create a Teams meeting
   */
  const createMeeting = useCallback(async (
    sessionData: TeamsSessionData
  ): Promise<TeamsMeetingResponse | null> => {
    if (!isAuthenticated) {
      toast({
        title: "Not Authenticated",
        description: "Please authenticate with Microsoft Teams first.",
        variant: "destructive",
      });
      return null;
    }

    setIsCreatingMeeting(true);
    
    try {
      const meetingData = await teamsService.createMeeting(sessionData);
      
      toast({
        title: "Meeting Created",
        description: "Teams meeting created successfully.",
      });
      
      return meetingData;
    } catch (error) {
      console.error('Failed to create Teams meeting:', error);
      toast({
        title: "Meeting Creation Failed",
        description: "Failed to create Teams meeting. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsCreatingMeeting(false);
    }
  }, [isAuthenticated, toast]);

  /**
   * Create Teams meeting and store in database
   */
  const createAndStoreMeeting = useCallback(async (
    sessionId: string,
    sessionData: TeamsSessionData
  ): Promise<boolean> => {
    if (!isAuthenticated) {
      const authSuccess = await authenticate();
      if (!authSuccess) {
        return false;
      }
    }

    setIsCreatingMeeting(true);

    try {
      // Create the Teams meeting
      const meetingData = await teamsService.createMeeting(sessionData);
      
      // Store meeting data in database
      await teamsService.storeMeetingInDatabase(sessionId, meetingData);
      
      toast({
        title: "Meeting Ready",
        description: "Teams meeting created and session updated successfully.",
      });
      
      return true;
    } catch (error) {
      console.error('Failed to create and store Teams meeting:', error);
      toast({
        title: "Meeting Setup Failed",
        description: "Failed to set up Teams meeting for this session.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsCreatingMeeting(false);
    }
  }, [isAuthenticated, authenticate, toast]);

  /**
   * Sign out from Teams
   */
  const signOut = useCallback(async (): Promise<void> => {
    try {
      await teamsService.signOut();
      setIsAuthenticated(false);
      
      toast({
        title: "Signed Out",
        description: "Successfully signed out from Microsoft Teams.",
      });
    } catch (error) {
      console.error('Failed to sign out from Teams:', error);
      toast({
        title: "Sign Out Error",
        description: "Failed to sign out from Teams.",
        variant: "destructive",
      });
    }
  }, [toast]);

  return {
    isAuthenticated,
    isInitializing,
    isCreatingMeeting,
    authenticate,
    createMeeting,
    createAndStoreMeeting,
    signOut,
    isTeamsEnabled,
  };
};

export default useTeamsIntegration;

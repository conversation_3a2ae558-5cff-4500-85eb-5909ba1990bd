import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useSubscriptionWorkflowStore } from "@/store/subscriptionWorkflowStore";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import PaymentProcessor from "@/components/payment/PaymentProcessor";
import { PaymentResult, WorkflowPaymentData } from "@/services/payment/types";
import { useToast } from "@/hooks/useToast";
import { useCountryPricing } from "@/hooks/useCountryPricing";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage
} from "@/components/ui/Breadcrumb";
import {
  CreditCard,
  Home
} from "lucide-react";
import { Link } from "react-router-dom";
import { ROUTES } from "@/routes/RouteConfig";

const NewSubscriptionPurchase: React.FC = () => {
  const navigate = useNavigate();
  const profileData = useProfileData();
  const { user } = useAuth();
  const { currentWorkflow, selectedProduct } = useSubscriptionWorkflowStore();
  const { showToast } = useToast();
  const { countryConfig } = useCountryPricing();
  const handlePaymentSuccess = (result: PaymentResult) => {
    showToast({
      title: "Payment Successful!",
      description: "Your subscription has been activated successfully.",
      type: "success"
    });

    // Redirect to subscriptions page after a short delay
    setTimeout(() => {
      navigate(ROUTES.STUDENT_SUBSCRIPTIONS.path);
    }, 2000);
  };

  const handlePaymentError = (error: string) => {
    showToast({
      title: "Payment Failed",
      description: error,
      type: "error"
    });
  };

  const handleBack = () => {
    navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PRICING.path);
  };

  // Prepare payment data
  const getWorkflowPaymentData = (): WorkflowPaymentData | null => {
    if (!currentWorkflow || !selectedProduct || !user) {
      return null;
    }

    // Use country-based pricing for currency and amount
    const currency = countryConfig.currency.code;
    const amount = countryConfig.priceMultiplier
      ? selectedProduct.price * countryConfig.exchangeRate * countryConfig.priceMultiplier
      : selectedProduct.price * countryConfig.exchangeRate;

    return {
      workflowId: currentWorkflow.id,
      studentId: user.id,
      productId: selectedProduct.id,
      amount: amount,
      currency: currency,
      description: `${selectedProduct.name} - ${currentWorkflow.product_type} Package`,
      customerEmail: user.email || ''
    };
  };

  const workflowPaymentData = getWorkflowPaymentData();

  return (
    <StudentPageLayout
      title="Complete Purchase"
      profileData={profileData}
      description="Complete your subscription purchase"
    >
      {/* Breadcrumb Navigation */}
      <div className="mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_DASHBOARD.path} className="flex items-center">
                  <Home className="h-4 w-4 mr-1" />
                  Dashboard
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_SUBSCRIPTIONS.path}>
                  Subscriptions
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_NEW_SUBSCRIPTION.path}>
                  New Subscription
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Complete Purchase</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="space-y-6">
        {selectedProduct && workflowPaymentData ? (
          <>
            {/* Payment Processor */}
            <PaymentProcessor
              workflowData={workflowPaymentData}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
              onBack={handleBack}
            />

            {/* Terms and Conditions */}
            <Card>
              <CardContent className="pt-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Terms and Conditions</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    By completing this purchase, you agree to our terms of service and privacy policy.
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <Link to="/terms" className="text-blue-600 hover:underline">
                      Terms of Service
                    </Link>
                    <Link to="/privacy" className="text-blue-600 hover:underline">
                      Privacy Policy
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>


          </>
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-12">
                <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No Product Selected
                </h3>
                <p className="text-gray-600 mb-6">
                  Please go back and complete the previous steps to proceed with the purchase.
                </p>
                <Button onClick={() => navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION.path)}>
                  Start Over
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </StudentPageLayout>
  );
};

export default NewSubscriptionPurchase;

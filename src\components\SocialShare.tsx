import { Twitter, Facebook, <PERSON>ed<PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import config from "@/api/config";

interface SocialShareProps {
  title: string;
  description?: string;
  url?: string;
  className?: string;
}

const SocialShare = ({
  title,
  description = "",
  url,
  className = "",
}: SocialShareProps) => {
  // Use config for base URL, fallback to window location
  const baseUrl = config.appUrl;
  const currentUrl = url || window.location.href;

  const encodedUrl = encodeURIComponent(currentUrl);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);

  const shareLinks = [
    {
      name: "Twitter",
      icon: <Twitter size={18} />,
      url: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    },
    {
      name: "Facebook",
      icon: <Facebook size={18} />,
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    },
    {
      name: "LinkedIn",
      icon: <Linkedin size={18} />,
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    },
    {
      name: "Copy Link",
      icon: <Link size={18} />,
      action: () => {
        navigator.clipboard.writeText(currentUrl);
        // You could add a toast notification here
      },
    },
  ];

  return (
    <div className={`flex gap-2 ${className}`}>
      {shareLinks.map((link) =>
        link.action ? (
          <Button
            key={link.name}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            onClick={link.action}
          >
            {link.icon}
            <span className="sr-only md:not-sr-only md:inline-block">
              {link.name}
            </span>
          </Button>
        ) : (
          <Button
            key={link.name}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            asChild
          >
            <a href={link.url} target="_blank" rel="noopener noreferrer">
              {link.icon}
              <span className="sr-only md:not-sr-only md:inline-block">
                {link.name}
              </span>
            </a>
          </Button>
        )
      )}
    </div>
  );
};

export default SocialShare;

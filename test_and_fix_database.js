// Test script to check database schema and apply fixes
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'your-supabase-url';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-key';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseSchema() {
  console.log('🔍 Checking database schema...');
  
  try {
    // Check students table structure
    const { data: studentsColumns, error: studentsError } = await supabase
      .rpc('get_table_columns', { table_name: 'students' });
    
    if (studentsError) {
      console.error('Error checking students table:', studentsError);
      return;
    }
    
    console.log('📋 Students table columns:', studentsColumns);
    
    // Check for specific issues
    const hasDateOfBirth = studentsColumns?.some(col => col.column_name === 'date_of_birth');
    const subjectsColumn = studentsColumns?.find(col => col.column_name === 'subjects_of_interest');
    const goalsColumn = studentsColumns?.find(col => col.column_name === 'learning_goals');
    
    console.log('🔍 Schema Analysis:');
    console.log(`  - date_of_birth exists: ${hasDateOfBirth}`);
    console.log(`  - subjects_of_interest type: ${subjectsColumn?.data_type}`);
    console.log(`  - learning_goals type: ${goalsColumn?.data_type}`);
    
    // Check recent logs for trigger errors
    const { data: logs, error: logsError } = await supabase
      .from('logs')
      .select('*')
      .or('message.ilike.%handle_student_candidate_completion%,context->>source.ilike.%handle_student_candidate_completion%')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (logsError) {
      console.error('Error checking logs:', logsError);
    } else {
      console.log('📝 Recent trigger logs:', logs?.length || 0, 'entries');
      logs?.forEach(log => {
        console.log(`  - ${log.created_at}: ${log.message}`);
        if (log.data) {
          console.log(`    Data:`, log.data);
        }
      });
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  }
}

async function applySchemaFix() {
  console.log('🔧 Applying schema fix...');
  
  try {
    // Read the SQL fix file
    const sqlFix = fs.readFileSync('./fix_students_table_schema.sql', 'utf8');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlFix });
    
    if (error) {
      console.error('Error applying schema fix:', error);
    } else {
      console.log('✅ Schema fix applied successfully');
    }
    
  } catch (error) {
    console.error('Error applying schema fix:', error);
  }
}

async function main() {
  console.log('🚀 Starting database diagnosis and fix...');
  
  await checkDatabaseSchema();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Uncomment the next line to apply the fix
  // await applySchemaFix();
  
  console.log('✅ Database check completed');
}

main().catch(console.error);

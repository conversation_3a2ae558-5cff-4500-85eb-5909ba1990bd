import React, { useEffect } from "react";
import { useProfileData } from "@/hooks/useProfileData";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card, CardContent } from "@/components/ui/Card";
import { Input } from "@/components/ui/Input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { Textarea } from "@/components/ui/TextArea";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/Avatar";
import {
  Search,
  Clock,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star,
} from "lucide-react";
import { usePendingRequestsStore, SessionRequest } from "@/store/pendingRequestsStore";
import { useRequestCardStore } from "@/store/requestCardStore";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";

// Request Card Component
const RequestCard: React.FC<{ request: SessionRequest }> = ({ request }) => {

  const {
    showAcceptDialog,
    showRejectDialog,
    showProfileDialog,
    message,
    selectedRequestId,
    setShowAcceptDialog,
    setShowRejectDialog,
    setShowProfileDialog,
    setMessage
  } = useRequestCardStore();

  const { acceptRequest, rejectRequest } = usePendingRequestsStore();

  // Check if this card's request is the selected one
  const isSelected = selectedRequestId === request.id;

  const getUrgencyBadge = (urgency: string) => {
    switch (urgency) {
      case "high":
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <Clock className="h-3 w-3" /> Within 24 hrs
          </Badge>
        );
      case "medium":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-orange-100 text-orange-800 hover:bg-orange-200">
            <Clock className="h-3 w-3" /> Within 48 hrs
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" /> Upcoming
          </Badge>
        );
    }
  };

  return (
    <Card className="mb-4">
      <CardContent className="pt-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Student Info */}
          <div className="flex items-start gap-3 md:w-1/4">
            <Avatar className="h-12 w-12">
              {request.studentImage ? (
                <AvatarImage src={request.studentImage} alt={request.studentName} />
              ) : (
                <AvatarFallback className="bg-purple-100 text-purple-800">
                  {request.studentName.split(" ").map(n => n[0]).join("")}
                </AvatarFallback>
              )}
            </Avatar>
            <div>
              <div className="font-medium">{request.studentName}</div>
              <div className="flex items-center text-sm text-gray-500">
                <Star className="h-3 w-3 text-yellow-500 fill-yellow-500 mr-1" />
                {request.studentRating}
                {request.studentHistory > 0 && (
                  <span className="ml-2">• {request.studentHistory} sessions</span>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs px-2 py-0 h-6 mt-1 text-purple-600"
                onClick={() => setShowProfileDialog(true, request.id)}
              >
                View Profile
              </Button>
            </div>
          </div>

          {/* Session Details */}
          <div className="md:w-2/4">
            <div className="flex flex-wrap gap-2 mb-2">
              {getUrgencyBadge(request.urgency)}
              {request.autoAccept && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Auto-Accept
                </Badge>
              )}
              {request.conflictsWith && (
                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" /> Conflict
                </Badge>
              )}
            </div>

            <h3 className="font-medium">{request.topic}: {request.subtopic}</h3>
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <Calendar className="h-4 w-4 mr-1" />
              {request.requestedDate} at {request.requestedTime} • {request.sessionType}
            </div>

            {request.notes && (
              <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded-md">
                <span className="font-medium">Notes: </span>
                {request.notes}
              </div>
            )}

            {request.conflictsWith && (
              <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded-md flex items-start">
                <AlertCircle className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
                <span>Conflicts with: {request.conflictsWith}</span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="md:w-1/4 flex md:flex-col md:items-end gap-2 mt-2 md:mt-0">
            <Button
              className="bg-green-600 hover:bg-green-700 flex-1 md:flex-none w-full md:w-auto"
              onClick={() => setShowAcceptDialog(true, request.id)}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Accept
            </Button>
            <Button
              variant="outline"
              className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 flex-1 md:flex-none w-full md:w-auto"
              onClick={() => setShowRejectDialog(true, request.id)}
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject
            </Button>
          </div>
        </div>
      </CardContent>

      {/* Accept Dialog - Only render if this request is selected */}
      {isSelected && showAcceptDialog && (
        <Dialog open={showAcceptDialog} onOpenChange={(open) => setShowAcceptDialog(open)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Accept Session Request</DialogTitle>
              <DialogDescription>
                You're accepting a session request from {request.studentName} for {request.topic}: {request.subtopic}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Add a message (optional)</label>
                <Textarea
                  placeholder="Looking forward to our session! Is there anything specific you'd like to focus on?"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAcceptDialog(false)}>
                Cancel
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={() => {
                  acceptRequest(request.id, message);
                  setShowAcceptDialog(false);
                }}
              >
                Confirm Accept
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Reject Dialog - Only render if this request is selected */}
      {isSelected && showRejectDialog && (
        <Dialog open={showRejectDialog} onOpenChange={(open) => setShowRejectDialog(open)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject Session Request</DialogTitle>
              <DialogDescription>
                You're rejecting a session request from {request.studentName} for {request.topic}: {request.subtopic}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Reason for rejection (optional)</label>
                <Textarea
                  placeholder="I'm not available at this time. Would you be able to reschedule for another day?"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  rejectRequest(request.id, message);
                  setShowRejectDialog(false);
                }}
              >
                Confirm Reject
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Student Profile Dialog - Only render if this request is selected */}
      {isSelected && showProfileDialog && (
        <Dialog open={showProfileDialog} onOpenChange={(open) => setShowProfileDialog(open)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Student Profile</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarFallback className="bg-purple-100 text-purple-800 text-xl">
                    {request.studentName.split(" ").map(n => n[0]).join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium text-lg">{request.studentName}</h3>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                    <span className="font-medium">{request.studentRating}</span>
                    <span className="text-gray-500 text-sm ml-2">• {request.studentHistory} sessions</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-md">
                <h4 className="font-medium mb-2">Session History</h4>
                {request.studentHistory > 0 ? (
                  <div className="text-sm">
                    <p>• Last session: 2 weeks ago</p>
                    <p>• Most common topic: {request.topic}</p>
                    <p>• Average session length: 60 minutes</p>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">This will be your first session with this student.</p>
                )}
              </div>

              <div className="bg-gray-50 p-3 rounded-md">
                <h4 className="font-medium mb-2">Learning Preferences</h4>
                <p className="text-sm text-gray-600">
                  Prefers visual explanations and practical examples. Learns best with interactive coding sessions.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setShowProfileDialog(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
};

const PendingRequests: React.FC = () => {
  const {
    requests,
    filter,
    searchQuery,
    isLoading,
    setFilter,
    setSearchQuery,
    fetchRequests
  } = usePendingRequestsStore();
  const profileData = useProfileData();

  // Fetch requests on component mount
  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  // Filter requests based on current filter and search query
  const filteredRequests = requests.filter(request => {
    const matchesFilter =
      filter === "all" ||
      (filter === "urgent" && request.urgency === "high") ||
      (filter === "auto-accept" && request.autoAccept) ||
      (filter === "conflicts" && request.conflictsWith);

    const matchesSearch =
      searchQuery === "" ||
      request.studentName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.subtopic.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${request.topic}: ${request.subtopic}`.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  return (
    <TutorPageLayout
      title="Pending Requests"
      profileData={profileData}
      description="Review and respond to session requests from students"
      actions={
        <span className="bg-purple-100 text-purple-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
          {filteredRequests.length} request{filteredRequests.length !== 1 ? 's' : ''}
        </span>
      }
    >
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search by student, topic, subtopic..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-full sm:w-auto" onValueChange={setFilter}>
          <TabsList className="grid grid-cols-4 w-full sm:w-auto">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="urgent">Urgent</TabsTrigger>
            <TabsTrigger value="auto-accept">Auto-Accept</TabsTrigger>
            <TabsTrigger value="conflicts">Conflicts</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Requests List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700 mx-auto"></div>
            <p className="mt-4 text-gray-500">Loading requests...</p>
          </div>
        ) : filteredRequests.length > 0 ? (
          filteredRequests.map(request => (
            <RequestCard key={request.id} request={request} />
          ))
        ) : (
          <div className="text-center py-12 bg-white rounded-lg shadow-sm">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100">
              <CheckCircle className="h-6 w-6 text-gray-400" />
            </div>
            <h3 className="mt-2 text-lg font-medium text-gray-900">No pending requests</h3>
            <p className="mt-1 text-gray-500">
              You're all caught up! Check back later for new session requests.
            </p>
          </div>
        )}
      </div>
    </TutorPageLayout>
  );
};

export default PendingRequests;

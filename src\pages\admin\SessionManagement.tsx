import React from "react";
import AdminSidebar from "@/components/admin/Sidebar";
import { Link } from "react-router-dom";
import {
  Calendar,
  Edit,
  Users,
  UserMinus,
  List,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import UserProfileMenu from "@/components/UserProfileMenu";

const SessionManagement = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Session Management</h1>
            <UserProfileMenu
              isAdmin={true}
              isAdminPage={true}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
            {/* Schedule a Session */}
            <Link
              to="/admin/sessions/schedule"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Calendar className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Schedule a Session</h3>
              <p className="text-sm text-gray-500">
                Create new tutoring sessions and assign tutors and students.
              </p>
            </Link>

            {/* Edit Session */}
            <Link
              to="/admin/sessions/edit"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Edit className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Edit Session</h3>
              <p className="text-sm text-gray-500">
                Modify existing sessions, change dates, times, or other details.
              </p>
            </Link>

            {/* Add Users to Session */}
            <Link
              to="/admin/sessions/add-users"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Users className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">
                Add Users in Session
              </h3>
              <p className="text-sm text-gray-500">
                Add additional students or observers to existing sessions.
              </p>
            </Link>

            {/* Remove User from Session */}
            <Link
              to="/admin/sessions/remove-user"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <UserMinus className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">
                Remove User from Session
              </h3>
              <p className="text-sm text-gray-500">
                Remove participants from scheduled or upcoming sessions.
              </p>
            </Link>

            {/* View All Sessions */}
            <Link
              to="/admin/sessions/list"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <List className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">View All Sessions</h3>
              <p className="text-sm text-gray-500">
                Browse and search through all scheduled and completed sessions.
              </p>
            </Link>

            {/* Session Analytics */}
            <Link
              to="/admin/sessions/analytics"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Clock className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Session Analytics</h3>
              <p className="text-sm text-gray-500">
                View statistics and reports about session attendance and
                completion.
              </p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionManagement;

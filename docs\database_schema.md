# Database Schema Documentation

## Core Tables

### profiles

The `profiles` table stores user profile information and is linked to the Supabase Auth users.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | UUID | Primary key, references auth.users(id) | PRIMARY KEY, REFERENCES auth.users(id) |
| first_name | TEXT | User's first name | NULL |
| last_name | TEXT | User's last name | NULL |
| user_type | TEXT | Type of user account | NOT NULL, CHECK (user_type IN ('student', 'tutor', 'admin', 'user')) |
| created_at | TIMESTAMP WITH TIME ZONE | When the profile was created | NOT NULL, DEFAULT now() |
| updated_at | TIMESTAMP WITH TIME ZONE | When the profile was last updated | NOT NULL, DEFAULT now() |
| profile_picture_url | TEXT | URL to the user's profile picture | NULL |
| email | TEXT | User's email address | NOT NULL, UNIQUE |
| timezone | TEXT | User's timezone in IANA format (e.g., 'America/New_York') | NULL |

#### Notes:
- The `timezone` field is used for displaying dates and times in the user's local timezone
- The `user_type` determines the user's role and permissions in the application
- The `profile_picture_url` stores the URL to the user's profile picture in storage

### tutors

The `tutors` table extends the `profiles` table with tutor-specific information.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | UUID | Primary key, references profiles(id) | PRIMARY KEY, REFERENCES profiles(id) ON DELETE CASCADE |
| education_level | TEXT | Tutor's education level | NULL |
| hourly_rate | NUMERIC | Tutor's hourly rate | NULL |
| subjects_taught | TEXT | Comma-separated list of subjects taught | NULL |
| teaching_experience | TEXT | Description of teaching experience | NULL |
| bio | TEXT | Tutor's biography | NULL |
| availability | JSONB | JSON object representing availability | NULL |
| verification_status | VARCHAR | Status of tutor verification | NULL |
| cv_file_path | VARCHAR | Path to tutor's CV file | NULL |
| rating | NUMERIC | Average rating from student feedback | NULL |
| created_at | TIMESTAMP WITH TIME ZONE | When the tutor record was created | NOT NULL, DEFAULT (now() AT TIME ZONE 'utc'::text) |
| updated_at | TIMESTAMP WITH TIME ZONE | When the tutor record was last updated | NOT NULL, DEFAULT (now() AT TIME ZONE 'utc'::text) |
| date_of_birth | DATE | Tutor's date of birth | NULL |

#### Notes:
- The `availability` field is a JSON object with day of week as keys (0-6, where 0 is Sunday) and time ranges as values
- The `subjects_taught` field is a comma-separated list that should be parsed into an array in the application
- The `id` field references the `profiles` table, creating a one-to-one relationship

### students

The `students` table extends the `profiles` table with student-specific information.

| Column | Type | Description | Constraints |
|--------|------|-------------|------------|
| id | UUID | Primary key, references profiles(id) | PRIMARY KEY, REFERENCES profiles(id) ON DELETE CASCADE |
| education_level | TEXT | Student's education level | NULL |
| subjects_of_interest | TEXT[] | Array of subjects the student is interested in | NULL, DEFAULT '{}' |
| learning_goals | TEXT[] | Array of learning goals | NULL, DEFAULT '{}' |
| study_preferences | JSONB | JSON object with study preferences | NULL |
| academic_history | JSONB | JSON object with academic history | NULL |
| hobbies | TEXT[] | Array of hobbies | NULL, DEFAULT '{}' |
| interests | TEXT[] | Array of interests | NULL, DEFAULT '{}' |
| location | TEXT | Student's location | NULL |
| date_of_birth | DATE | Student's date of birth | NULL |
| profile_completeness | INTEGER | Percentage of profile completion | DEFAULT 0 |
| social_links | JSONB | JSON object with social media links | DEFAULT '{}'::jsonb |

#### Notes:
- The `profile_completeness` is calculated by a trigger function
- The `subjects_of_interest` and `learning_goals` are stored as arrays
- The `id` field references the `profiles` table, creating a one-to-one relationship

## Time-Related Features

### Timezone Usage

The `timezone` field in the `profiles` table is used for:

1. **Session Scheduling**: Converting session times to the user's local timezone
2. **Availability Display**: Showing tutor availability in the student's timezone
3. **Notifications**: Sending notifications at appropriate times in the user's timezone
4. **Calendar Integration**: Exporting calendar events with correct timezone information

### Best Practices for Working with Timezones

1. **Storage**: Always store dates and times in UTC in the database
2. **Display**: Convert to the user's timezone only for display purposes
3. **Input**: When accepting date/time input from users, capture their timezone
4. **Calculations**: Perform date/time calculations in UTC, then convert results

## Code Examples

### Fetching a User's Timezone

```typescript
// Example of fetching a user's timezone from the profiles table
const { data, error } = await supabase
  .from('profiles')
  .select('timezone')
  .eq('id', userId)
  .single();

const userTimezone = data?.timezone || 'UTC';
```

### Converting Times to User's Timezone

```typescript
// Example of converting a UTC time to the user's timezone
const convertToUserTimezone = (utcTime, userTimezone) => {
  return new Date(utcTime).toLocaleString('en-US', {
    timeZone: userTimezone
  });
};
```

## Schema Changes Log

| Date | Change | Reason |
|------|--------|--------|
| YYYY-MM-DD | Added `timezone` column to `profiles` table | To support timezone-aware scheduling and display |

## Related Components

- `TimezoneSelect.tsx`: Component for selecting a timezone
- `ProfileTimezoneSettings.tsx`: Component for updating timezone settings

## Additional Resources

- [IANA Timezone Database](https://www.iana.org/time-zones)
- [List of tz database time zones](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)
- [JavaScript Internationalization API](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)

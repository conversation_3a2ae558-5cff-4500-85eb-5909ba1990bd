# Razorpay Backend Deployment Guide

This guide covers the deployment of Razorpay backend integration using Supabase Edge Functions and database setup.

## 📋 Prerequisites

1. **Supabase Project** - Active Supabase project
2. **Supabase CLI** - Install from [Supabase CLI docs](https://supabase.com/docs/guides/cli)
3. **Razorpay Account** - Get API keys from [Razorpay Dashboard](https://dashboard.razorpay.com/)

## 🗄️ Database Setup

### Step 1: Run the Migration

Execute the migration script in your Supabase SQL Editor:

```sql
-- Copy and paste the contents of supabase/migrations/20241201000000_razorpay_backend_setup.sql
-- This will create:
-- - payment_orders table
-- - payment_refunds table  
-- - Update payments table with provider columns
-- - Insert Razorpay provider configuration
-- - Create indexes and RLS policies
```

### Step 2: Verify Database Setup

Run these verification queries:

```sql
-- Check new tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('payment_orders', 'payment_refunds');

-- Check Razorpay provider
SELECT name, display_name, is_active, supported_currencies 
FROM payment_providers 
WHERE name = 'razorpay';
```

## 🚀 Edge Functions Deployment

### Step 1: Initialize Supabase CLI

```bash
# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref YOUR_PROJECT_REF
```

### Step 2: Deploy Edge Functions

```bash
# Deploy all Razorpay functions
supabase functions deploy razorpay-create-order
supabase functions deploy razorpay-verify-payment
supabase functions deploy razorpay-refund

# Or deploy all at once
supabase functions deploy
```

### Step 3: Set Environment Variables

Set the following secrets in your Supabase project:

```bash
# Set Razorpay credentials
supabase secrets set RAZORPAY_KEY_ID=rzp_test_your_key_id
supabase secrets set RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Verify secrets are set
supabase secrets list
```

## 🔧 Environment Configuration

### Frontend Environment Variables

Add these to your `.env` file:

```env
# Razorpay Configuration
VITE_RAZORPAY_KEY_ID=rzp_test_your_key_id

# Supabase Configuration (should already exist)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
```

### Supabase Environment Variables

These are set as secrets in Supabase:

```env
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 🧪 Testing the Integration

### Test Edge Functions

```bash
# Test create order function
curl -X POST 'https://your-project.supabase.co/functions/v1/razorpay-create-order' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "amount": 2150,
    "currency": "INR",
    "receipt": "receipt_test_123",
    "notes": {
      "description": "Test order"
    }
  }'
```

### Test Payment Flow

1. **Create Order**: Use the create-order endpoint
2. **Process Payment**: Use Razorpay checkout with the order ID
3. **Verify Payment**: Use the verify-payment endpoint

## 📊 Monitoring and Logs

### View Function Logs

```bash
# View logs for specific function
supabase functions logs razorpay-create-order

# Follow logs in real-time
supabase functions logs razorpay-verify-payment --follow
```

### Database Monitoring

Monitor payment activities:

```sql
-- Recent payment orders
SELECT * FROM payment_orders 
ORDER BY created_at DESC 
LIMIT 10;

-- Payment success rate
SELECT 
  status,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM payments 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY status;
```

## 🔒 Security Considerations

### 1. API Key Security
- Never expose Razorpay secret key in frontend
- Use environment variables for all sensitive data
- Rotate keys regularly

### 2. Signature Verification
- Always verify payment signatures on backend
- Implement webhook signature verification
- Log all verification attempts

### 3. Database Security
- RLS policies are enabled on all payment tables
- Only authenticated users can access their data
- Admin-only access for refunds

## 🚨 Troubleshooting

### Common Issues

1. **Function Deployment Fails**
   ```bash
   # Check function syntax
   deno check supabase/functions/razorpay-create-order/index.ts
   ```

2. **Environment Variables Not Set**
   ```bash
   # List all secrets
   supabase secrets list
   
   # Set missing secrets
   supabase secrets set VARIABLE_NAME=value
   ```

3. **Database Connection Issues**
   - Verify RLS policies
   - Check user authentication
   - Ensure proper table permissions

4. **Razorpay API Errors**
   - Verify API keys are correct
   - Check Razorpay dashboard for errors
   - Ensure test/live mode consistency

### Debug Mode

Enable debug logging in Edge Functions:

```typescript
// Add to function code for debugging
console.log('Debug info:', { user, orderData, response });
```

## 📈 Next Steps

1. **Webhook Integration**: Set up Razorpay webhooks for real-time updates
2. **Error Handling**: Implement comprehensive error handling and retry logic
3. **Analytics**: Add payment analytics and reporting
4. **Testing**: Set up automated testing for payment flows

## 🔗 Useful Links

- [Razorpay API Documentation](https://razorpay.com/docs/api/)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Supabase CLI Reference](https://supabase.com/docs/reference/cli)

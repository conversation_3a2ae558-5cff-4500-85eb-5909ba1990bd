import { useEffect } from 'react';
import { create } from 'zustand';
import { useAuth } from '@/context/AuthContext';

export interface TimezoneData {
  value: string;
  label: string;
  offset: string;
}

// Timezone aliases mapping - maps legacy/alias timezones to their canonical forms
export const TIMEZONE_ALIASES: Record<string, string> = {
  'Asia/Calcutta': 'Asia/Kolkata',
  'US/Eastern': 'America/New_York',
  'US/Central': 'America/Chicago',
  'US/Mountain': 'America/Denver',
  'US/Pacific': 'America/Los_Angeles',
  'US/Alaska': 'America/Anchorage',
  'US/Hawaii': 'Pacific/Honolulu',
  'US/East-Indiana': 'America/Indiana/Indianapolis',
  'US/Indiana-Starke': 'America/Indiana/Knox',
  'US/Michigan': 'America/Detroit',
  'US/Arizona': 'America/Phoenix',
  'US/Aleutian': 'America/Adak',
  'US/Samoa': 'Pacific/Pago_Pago',
  'Europe/Belfast': 'Europe/London',
  'GB': 'Europe/London',
  'GMT': 'UTC',
  'UCT': 'UTC',
  'Universal': 'UTC',
  'Zulu': 'UTC',
};

/**
 * Normalize a timezone to its canonical form
 */
export const normalizeTimezone = (timezone: string): string => {
  return TIMEZONE_ALIASES[timezone] || timezone;
};

// Common timezones with their display information (using canonical forms only)
export const COMMON_TIMEZONES: TimezoneData[] = [
  // US Timezones
  { value: 'America/New_York', label: 'Eastern Time (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Detroit', label: 'Eastern Time - Detroit (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Kentucky/Louisville', label: 'Eastern Time - Louisville (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Kentucky/Monticello', label: 'Eastern Time - Monticello (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Indiana/Indianapolis', label: 'Eastern Time - Indianapolis (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Indiana/Vincennes', label: 'Eastern Time - Vincennes (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Indiana/Winamac', label: 'Eastern Time - Winamac (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Indiana/Marengo', label: 'Eastern Time - Marengo (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Indiana/Petersburg', label: 'Eastern Time - Petersburg (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Indiana/Vevay', label: 'Eastern Time - Vevay (ET)', offset: 'UTC-5/-4' },
  { value: 'America/Chicago', label: 'Central Time (CT)', offset: 'UTC-6/-5' },
  { value: 'America/Indiana/Tell_City', label: 'Central Time - Tell City (CT)', offset: 'UTC-6/-5' },
  { value: 'America/Indiana/Knox', label: 'Central Time - Knox (CT)', offset: 'UTC-6/-5' },
  { value: 'America/Menominee', label: 'Central Time - Menominee (CT)', offset: 'UTC-6/-5' },
  { value: 'America/North_Dakota/Center', label: 'Central Time - Center, ND (CT)', offset: 'UTC-6/-5' },
  { value: 'America/North_Dakota/New_Salem', label: 'Central Time - New Salem, ND (CT)', offset: 'UTC-6/-5' },
  { value: 'America/North_Dakota/Beulah', label: 'Central Time - Beulah, ND (CT)', offset: 'UTC-6/-5' },
  { value: 'America/Denver', label: 'Mountain Time (MT)', offset: 'UTC-7/-6' },
  { value: 'America/Boise', label: 'Mountain Time - Boise (MT)', offset: 'UTC-7/-6' },
  { value: 'America/Phoenix', label: 'Arizona Time (MST)', offset: 'UTC-7' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)', offset: 'UTC-8/-7' },
  { value: 'America/Anchorage', label: 'Alaska Time (AKST)', offset: 'UTC-9/-8' },
  { value: 'America/Juneau', label: 'Alaska Time - Juneau (AKST)', offset: 'UTC-9/-8' },
  { value: 'America/Sitka', label: 'Alaska Time - Sitka (AKST)', offset: 'UTC-9/-8' },
  { value: 'America/Metlakatla', label: 'Alaska Time - Metlakatla (AKST)', offset: 'UTC-9/-8' },
  { value: 'America/Yakutat', label: 'Alaska Time - Yakutat (AKST)', offset: 'UTC-9/-8' },
  { value: 'America/Nome', label: 'Alaska Time - Nome (AKST)', offset: 'UTC-9/-8' },
  { value: 'America/Adak', label: 'Hawaii-Aleutian Time - Adak (HST)', offset: 'UTC-10/-9' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time (HST)', offset: 'UTC-10' },

  // US Territories
  { value: 'America/Puerto_Rico', label: 'Atlantic Time - Puerto Rico (AST)', offset: 'UTC-4' },
  { value: 'America/St_Thomas', label: 'Atlantic Time - US Virgin Islands (AST)', offset: 'UTC-4' },
  { value: 'Pacific/Guam', label: 'Chamorro Time - Guam (ChST)', offset: 'UTC+10' },
  { value: 'Pacific/Saipan', label: 'Chamorro Time - Northern Mariana Islands (ChST)', offset: 'UTC+10' },
  { value: 'Pacific/Pago_Pago', label: 'Samoa Time - American Samoa (SST)', offset: 'UTC-11' },
  { value: 'Pacific/Wake', label: 'Wake Island Time (WAKT)', offset: 'UTC+12' },
  { value: 'Pacific/Midway', label: 'Samoa Time - Midway (SST)', offset: 'UTC-11' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)', offset: 'UTC+0/+1' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)', offset: 'UTC+1/+2' },
  { value: 'Europe/Berlin', label: 'Central European Time (CET)', offset: 'UTC+1/+2' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)', offset: 'UTC+9' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)', offset: 'UTC+8' },
  { value: 'Asia/Kolkata', label: 'India Standard Time (IST)', offset: 'UTC+5:30' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AEST)', offset: 'UTC+10/+11' },
  { value: 'UTC', label: 'Coordinated Universal Time (UTC)', offset: 'UTC+0' },
];

export interface UseTimezoneReturn {
  detectedTimezone: string;
  currentTimezone: string;
  isTimezoneConfirmed: boolean;
  getTimezoneDisplayInfo: (timezone: string) => TimezoneData;
  formatTimeInTimezone: (date: Date, timezone?: string) => string;
  detectUserTimezone: () => string;
  normalizeTimezone: (timezone: string) => string;
}

// Zustand store for timezone state
interface TimezoneStore {
  detectedTimezone: string;
  setDetectedTimezone: (timezone: string) => void;
  detectAndSetTimezone: () => void;
}

const useTimezoneStore = create<TimezoneStore>((set) => ({
  detectedTimezone: '',
  setDetectedTimezone: (timezone: string) => set({ detectedTimezone: timezone }),
  detectAndSetTimezone: () => {
    try {
      const detected = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const normalized = normalizeTimezone(detected);
      set({ detectedTimezone: normalized });
    } catch (error) {
      console.error('Error detecting timezone:', error);
      set({ detectedTimezone: 'UTC' });
    }
  },
}));

/**
 * Custom hook for timezone management
 * Provides utilities for detecting, displaying, and formatting timezones
 */
export const useTimezone = (): UseTimezoneReturn => {
  const { profileData } = useAuth();
  const { detectedTimezone, detectAndSetTimezone } = useTimezoneStore();

  // Detect user's timezone on mount
  useEffect(() => {
    detectAndSetTimezone();
  }, [detectAndSetTimezone]);

  /**
   * Detect the user's timezone using browser API and normalize it
   */
  const detectUserTimezone = (): string => {
    try {
      const detected = Intl.DateTimeFormat().resolvedOptions().timeZone;
      return normalizeTimezone(detected);
    } catch (error) {
      console.error('Error detecting timezone:', error);
      return 'UTC';
    }
  };

  /**
   * Get display information for a timezone (normalizes input first)
   */
  const getTimezoneDisplayInfo = (timezone: string): TimezoneData => {
    const normalizedTimezone = normalizeTimezone(timezone);
    const timezoneData = COMMON_TIMEZONES.find(tz => tz.value === normalizedTimezone);
    if (timezoneData) {
      return timezoneData;
    }

    // If not in common list, create basic info using the normalized timezone
    return {
      value: normalizedTimezone,
      label: normalizedTimezone.replace(/_/g, ' '),
      offset: 'Unknown'
    };
  };

  /**
   * Format a date in a specific timezone
   */
  const formatTimeInTimezone = (date: Date, timezone?: string): string => {
    const tz = timezone || currentTimezone || detectedTimezone || 'UTC';

    // Validate timezone before using it
    if (!tz || tz.trim() === '') {
      console.warn('Empty timezone provided, using UTC as fallback');
      return date.toLocaleString('en-US', {
        timeZone: 'UTC',
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
      });
    }

    try {
      return date.toLocaleString('en-US', {
        timeZone: tz,
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
      });
    } catch (error) {
      console.error('Error formatting time in timezone:', error, 'Timezone:', tz);
      // Fallback to UTC if the timezone is invalid
      try {
        return date.toLocaleString('en-US', {
          timeZone: 'UTC',
          weekday: 'short',
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          timeZoneName: 'short'
        });
      } catch (fallbackError) {
        // Final fallback to basic locale string
        return date.toLocaleString();
      }
    }
  };

  // Current timezone from profile or detected (normalize both)
  const currentTimezone = profileData?.timezone
    ? normalizeTimezone(profileData.timezone)
    : detectedTimezone;

  // Check if timezone is confirmed (matches detected)
  const isTimezoneConfirmed = currentTimezone === detectedTimezone;

  return {
    detectedTimezone,
    currentTimezone,
    isTimezoneConfirmed,
    getTimezoneDisplayInfo,
    formatTimeInTimezone,
    detectUserTimezone,
    normalizeTimezone,
  };
};

# Requirements Fulfillment

This document explains how the proposed database schema fulfills your specific business requirements.

## 1. Flexible Tutor Assignment

### Requirement
> While creating a batch admin is provided with a flexibility to chose if:
> - a tutor is assigned to a complete batch (by default)
> - a tutor is assigned per topic/subtopic (this can be done later as well by modifying a batch).
> - <PERSON><PERSON> can override tutor assignments at batch/topic/subtopic/session level and manage session logistics.

### Implementation

The schema supports this requirement through a hierarchical tutor assignment system:

1. **Batch Level Assignment**:
   ```sql
   CREATE TABLE batches (
     -- other fields
     default_tutor_id UUID REFERENCES profiles(id),
     -- other fields
   );
   ```

2. **Topic Level Assignment**:
   ```sql
   CREATE TABLE batch_topics (
     -- other fields
     custom_tutor_id UUID REFERENCES profiles(id),
     -- other fields
   );
   ```

3. **Subtopic Level Assignment**:
   ```sql
   CREATE TABLE batch_subtopics (
     -- other fields
     custom_tutor_id UUID REFERENCES profiles(id),
     -- other fields
   );
   ```

4. **Session Level Assignment**:
   ```sql
   CREATE TABLE sessions (
     -- other fields
     tutor_id UUID REFERENCES profiles(id) NOT NULL,
     -- other fields
   );
   ```

5. **Tutor Assignment Logic**:
   ```sql
   CREATE OR REPLACE FUNCTION get_assigned_tutor_for_session(
       p_batch_id UUID,
       p_topic_id UUID,
       p_subtopic_id UUID DEFAULT NULL
   )
   RETURNS UUID AS $$
   -- Logic to check subtopic, topic, and batch levels in order
   $$ LANGUAGE plpgsql;
   ```

This implementation allows admins to:
- Assign a default tutor to an entire batch
- Override the default tutor for specific topics
- Override both the batch and topic tutors for specific subtopics
- Assign any tutor to a specific session regardless of the hierarchy

The key improvement is that tutor resolution happens at the session level, not the subtopic level. This recognizes that the same subtopic can be covered across multiple sessions, each potentially with different tutors. The `get_assigned_tutor_for_session` function determines the assigned tutor based on the hierarchy, but the actual tutor assignment is stored directly in the sessions table.

## 2. Learning Journey

### Requirement
> Learning Journey:
> - Modal Detail Views: Click on a node (topic/session) in the map opens a side panel with detailed view at sub-topic and session level + editable notes.
> - Backend-Powered Lazy Loading: Only fetch and render data for visible/expanded parts of the journey.

### Implementation

The schema supports this requirement through its hierarchical structure and relationships:

1. **Hierarchical Content Structure**:
   ```sql
   CREATE TABLE subjects (id UUID PRIMARY KEY, name TEXT, ...);
   CREATE TABLE topics (id UUID PRIMARY KEY, subject_id UUID REFERENCES subjects(id), ...);
   CREATE TABLE subtopics (id UUID PRIMARY KEY, topic_id UUID REFERENCES topics(id), ...);
   ```

2. **Progress Tracking**:
   ```sql
   CREATE TABLE batch_topics (
     -- other fields
     status TEXT NOT NULL CHECK (status IN ('not_started', 'in_progress', 'completed')),
     -- other fields
   );

   CREATE TABLE batch_subtopics (
     -- other fields
     status TEXT NOT NULL CHECK (status IN ('not_started', 'in_progress', 'completed')),
     -- other fields
   );
   ```

3. **Session History**:
   ```sql
   CREATE TABLE sessions (
     -- other fields
     batch_id UUID REFERENCES batches(id) NOT NULL,
     topic_id UUID REFERENCES topics(id) NOT NULL,
     subtopic_id UUID REFERENCES subtopics(id),
     -- other fields
   );
   ```

This implementation supports:
- A tree-like structure for the learning journey (subjects → topics → subtopics)
- Progress tracking at both topic and subtopic levels
- Lazy loading by allowing queries to filter by specific topics or subtopics
- Modal detail views by providing detailed information about subtopics and sessions

## 3. Session Management

### Requirement
> Session management features should be handled separately, with cancellation/rejection options not included in the actions modal.

### Implementation

The schema supports comprehensive session management:

1. **Session Requests**:
   ```sql
   CREATE TABLE session_requests (
     -- other fields
     status TEXT NOT NULL CHECK (status IN ('pending', 'accepted', 'rejected', 'cancelled')),
     -- other fields
   );
   ```

2. **Sessions**:
   ```sql
   CREATE TABLE sessions (
     -- other fields
     status TEXT NOT NULL CHECK (status IN ('scheduled', 'completed', 'cancelled')),
     -- other fields
   );
   ```

3. **Session Details**:
   ```sql
   CREATE TABLE session_details (
     session_id UUID PRIMARY KEY REFERENCES sessions(id),
     tutor_talk_time INTEGER,
     whiteboard_interactions INTEGER,
     -- other fields
   );
   ```

4. **Session Feedback**:
   ```sql
   CREATE TABLE session_feedback (
     -- other fields
     session_id UUID REFERENCES sessions(id) NOT NULL,
     submitted_by UUID REFERENCES profiles(id) NOT NULL,
     rating INTEGER CHECK (rating BETWEEN 1 AND 5),
     comments TEXT,
     -- other fields
   );
   ```

This implementation supports:
- Separate handling of session requests and actual sessions
- Different statuses for requests (pending, accepted, rejected, cancelled)
- Different statuses for sessions (scheduled, completed, cancelled)
- Detailed session metrics and feedback

## 4. Tutor Availability Management

### Requirement
> The Tutor Availability Management System should include an interactive availability grid, Google Calendar integration, auto-accept rules engine, tutor preferences, and time zone management.

### Implementation

The schema supports tutor availability management:

1. **Availability Slots**:
   ```sql
   CREATE TABLE tutor_availability (
     -- other fields
     tutor_id UUID REFERENCES profiles(id) NOT NULL,
     day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6),
     start_time TIME NOT NULL,
     end_time TIME NOT NULL,
     status TEXT NOT NULL CHECK (status IN ('available', 'auto_accept', 'manual_approval')),
     -- other fields
   );
   ```

2. **Auto-Accept Rules**:
   ```sql
   CREATE TABLE tutor_auto_accept_rules (
     -- other fields
     tutor_id UUID REFERENCES profiles(id) NOT NULL,
     name TEXT NOT NULL,
     is_active BOOLEAN DEFAULT true,
     existing_students_only BOOLEAN DEFAULT false,
     -- other fields
   );

   CREATE TABLE rule_topics (
     -- other fields
     rule_id UUID REFERENCES tutor_auto_accept_rules(id) NOT NULL,
     topic_id UUID REFERENCES topics(id) NOT NULL,
     -- other fields
   );

   CREATE TABLE rule_time_ranges (
     -- other fields
     rule_id UUID REFERENCES tutor_auto_accept_rules(id) NOT NULL,
     day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6),
     start_time TIME,
     end_time TIME,
     -- other fields
   );
   ```

3. **Time Zone Management**:
   ```sql
   CREATE TABLE profiles (
     -- other fields
     timezone TEXT,
     -- other fields
   );
   ```

This implementation supports:
- An availability grid with day and time slots
- Auto-accept rules with conditions based on topics, time ranges, and student history
- Time zone management for both tutors and students

## 5. Performance Tracking

### Requirement
> Create a Performance & Activity Monitoring page for tutors with student selection by batch, metrics like completion percentage, attendance rate, session feedback scores, and inactivity flags for students needing follow-up.

### Implementation

The schema supports performance tracking:

1. **Batch Progress**:
   ```sql
   CREATE TABLE batch_topics (
     -- other fields
     status TEXT NOT NULL CHECK (status IN ('not_started', 'in_progress', 'completed')),
     -- other fields
   );

   CREATE TABLE batch_subtopics (
     -- other fields
     status TEXT NOT NULL CHECK (status IN ('not_started', 'in_progress', 'completed')),
     -- other fields
   );
   ```

2. **Session Metrics**:
   ```sql
   CREATE TABLE session_details (
     session_id UUID PRIMARY KEY REFERENCES sessions(id),
     tutor_talk_time INTEGER,
     whiteboard_interactions INTEGER,
     messages_sent INTEGER,
     messages_received INTEGER,
     participation_score DECIMAL(3,1),
     participation_level TEXT,
     -- other fields
   );
   ```

3. **Session Feedback**:
   ```sql
   CREATE TABLE session_feedback (
     -- other fields
     rating INTEGER CHECK (rating BETWEEN 1 AND 5),
     comments TEXT,
     -- other fields
   );
   ```

This implementation supports:
- Tracking completion percentage through batch_topics and batch_subtopics status
- Tracking attendance rate through sessions and their status
- Tracking session feedback scores through session_feedback
- Identifying inactive students by analyzing session history and request patterns

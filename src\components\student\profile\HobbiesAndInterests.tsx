import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Edit, Plus, Heart } from "lucide-react";

interface HobbiesAndInterestsProps {
  hobbies: string[];
}

const HobbiesAndInterests: React.FC<HobbiesAndInterestsProps> = ({ hobbies }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center">
          <CardTitle className="text-xl">Hobbies & interests</CardTitle>
          <span className="ml-2 text-sm text-gray-500">{hobbies.length} records</span>
        </div>
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {hobbies.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {hobbies.map((hobby, index) => (
              <Badge key={index} variant="secondary" className="bg-gray-100 hover:bg-gray-200 text-gray-800">
                {hobby}
              </Badge>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-500">
            <Heart className="h-10 w-10 mx-auto mb-2 opacity-20" />
            <p>No hobbies or interests added yet</p>
            <Button variant="outline" size="sm" className="mt-2">
              <Plus className="h-4 w-4 mr-1" />
              Add Hobby or Interest
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default HobbiesAndInterests;

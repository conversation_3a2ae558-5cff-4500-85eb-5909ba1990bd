import React from "react";
import { z } from "zod";
import FileUpload, { FileUploadProps } from "./FileUpload";
import { useGuestTutorFormStore } from "@/store/guestTutorFormStore";
import { cvFileSchema } from "@/services/errorHandler";

// Export the type for use in parent components
export type CVFileValue = z.infer<typeof cvFileSchema>;

type FileUploadCVProps = Omit<
  FileUploadProps,
  "accept" | "maxSize" | "buttonText"
> & {
  buttonText?: string;
  onFileRemove?: () => void;
  required?: boolean;
  status?: "idle" | "uploading" | "success" | "error";
  fileName?: string;
  errorMessage?: string;
};

const FileUploadCV = React.forwardRef<HTMLDivElement, FileUploadCVProps>(({
  buttonText = "Upload CV",
  onFileRemove,
  onFileSelect,
  required = true,
  status = "idle",
  fileName,
  errorMessage,
  ...props
}, ref) => {
  const { cvFile, setCvFile, touchField } = useGuestTutorFormStore();

  // Wrap the onFileSelect to handle errors properly
  const handleFileSelect = (file: File) => {
    // Mark field as touched
    touchField("cvFile");

    if (onFileSelect) {
      onFileSelect(file);
    }

    if (file) {
      // Validate the file
      const validation = useGuestTutorFormStore
        .getState()
        .validateField("cvFile", file.name);
      setCvFile(file.name, validation.isValid, validation.error);
    }
  };

  // Wrap the onFileRemove to handle validation
  const handleFileRemove = () => {
    // Mark field as touched
    touchField("cvFile");

    if (onFileRemove) {
      onFileRemove();
    }

    // Validate empty value
    const validation = useGuestTutorFormStore
      .getState()
      .validateField("cvFile", "");
    setCvFile("", validation.isValid, validation.error);
  };

  // Render error message only if field has been touched
  const renderErrorMessage = () => {
    if (cvFile.touched && cvFile.error) {
      return (
        <div className="text-sm font-medium text-destructive mt-2">
          {cvFile.error}
        </div>
      );
    }
    return null;
  };

  // Only show file info if upload was successful
  const showFileInfo = status === "success" && fileName;

  return (
    <div ref={ref} className="space-y-2">
      {!showFileInfo && (
        <FileUpload
          accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          maxSize={10 * 1024 * 1024} // 10MB
          buttonText={buttonText}
          dragDropText={`Drag and drop your CV here, or click to select${
            required ? " (required)" : ""
          }`}
          onFileRemove={handleFileRemove}
          onFileSelect={handleFileSelect}
          required={required}
          status={status}
          {...props}
        />
      )}

      {/* Show file info only after successful upload */}
      {showFileInfo && (
        <div className="p-4 border border-border rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">
                Selected file: {fileName}
              </span>
            </div>
            <button
              type="button"
              onClick={handleFileRemove}
              className="text-destructive hover:text-destructive/80"
              aria-label="Remove file"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Add error message display */}
      {renderErrorMessage()}

      {/* Show error message from props if provided */}
      {errorMessage && status === "error" && (
        <div className="text-sm font-medium text-destructive mt-2">
          {errorMessage}
        </div>
      )}
    </div>
  );
});

FileUploadCV.displayName = "FileUploadCV";

export default FileUploadCV;

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 250 70% 98%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 260 77% 75%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 255 94% 87%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 260 77% 75%;

    --radius: 0.75rem;

    /* No longer needed as we use the HorizontalDivider component */

    --sidebar-background: 260 77% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 260 77% 75%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
    --sidebar-ring: 260 77% 75%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 260 77% 75%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 260 77% 75%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply text-foreground;
    background-color: white;
    font-feature-settings: "rlig" 1, "calt" 1;
    margin: 0;
    padding: 0;
  }

  html {
    scroll-behavior: smooth;
  }

  #root {
    min-height: 100vh;
  }

  /* Ensure sticky positioning works correctly */
  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }
}

@layer components {
  .hero-gradient {
    @apply bg-gradient-to-br from-rfpurple-200 via-white to-rfblue-200;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-white to-rfpurple-50;
  }

  .button-gradient {
    @apply bg-gradient-to-r from-rfpurple-500 to-rfpurple-600 hover:from-rfpurple-600 hover:to-rfpurple-700;
  }

  /* App layout structure */
  .app-layout {
    display: flex;
    min-height: 100vh;
    position: relative;
  }

  .app-layout .sidebar {
    position: sticky;
    top: 0;
    height: 100vh;
    z-index: 40;
    overflow-y: auto;
    flex-shrink: 0;
  }

  .app-layout .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    min-height: 100vh;
  }
}


/* Add responsive styles for the hero section */
.hero-section {
  --background: 0 0% 100% !important;
  background-color: hsl(0, 0%, 100%) !important;
  padding: 1rem;
}

@media (min-width: 640px) {
  .hero-section {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .hero-section {
    padding: 2rem;
  }
}



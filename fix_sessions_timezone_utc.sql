-- Fix sessions table and related functions to use UTC timezone
-- This ensures all timestamps are stored in UTC for proper timezone handling

-- =====================================================
-- STEP 1: UPDATE THE UPDATE_TIMESTAMP FUNCTION TO USE UTC
-- =====================================================

-- Drop and recreate the update_timestamp function to use UTC
DROP FUNCTION IF EXISTS update_timestamp() CASCADE;

CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    -- Use timezone('UTC', now()) to ensure UTC storage
    NEW.updated_at = timezone('UTC', now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 2: UPDATE SESSIONS TABLE DEFAULT VALUES TO USE UTC
-- =====================================================

-- Update the default values for created_at and updated_at to use UTC
ALTER TABLE sessions 
ALTER COLUMN created_at SET DEFAULT timezone('UTC', now());

ALTER TABLE sessions 
ALTER COLUMN updated_at SET DEFAULT timezone('UTC', now());

-- =====================================================
-- STEP 3: RECREATE ALL TRIGGERS THAT USE THE UPDATE_TIMESTAMP FUNCTION
-- =====================================================

-- Recreate triggers for all tables that use the update_timestamp function
CREATE TRIGGER update_profiles_timestamp BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_subjects_timestamp BEFORE UPDATE ON subjects FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_topics_timestamp BEFORE UPDATE ON topics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_subtopics_timestamp BEFORE UPDATE ON subtopics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_resources_timestamp BEFORE UPDATE ON resources FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batches_timestamp BEFORE UPDATE ON batches FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batch_topics_timestamp BEFORE UPDATE ON batch_topics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_batch_subtopics_timestamp BEFORE UPDATE ON batch_subtopics FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_sessions_timestamp BEFORE UPDATE ON sessions FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_requests_timestamp BEFORE UPDATE ON session_requests FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_details_timestamp BEFORE UPDATE ON session_details FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_session_feedback_timestamp BEFORE UPDATE ON session_feedback FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_tutor_availability_timestamp BEFORE UPDATE ON tutor_availability FOR EACH ROW EXECUTE FUNCTION update_timestamp();
CREATE TRIGGER update_tutor_auto_accept_rules_timestamp BEFORE UPDATE ON tutor_auto_accept_rules FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- =====================================================
-- STEP 4: UPDATE OTHER CRITICAL TABLES TO USE UTC DEFAULTS
-- =====================================================

-- Update other critical tables that need UTC timestamps
ALTER TABLE session_requests 
ALTER COLUMN created_at SET DEFAULT timezone('UTC', now()),
ALTER COLUMN updated_at SET DEFAULT timezone('UTC', now());

ALTER TABLE session_details 
ALTER COLUMN created_at SET DEFAULT timezone('UTC', now()),
ALTER COLUMN updated_at SET DEFAULT timezone('UTC', now());

ALTER TABLE session_feedback 
ALTER COLUMN created_at SET DEFAULT timezone('UTC', now()),
ALTER COLUMN updated_at SET DEFAULT timezone('UTC', now());

-- Update subscription workflow tables
ALTER TABLE subscription_workflows 
ALTER COLUMN created_at SET DEFAULT timezone('UTC', now()),
ALTER COLUMN updated_at SET DEFAULT timezone('UTC', now());

-- Update payment related tables
ALTER TABLE payments 
ALTER COLUMN created_at SET DEFAULT timezone('UTC', now()),
ALTER COLUMN updated_at SET DEFAULT timezone('UTC', now());

-- =====================================================
-- STEP 5: CREATE HELPER FUNCTIONS FOR TIMEZONE CONVERSION
-- =====================================================

-- Function to convert UTC timestamp to user's timezone
CREATE OR REPLACE FUNCTION convert_to_user_timezone(
    utc_timestamp TIMESTAMP WITH TIME ZONE,
    user_timezone TEXT
)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    -- Convert UTC timestamp to user's timezone
    RETURN utc_timestamp AT TIME ZONE user_timezone;
EXCEPTION
    WHEN OTHERS THEN
        -- If timezone conversion fails, return original timestamp
        RETURN utc_timestamp;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to get current UTC timestamp (for explicit use in queries)
CREATE OR REPLACE FUNCTION utc_now()
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN timezone('UTC', now());
END;
$$ LANGUAGE plpgsql STABLE;

-- =====================================================
-- STEP 6: UPDATE EXISTING DATA TO ENSURE UTC STORAGE
-- =====================================================

-- Note: This step assumes your database server is already in UTC
-- If your server is in a different timezone, you may need to adjust existing data

-- Check current timezone setting
SELECT current_setting('timezone') as current_db_timezone;

-- Show sample of current session timestamps to verify
SELECT 
    id,
    scheduled_at,
    created_at,
    updated_at,
    extract(timezone from created_at) as created_at_tz_offset,
    extract(timezone from scheduled_at) as scheduled_at_tz_offset
FROM sessions 
LIMIT 5;

-- =====================================================
-- STEP 7: VERIFICATION QUERIES
-- =====================================================

-- Verify the function works correctly
SELECT 
    'UTC Now' as test_name,
    utc_now() as utc_timestamp,
    timezone('UTC', now()) as direct_utc,
    now() as server_now;

-- Test timezone conversion function
SELECT 
    'Timezone Conversion Test' as test_name,
    utc_now() as utc_time,
    convert_to_user_timezone(utc_now(), 'America/New_York') as ny_time,
    convert_to_user_timezone(utc_now(), 'Europe/London') as london_time,
    convert_to_user_timezone(utc_now(), 'Asia/Tokyo') as tokyo_time;

-- Verify table defaults
SELECT 
    table_name,
    column_name,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sessions' 
AND column_name IN ('created_at', 'updated_at')
ORDER BY table_name, column_name;

-- =====================================================
-- STEP 8: GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions on the new functions
GRANT EXECUTE ON FUNCTION convert_to_user_timezone(TIMESTAMP WITH TIME ZONE, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION convert_to_user_timezone(TIMESTAMP WITH TIME ZONE, TEXT) TO anon;
GRANT EXECUTE ON FUNCTION utc_now() TO authenticated;
GRANT EXECUTE ON FUNCTION utc_now() TO anon;

-- =====================================================
-- STEP 9: USAGE EXAMPLES AND BEST PRACTICES
-- =====================================================

/*
USAGE EXAMPLES:

1. Insert a new session with explicit UTC timestamp:
INSERT INTO sessions (batch_id, topic_id, tutor_id, student_id, scheduled_at, duration_min, status, mode, created_by)
VALUES ('batch-id', 'topic-id', 'tutor-id', 'student-id', utc_now(), 60, 'scheduled', 'video', 'student');

2. Query sessions and convert to user's timezone:
SELECT 
    id,
    convert_to_user_timezone(scheduled_at, 'America/New_York') as scheduled_at_ny,
    convert_to_user_timezone(created_at, p.timezone) as created_at_user_tz
FROM sessions s
JOIN profiles p ON s.student_id = p.id
WHERE s.student_id = 'user-id';

3. Filter sessions by date range in user's timezone:
SELECT * FROM sessions s
JOIN profiles p ON s.student_id = p.id
WHERE convert_to_user_timezone(s.scheduled_at, p.timezone)::date = '2024-01-15';

BEST PRACTICES:
- Always store timestamps in UTC using timezone('UTC', now()) or utc_now()
- Convert to user timezone only for display purposes
- Use the user's timezone from profiles.timezone for conversion
- Test timezone conversions thoroughly with different timezones
*/

// src/components/admin/batches/BatchCreationSidebar.tsx
import React from "react";
import { useBatchCreationStore } from "@/store/batchCreationStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import {
  User,
  Package,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  BookOpen,
  Users,
  MapPin,
  GraduationCap,
  ExternalLink
} from "lucide-react";
import { format } from "date-fns";

const BatchCreationSidebar: React.FC = () => {
  const { selectedStudentData, selectedSubscription } = useBatchCreationStore();

  // Handler for viewing complete student record
  const handleViewStudentRecord = (studentId: string) => {
    // Open student record in new tab
    window.open(`/admin/students/${studentId}`, '_blank');
  };

  // Get the selected subscription details
  const subscription = selectedStudentData?.subscriptions.find(
    sub => sub.id === selectedSubscription
  );

  if (!selectedStudentData) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Users className="h-5 w-5 mr-2" />
              Student Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 text-center py-8">
              Select a student to view their information
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Student Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <User className="h-5 w-5 mr-2" />
            Student Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <p className="font-medium text-gray-900">
              {selectedStudentData.first_name} {selectedStudentData.last_name}
            </p>
            <p className="text-sm text-gray-600">{selectedStudentData.email}</p>

            {/* Location and Grade Information */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                <span>{getStudentLocation(selectedStudentData)}</span>
              </div>
              <div className="flex items-center">
                <GraduationCap className="h-4 w-4 mr-1" />
                <span>{getStudentGrade(selectedStudentData)}</span>
              </div>
            </div>
          </div>

          <div className="pt-2 border-t border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-700">
                Available Subscriptions: {selectedStudentData.subscriptions.length}
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewStudentRecord(selectedStudentData.id)}
                className="text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                View Record
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              Select a subscription below to view details
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Subscription Details */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Package className="h-5 w-5 mr-2" />
              Subscription Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Product Type</p>
                <p className="text-gray-900 capitalize font-medium">{subscription.product_type}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Days Remaining</p>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                  <span className={`text-sm ${
                    subscription.days_remaining < 30 ? 'text-orange-600' : 'text-green-600'
                  }`}>
                    {subscription.days_remaining} days
                  </span>
                </div>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-600">Expires On</p>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1 text-gray-500" />
                <span className="text-sm text-gray-600">
                  {format(new Date(subscription.current_period_end), 'MMM dd, yyyy')}
                </span>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-600">Product Name</p>
              <p className="text-gray-900 font-medium">{subscription.product_name}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Admin Assistance */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              {subscription.admin_assistance_required ? (
                <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
              ) : (
                <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              )}
              Admin Assistance
            </CardTitle>
          </CardHeader>
          <CardContent>
            {subscription.admin_assistance_required ? (
              <div className="space-y-3">
                <div className="flex items-center">
                  <Badge variant="destructive" className="mr-2">Required</Badge>
                  <span className="text-sm text-gray-600">Student needs help</span>
                </div>
                
                {subscription.admin_assistance_notes && (
                  <div className="p-3 bg-orange-50 border border-orange-200 rounded-md">
                    <p className="text-sm font-medium text-orange-800 mb-1">Request Details:</p>
                    <p className="text-sm text-orange-700 italic">
                      "{subscription.admin_assistance_notes}"
                    </p>
                  </div>
                )}
                
                <div className="flex items-center text-sm text-orange-600">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>Pending Review</span>
                </div>
              </div>
            ) : (
              <div className="flex items-center text-green-600">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">No assistance required</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Estimated Sessions & Pricing */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <BookOpen className="h-5 w-5 mr-2" />
              Batch Planning
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Estimated Sessions</p>
                <div className="flex items-center">
                  <BookOpen className="h-4 w-4 mr-1 text-gray-500" />
                  <span className="text-gray-900 font-medium">
                    {getEstimatedSessions(subscription.product_type)} sessions
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Session Duration</p>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1 text-gray-500" />
                  <span className="text-gray-900">60 minutes</span>
                </div>
              </div>
            </div>

            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-800">Subscription Value</span>
                <span className="font-medium text-blue-700">
                  {getSubscriptionValue(subscription.product_type)}
                </span>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                Based on {subscription.product_type} package pricing
              </p>
            </div>

            <div className="text-xs text-gray-500 space-y-1">
              <p>• Sessions can be scheduled flexibly</p>
              <p>• Tutor assignment can be changed later</p>
              <p>• Progress tracking will be available</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Helper functions
const getEstimatedSessions = (productType: string): number => {
  switch (productType) {
    case 'booster':
      return 25;
    case 'preparation':
      return 40;
    case 'customized':
      return 30;
    default:
      return 20;
  }
};

const getSubscriptionValue = (productType: string): string => {
  switch (productType) {
    case 'booster':
      return '$299.99';
    case 'preparation':
      return '$399.99';
    case 'customized':
      return '$199.99';
    default:
      return '$249.99';
  }
};

// Helper function to get student location
const getStudentLocation = (studentData: any): string => {
  // Try to get location from various possible fields
  if (studentData.city && studentData.country) {
    return `${studentData.city}, ${studentData.country}`;
  } else if (studentData.location) {
    return studentData.location;
  } else if (studentData.city) {
    return studentData.city;
  } else if (studentData.country) {
    return studentData.country;
  } else if (studentData.timezone) {
    // Fallback to timezone if no location data
    return `Timezone: ${studentData.timezone}`;
  }
  return 'Location not specified';
};

// Helper function to get student grade
const getStudentGrade = (studentData: any): string => {
  if (studentData.grade) {
    return `Grade ${studentData.grade}`;
  } else if (studentData.education_level) {
    return studentData.education_level;
  } else if (studentData.academic_level) {
    return studentData.academic_level;
  }
  return 'Grade not specified';
};

export default BatchCreationSidebar;

import { config as envConfig } from "@/config/environment";

// API configuration with environment-based URLs
const config = {
  // Use environment configuration for base URLs
  apiUrl: envConfig.apiUrl,
  appUrl: envConfig.appUrl,

  // API request settings
  timeout: 30000,
  withCredentials: true,

  // Headers
  defaultHeaders: {
    "Content-Type": "application/json",
  },

  // Feature flags from environment
  features: envConfig.features,

  // Other potential configuration options
  pagination: {
    defaultLimit: 10,
    maxLimit: 100,
  },

  // Cache settings
  cache: {
    defaultTTL: 300, // seconds
  }
};

export default config;


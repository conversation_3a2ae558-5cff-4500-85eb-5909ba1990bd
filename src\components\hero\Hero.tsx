import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { Button } from "@/components/ui/Button";
import { Loader2, Info } from "lucide-react";
import EmailEntryForm from "@/components/auth/EmailEntryForm";
import { ROUTES, getRouteForUser } from "@/routes/RouteConfig";
import {
  shouldShowTutorApplicationOptions,
  needsCompleteSignup,
  needsContinueToOnboarding,
} from "@/utils/tutorStateUtils";
import { useUserStatusCheck } from "@/hooks/useUserStatusCheck";
import {
  HeroButtonsContainer,
  HeroButton,
  HeroSVGPair,
} from "./HeroSections";
import "./Hero.css";

const Hero = () => {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [submittedEmail, setSubmittedEmail] = useState("");
  const [modalState, setModalState] = useState<
    "email-entry" | "processing" | "result"
  >("email-entry");

  // Use our custom hook
  const {
    checkUserStatus,
    isRegistered,
    needsOnboarding,
    isGuest,
    isNew,
    userStatus,
    userType,
    isOnboarded,
    isLoading,
    reset,
  } = useUserStatusCheck();

  // Setup login password function
  const setupLoginPassword = () => {
    // Navigate to signup page with special params indicating this is a guest upgrade
    localStorage.setItem("pendingSignupEmail", submittedEmail);
    setIsModalOpen(false);
    navigate(ROUTES.REGISTER.path, {
      state: {
        email: submittedEmail,
        source: "guest-upgrade",
        isGuestUpgrade: true,
      },
    });
  };

  const handleFindTutorClick = () => {
    console.log("Find a Tutor button clicked");
    navigate(ROUTES.TUTOR_SEARCH.path);
  };

  const handleBecomeTutorClick = () => {
    setModalState("email-entry");
    setIsModalOpen(true);
  };

  const handleEmailSubmitted = async (email: string) => {
    setSubmittedEmail(email);
    setModalState("processing");

    // Use our hook to check user status
    await checkUserStatus(email);

    // After checking, move to result state
    setModalState("result");
  };

  const handleProceedAsGuest = () => {
    localStorage.setItem("pendingTutorEmail", submittedEmail);
    setIsModalOpen(false);
    navigate(ROUTES.BECOME_TUTOR.path);
  };

  const handleCreateAccount = () => {
    localStorage.setItem("pendingSignupEmail", submittedEmail);
    setIsModalOpen(false);
    navigate(ROUTES.REGISTER.path, {
      state: { email: submittedEmail, source: "become-tutor" },
    });
  };

  const handleContinueToOnboarding = () => {
    setIsModalOpen(false);
    const redirectPath = getRouteForUser(
      "tutor",
      isOnboarded,
      userStatus,
      "become-tutor"
    );
    navigate(redirectPath);
  };

  // Reset modal state when it closes
  useEffect(() => {
    if (!isModalOpen) {
      setTimeout(() => {
        setModalState("email-entry");
        reset(); // Reset the store state
      }, 300);
    }
  }, [isModalOpen]);

  const renderModalContent = () => {
    switch (modalState) {
      case "processing":
        return (
          <>
            <DialogHeader>
              <DialogTitle>Checking your email</DialogTitle>
              <DialogDescription>
                Please wait while we check if you've used this email before.
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-purple-500" />
            </div>
          </>
        );

      case "result":
        // Use utility functions to determine which UI to show
        if (needsContinueToOnboarding(userStatus, "tutor", isOnboarded)) {
          return (
            <>
              <DialogHeader>
                <DialogTitle>Welcome back!</DialogTitle>
                <DialogDescription className="flex items-start gap-2 mt-2">
                  <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span>
                    We found your account. You need to complete your tutor
                    onboarding process.
                  </span>
                </DialogDescription>
              </DialogHeader>
              <DialogFooter className="mt-4">
                <Button className="w-full" onClick={handleContinueToOnboarding}>
                  Continue to onboarding
                </Button>
              </DialogFooter>
            </>
          );
        } else if (needsCompleteSignup(userStatus)) {
          return (
            <>
              <DialogHeader>
                <DialogTitle>Welcome back!</DialogTitle>
                <DialogDescription className="flex items-start gap-2 mt-2">
                  <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span>
                    You've visited before. Set up your password to continue.
                  </span>
                </DialogDescription>
              </DialogHeader>
              <DialogFooter className="mt-4">
                <Button className="w-full" onClick={setupLoginPassword}>
                  Complete sign up
                </Button>
              </DialogFooter>
            </>
          );
        } else if (shouldShowTutorApplicationOptions(userStatus)) {
          return (
            <>
              <DialogHeader>
                <DialogTitle>Welcome!</DialogTitle>
                <DialogDescription className="flex items-start gap-2 mt-2">
                  <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span>
                    This email is new to our system. How would you like to
                    proceed?
                  </span>
                </DialogDescription>
              </DialogHeader>
              <DialogFooter className="flex flex-col sm:flex-row gap-2 mt-4">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleProceedAsGuest}
                >
                  Proceed as guest
                </Button>
                <Button className="w-full" onClick={handleCreateAccount}>
                  Create an account
                </Button>
              </DialogFooter>
            </>
          );
        }
        // Default case for any other user status
        return (
          <>
            <DialogHeader>
              <DialogTitle>Account Found</DialogTitle>
              <DialogDescription className="flex items-start gap-2 mt-2">
                <Info className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                <span>
                  We found your account. You are not allowed to proceed. Please
                  contact administrator or send an inquiry with details.
                </span>
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="mt-4">
              <Button className="w-full" onClick={() => setIsModalOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </>
        );

      case "email-entry":
      default:
        return (
          <>
            <DialogHeader>
              <DialogTitle>Become a Tutor</DialogTitle>
              <DialogDescription>
                Enter your email to get started. We'll check if you've already
                signed up and help you continue or create an account.
              </DialogDescription>
            </DialogHeader>

            <EmailEntryForm onEmailSubmitted={handleEmailSubmitted} />
          </>
        );
    }
  };

  return (
    <section className="relative w-full h-[750px] overflow-hidden">
      {/* Background SVG Layer */}
      <div className="absolute inset-0 w-full h-full">
        <HeroSVGPair />
      </div>

      <div className="flex w-full h-full relative z-10">
        {/* Left Section with Content - overlapping left SVG */}
        <div className="w-1/2 h-full flex items-center justify-center p-8 relative z-20">
          <div className="max-w-lg text-left transform -translate-y-8">
            <h1 className="text-5xl md:text-6xl font-bold text-[#3e1168] mb-6 leading-tight whitespace-nowrap">
              Reinforce Your
              <br />
              Learning Journey
            </h1>
            <p className="text-xl text-gray-800 leading-relaxed">
              Experience the power of reinforcement-based learning with AI
              support and human expertise. Master subjects faster, retain
              knowledge longer.
            </p>
          </div>
        </div>

        {/* Right Section with Child Image - overlapping right SVG */}
        <div className="w-1/2 h-full relative z-20">
          {/* Child Image */}
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src="/svg/heroChild.png"
              alt="Student with backpack"
              className="h-full max-h-[400px] w-auto object-contain"
            />
          </div>
        </div>

        {/* Buttons container - positioned to overlap the sections */}
        <HeroButtonsContainer>
          <HeroButton
            primary
            onClick={handleFindTutorClick}
            className="shadow-sm"
          >
            Find a Tutor
          </HeroButton>
          <HeroButton
            onClick={handleBecomeTutorClick}
          >
            Become a Tutor
          </HeroButton>
        </HeroButtonsContainer>
      </div>

      {/* Dynamic Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          {renderModalContent()}
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default Hero;






import { Button } from "@/components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Input } from "@/components/ui/Input";
import {
  ChevronLeft,
  ChevronRight,
  Search,
  User,
  MapPin,
  X,
} from "lucide-react";
import { ReactNode, useState } from "react";

// Define the event type
export interface CalendarEvent {
  id: number;
  title: string;
  tutorName: string;
  tutorPhoto: string;
  date: Date;
  startTime: string;
  endTime: string;
  location: string;
  type: string;
  color: string;
  icon: ReactNode;
}

// Define the props for the StudentCalendar component
interface StudentCalendarProps {
  calendarView: "day" | "week" | "month";
  selectedDate: Date;
  currentMonth: Date;
  calendarEvents: CalendarEvent[];
  setCalendarView: (view: "day" | "week" | "month") => void;
  setSelectedDate: (date: Date) => void;
  nextPeriod: () => void;
  prevPeriod: () => void;
}

const StudentCalendar = ({
  calendarView,
  selectedDate,
  currentMonth,
  calendarEvents,
  setCalendarView,
  setSelectedDate,
  nextPeriod,
  prevPeriod,
}: StudentCalendarProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);

  // Filter events based on search query
  const filteredEvents = searchQuery.trim() === ""
    ? calendarEvents
    : calendarEvents.filter(event =>
        event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.tutorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.location.toLowerCase().includes(searchQuery.toLowerCase())
      );
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Calendar</CardTitle>
            <CardDescription>
              View your scheduled sessions and activities
            </CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
            <div className="flex items-center justify-between sm:justify-start space-x-1">
              <Button variant="outline" size="icon" onClick={prevPeriod}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="text-sm font-medium whitespace-nowrap">
                {calendarView === "day"
                  ? selectedDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })
                  : calendarView === "week"
                    ? `Week of ${selectedDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`
                    : `${currentMonth.toLocaleString('default', { month: 'long' })} ${currentMonth.getFullYear()}`
                }
              </div>
              <Button variant="outline" size="icon" onClick={nextPeriod}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex bg-gray-100 rounded-md p-0.5 justify-center">
              <Button
                variant={calendarView === "day" ? "default" : "ghost"}
                size="sm"
                className={`text-xs h-7 ${calendarView === "day" ? "bg-purple-500 text-white hover:bg-purple-600" : ""}`}
                onClick={() => setCalendarView("day")}
              >
                Day
              </Button>
              <Button
                variant={calendarView === "week" ? "default" : "ghost"}
                size="sm"
                className={`text-xs h-7 ${calendarView === "week" ? "bg-purple-500 text-white hover:bg-purple-600" : ""}`}
                onClick={() => setCalendarView("week")}
              >
                Week
              </Button>
              <Button
                variant={calendarView === "month" ? "default" : "ghost"}
                size="sm"
                className={`text-xs h-7 ${calendarView === "month" ? "bg-purple-500 text-white hover:bg-purple-600" : ""}`}
                onClick={() => setCalendarView("month")}
              >
                Month
              </Button>
            </div>
            <div className="relative hidden sm:block">
              {showSearch ? (
                <div className="flex items-center">
                  <Input
                    type="text"
                    placeholder="Search events..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="h-9 w-[200px] mr-2"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      setSearchQuery("");
                      setShowSearch(false);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowSearch(true)}
                >
                  <Search className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {searchQuery.trim() !== "" && (
          <div className="mb-4 p-3 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-700">
              Showing {filteredEvents.length} results for "{searchQuery}"
            </p>
          </div>
        )}

        {calendarView === "week" && (
          <WeekView
            calendarEvents={filteredEvents}
            selectedDate={selectedDate}
          />
        )}

        {calendarView === "day" && (
          <DayView
            selectedDate={selectedDate}
            calendarEvents={filteredEvents}
          />
        )}

        {calendarView === "month" && (
          <MonthView
            currentMonth={currentMonth}
            selectedDate={selectedDate}
            calendarEvents={filteredEvents}
            setSelectedDate={setSelectedDate}
            setCalendarView={setCalendarView}
          />
        )}
      </CardContent>
    </Card>
  );
};

// Week View Component
interface WeekViewProps {
  calendarEvents: CalendarEvent[];
  selectedDate: Date;
}

const WeekView = ({ calendarEvents, selectedDate }: WeekViewProps) => {
  // Calculate the start of the week (Monday)
  const getWeekDates = () => {
    const dates = [];
    const dayOfWeek = selectedDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Calculate how many days to go back to get to Monday
    // If today is Sunday (0), we need to go back 6 days to get to Monday
    // If today is Monday (1), we need to go back 0 days
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

    // Get Monday's date
    const monday = new Date(selectedDate);
    monday.setDate(selectedDate.getDate() - daysToMonday);

    // Generate dates for the week
    for (let i = 0; i < 7; i++) {
      const date = new Date(monday);
      date.setDate(monday.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  const weekDates = getWeekDates();
  const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

  return (
    <div className="mt-4">
      {/* Days of the week header */}
      <div className="grid grid-cols-7 gap-1 mb-1">
        {daysOfWeek.map((day) => (
          <div key={day} className="text-center py-2 text-sm font-medium">
            {day}
          </div>
        ))}
      </div>

      {/* Date numbers row */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {weekDates.map((date, index) => {
          const isToday = date.toDateString() === new Date().toDateString();
          return (
            <div
              key={index}
              className={`text-center py-1 text-lg font-semibold ${isToday ? 'text-blue-600' : ''}`}
            >
              {date.getDate()}
            </div>
          );
        })}
      </div>

      {/* Calendar grid with time slots */}
      <div className="relative border rounded-lg overflow-hidden bg-white" style={{ height: "600px" }}>
        {/* Time indicators */}
        <div className="absolute left-0 top-0 bottom-0 w-16 border-r bg-gray-50 flex flex-col">
          {Array.from({ length: 13 }).map((_, i) => (
            <div key={i} className="flex items-center justify-center h-12 text-xs text-gray-500">
              {i + 8}:00 {i + 8 < 12 ? "AM" : i + 8 === 12 ? "PM" : "PM"}
            </div>
          ))}
        </div>

        {/* Calendar content */}
        <div className="ml-16 grid grid-cols-7 h-full">
          {/* Grid lines for each day */}
          {weekDates.map((_, day) => (
            <div key={day} className="relative border-r h-full">
              {/* Horizontal time lines */}
              {Array.from({ length: 13 }).map((_, i) => (
                <div key={i} className="absolute w-full border-t border-gray-100" style={{ top: `${i * 48}px` }}></div>
              ))}

              {/* Events for this day */}
              {calendarEvents
                .filter(event => {
                  // Get the date for this column
                  const columnDate = weekDates[day];
                  // Check if the event is on this date
                  return event.date.getDate() === columnDate.getDate() &&
                         event.date.getMonth() === columnDate.getMonth() &&
                         event.date.getFullYear() === columnDate.getFullYear();
                })
                .map(event => {
                  // Calculate position based on time
                  const startHour = parseInt(event.startTime.split(':')[0]);
                  const startMinutes = parseInt(event.startTime.split(':')[1]) || 0;
                  const isPM = event.startTime.includes('PM') && startHour !== 12;
                  const hour24 = isPM ? startHour + 12 : startHour;

                  // Position calculation (8AM is the start of our grid)
                  const topPosition = (hour24 - 8) * 48 + (startMinutes / 60) * 48;

                  // Calculate height based on duration
                  const endHour = parseInt(event.endTime.split(':')[0]);
                  const endMinutes = parseInt(event.endTime.split(':')[1]) || 0;
                  const endIsPM = event.endTime.includes('PM') && endHour !== 12;
                  const endHour24 = endIsPM ? endHour + 12 : endHour;

                  const duration = (endHour24 - hour24) * 60 + (endMinutes - startMinutes);
                  const height = (duration / 60) * 48;

                  return (
                    <div
                      key={event.id}
                      className={`absolute rounded-md p-2 border overflow-hidden ${event.color}`}
                      style={{
                        top: `${topPosition}px`,
                        left: '4px',
                        right: '4px',
                        height: `${height}px`,
                        zIndex: 10
                      }}
                    >
                      <div className="flex items-start h-full">
                        <div className="mr-2 mt-0.5">{event.icon}</div>
                        <div className="overflow-hidden">
                          <div className="font-medium text-sm truncate">{event.title}</div>
                          <div className="text-xs truncate">{event.startTime} - {event.endTime}</div>
                          <div className="text-xs truncate">{event.location}</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Day View Component
interface DayViewProps {
  selectedDate: Date;
  calendarEvents: CalendarEvent[];
}

const DayView = ({ selectedDate, calendarEvents }: DayViewProps) => {
  return (
    <div className="mt-4">
      {/* Day header */}
      <div className="text-center py-2 mb-4">
        <h3 className="text-xl font-semibold">
          {selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })}
        </h3>
      </div>

      {/* Calendar grid with time slots */}
      <div className="relative border rounded-lg overflow-hidden bg-white" style={{ height: "600px" }}>
        {/* Time indicators */}
        <div className="absolute left-0 top-0 bottom-0 w-16 border-r bg-gray-50 flex flex-col">
          {Array.from({ length: 16 }).map((_, i) => (
            <div key={i} className="flex items-center justify-center h-12 text-xs text-gray-500">
              {i + 8}:00 {i + 8 < 12 ? "AM" : i + 8 === 12 ? "PM" : "PM"}
            </div>
          ))}
        </div>

        {/* Calendar content */}
        <div className="ml-16 h-full relative">
          {/* Horizontal time lines */}
          {Array.from({ length: 16 }).map((_, i) => (
            <div key={i} className="absolute w-full border-t border-gray-100" style={{ top: `${i * 48}px` }}></div>
          ))}

          {/* Events for this day */}
          {calendarEvents
            .filter(event =>
              event.date.getDate() === selectedDate.getDate() &&
              event.date.getMonth() === selectedDate.getMonth() &&
              event.date.getFullYear() === selectedDate.getFullYear()
            )
            .map(event => {
              // Calculate position based on time
              const startHour = parseInt(event.startTime.split(':')[0]);
              const startMinutes = parseInt(event.startTime.split(':')[1]) || 0;
              const isPM = event.startTime.includes('PM') && startHour !== 12;
              const hour24 = isPM ? startHour + 12 : startHour;

              // Position calculation (8AM is the start of our grid)
              const topPosition = (hour24 - 8) * 48 + (startMinutes / 60) * 48;

              // Calculate height based on duration
              const endHour = parseInt(event.endTime.split(':')[0]);
              const endMinutes = parseInt(event.endTime.split(':')[1]) || 0;
              const endIsPM = event.endTime.includes('PM') && endHour !== 12;
              const endHour24 = endIsPM ? endHour + 12 : endHour;

              const duration = (endHour24 - hour24) * 60 + (endMinutes - startMinutes);
              const height = (duration / 60) * 48;

              return (
                <div
                  key={event.id}
                  className={`absolute rounded-md p-3 border overflow-hidden ${event.color}`}
                  style={{
                    top: `${topPosition}px`,
                    left: '8px',
                    right: '8px',
                    height: `${height}px`,
                    zIndex: 10
                  }}
                >
                  <div className="flex items-start h-full">
                    <div className="mr-3 mt-0.5">{event.icon}</div>
                    <div className="overflow-hidden">
                      <div className="font-medium text-sm">{event.title}</div>
                      <div className="text-sm">{event.startTime} - {event.endTime}</div>
                      <div className="text-sm mt-1">{event.location}</div>
                      <div className="text-sm mt-1">Tutor: {event.tutorName}</div>
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

// Month View Component
interface MonthViewProps {
  currentMonth: Date;
  selectedDate: Date;
  calendarEvents: CalendarEvent[];
  setSelectedDate: (date: Date) => void;
  setCalendarView: (view: "day" | "week" | "month") => void;
}

const MonthView = ({
  currentMonth,
  selectedDate,
  calendarEvents,
  setSelectedDate,
  setCalendarView
}: MonthViewProps) => {
  return (
    <div className="mt-4">
      {/* Days of the week header */}
      <div className="grid grid-cols-7 gap-1 mb-1">
        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
          <div key={day} className="text-center py-2 text-sm font-medium">
            <span className="hidden sm:inline">{day}</span>
            <span className="sm:hidden">{day.charAt(0)}</span>
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1 auto-rows-fr">
        {(() => {
          // Get the first day of the month
          const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
          // Get the last day of the month
          const lastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
          // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
          const firstDayOfWeek = firstDay.getDay();
          // Total days in the month
          const daysInMonth = lastDay.getDate();

          // Create an array to hold all calendar cells
          const calendarCells = [];

          // Add empty cells for days before the first day of the month
          for (let i = 0; i < firstDayOfWeek; i++) {
            calendarCells.push(
              <div key={`empty-${i}`} className="h-16 sm:h-24 border bg-gray-50 p-1"></div>
            );
          }

          // Add cells for each day of the month
          for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
            const isToday = date.toDateString() === new Date().toDateString();
            const isSelected = date.toDateString() === selectedDate.toDateString();

            // Get events for this day
            const dayEvents = calendarEvents.filter(event =>
              event.date.getDate() === day &&
              event.date.getMonth() === currentMonth.getMonth() &&
              event.date.getFullYear() === currentMonth.getFullYear()
            );

            calendarCells.push(
              <div
                key={`day-${day}`}
                className={`h-16 sm:h-24 border p-1 relative ${isToday ? 'bg-blue-50' : 'bg-white'} ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
                onClick={() => {
                  setSelectedDate(date);
                  setCalendarView("day");
                }}
              >
                <div className={`text-right mb-1 ${isToday ? 'font-bold text-blue-600' : ''}`}>
                  {day}
                </div>
                <div className="overflow-y-auto max-h-8 sm:max-h-16">
                  {dayEvents.slice(0, 3).map(event => (
                    <div
                      key={event.id}
                      className={`text-xs p-1 mb-1 rounded truncate ${event.color}`}
                    >
                      {event.title}
                    </div>
                  ))}
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-gray-500 pl-1">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          }

          // Add empty cells for days after the last day of the month to complete the grid
          const totalCells = calendarCells.length;
          const remainingCells = 42 - totalCells; // 6 rows of 7 days

          for (let i = 0; i < remainingCells; i++) {
            calendarCells.push(
              <div key={`empty-end-${i}`} className="h-16 sm:h-24 border bg-gray-50 p-1"></div>
            );
          }

          return calendarCells;
        })()}
      </div>
    </div>
  );
};

export default StudentCalendar;

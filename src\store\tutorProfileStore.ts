import { create } from "zustand";
import { supabase } from "@/lib/supabaseClient";
import { generateProfilePicturePath, getBucketForUserType, generateUniqueFileName } from "@/utils/storageUtils";

// Types for our tutor profile data
export interface TutorEducation {
  id?: string;
  degree: string;
  institution: string;
  year: string;
}

export interface TutorAvailabilitySlot {
  start: string;
  end: string;
}

export interface TutorAvailability {
  day: string;
  slots: TutorAvailabilitySlot[];
}

export interface TutorPolicies {
  cancellation: string;
  backgroundCheck: boolean;
  responseTime: string;
}

export interface TutorReview {
  id: string;
  studentName: string;
  rating: number;
  comment: string;
  date: string;
  tutorResponse?: string;
}

export interface TutorProfileData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  tagline: string;
  bio: string;
  hourlyRate: number;
  totalHours: number;
  rating: number;
  reviewCount: number;
  profilePictureUrl: string;
  specializations: string[];
  education: TutorEducation[];
  subjects: string[];
  availability: TutorAvailability[];
  policies: TutorPolicies;
  reviews: TutorReview[];
}

interface TutorProfileState {
  // State
  tutorData: TutorProfileData | null;
  isLoading: boolean;
  error: string | null;
  isEditing: boolean;
  editData: Partial<TutorProfileData>;
  expandedBio: boolean;
  activeTab: string;
  avatarFile: File | null;
  avatarPreview: string | null;

  // Actions
  fetchTutorProfile: (userId: string) => Promise<void>;
  setIsEditing: (isEditing: boolean) => void;
  updateEditData: <K extends keyof TutorProfileData>(
    field: K,
    value: TutorProfileData[K]
  ) => void;
  updateNestedEditData: <
    K extends keyof TutorProfileData,
    N extends keyof TutorProfileData[K]
  >(
    parent: K,
    field: N,
    value: any
  ) => void;
  setAvatarFile: (file: File | null) => void;
  setAvatarPreview: (preview: string | null) => void;
  setExpandedBio: (expanded: boolean) => void;
  setActiveTab: (tab: string) => void;
  saveProfile: (userId: string) => Promise<boolean>;
  cancelEditing: () => void;
}

// Helper function to process availability data from database format to component format
const processAvailabilityData = (data: any[]) => {
  const days = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];
  return days.map((day) => {
    const dayData = data.filter((slot: any) => slot.day_of_week === day);
    return {
      day,
      slots: dayData.map((slot: any) => ({
        start: slot.start_time,
        end: slot.end_time,
      })),
    };
  });
};

export const useTutorProfileStore = create<TutorProfileState>((set, get) => ({
  // Initial state
  tutorData: null,
  isLoading: false,
  error: null,
  isEditing: false,
  editData: {},
  expandedBio: false,
  activeTab: "about",
  avatarFile: null,
  avatarPreview: null,

  // Actions
  fetchTutorProfile: async (userId) => {
    try {
      set({ isLoading: true, error: null });

      // Fetch basic profile data
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (profileError) throw profileError;

      // Fetch tutor-specific data
      const { data: tutorData, error: tutorError } = await supabase
        .from("candidate_tutor")
        .select("*")
        .eq("id", userId)
        .single();

      if (tutorError) throw tutorError;

      // Fetch education data
      const { data: educationData, error: educationError } = await supabase
        .from("tutor_education")
        .select("*")
        .eq("tutor_id", userId);

      if (educationError) throw educationError;

      // Fetch reviews
      const { data: reviewsData, error: reviewsError } = await supabase
        .from("tutor_reviews")
        .select("*")
        .eq("tutor_id", userId)
        .order("created_at", { ascending: false });

      if (reviewsError) throw reviewsError;

      // Fetch availability
      const { data: availabilityData, error: availabilityError } =
        await supabase
          .from("tutor_availability")
          .select("*")
          .eq("tutor_id", userId);

      if (availabilityError) throw availabilityError;

      // Combine all data
      const combinedData: TutorProfileData = {
        id: userId,
        firstName: profileData.first_name || "",
        lastName: profileData.last_name || "",
        email: profileData.email || "",
        tagline: tutorData.tagline || "",
        bio: tutorData.bio || "",
        hourlyRate: tutorData.hourly_rate || 0,
        totalHours: tutorData.total_hours || 0,
        rating: tutorData.rating || 0,
        reviewCount: reviewsData.length || 0,
        profilePictureUrl: profileData.profile_picture_url || "",
        specializations: tutorData.specializations || [],
        education:
          educationData.map((edu: any) => ({
            id: edu.id,
            degree: edu.degree,
            institution: edu.institution,
            year: edu.year,
          })) || [],
        subjects: tutorData.subjects || [],
        availability: processAvailabilityData(availabilityData) || [],
        policies: {
          cancellation: tutorData.cancellation_policy || "",
          backgroundCheck: tutorData.background_check_verified || false,
          responseTime: tutorData.avg_response_time || "24 hours",
        },
        reviews:
          reviewsData.map((review: any) => ({
            id: review.id,
            studentName: review.student_name,
            rating: review.rating,
            comment: review.comment,
            date: review.created_at,
            tutorResponse: review.tutor_response,
          })) || [],
      };

      set({
        tutorData: combinedData,
        editData: combinedData,
        avatarPreview: combinedData.profilePictureUrl,
        isLoading: false,
      });
    } catch (error) {
      console.error("Error fetching tutor data:", error);
      set({
        error:
          error instanceof Error ? error.message : "An unknown error occurred",
        isLoading: false,
      });
    }
  },

  setIsEditing: (isEditing) => set({ isEditing }),

  updateEditData: (field, value) =>
    set((state) => ({
      editData: { ...state.editData, [field]: value },
    })),

  updateNestedEditData: (parent, field, value) =>
    set((state) => ({
      editData: {
        ...state.editData,
        [parent]: {
          ...((state.editData[parent] as any) || {}),
          [field]: value,
        },
      },
    })),

  setAvatarFile: (file) => set({ avatarFile: file }),

  setAvatarPreview: (preview) => set({ avatarPreview: preview }),

  setExpandedBio: (expanded) => set({ expandedBio: expanded }),

  setActiveTab: (tab) => set({ activeTab: tab }),

  saveProfile: async (userId) => {
    const { tutorData, editData, avatarFile } = get();

    if (!tutorData) return false;

    try {
      set({ isLoading: true, error: null });

      // Upload avatar if changed
      let profilePictureUrl = tutorData.profilePictureUrl;

      if (avatarFile) {
        const fileName = generateUniqueFileName(avatarFile.name);
        const filePath = generateProfilePicturePath('tutor', userId, fileName);
        const bucket = getBucketForUserType('tutor');

        const { error: uploadError } = await supabase.storage
          .from(bucket)
          .upload(filePath, avatarFile);

        if (uploadError) throw uploadError;

        // Get public URL
        const { data } = supabase.storage
          .from(bucket)
          .getPublicUrl(filePath);

        profilePictureUrl = data.publicUrl;
      }

      // Update profiles table
      const { error: profileError } = await supabase
        .from("profiles")
        .update({
          first_name: editData.firstName,
          last_name: editData.lastName,
          profile_picture_url: profilePictureUrl,
          updated_at: new Date().toISOString(),
        })
        .eq("id", userId);

      if (profileError) throw profileError;

      // Update tutor-specific data
      const { error: tutorError } = await supabase
        .from("candidate_tutor")
        .update({
          tagline: editData.tagline,
          bio: editData.bio,
          hourly_rate: editData.hourlyRate,
          specializations: editData.specializations,
          subjects: editData.subjects,
          cancellation_policy: editData.policies?.cancellation,
        })
        .eq("id", userId);

      if (tutorError) throw tutorError;

      // Update local state with new data
      set((state) => ({
        tutorData: {
          ...state.tutorData!,
          ...editData,
          profilePictureUrl,
        },
        isLoading: false,
        isEditing: false,
        avatarFile: null,
      }));

      return true;
    } catch (error) {
      console.error("Error updating profile:", error);
      set({
        error:
          error instanceof Error ? error.message : "An unknown error occurred",
        isLoading: false,
      });
      return false;
    }
  },

  cancelEditing: () => {
    const { tutorData } = get();
    set({
      editData: tutorData || {},
      avatarPreview: tutorData?.profilePictureUrl || null,
      avatarFile: null,
      isEditing: false,
    });
  },
}));

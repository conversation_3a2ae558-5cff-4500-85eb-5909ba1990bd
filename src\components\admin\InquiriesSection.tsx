import React, { useState, useEffect } from "react";
import { supabase } from "../../lib/supabaseClient";

interface Inquiry {
  id: string;
  created_at: string;
  name: string;
  email: string;
  phone: string | null;
  inquiry_type: string;
  message: string;
  status: "new" | "in_progress" | "resolved";
}

const InquiriesSection = () => {
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedInquiry, setSelectedInquiry] = useState<Inquiry | null>(null);

  useEffect(() => {
    fetchInquiries();
  }, []);

  const fetchInquiries = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from("inquiries")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching inquiries:", error);
    } else {
      setInquiries(data || []);
    }
    setLoading(false);
  };

  const updateInquiryStatus = async (
    id: string,
    status: "new" | "in_progress" | "resolved"
  ) => {
    const { error } = await supabase
      .from("inquiries")
      .update({ status })
      .eq("id", id);

    if (error) {
      console.error("Error updating inquiry status:", error);
    } else {
      fetchInquiries();
      if (selectedInquiry && selectedInquiry.id === id) {
        setSelectedInquiry({ ...selectedInquiry, status });
      }
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="md:col-span-1 bg-white p-4 rounded shadow">
        <h2 className="text-xl font-bold mb-4">Inquiries</h2>
        {loading ? (
          <p>Loading inquiries...</p>
        ) : inquiries.length === 0 ? (
          <p>No inquiries found.</p>
        ) : (
          <div className="space-y-2">
            {inquiries.map((inquiry) => (
              <div
                key={inquiry.id}
                className={`p-3 rounded cursor-pointer ${
                  selectedInquiry?.id === inquiry.id
                    ? "bg-blue-100 border-l-4 border-blue-500"
                    : "bg-gray-50 hover:bg-gray-100"
                }`}
                onClick={() => setSelectedInquiry(inquiry)}
              >
                <div className="flex justify-between items-center">
                  <p className="font-medium">{inquiry.name}</p>
                  <span
                    className={`text-xs px-2 py-1 rounded ${
                      inquiry.status === "new"
                        ? "bg-red-100 text-red-800"
                        : inquiry.status === "in_progress"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-green-100 text-green-800"
                    }`}
                  >
                    {inquiry.status === "new"
                      ? "New"
                      : inquiry.status === "in_progress"
                      ? "In Progress"
                      : "Resolved"}
                  </span>
                </div>
                <p className="text-sm text-gray-600 truncate">
                  {inquiry.inquiry_type}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(inquiry.created_at).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="md:col-span-2 bg-white p-4 rounded shadow">
        {selectedInquiry ? (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">{selectedInquiry.inquiry_type}</h2>
              <div className="space-x-2">
                <button
                  className={`px-3 py-1 text-sm rounded ${
                    selectedInquiry.status === "new"
                      ? "bg-red-500 text-white"
                      : "bg-gray-200"
                  }`}
                  onClick={() => updateInquiryStatus(selectedInquiry.id, "new")}
                >
                  New
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded ${
                    selectedInquiry.status === "in_progress"
                      ? "bg-yellow-500 text-white"
                      : "bg-gray-200"
                  }`}
                  onClick={() =>
                    updateInquiryStatus(selectedInquiry.id, "in_progress")
                  }
                >
                  In Progress
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded ${
                    selectedInquiry.status === "resolved"
                      ? "bg-green-500 text-white"
                      : "bg-gray-200"
                  }`}
                  onClick={() =>
                    updateInquiryStatus(selectedInquiry.id, "resolved")
                  }
                >
                  Resolved
                </button>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600">
                From: {selectedInquiry.name} ({selectedInquiry.email})
              </p>
              <p className="text-sm text-gray-600">
                Date: {new Date(selectedInquiry.created_at).toLocaleString()}
              </p>
            </div>

            <div className="p-4 bg-gray-50 rounded">
              <p className="whitespace-pre-wrap">{selectedInquiry.message}</p>
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500">
            Select an inquiry to view details
          </div>
        )}
      </div>
    </div>
  );
};

export default InquiriesSection;





import React from "react";
import { useTutorAvailabilityStore } from "@/store/tutorAvailabilityStore";
import { Button } from "@/components/ui/Button";
import { Switch } from "@/components/ui/Switch";
import { Label } from "@/components/ui/Label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Calendar, Bell, RefreshCw, AlertTriangle } from "lucide-react";

const GoogleCalendarIntegration: React.FC = () => {
  const {
    googleCalendarSettings,
    updateGoogleCalendarSettings,
  } = useTutorAvailabilityStore();

  const handleConnect = () => {
    // In a real implementation, this would initiate OAuth flow with Google
    updateGoogleCalendarSettings({ connected: true });
  };

  const handleDisconnect = () => {
    // In a real implementation, this would revoke access
    updateGoogleCalendarSettings({ connected: false });
  };

  const handleToggleSetting = (setting: keyof typeof googleCalendarSettings, value: boolean) => {
    updateGoogleCalendarSettings({ [setting]: value });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Google Calendar Integration</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-blue-500" />
            Google Calendar
          </CardTitle>
          <CardDescription>
            Sync your availability with Google Calendar for better scheduling
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!googleCalendarSettings.connected ? (
            <div className="text-center py-6">
              <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">
                Connect to Google Calendar
              </h3>
              <p className="text-gray-500 mb-4">
                Link your Google Calendar to automatically sync your availability
                and manage conflicts.
              </p>
              <Button onClick={handleConnect}>
                Connect Google Calendar
              </Button>
            </div>
          ) : (
            <>
              <div className="bg-green-50 border border-green-200 rounded-md p-4 flex items-start">
                <div className="bg-green-100 rounded-full p-2 mr-3">
                  <Calendar className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-green-800">
                    Connected to Google Calendar
                  </h4>
                  <p className="text-sm text-green-700 mt-1">
                    Your Google Calendar is successfully linked to your tutor
                    account.
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">
                      Push Availability to Calendar
                    </Label>
                    <p className="text-sm text-gray-500">
                      Add your availability slots to Google Calendar as "Available" events
                    </p>
                  </div>
                  <Switch
                    checked={googleCalendarSettings.syncAvailability}
                    onCheckedChange={(checked) =>
                      handleToggleSetting("syncAvailability", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">
                      Pull Busy Times from Calendar
                    </Label>
                    <p className="text-sm text-gray-500">
                      Automatically block times when you have events in Google Calendar
                    </p>
                  </div>
                  <Switch
                    checked={googleCalendarSettings.pullBusyTimes}
                    onCheckedChange={(checked) =>
                      handleToggleSetting("pullBusyTimes", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">
                      Session Reminders
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receive email/SMS reminders for upcoming sessions via Google Calendar
                    </p>
                  </div>
                  <Switch
                    checked={googleCalendarSettings.sendReminders}
                    onCheckedChange={(checked) =>
                      handleToggleSetting("sendReminders", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">
                      Conflict Alerts
                    </Label>
                    <p className="text-sm text-gray-500">
                      Get notified if you mark time as "Busy" in Google Calendar that conflicts with availability
                    </p>
                  </div>
                  <Switch
                    checked={googleCalendarSettings.conflictAlerts}
                    onCheckedChange={(checked) =>
                      handleToggleSetting("conflictAlerts", checked)
                    }
                  />
                </div>
              </div>
            </>
          )}
        </CardContent>
        {googleCalendarSettings.connected && (
          <CardFooter className="flex justify-between border-t pt-6">
            <Button variant="outline" onClick={() => {}}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Sync Now
            </Button>
            <Button
              variant="outline"
              className="text-red-500 hover:text-red-700"
              onClick={handleDisconnect}
            >
              Disconnect
            </Button>
          </CardFooter>
        )}
      </Card>

      {googleCalendarSettings.connected && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2 text-blue-500" />
              Calendar Notifications
            </CardTitle>
            <CardDescription>
              Manage how you receive notifications for calendar events
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 flex items-start">
              <div className="bg-yellow-100 rounded-full p-2 mr-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <h4 className="font-medium text-yellow-800">
                  Notification Settings
                </h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Notification settings are managed through your Google Calendar settings. 
                  Changes made here will only affect how events are created.
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">
                    Email Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Receive email notifications for session bookings and changes
                  </p>
                </div>
                <Switch checked={true} disabled />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">
                    Calendar Notifications
                  </Label>
                  <p className="text-sm text-gray-500">
                    Receive Google Calendar notifications based on your Google Calendar settings
                  </p>
                </div>
                <Switch checked={true} disabled />
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default GoogleCalendarIntegration;

import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { getEmailConfirmationRedirect } from "@/routes/RouteConfig";

const ConfirmationCallback = () => {
  const navigate = useNavigate();
  const { refreshUserData, userType, isOnboarded, isInitialized, user } = useAuth();
  const [searchParams] = useSearchParams();
  const source = searchParams.get("source");
  const oauthUserType = searchParams.get("oauth_usertype");
  const isTutorApplication = source === "become-tutor";
  const isOAuthCallback = !!oauthUserType;

  useEffect(() => {
    // Debug: Log URL information for OAuth callbacks
    if (isOAuthCallback) {
      console.log("OAuth callback URL debug:", {
        fullUrl: window.location.href,
        pathname: window.location.pathname,
        search: window.location.search,
        hash: window.location.hash,
        oauthUserType,
      });
    }

    // For OAuth callbacks, wait for both initialization AND user to be available
    if (isOAuthCallback) {
      if (!isInitialized) {
        console.log("OAuth callback detected, waiting for AuthContext initialization...");
        return;
      }

      if (!user) {
        console.log("OAuth callback detected, AuthContext initialized but no user yet, waiting...");
        return;
      }
    }

    // Process the email confirmation or OAuth callback
    const handleEmailConfirmation = async () => {
      try {
        // Handle OAuth callback
        if (isOAuthCallback) {
          console.log("Processing OAuth callback for userType:", oauthUserType);
          console.log("AuthContext initialized:", isInitialized);
          console.log("User from AuthContext:", !!user);

          if (user) {
            console.log("OAuth session established successfully via AuthContext");
            // Refresh user data to ensure metadata is updated
            await refreshUserData();

            // Redirect to appropriate onboarding page
            const redirectPath = oauthUserType === 'tutor'
              ? '/onboard-tutor'
              : '/onboard-student';

            console.log("Redirecting OAuth user to:", redirectPath);
            navigate(redirectPath);
            return;
          } else {
            console.error("OAuth callback but no user found in AuthContext");
            // Fallback to login if no user
            navigate('/login');
            return;
          }
        }

        // For non-OAuth callbacks, get session directly
        const {
          data: { session },
        } = await supabase.auth.getSession();

        // Handle email confirmation (existing logic)
        // Store confirmation success in localStorage
        localStorage.setItem("emailConfirmed", "true");

        if (isTutorApplication) {
          // For tutor application, set specific message
          localStorage.setItem(
            "confirmationMessage",
            "Your email has been confirmed! You can now continue with your tutor application."
          );

          if (session) {
            // Refresh user data from context instead of direct DB queries
            await refreshUserData();
          }
        }

        // For other flows, sign out and redirect to login
        if (!isTutorApplication && session) {
          await supabase.auth.signOut();
        }

        // Get the appropriate redirect path from our centralized routing config
        const redirectPath = getEmailConfirmationRedirect(
          source,
          userType,
          isOnboarded
        );

        // Redirect to the appropriate page
        navigate(redirectPath);

        // Try to close the original tab or communicate with it
        if (window.opener) {
          // If this window was opened by another window, try to communicate with it
          try {
            window.opener.postMessage(
              { type: "EMAIL_CONFIRMED" },
              window.location.origin
            );
            // Close this tab after a short delay to ensure the message is sent
            setTimeout(() => window.close(), 500);
          } catch (e) {
            console.error("Failed to communicate with opener window:", e);
          }
        }
      } catch (error) {
        console.error("Error during email confirmation:", error);
        localStorage.setItem(
          "confirmationError",
          "There was a problem confirming your email. Please try again or contact support."
        );

        // Use the same routing logic for error cases
        const redirectPath = getEmailConfirmationRedirect(
          source,
          userType,
          isOnboarded
        );
        navigate(redirectPath);
      }
    };

    handleEmailConfirmation();
  }, [
    navigate,
    isTutorApplication,
    refreshUserData,
    userType,
    isOnboarded,
    source,
    isOAuthCallback,
    isInitialized,
    user,
    oauthUserType,
  ]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4">
          {isOAuthCallback ? "Authenticating your account..." : "Confirming your email..."}
        </p>
        <p className="text-sm text-gray-500 mt-2">
          You will be redirected automatically.
        </p>
      </div>
    </div>
  );
};

export default ConfirmationCallback;

import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { supabase } from "@/lib/supabaseClient";
import { useToast } from "@/components/ui/UseToast";
import { Loader2, Mail } from "lucide-react";
import { Button } from "@/components/ui/Button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/Form";
import { Input } from "@/components/ui/Input";
import { isDisposableEmail } from "@/api/disposableCheck";

const emailSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

type EmailEntryFormProps = {
  onEmailSubmitted: (email: string) => void;
};

const EmailEntryForm = ({ onEmailSubmitted }: EmailEntryFormProps) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof emailSchema>) => {
    setIsSubmitting(true);

    try {
      // Check for disposable email
      const isDisposable = await isDisposableEmail(values.email);
      if (isDisposable) {
        toast({
          title: "Invalid email",
          description: "Please use a non-disposable email address",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // Call the callback with the email
      onEmailSubmitted(values.email);
    } catch (error) {
      console.error("Error processing email:", error);
      toast({
        title: "Something went wrong",
        description:
          error.message || "Failed to process email. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Checking...
            </>
          ) : (
            "Continue"
          )}
        </Button>
      </form>
    </Form>
  );
};

export default EmailEntryForm;

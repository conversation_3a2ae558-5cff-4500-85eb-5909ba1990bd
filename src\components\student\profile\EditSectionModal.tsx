import React, { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/Dialog";
import { Button } from "@/components/ui/Button";
import { Label } from "@/components/ui/Label";
import { Input } from "@/components/ui/Input";
import { cn } from "@/lib/utils";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/Tooltip";
import { Info } from "lucide-react";

interface EditSectionModalProps {
  /**
   * Whether the modal is open
   */
  isOpen: boolean;

  /**
   * Function to call when the modal is closed
   */
  onClose: () => void;

  /**
   * Title of the modal
   */
  title: string;

  /**
   * Description of the modal
   */
  description?: string;

  /**
   * Form fields to render in the modal
   */
  children: ReactNode;

  /**
   * Function to call when the form is submitted
   */
  onSubmit: () => void;

  /**
   * Custom footer content (optional)
   */
  footerContent?: ReactNode;

  /**
   * Custom save button text or element (optional)
   */
  saveButtonText?: string | ReactNode;

  /**
   * Custom cancel button text or element (optional)
   */
  cancelButtonText?: string | ReactNode;

  /**
   * Whether the save button is disabled (optional)
   */
  isSaveDisabled?: boolean;

  /**
   * Whether the form is currently submitting (optional)
   */
  isSubmitting?: boolean;

  /**
   * Maximum width of the modal (optional)
   */
  maxWidth?: string;

  /**
   * Layout type for form fields (optional)
   * - 'default': Standard vertical layout with space between fields
   * - 'grid': Two-column grid layout for form fields
   * - 'compact': Reduced spacing between form fields
   */
  layout?: 'default' | 'grid' | 'compact';

  /**
   * Additional CSS class for the content container (optional)
   */
  contentClassName?: string;

  /**
   * Additional CSS class for the form fields container (optional)
   */
  fieldsContainerClassName?: string;
}

/**
 * A reusable modal component for editing different sections of the profile
 */
const EditSectionModal: React.FC<EditSectionModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  onSubmit,
  footerContent,
  saveButtonText = "OK",
  cancelButtonText = "Cancel",
  isSaveDisabled = false,
  isSubmitting = false,
  maxWidth = "sm:max-w-[600px]",
  layout = 'default',
  contentClassName,
  fieldsContainerClassName
}) => {
  // Determine the container class based on the layout type
  const getContainerClass = () => {
    switch (layout) {
      case 'grid':
        // Changed to always use a single column layout for better vertical stacking
        return "flex flex-col space-y-4";
      case 'compact':
        return "flex flex-col space-y-2 py-2";
      default:
        return "flex flex-col space-y-4 py-4";
    }
  };

  // Apply the appropriate container class
  const containerClass = cn(
    getContainerClass(),
    fieldsContainerClassName
  );

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className={cn("max-w-[95vw] w-full", maxWidth, contentClassName)}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className={containerClass}>
          {/* All children are now rendered directly in a vertical stack */}
          {children}
        </div>

        <DialogFooter className="pt-2 mt-2 border-t border-gray-100 flex flex-col-reverse sm:flex-row gap-2">
          {footerContent || (
            <>
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="min-w-[80px] w-full sm:w-auto"
              >
                {cancelButtonText}
              </Button>
              <Button
                onClick={onSubmit}
                disabled={isSaveDisabled || isSubmitting}
                className="min-w-[120px] w-full sm:w-auto"
              >
                {saveButtonText}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

/**
 * FormField component for consistent form field styling within EditSectionModal
 */
interface FormFieldProps {
  /** Label for the form field */
  label: string;
  /** ID for the form field (used for label association) */
  id: string;
  /** Children to render (typically an Input component) */
  children: ReactNode;
  /** Optional help text to display below the field */
  helpText?: string;
  /** Additional CSS class for the field container */
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  id,
  children,
  helpText,
  className
}) => {
  return (
    <div className={cn("space-y-2 w-full", className)}>
      <div className="flex items-center">
        <Label htmlFor={id} className="text-sm font-medium block">
          {label}
        </Label>
        {helpText && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 text-gray-400 ml-2 cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">{helpText}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      {children}
    </div>
  );
};

/**
 * FormInput component for consistent input styling within EditSectionModal
 */
interface FormInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  /** Label for the input field */
  label: string;
  /** Optional help text to display below the field */
  helpText?: string;
  /** Additional CSS class for the field container */
  containerClassName?: string;
}

export const FormInput: React.FC<FormInputProps> = ({
  label,
  id,
  helpText,
  containerClassName,
  ...props
}) => {
  const inputId = id || props.name || label.toLowerCase().replace(/\s+/g, '_');

  return (
    <FormField
      label={label}
      id={inputId}
      helpText={helpText}
      className={containerClassName}
    >
      <Input
        id={inputId}
        className="w-full"
        {...props}
      />
    </FormField>
  );
};

export default EditSectionModal;

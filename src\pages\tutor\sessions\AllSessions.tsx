import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import { Badge } from "@/components/ui/Badge";
import { Checkbox } from "@/components/ui/Checkbox";
import {
  Search,
  Filter,
  ArrowUpDown,
  Columns,
  Clipboard,
  Check,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";

// Sample session data
const sessionsData = [
  {
    id: "S-1001",
    externalId: "EXT-917",
    studentName: "<PERSON> Johnson",
    studentEmail: "<EMAIL>",
    country: "United States",
    topic: "Reinforcement Learning",
    sessionType: "One-on-One",
    status: "Completed",
    date: "2023-11-15",
    time: "10:00 AM",
    duration: 60,
  },
  {
    id: "S-1002",
    externalId: "EXT-284",
    studentName: "Bella <PERSON>",
    studentEmail: "<EMAIL>",
    country: "Germany",
    topic: "Neural Networks",
    sessionType: "Group",
    status: "Scheduled",
    date: "2023-11-20",
    time: "2:00 PM",
    duration: 90,
  },
  {
    id: "S-1003",
    externalId: "EXT-617",
    studentName: "Charlie Davis",
    studentEmail: "<EMAIL>",
    country: "Argentina",
    topic: "Computer Vision",
    sessionType: "One-on-One",
    status: "Active",
    date: "2023-11-18",
    time: "3:30 PM",
    duration: 60,
  },
  {
    id: "S-1004",
    externalId: "EXT-162",
    studentName: "David Wilson",
    studentEmail: "<EMAIL>",
    country: "Canada",
    topic: "Natural Language Processing",
    sessionType: "One-on-One",
    status: "Cancelled",
    date: "2023-11-10",
    time: "11:00 AM",
    duration: 45,
  },
];

const AllSessions = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSessions, setSelectedSessions] = useState<string[]>([]);
  const [visibleColumns, setVisibleColumns] = useState({
    id: true,
    externalId: true,
    student: true,
    country: true,
    topic: true,
    sessionType: true,
    status: true,
    date: true,
  });
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "ascending" | "descending";
  } | null>(null);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  // Filter sessions based on search term
  const filteredSessions = sessionsData.filter((session) => {
    const searchString = searchTerm.toLowerCase();
    return (
      session.id.toLowerCase().includes(searchString) ||
      session.externalId.toLowerCase().includes(searchString) ||
      session.studentName.toLowerCase().includes(searchString) ||
      session.studentEmail.toLowerCase().includes(searchString) ||
      session.country.toLowerCase().includes(searchString) ||
      session.topic.toLowerCase().includes(searchString) ||
      session.status.toLowerCase().includes(searchString)
    );
  });

  // Sort sessions
  const sortedSessions = React.useMemo(() => {
    let sortableSessions = [...filteredSessions];
    if (sortConfig !== null) {
      sortableSessions.sort((a, b) => {
        if (
          a[sortConfig.key as keyof typeof a] <
          b[sortConfig.key as keyof typeof b]
        ) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (
          a[sortConfig.key as keyof typeof a] >
          b[sortConfig.key as keyof typeof b]
        ) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableSessions;
  }, [filteredSessions, sortConfig]);

  // Request sort
  const requestSort = (key: string) => {
    let direction: "ascending" | "descending" = "ascending";
    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "ascending"
    ) {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSessions(sortedSessions.map((session) => session.id));
    } else {
      setSelectedSessions([]);
    }
  };

  // Handle select one
  const handleSelectOne = (sessionId: string, checked: boolean) => {
    if (checked) {
      setSelectedSessions([...selectedSessions, sessionId]);
    } else {
      setSelectedSessions(selectedSessions.filter((id) => id !== sessionId));
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string, field: string) => {
    navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(null), 2000);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "scheduled":
        return <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>;
      case "active":
        return <Badge className="bg-purple-100 text-purple-800">Active</Badge>;
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <TutorPageLayout
      title="All Sessions"
      description="View and manage all your tutoring sessions"
    >
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {/* Table controls */}
        <div className="p-4 border-b flex flex-wrap items-center justify-between gap-4">
          <div className="relative flex-grow max-w-md">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={18}
            />
            <Input
              placeholder="Search sessions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <Filter size={16} className="mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {/* Filter options would go here */}
                <DropdownMenuCheckboxItem checked>
                  All Sessions
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem>
                  Completed
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem>
                  Scheduled
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem>
                  Active
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem>
                  Cancelled
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
            >
              <ArrowUpDown size={16} className="mr-2" />
              Sort
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <Columns size={16} className="mr-2" />
                  Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.id}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      id: !!checked,
                    })
                  }
                >
                  Session ID
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.externalId}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      externalId: !!checked,
                    })
                  }
                >
                  External ID
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.student}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      student: !!checked,
                    })
                  }
                >
                  Student
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.country}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      country: !!checked,
                    })
                  }
                >
                  Country
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.topic}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      topic: !!checked,
                    })
                  }
                >
                  Topic
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.sessionType}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      sessionType: !!checked,
                    })
                  }
                >
                  Session Type
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.status}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      status: !!checked,
                    })
                  }
                >
                  Status
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.date}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      date: !!checked,
                    })
                  }
                >
                  Date & Time
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {selectedSessions.length > 0 && (
              <Button variant="outline" size="sm">
                {selectedSessions.length} Selected
              </Button>
            )}
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="w-12">
                  <Checkbox
                    checked={
                      selectedSessions.length === sortedSessions.length &&
                      sortedSessions.length > 0
                    }
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </TableHead>
                {visibleColumns.id && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("id")}
                  >
                    Session ID
                  </TableHead>
                )}
                {visibleColumns.externalId && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("externalId")}
                  >
                    External ID
                  </TableHead>
                )}
                {visibleColumns.student && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("studentName")}
                  >
                    Student
                  </TableHead>
                )}
                {visibleColumns.country && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("country")}
                  >
                    Country
                  </TableHead>
                )}
                {visibleColumns.topic && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("topic")}
                  >
                    Topic
                  </TableHead>
                )}
                {visibleColumns.sessionType && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("sessionType")}
                  >
                    Session Type
                  </TableHead>
                )}
                {visibleColumns.status && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("status")}
                  >
                    Status
                  </TableHead>
                )}
                {visibleColumns.date && (
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => requestSort("date")}
                  >
                    Date & Time
                  </TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedSessions.map((session) => (
                <TableRow key={session.id} className="hover:bg-gray-50">
                  <TableCell>
                    <Checkbox
                      checked={selectedSessions.includes(session.id)}
                      onCheckedChange={(checked) =>
                        handleSelectOne(session.id, !!checked)
                      }
                      aria-label={`Select session ${session.id}`}
                    />
                  </TableCell>
                  {visibleColumns.id && (
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        {session.id}
                        <button
                          onClick={() =>
                            copyToClipboard(
                              session.id,
                              `id-${session.id}`
                            )
                          }
                          className="ml-2 text-gray-400 hover:text-gray-600"
                        >
                          {copiedField === `id-${session.id}` ? (
                            <Check size={16} className="text-green-500" />
                          ) : (
                            <Clipboard size={16} />
                          )}
                        </button>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.externalId && (
                    <TableCell>
                      <div className="flex items-center">
                        {session.externalId}
                        <button
                          onClick={() =>
                            copyToClipboard(
                              session.externalId,
                              `ext-${session.id}`
                            )
                          }
                          className="ml-2 text-gray-400 hover:text-gray-600"
                        >
                          {copiedField === `ext-${session.id}` ? (
                            <Check size={16} className="text-green-500" />
                          ) : (
                            <Clipboard size={16} />
                          )}
                        </button>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.student && (
                    <TableCell>
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded-full"
                            src={`https://ui-avatars.com/api/?name=${session.studentName}&background=random`}
                            alt={session.studentName}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="font-medium text-gray-900">
                            {session.studentName}
                          </div>
                          <div className="text-gray-500 text-sm">
                            {session.studentEmail}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.country && (
                    <TableCell>
                      <div className="flex items-center">
                        <span className="mr-2">{session.country}</span>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.topic && (
                    <TableCell>{session.topic}</TableCell>
                  )}
                  {visibleColumns.sessionType && (
                    <TableCell>{session.sessionType}</TableCell>
                  )}
                  {visibleColumns.status && (
                    <TableCell>
                      {getStatusBadge(session.status)}
                    </TableCell>
                  )}
                  {visibleColumns.date && (
                    <TableCell>
                      <div>
                        <div>{formatDate(session.date)}</div>
                        <div className="text-gray-500 text-sm">
                          {session.time} ({session.duration} min)
                        </div>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </TutorPageLayout>
  );
};

export default AllSessions;

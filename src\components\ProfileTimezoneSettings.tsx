import React, { useEffect } from 'react';
import TimezoneSelect from './TimezoneSelect';
import { supabase } from '@/lib/supabaseWrapper';
import { useAuthContext } from '@/context/AuthContext';
import { create } from 'zustand';

interface ProfileTimezoneSettingsState {
  timezone: string;
  isLoading: boolean;
  error: string | null;
  successMessage: string | null;

  // Actions
  setTimezone: (timezone: string) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  reset: () => void;
}

const useProfileTimezoneStore = create<ProfileTimezoneSettingsState>((set) => ({
  timezone: '',
  isLoading: false,
  error: null,
  successMessage: null,

  setTimezone: (timezone) => set({ timezone }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setSuccessMessage: (successMessage) => set({ successMessage }),
  reset: () => set({
    timezone: '',
    isLoading: false,
    error: null,
    successMessage: null
  }),
}));

interface ProfileTimezoneSettingsProps {
  className?: string;
  onSaved?: () => void;
}

const ProfileTimezoneSettings: React.FC<ProfileTimezoneSettingsProps> = ({
  className = '',
  onSaved
}) => {
  const { profileData, refreshProfile } = useAuthContext();
  const {
    timezone,
    isLoading,
    error,
    successMessage,
    setTimezone,
    setIsLoading,
    setError,
    setSuccessMessage,
    reset
  } = useProfileTimezoneStore();

  // Initialize timezone from profile data
  useEffect(() => {
    if (profileData?.timezone) {
      setTimezone(profileData.timezone);
    }
  }, [profileData?.timezone, setTimezone]);

  const handleSave = async () => {
    if (!profileData?.id) {
      setError('User profile not found');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ timezone })
        .eq('id', profileData.id);

      if (updateError) {
        throw updateError;
      }

      setSuccessMessage('Timezone updated successfully');

      // Refresh the profile data to get the updated timezone
      if (refreshProfile) {
        await refreshProfile();
      }

      if (onSaved) {
        onSaved();
      }
    } catch (err) {
      console.error('Error updating timezone:', err);
      setError(err instanceof Error ? err.message : 'Failed to update timezone');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`p-4 bg-white rounded-lg shadow ${className}`}>
      <h3 className="text-lg font-medium mb-4">Timezone Settings</h3>

      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-4">
          Setting your timezone helps us display session times correctly and ensures
          that scheduling works properly for your location.
        </p>

        <TimezoneSelect
          value={timezone}
          onChange={setTimezone}
          label="Your Timezone"
          showCurrentTime={true}
          error={error || undefined}
        />
      </div>

      {successMessage && (
        <div className="mb-4 p-2 bg-green-50 text-green-700 rounded border border-green-200">
          {successMessage}
        </div>
      )}

      <div className="flex justify-end">
        <button
          type="button"
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
          onClick={handleSave}
          disabled={isLoading || !timezone}
        >
          {isLoading ? 'Saving...' : 'Save Timezone'}
        </button>
      </div>
    </div>
  );
};

export default ProfileTimezoneSettings;

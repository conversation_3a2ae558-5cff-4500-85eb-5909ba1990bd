import React, { ReactNode } from 'react';
import AdminSidebar from '@/components/admin/Sidebar';
import UserNavbar from '@/components/ui/UserNavbar';
import HorizontalDivider from '@/components/ui/HorizontalDivider';

interface AdminPageLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
  actions?: ReactNode;
}

/**
 * A consistent layout for all admin pages that includes the sidebar, navbar,
 * and proper horizontal divider with no margins.
 */
const AdminPageLayout: React.FC<AdminPageLayoutProps> = ({
  children,
  title,
  description,
  actions
}) => {
  return (
    <div className="app-layout bg-gray-50">
      <div className="sidebar sticky top-0 h-screen">
        <AdminSidebar />
      </div>
      <div className="content-area">
        <UserNavbar
          title={title}
          isAdmin={true}
          isAdminPage={true}
          actions={actions}
          className="px-0 py-0 border-0"
        />

        <main className="flex-grow py-6 px-6 overflow-y-auto">
          <div className="max-w-7xl mx-auto">
            {description && (
              <p className="mt-2 text-lg text-gray-500 mb-6">
                {description}
              </p>
            )}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminPageLayout;

import React, { useState, useEffect } from "react";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/Tabs";
import { Button } from "@/components/ui/Button";
import { Calendar, Clock, Save, RefreshCw, Users, CalendarCheck } from "lucide-react";
import { useToast } from "@/components/ui/UseToast";
import LoadingSpinner from "@/components/LoadingSpinner";
import AvailabilityGrid from "@/components/student/availability/AvailabilityGrid";
import StudentCalendarView from "@/components/student/availability/StudentCalendarView";
import AssignedTutors from "@/components/student/availability/AssignedTutors";
import { useStudentAvailabilityStore } from "@/store/studentAvailabilityStore";
import { Badge } from "@/components/ui/Badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { InfoIcon } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";

const StudentSchedulePage: React.FC = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const {
    setStudentId,
    fetchAvailabilityData,
    saveAvailabilityData,
    isLoading,
    error,
    pendingChanges,
    resetPendingChanges,
  } = useStudentAvailabilityStore();

  const [activeTab, setActiveTab] = useState("availability");

  // Initialize student ID and load availability data on component mount
  useEffect(() => {
    if (user?.id) {
      setStudentId(user.id);
      fetchAvailabilityData();
    }
  }, [user?.id, setStudentId, fetchAvailabilityData]);

  // Handle save button click
  const handleSave = async () => {
    try {
      await saveAvailabilityData();
      toast({
        title: "Changes saved",
        description: "Your availability settings have been updated successfully.",
      });
      resetPendingChanges();
    } catch (error) {
      toast({
        title: "Error saving changes",
        description: "There was a problem saving your availability settings. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <StudentPageLayout
      title="My Schedule"
      description={
        <div className="flex items-center space-x-2">
          <span>Manage your availability and view your scheduled sessions.</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon className="h-4 w-4 text-gray-500 cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Your availability will be visible to admins and tutors assigned to you at the batch, topic/subtopic, or session level.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      }
      actions={
        pendingChanges ? (
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        ) : null
      }
    >
      {isLoading && !error ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="lg" fullScreen={false} />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <h3 className="text-red-800 font-medium">Error loading data</h3>
          <p className="text-red-700 mt-1">{error}</p>
          <Button
            variant="outline"
            className="mt-2"
            onClick={() => fetchAvailabilityData()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      ) : (
        <>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid grid-cols-3 w-full max-w-2xl">
              <TabsTrigger value="availability" className="flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Availability Grid
              </TabsTrigger>
              <TabsTrigger value="calendar" className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Calendar View
              </TabsTrigger>
              <TabsTrigger value="tutors" className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                Assigned Tutors
              </TabsTrigger>
            </TabsList>

            <TabsContent value="availability" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Interactive Availability Grid</CardTitle>
                  <CardDescription>
                    Click and drag to create availability windows. Click on existing slots to change their status.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-4 flex flex-wrap gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      Available
                    </Badge>
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                      Preferred
                    </Badge>
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      Unavailable
                    </Badge>
                  </div>
                  <AvailabilityGrid />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="calendar" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Calendar View</CardTitle>
                  <CardDescription>
                    View your scheduled sessions and availability in a calendar format.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <StudentCalendarView />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tutors" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Assigned Tutors</CardTitle>
                  <CardDescription>
                    View tutors who can see your availability settings.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <AssignedTutors />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </StudentPageLayout>
  );
};

export default StudentSchedulePage;

import React from "react";
import AdminSidebar from "@/components/admin/Sidebar";
import { Link } from "react-router-dom";
import { UserPlus, Users, User, Edit, Trash2 } from "lucide-react";
import UserNavbar from "@/components/ui/UserNavbar";

const UserManagement = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <UserNavbar
            title="User Management"
            isAdmin={true}
            isAdminPage={true}
            className="mb-6 px-0 py-0 border-0"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <Link
              to="/admin/users/create"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <UserPlus className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Add New User</h3>
              <p className="text-sm text-gray-500">
                Create new user accounts with appropriate roles and permissions.
              </p>
            </Link>

            <Link
              to="/admin/users/list"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Users className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">List All Users</h3>
              <p className="text-sm text-gray-500">
                View a complete list of all users registered on the platform.
              </p>
            </Link>

            <Link
              to="/admin/users/view"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <User className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">View User Details</h3>
              <p className="text-sm text-gray-500">
                Access detailed information about specific users and their
                activity.
              </p>
            </Link>

            <Link
              to="/admin/users/edit"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Edit className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Edit User Details</h3>
              <p className="text-sm text-gray-500">
                Modify user information, roles, and account settings.
              </p>
            </Link>

            <Link
              to="/admin/users/delete"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Trash2 className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Delete User</h3>
              <p className="text-sm text-gray-500">
                Remove users who are no longer active on the platform.
              </p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserManagement;

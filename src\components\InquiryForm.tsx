import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Label } from "@/components/ui/Label";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/TextArea";
import { Button } from "@/components/ui/Button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { useToast } from "@/components/ui/UseToast";
import { supabase } from "@/lib/supabaseClient";

const InquiryForm = () => {
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    inquiryType: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pre-select inquiry type from URL parameters
  useEffect(() => {
    const typeParam = searchParams.get("type");
    if (typeParam) {
      // Validate that the type parameter is one of the valid options
      const validTypes = ["general", "tutoring", "courses", "technical", "billing", "feedback", "tutor_request", "other"];
      if (validTypes.includes(typeParam)) {
        const updates: Partial<typeof formData> = { inquiryType: typeParam };

        // Pre-fill message for tutoring services
        if (typeParam === "tutoring") {
          updates.message = `I want to schedule a call with rfLearn team.

Reason:
Preferred Date and Time:`;
        }

        setFormData(prev => ({ ...prev, ...updates }));
      }
    }
  }, [searchParams]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({ ...prev, inquiryType: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form data
      if (
        !formData.name ||
        !formData.email ||
        !formData.inquiryType ||
        !formData.message
      ) {
        throw new Error("Please fill in all required fields");
      }

      // Submit to Supabase
      const { error } = await supabase.from("inquiries").insert({
        name: formData.name,
        email: formData.email,
        phone: formData.phone || null,
        inquiry_type: formData.inquiryType,
        message: formData.message,
        status: "new",
      });

      if (error) {
        throw error;
      }

      toast({
        title: "Inquiry Submitted",
        description:
          "Thank you for your inquiry. We will get back to you soon!",
      });

      // Reset form
      setFormData({
        name: "",
        email: "",
        phone: "",
        inquiryType: "",
        message: "",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description:
          error.message || "Failed to submit inquiry. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-lg lg:mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold">Contact Us</CardTitle>
        <CardDescription>
          Have questions about our courses or tutoring services? Fill out this
          form and our team will get back to you.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-3 md:space-y-3 lg:space-y-4">
          <div className="space-y-1 sm:space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Your name"
              required
            />
          </div>

          <div className="space-y-1 sm:space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="space-y-1 sm:space-y-2">
            <Label htmlFor="phone">Phone (Optional)</Label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder="(*************"
            />
          </div>

          <div className="space-y-1 sm:space-y-2">
            <Label htmlFor="inquiryType">Inquiry Type</Label>
            <Select
              value={formData.inquiryType}
              onValueChange={handleSelectChange}
            >
              <SelectTrigger id="inquiryType">
                <SelectValue placeholder="Select inquiry type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Question</SelectItem>
                <SelectItem value="tutoring">Tutoring Services</SelectItem>
                <SelectItem value="courses">Course Information</SelectItem>
                <SelectItem value="technical">Technical Support</SelectItem>
                <SelectItem value="billing">Billing</SelectItem>
                <SelectItem value="feedback">Feedback</SelectItem>
                <SelectItem value="tutor_request">Tutor Request</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1 sm:space-y-2">
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              placeholder="Please describe your inquiry in detail..."
              rows={2}
              className="sm:min-h-[100px] md:min-h-[100px] lg:min-h-[120px]"
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full button-gradient text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit Inquiry"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default InquiryForm;

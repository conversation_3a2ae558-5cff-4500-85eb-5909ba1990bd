// Payment provider types and interfaces

export interface PaymentResult {
  success: boolean;
  paymentId: string;
  orderId?: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  metadata?: Record<string, any>;
  error?: PaymentError;
}

export interface PaymentError {
  code: string;
  message: string;
  retryable: boolean;
  provider: string;
}

export type PaymentStatus = 
  | 'pending' 
  | 'processing' 
  | 'succeeded' 
  | 'failed' 
  | 'canceled' 
  | 'requires_action';

export type PaymentMethod = 
  | 'card' 
  | 'netbanking' 
  | 'wallet' 
  | 'upi' 
  | 'emi' 
  | 'paypal_account' 
  | 'apple_pay' 
  | 'google_pay';

export interface PaymentProvider {
  name: string;
  displayName: string;
  supportedCurrencies: string[];
  supportedMethods: PaymentMethod[];
  
  // Core payment operations
  createOrder(params: CreateOrderParams): Promise<PaymentResult>;
  processPayment(params: ProcessPaymentParams): Promise<PaymentResult>;
  verifyPayment(params: VerifyPaymentParams): Promise<PaymentResult>;
  
  // Optional operations
  refundPayment?(params: RefundPaymentParams): Promise<PaymentResult>;
  getPaymentStatus?(paymentId: string): Promise<PaymentResult>;
}

export interface CreateOrderParams {
  amount: number;
  currency: string;
  description?: string;
  customerEmail?: string;
  customerId?: string;
  metadata?: Record<string, any>;
}

export interface ProcessPaymentParams {
  orderId: string;
  paymentMethod: PaymentMethod;
  customerDetails?: CustomerDetails;
  metadata?: Record<string, any>;
}

export interface VerifyPaymentParams {
  paymentId: string;
  orderId: string;
  signature?: string;
  metadata?: Record<string, any>;
}

export interface RefundPaymentParams {
  paymentId: string;
  amount?: number;
  reason?: string;
}

export interface CustomerDetails {
  name?: string;
  email?: string;
  phone?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
  };
}

// Database types
export interface PaymentRecord {
  id: string;
  workflow_id: string;
  student_id: string;
  provider_id?: string;
  provider_payment_id?: string;
  provider_customer_id?: string;
  provider_metadata?: Record<string, any>;
  amount: number;
  currency: string;
  status: PaymentStatus;
  payment_method_type?: PaymentMethod;
  description?: string;
  receipt_email?: string;
  receipt_url?: string;
  failure_code?: string;
  failure_message?: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentProvider_DB {
  id: string;
  name: string;
  display_name: string;
  is_active: boolean;
  configuration?: Record<string, any>;
  supported_currencies: string[];
  supported_methods: PaymentMethod[];
  created_at: string;
  updated_at: string;
}

// Workflow integration types
export interface WorkflowPaymentData {
  workflowId: string;
  studentId: string;
  productId: string;
  amount: number;
  currency: string;
  description: string;
  customerEmail: string;
}

export interface PaymentProviderConfig {
  name: string;
  enabled: boolean;
  configuration: Record<string, any>;
}

import { useState, useEffect } from "react";
import { useLocation, Link, useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabaseClient";
import { useToast } from "@/components/ui/UseToast";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { getEmailConfirmationRedirect } from "@/routes/RouteConfig";
import { useAuth } from "@/context/AuthContext";

const ConfirmEmail = () => {
  const location = useLocation();
  const email = location.state?.email || "";
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(35); // Start with 35 seconds initially
  const { toast } = useToast();
  const navigate = useNavigate();
  const { userType, onboardingStatus } = useAuth();
  
  // Cooldown periods in seconds
  const INITIAL_COOLDOWN = 35; // First-time cooldown (Supabase requires 33 seconds)
  const RESEND_COOLDOWN = 60;  // Subsequent cooldowns

  useEffect(() => {
    let timer: number | null = null;
    
    if (countdown > 0) {
      timer = window.setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  // Add this useEffect to listen for messages from the confirmation tab
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Verify the origin for security
      if (event.origin !== window.location.origin) return;
      
      // Check if this is our confirmation message
      if (event.data?.type === "EMAIL_CONFIRMED") {
        // Get source from URL if available
        const urlParams = new URLSearchParams(window.location.search);
        const source = urlParams.get('source');
        
        // Get the redirect path from our centralized routing config
        const redirectPath = getEmailConfirmationRedirect(source, userType, onboardingStatus);
        
        // Redirect to the appropriate page
        navigate(redirectPath);
      }
    };
    
    // Add the event listener
    window.addEventListener("message", handleMessage);
    
    // Clean up
    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [navigate, userType, onboardingStatus]);

  const handleResendEmail = async () => {
    if (!email) {
      toast({
        title: "Error",
        description: "Email address is missing. Please try signing up again.",
        variant: "destructive",
      });
      return;
    }

    setIsResending(true);
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        // Check for timing-related error
        if (error.message && error.message.includes("security purposes")) {
          // Extract the required wait time if possible
          const timeMatch = error.message.match(/after (\d+) seconds/);
          const waitTime = timeMatch ? parseInt(timeMatch[1]) + 2 : INITIAL_COOLDOWN;
          
          setCountdown(waitTime);
          throw new Error(`Please wait ${waitTime} seconds before requesting another email.`);
        }
        throw error;
      }

      toast({
        title: "Email resent",
        description: "We've sent another confirmation email to your inbox.",
      });
      
      // Start the regular cooldown timer for subsequent attempts
      setCountdown(RESEND_COOLDOWN);
    } catch (error) {
      console.error("Error resending email:", error);
      toast({
        title: "Failed to resend",
        description: error.message || "There was an error resending the confirmation email.",
        variant: "destructive",
      });
    } finally {
      setIsResending(false);
    }
  };

  // Format the countdown time as MM:SS
  const formatCountdown = () => {
    const minutes = Math.floor(countdown / 60);
    const seconds = countdown % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 text-center">
          <div className="flex justify-center">
            <div className="w-24 h-24 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Email confirmation
          </h2>
          
          <p className="mt-2 text-md text-gray-600">
            We have sent you a confirmation email.
          </p>
          
          <p className="mt-2 text-md text-gray-600">
            To finish setting up your account, please check your inbox
          </p>
          
          <p className="mt-4 text-md text-gray-600">
            If you don't see the email, please check your spam folder.
          </p>
          
          <div className="mt-6">
            <p className="text-md">
              Didn't get it? {" "}
              <button
                onClick={handleResendEmail}
                disabled={isResending || countdown > 0}
                className={`text-purple-600 hover:text-purple-800 font-medium focus:outline-none ${
                  (isResending || countdown > 0) ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                {isResending 
                  ? "Resending..." 
                  : countdown > 0 
                    ? `Resend available in ${formatCountdown()}` 
                    : "Resend Email"}
              </button>
            </p>
          </div>
          
          <div className="mt-8">
            <Link
              to="/login"
              className="text-sm text-gray-600 hover:text-gray-900"
            >
              Return to login
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ConfirmEmail;




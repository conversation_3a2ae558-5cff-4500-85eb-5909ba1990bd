import { supabase } from '@/lib/supabaseClient';
import { PaymentProviderFactory } from './PaymentProviderFactory';
import { 
  PaymentResult, 
  WorkflowPaymentData, 
  PaymentRecord, 
  PaymentProvider_DB 
} from './types';

export class PaymentService {
  /**
   * Process payment for a subscription workflow
   */
  static async processWorkflowPayment(
    workflowData: WorkflowPaymentData,
    providerName?: string
  ): Promise<PaymentResult> {
    try {
      // Determine payment provider
      const selectedProvider = providerName || 
        PaymentProviderFactory.getProviderForCurrency(workflowData.currency);

      if (!PaymentProviderFactory.isProviderAvailable(selectedProvider)) {
        throw new Error(`Payment provider ${selectedProvider} is not available`);
      }

      // Get provider instance
      const provider = PaymentProviderFactory.create(selectedProvider);

      // Create order
      const orderResult = await provider.createOrder({
        amount: workflowData.amount,
        currency: workflowData.currency,
        description: workflowData.description,
        customerEmail: workflowData.customerEmail,
        metadata: {
          workflow_id: workflowData.workflowId,
          student_id: workflowData.studentId,
          product_id: workflowData.productId
        }
      });

      if (!orderResult.success) {
        throw new Error(orderResult.error?.message || 'Failed to create order');
      }

      // Save payment record to database
      const paymentRecord = await this.createPaymentRecord({
        workflow_id: workflowData.workflowId,
        student_id: workflowData.studentId,
        provider_name: selectedProvider,
        provider_payment_id: orderResult.orderId!,
        amount: workflowData.amount,
        currency: workflowData.currency,
        description: workflowData.description,
        metadata: orderResult.metadata
      });

      // Update workflow with payment information
      await this.updateWorkflowPaymentInfo(workflowData.workflowId, {
        provider_name: selectedProvider,
        provider_order_id: orderResult.orderId!,
        total_amount: workflowData.amount,
        currency: workflowData.currency,
        payment_status: 'pending'
      });

      // Process payment
      const paymentResult = await provider.processPayment({
        orderId: orderResult.orderId!,
        paymentMethod: 'card', // Default, can be customized
        metadata: orderResult.metadata
      });

      // Update payment status
      if (paymentResult.success) {
        await this.updatePaymentStatus(
          paymentRecord.id,
          'succeeded',
          paymentResult.paymentId,
          paymentResult.metadata
        );

        // Complete workflow
        await this.completeWorkflowPayment(workflowData.workflowId);
      } else {
        await this.updatePaymentStatus(
          paymentRecord.id,
          'failed',
          paymentResult.paymentId,
          paymentResult.metadata,
          paymentResult.error?.code,
          paymentResult.error?.message
        );
      }

      return paymentResult;
    } catch (error) {
      console.error('Payment processing error:', error);
      return {
        success: false,
        paymentId: '',
        status: 'failed',
        amount: workflowData.amount,
        currency: workflowData.currency,
        error: {
          code: 'PAYMENT_SERVICE_ERROR',
          message: error instanceof Error ? error.message : 'Payment processing failed',
          retryable: true,
          provider: 'unknown'
        }
      };
    }
  }

  /**
   * Create payment record in database
   */
  private static async createPaymentRecord(data: {
    workflow_id: string;
    student_id: string;
    provider_name: string;
    provider_payment_id: string;
    amount: number;
    currency: string;
    description: string;
    metadata?: Record<string, any>;
  }): Promise<PaymentRecord> {
    // Get provider ID
    const { data: provider, error: providerError } = await supabase
      .from('payment_providers')
      .select('id')
      .eq('name', data.provider_name)
      .single();

    if (providerError || !provider) {
      throw new Error(`Payment provider ${data.provider_name} not found`);
    }

    // Create payment record
    const { data: payment, error } = await supabase
      .from('payments')
      .insert({
        workflow_id: data.workflow_id,
        student_id: data.student_id,
        provider_id: provider.id,
        provider_payment_id: data.provider_payment_id,
        amount: data.amount,
        currency: data.currency,
        status: 'pending',
        description: data.description,
        provider_metadata: data.metadata || {}
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create payment record: ${error.message}`);
    }

    return payment;
  }

  /**
   * Update payment status
   */
  private static async updatePaymentStatus(
    paymentId: string,
    status: string,
    providerPaymentId?: string,
    metadata?: Record<string, any>,
    failureCode?: string,
    failureMessage?: string
  ): Promise<void> {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (providerPaymentId) {
      updateData.provider_payment_id = providerPaymentId;
    }

    if (metadata) {
      updateData.provider_metadata = metadata;
    }

    if (status === 'succeeded') {
      updateData.succeeded_at = new Date().toISOString();
    } else if (status === 'failed') {
      updateData.failed_at = new Date().toISOString();
      if (failureCode) updateData.failure_code = failureCode;
      if (failureMessage) updateData.failure_message = failureMessage;
    }

    const { error } = await supabase
      .from('payments')
      .update(updateData)
      .eq('id', paymentId);

    if (error) {
      console.error('Failed to update payment status:', error);
    }
  }

  /**
   * Update workflow payment information
   */
  private static async updateWorkflowPaymentInfo(
    workflowId: string,
    data: {
      provider_name: string;
      provider_order_id: string;
      total_amount: number;
      currency: string;
      payment_status: string;
    }
  ): Promise<void> {
    // Get provider ID
    const { data: provider, error: providerError } = await supabase
      .from('payment_providers')
      .select('id')
      .eq('name', data.provider_name)
      .single();

    if (providerError || !provider) {
      throw new Error(`Payment provider ${data.provider_name} not found`);
    }

    const { error } = await supabase
      .from('subscription_workflows')
      .update({
        provider_id: provider.id,
        provider_order_id: data.provider_order_id,
        total_amount: data.total_amount,
        currency: data.currency,
        payment_status: data.payment_status
      })
      .eq('id', workflowId);

    if (error) {
      console.error('Failed to update workflow payment info:', error);
    }
  }

  /**
   * Complete workflow payment
   */
  private static async completeWorkflowPayment(workflowId: string): Promise<void> {
    const { error } = await supabase
      .from('subscription_workflows')
      .update({
        payment_status: 'succeeded',
        payment_completed_at: new Date().toISOString(),
        step_4_completed: true,
        step_4_completed_at: new Date().toISOString(),
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('id', workflowId);

    if (error) {
      console.error('Failed to complete workflow payment:', error);
    }
  }

  /**
   * Get available payment providers
   */
  static getAvailableProviders(): string[] {
    return PaymentProviderFactory.getAvailableProviders();
  }

  /**
   * Get payment provider for currency
   */
  static getProviderForCurrency(currency: string): string {
    return PaymentProviderFactory.getProviderForCurrency(currency);
  }

  /**
   * Get payment history for student
   */
  static async getPaymentHistory(studentId: string): Promise<PaymentRecord[]> {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        payment_providers (
          name,
          display_name
        )
      `)
      .eq('student_id', studentId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Failed to fetch payment history:', error);
      return [];
    }

    return data || [];
  }
}

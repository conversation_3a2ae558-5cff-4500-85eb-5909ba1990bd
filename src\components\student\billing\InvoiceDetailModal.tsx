// src/components/student/billing/InvoiceDetailModal.tsx
import React from "react";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/Dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/Table";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Download, Printer } from "lucide-react";
import { Invoice } from "@/store/billingStore";
import { format } from "date-fns";

interface InvoiceDetailModalProps {
  invoice: Invoice | null;
  isOpen: boolean;
  onClose: () => void;
}

const InvoiceDetailModal: React.FC<InvoiceDetailModalProps> = ({ invoice, isOpen, onClose }) => {
  if (!invoice) return null;

  // Format date
  const formatDate = (date: Date) => {
    return format(date, "MMMM d, yyyy");
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return "bg-green-100 text-green-800 hover:bg-green-200";
      case 'pending':
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
      case 'failed':
        return "bg-red-100 text-red-800 hover:bg-red-200";
      case 'refunded':
        return "bg-blue-100 text-blue-800 hover:bg-blue-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Invoice #{invoice.id.substring(0, 8)}</DialogTitle>
          <DialogDescription>
            Issued on {formatDate(invoice.created_at)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Header */}
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold">RF Learn</h3>
              <p className="text-sm text-gray-500">123 Education Street</p>
              <p className="text-sm text-gray-500">Learning City, LC 12345</p>
              <p className="text-sm text-gray-500"><EMAIL></p>
            </div>
            <div className="text-right">
              <Badge className={getStatusColor(invoice.status)}>
                {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
              </Badge>
              <p className="text-sm text-gray-500 mt-2">Invoice #: {invoice.id}</p>
              <p className="text-sm text-gray-500">Date: {formatDate(invoice.created_at)}</p>
              <p className="text-sm text-gray-500">Payment Method: {invoice.payment_method}</p>
            </div>
          </div>

          {/* Invoice Items */}
          <div>
            <h4 className="text-md font-semibold mb-2">Invoice Items</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead className="text-right">Unit Price</TableHead>
                  <TableHead className="text-right">Subtotal</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoice.items && invoice.items.length > 0 ? (
                  invoice.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.product_name}</TableCell>
                      <TableCell className="text-right">{item.quantity}</TableCell>
                      <TableCell className="text-right">${item.unit_price.toFixed(2)}</TableCell>
                      <TableCell className="text-right">${item.subtotal.toFixed(2)}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                      No items found
                    </TableCell>
                  </TableRow>
                )}

                {/* Total Row */}
                <TableRow>
                  <TableCell colSpan={3} className="text-right font-semibold">Total</TableCell>
                  <TableCell className="text-right font-semibold">${invoice.amount.toFixed(2)}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          {/* Payment Information */}
          <div className="border-t pt-4">
            <h4 className="text-md font-semibold mb-2">Payment Information</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <p className="text-gray-500">Payment ID:</p>
                <p>{invoice.payment_id}</p>
              </div>
              <div>
                <p className="text-gray-500">Payment Method:</p>
                <p>{invoice.payment_method}</p>
              </div>
              <div>
                <p className="text-gray-500">Payment Date:</p>
                <p>{formatDate(invoice.created_at)}</p>
              </div>
              <div>
                <p className="text-gray-500">Payment Status:</p>
                <Badge className={getStatusColor(invoice.status)}>
                  {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                </Badge>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button variant="outline">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InvoiceDetailModal;
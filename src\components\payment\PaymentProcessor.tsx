import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import LoadingSpinner from '@/components/LoadingSpinner';
import PaymentProviderSelector from './PaymentProviderSelector';
import { PaymentService } from '@/services/payment/PaymentService';
import { WorkflowPaymentData, PaymentResult } from '@/services/payment/types';
import { 
  CreditCard, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  ArrowLeft 
} from 'lucide-react';

interface PaymentProcessorProps {
  workflowData: WorkflowPaymentData;
  onSuccess: (result: PaymentResult) => void;
  onError: (error: string) => void;
  onBack?: () => void;
}

type PaymentStep = 'provider_selection' | 'processing' | 'success' | 'error';

const PaymentProcessor: React.FC<PaymentProcessorProps> = ({
  workflowData,
  onSuccess,
  onError,
  onBack
}) => {
  const [currentStep, setCurrentStep] = useState<PaymentStep>('provider_selection');
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [paymentResult, setPaymentResult] = useState<PaymentResult | null>(null);
  const [error, setError] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Get available providers
  const availableProviders = PaymentService.getAvailableProviders();
  
  // Set default provider if not selected
  React.useEffect(() => {
    if (!selectedProvider && availableProviders.length > 0) {
      const defaultProvider = PaymentService.getProviderForCurrency(workflowData.currency);
      if (availableProviders.includes(defaultProvider)) {
        setSelectedProvider(defaultProvider);
      } else {
        setSelectedProvider(availableProviders[0]);
      }
    }
  }, [availableProviders, workflowData.currency, selectedProvider]);

  const handlePaymentProcess = async () => {
    if (!selectedProvider) {
      setError('Please select a payment provider');
      return;
    }

    setIsProcessing(true);
    setCurrentStep('processing');
    setError('');

    try {
      const result = await PaymentService.processWorkflowPayment(
        workflowData,
        selectedProvider
      );

      setPaymentResult(result);

      if (result.success) {
        setCurrentStep('success');
        onSuccess(result);
      } else {
        setCurrentStep('error');
        setError(result.error?.message || 'Payment failed');
        onError(result.error?.message || 'Payment failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment processing failed';
      setCurrentStep('error');
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRetry = () => {
    setCurrentStep('provider_selection');
    setError('');
    setPaymentResult(null);
  };

  const renderProviderSelection = () => (
    <div className="space-y-6">
      <PaymentProviderSelector
        availableProviders={availableProviders}
        selectedProvider={selectedProvider}
        onProviderSelect={setSelectedProvider}
        currency={workflowData.currency}
      />

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            Order Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">{workflowData.description}</span>
              <span className="text-lg font-bold">
                {workflowData.currency.toUpperCase()} {workflowData.amount.toFixed(2)}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <p>Student: {workflowData.customerEmail}</p>
              <p>Payment Method: {selectedProvider ? 
                selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1) : 
                'Not selected'
              }</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Notice */}
      <div className="bg-green-50 p-4 rounded-lg">
        <div className="flex items-center mb-2">
          <Shield className="h-5 w-5 text-green-600 mr-2" />
          <span className="text-green-800 font-medium">Secure Payment</span>
        </div>
        <p className="text-green-700 text-sm">
          Your payment information is encrypted and secure. We use industry-standard security measures.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between">
        {onBack && (
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        )}
        <Button
          onClick={handlePaymentProcess}
          disabled={!selectedProvider || isProcessing}
          className="bg-green-600 hover:bg-green-700 ml-auto"
        >
          {isProcessing ? (
            <>
              <div className="flex items-center">
                <LoadingSpinner size="sm" fullScreen={false} />
                <span className="ml-2">Processing...</span>
              </div>
            </>
          ) : (
            <>
              Pay {workflowData.currency.toUpperCase()} {workflowData.amount.toFixed(2)}
              <CreditCard className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );

  const renderProcessing = () => (
    <div className="text-center py-12">
      <div className="mb-4">
        <LoadingSpinner size="lg" fullScreen={false} />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Processing Payment
      </h3>
      <p className="text-gray-600 mb-4">
        Please wait while we process your payment securely...
      </p>
      <div className="bg-blue-50 p-4 rounded-lg max-w-md mx-auto">
        <p className="text-blue-800 text-sm">
          <strong>Do not close this window</strong> or refresh the page during payment processing.
        </p>
      </div>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center py-12">
      <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Payment Successful!
      </h3>
      <p className="text-gray-600 mb-6">
        Your payment has been processed successfully. You now have access to your subscription.
      </p>
      {paymentResult && (
        <div className="bg-green-50 p-4 rounded-lg max-w-md mx-auto">
          <div className="text-sm text-green-800">
            <p><strong>Payment ID:</strong> {paymentResult.paymentId}</p>
            <p><strong>Amount:</strong> {workflowData.currency.toUpperCase()} {workflowData.amount.toFixed(2)}</p>
            <p><strong>Status:</strong> {paymentResult.status}</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderError = () => (
    <div className="text-center py-12">
      <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Payment Failed
      </h3>
      <p className="text-gray-600 mb-6">
        We encountered an issue processing your payment. Please try again.
      </p>
      
      {error && (
        <Alert className="max-w-md mx-auto mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={handleRetry}>
          Try Again
        </Button>
        {onBack && (
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="h-5 w-5 mr-2" />
          Payment Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        {currentStep === 'provider_selection' && renderProviderSelection()}
        {currentStep === 'processing' && renderProcessing()}
        {currentStep === 'success' && renderSuccess()}
        {currentStep === 'error' && renderError()}
      </CardContent>
    </Card>
  );
};

export default PaymentProcessor;

import { create } from "zustand";

interface NavbarState {
  isMenuOpen: boolean;
  displayName: string;
  setIsMenuOpen: (isOpen: boolean) => void;
  toggleMenu: () => void;
  setDisplayName: (name: string) => void;
}

export const useNavbarStore = create<NavbarState>((set) => ({
  isMenuOpen: false,
  displayName: "",
  setIsMenuOpen: (isOpen) => set({ isMenuOpen: isOpen }),
  toggleMenu: () => set((state) => ({ isMenuOpen: !state.isMenuOpen })),
  setDisplayName: (name) => set({ displayName: name }),
}));
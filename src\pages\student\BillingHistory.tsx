// src/pages/student/BillingHistory.tsx
import React, { useEffect, useState } from "react";
import { useAuth } from "@/context/AuthContext";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useBillingStore } from "@/store/billingStore";
import InvoiceList from "@/components/student/billing/InvoiceList";
import InvoiceDetailModal from "@/components/student/billing/InvoiceDetailModal";
import LoadingSpinner from "@/components/LoadingSpinner";
import { AlertCircle, Search } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/Tabs";

const BillingHistory: React.FC = () => {
  const { user } = useAuth();
  const {
    invoices,
    isLoading,
    error,
    fetchInvoices,
    getInvoiceDetails
  } = useBillingStore();

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  useEffect(() => {
    if (user?.id) {
      fetchInvoices(user.id);
    }
  }, [fetchInvoices, user?.id]);

  // Filter invoices based on search term and status
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch =
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.payment_method?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.status.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || invoice.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle view invoice details
  const handleViewInvoiceDetails = async (invoiceId: string) => {
    const invoiceDetails = await getInvoiceDetails(invoiceId);
    if (invoiceDetails) {
      setSelectedInvoice(invoiceDetails);
      setIsDetailModalOpen(true);
    }
  };

  return (
    <StudentPageLayout
      title="Billing History"
      description="View your billing history and invoices"
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading billing history...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => user?.id && fetchInvoices(user.id)}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="relative flex-grow max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search invoices..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Tabs
              defaultValue="all"
              className="w-full sm:w-auto"
              value={statusFilter}
              onValueChange={setStatusFilter}
            >
              <TabsList className="grid grid-cols-4 w-full sm:w-auto">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="paid">Paid</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="refunded">Refunded</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Invoice List */}
          {filteredInvoices.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <p className="text-gray-500">No invoices found</p>
              {searchTerm || statusFilter !== "all" ? (
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => {
                    setSearchTerm("");
                    setStatusFilter("all");
                  }}
                >
                  Clear Filters
                </Button>
              ) : null}
            </div>
          ) : (
            <InvoiceList
              invoices={filteredInvoices}
              onViewDetails={handleViewInvoiceDetails}
            />
          )}
        </div>
      )}

      {/* Invoice Detail Modal */}
      <InvoiceDetailModal
        invoice={selectedInvoice}
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
      />
    </StudentPageLayout>
  );
};

export default BillingHistory;
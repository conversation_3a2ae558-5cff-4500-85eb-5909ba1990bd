import React, { useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON>,
  <PERSON><PERSON>2,
  <PERSON><PERSON>ircle2,
  <PERSON>C<PERSON>cle,
  <PERSON>ertTriangle,
  Refresh<PERSON>w,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useProcessStepsStore } from "@/store/processStepsStore";

export interface ProcessStep {
  id: string;
  label: string;
  status: "pending" | "loading" | "complete" | "error";
}

const ProcessStepsModal: React.FC = () => {
  const { isOpen, title, steps, timedOut, persistedState, close, resetState } =
    useProcessStepsStore();

  // Add logging to track modal state
  useEffect(() => {
    console.log("ProcessStepsModal: State updated", {
      isOpen,
      title,
      steps: steps.map((s) => ({ id: s.id, status: s.status })),
      timedOut,
      persistedState,
    });
  }, [isOpen, title, steps, timedOut, persistedState]);

  // Check for persisted state on component mount
  useEffect(() => {
    if (persistedState) {
      console.log("ProcessStepsModal: Using persisted process steps state");
    }

    // Clean up when component unmounts
    return () => {
      console.log(
        "ProcessStepsModal: Cleanup - checking if we should reset state"
      );

      // Get the current state directly from the store to ensure we have the latest value
      const currentState = useProcessStepsStore.getState();

      // Only reset if not persisted
      if (!currentState.persistedState) {
        console.log(
          "ProcessStepsModal: Cleanup - resetting state (not persisted)"
        );
        resetState();
      } else {
        console.log(
          "ProcessStepsModal: Cleanup - NOT resetting state (persisted for navigation)"
        );
      }
    };
  }, [resetState]); // Remove persistedState from dependencies to avoid stale closures

  const handleRefresh = () => {
    console.log("ProcessStepsModal: Forcing complete page reload");

    // Forcibly reset the modal state
    useProcessStepsStore.getState().resetState();

    // Clear ALL persisted state that might be causing issues
    sessionStorage.clear();
    localStorage.removeItem("process-steps-storage");
    localStorage.removeItem("processStepsState");

    // Force a hard reload from the server, not from cache
    window.location.href = window.location.href.split("#")[0] + "?fresh=" + Date.now();
  };

  // Don't show modal if we're not on an onboarding page and it's persisted state
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
  const isOnOnboardingPage = currentPath.includes('/onboard-');

  if (!isOpen) {
    console.log("ProcessStepsModal: Modal is not open, not rendering");
    return null;
  }

  // If modal is persisted but we're not on an onboarding page, reset it
  if (persistedState && !isOnOnboardingPage) {
    console.log("ProcessStepsModal: Persisted modal detected outside onboarding page, resetting");
    resetState();
    return null;
  }

  console.log("ProcessStepsModal: Rendering modal with steps", steps);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-lg shadow-lg max-w-md w-full mx-4"
      >
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold">{title}</h2>
          {!persistedState && (
            <button
              onClick={() => {
                console.log("ProcessStepsModal: Close button clicked");
                close();
              }}
              className="text-gray-500 hover:text-gray-700 rounded-full p-1 hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        <div className="p-4">
          {timedOut ? (
            <div className="flex flex-col items-center space-y-4">
              <AlertTriangle className="h-12 w-12 text-amber-500" />
              <p className="text-center">
                This operation is taking longer than expected.
              </p>
              <Button
                onClick={handleRefresh}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh Page
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center py-2">
                  <div className="w-8 h-8 flex-shrink-0 flex items-center justify-center">
                    {step.status === "loading" && (
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    )}
                    {step.status === "complete" && (
                      <CheckCircle2 className="h-6 w-6 text-green-500" />
                    )}
                    {step.status === "error" && (
                      <XCircle className="h-6 w-6 text-red-500" />
                    )}
                    {step.status === "pending" && (
                      <div className="h-5 w-5 rounded-full border-2 border-gray-200" />
                    )}
                  </div>
                  <span className="ml-3 text-sm font-medium">{step.label}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default ProcessStepsModal;



import { supabase } from "@/lib/supabaseClient";

export const requestPasswordReset = async (email: string) => {
  console.log("Sending password reset request for:", email);
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error("Password reset request failed:", error);
    return {
      success: false,
      error:
        error.message || "Failed to send reset email. Please try again later.",
    };
  }
};

// Make sure all other auth functions use supabase instead of apiClient
export const login = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return { success: true, user: data.user };
  } catch (error) {
    console.error("Login failed:", error);
    return {
      success: false,
      error: error.message || "Login failed. Please check your credentials.",
    };
  }
};

export const signup = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });

    if (error) throw error;
    return { success: true, user: data.user };
  } catch (error) {
    console.error("Signup failed:", error);
    return {
      success: false,
      error: error.message || "Signup failed. Please try again later.",
    };
  }
};

export const logout = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error("Logout failed:", error);
    return {
      success: false,
      error: error.message || "Logout failed. Please try again later.",
    };
  }
};

export const setUserAsAdmin = async () => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: "No authenticated user found" };
    }

    // Update the user's metadata to include admin role
    const { error } = await supabase.auth.updateUser({
      data: { role: "admin" },
    });

    if (error) throw error;

    // Also update the profiles table if you're using it
    try {
      const { error: profileError } = await supabase
        .from("profiles")
        .update({ user_type: "admin" })
        .eq("id", user.id);

      if (profileError) {
        console.error("Profile update error:", profileError);
        // Continue anyway as the auth metadata is more important
      }
    } catch (profileErr) {
      console.error("Profile table update failed:", profileErr);
      // Continue anyway, the auth metadata update is more important
    }

    return { success: true };
  } catch (error) {
    console.error("Set admin error:", error);
    return {
      success: false,
      error: error.message || "Failed to set user as admin",
    };
  }
};

// Export as default object with all auth methods
export default {
  sendPasswordResetEmail: requestPasswordReset,
  login,
  signup,
  logout,
  setUserAsAdmin,
  // Other auth methods...
};

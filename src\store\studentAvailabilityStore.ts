import { create } from "zustand";
import {
  studentAvailabilityService,
  transformStudentAvailabilitySlotForUI,
  transformStudentAvailabilitySlotForDB,
  dayNameToDayOfWeek,
  dayOfWeekToDayName,
  StudentAvailabilitySlot
} from "@/services/studentAvailabilityService";
import { useAuth } from "@/context/AuthContext";

// Types for student availability management
export interface AvailabilitySlot {
  id: string;
  day: string;
  startTime: string;
  endTime: string;
  status: "available" | "preferred" | "unavailable";
}

export interface ScheduledSession {
  id: string;
  title: string;
  date: Date;
  startTime: string;
  endTime: string;
  tutorName: string;
  tutorId: string;
  topic: string;
  subtopic?: string;
  batchId: string;
  status: "scheduled" | "completed" | "cancelled";
}

export interface AssignedTutor {
  id: string;
  name: string;
  email: string;
  photoUrl?: string;
  assignmentType: "batch" | "topic" | "subtopic" | "session";
  assignmentName: string;
}

interface StudentAvailabilityState {
  // State
  studentId: string | null;
  availabilitySlots: AvailabilitySlot[];
  scheduledSessions: ScheduledSession[];
  assignedTutors: AssignedTutor[];
  isLoading: boolean;
  error: string | null;
  pendingChanges: boolean;

  // UI state
  selectedDay: string | null;
  selectedSlot: string | null;
  isCreatingSlot: boolean;

  // Grid UI state
  startHour: string;
  endHour: string;
  isDragging: boolean;
  dragStart: { day: string; time: string } | null;
  dragEnd: { day: string; time: string } | null;
  slotStatus: "available" | "preferred" | "unavailable";

  // Actions
  setStudentId: (studentId: string) => void;
  addAvailabilitySlot: (slot: Omit<AvailabilitySlot, "id">) => Promise<string>;
  updateAvailabilitySlot: (id: string, updates: Partial<AvailabilitySlot>) => Promise<void>;
  deleteAvailabilitySlot: (id: string) => Promise<void>;
  fetchAvailabilityData: () => Promise<void>;
  saveAvailabilityData: () => Promise<boolean>;

  setSelectedDay: (day: string | null) => void;
  setSelectedSlot: (slotId: string | null) => void;
  setIsCreatingSlot: (isCreating: boolean) => void;

  // Grid UI actions
  setStartHour: (hour: string) => void;
  setEndHour: (hour: string) => void;
  setIsDragging: (isDragging: boolean) => void;
  setDragStart: (dragStart: { day: string; time: string } | null) => void;
  setDragEnd: (dragEnd: { day: string; time: string } | null) => void;
  setSlotStatus: (status: "available" | "preferred" | "unavailable") => void;
  handleCreateSlot: () => void;
  resetPendingChanges: () => void;

  // Mock data loading functions (to be replaced with actual API calls)
  fetchAvailabilityData: () => Promise<void>;
  saveAvailabilityData: () => Promise<void>;
}

// Sample data for development
const sampleAvailabilitySlots: AvailabilitySlot[] = [
  {
    id: "slot1",
    day: "Monday",
    startTime: "09:00",
    endTime: "12:00",
    status: "available",
  },
  {
    id: "slot2",
    day: "Monday",
    startTime: "14:00",
    endTime: "17:00",
    status: "preferred",
  },
  {
    id: "slot3",
    day: "Wednesday",
    startTime: "10:00",
    endTime: "14:00",
    status: "unavailable",
  },
  {
    id: "slot4",
    day: "Friday",
    startTime: "13:00",
    endTime: "18:00",
    status: "available",
  },
];

const sampleScheduledSessions: ScheduledSession[] = [
  {
    id: "session1",
    title: "Calculus - Derivatives",
    date: new Date(2023, 10, 15),
    startTime: "10:00 AM",
    endTime: "11:00 AM",
    tutorName: "Dr. Jane Smith",
    tutorId: "tutor1",
    topic: "Calculus",
    subtopic: "Derivatives",
    batchId: "batch1",
    status: "scheduled",
  },
  {
    id: "session2",
    title: "Physics - Mechanics",
    date: new Date(2023, 10, 17),
    startTime: "2:00 PM",
    endTime: "3:30 PM",
    tutorName: "Prof. John Doe",
    tutorId: "tutor2",
    topic: "Physics",
    subtopic: "Mechanics",
    batchId: "batch1",
    status: "scheduled",
  },
];

const sampleAssignedTutors: AssignedTutor[] = [
  {
    id: "tutor1",
    name: "Dr. Jane Smith",
    email: "<EMAIL>",
    photoUrl: "https://randomuser.me/api/portraits/women/1.jpg",
    assignmentType: "batch",
    assignmentName: "Mathematics Booster",
  },
  {
    id: "tutor2",
    name: "Prof. John Doe",
    email: "<EMAIL>",
    photoUrl: "https://randomuser.me/api/portraits/men/1.jpg",
    assignmentType: "topic",
    assignmentName: "Physics",
  },
  {
    id: "tutor3",
    name: "Dr. Emily Johnson",
    email: "<EMAIL>",
    photoUrl: "https://randomuser.me/api/portraits/women/2.jpg",
    assignmentType: "subtopic",
    assignmentName: "Organic Chemistry",
  },
];

// Create the store
export const useStudentAvailabilityStore = create<StudentAvailabilityState>((set, get) => ({
  // Initial state
  studentId: null,
  availabilitySlots: [],
  scheduledSessions: sampleScheduledSessions,
  assignedTutors: sampleAssignedTutors,
  isLoading: false,
  error: null,
  pendingChanges: false,

  // UI state
  selectedDay: null,
  selectedSlot: null,
  isCreatingSlot: false,

  // Grid UI state
  startHour: "08",
  endHour: "24",
  isDragging: false,
  dragStart: null,
  dragEnd: null,
  slotStatus: "available",

  // Actions
  setStudentId: (studentId: string) => {
    set({ studentId });
  },

  addAvailabilitySlot: async (slot) => {
    const { studentId } = get();
    if (!studentId) {
      throw new Error("Student ID is required to add availability slot");
    }

    console.log("Adding student availability slot with data:", slot);
    set({ isLoading: true, error: null });

    try {
      // Transform UI slot to database format
      const dbSlot = transformStudentAvailabilitySlotForDB(slot, studentId);

      // Create slot in database
      const createdSlot = await studentAvailabilityService.createAvailabilitySlot(dbSlot);

      // Transform back to UI format and add to state
      const uiSlot = transformStudentAvailabilitySlotForUI(createdSlot);

      set((state) => ({
        availabilitySlots: [...state.availabilitySlots, uiSlot],
        isLoading: false,
        pendingChanges: false
      }));

      console.log("Student slot created successfully with ID:", createdSlot.id);
      return createdSlot.id;
    } catch (error) {
      console.error("Error adding student availability slot:", error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to add availability slot"
      });
      throw error;
    }
  },

  updateAvailabilitySlot: async (id, updates) => {
    set({ isLoading: true, error: null });

    try {
      // Transform updates to database format if needed
      const dbUpdates: any = {};
      if (updates.status) dbUpdates.status = updates.status;
      if (updates.startTime) dbUpdates.start_time = updates.startTime;
      if (updates.endTime) dbUpdates.end_time = updates.endTime;
      if (updates.day) dbUpdates.day_of_week = dayNameToDayOfWeek(updates.day);

      // Update slot in database
      const updatedSlot = await studentAvailabilityService.updateAvailabilitySlot(id, dbUpdates);

      // Transform back to UI format and update state
      const uiSlot = transformStudentAvailabilitySlotForUI(updatedSlot);

      set((state) => ({
        availabilitySlots: state.availabilitySlots.map((slot) =>
          slot.id === id ? uiSlot : slot
        ),
        isLoading: false,
        pendingChanges: false
      }));
    } catch (error) {
      console.error("Error updating student availability slot:", error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to update availability slot"
      });
      throw error;
    }
  },

  deleteAvailabilitySlot: async (id) => {
    set({ isLoading: true, error: null });

    try {
      // Delete slot from database
      await studentAvailabilityService.deleteAvailabilitySlot(id);

      // Remove from state
      set((state) => ({
        availabilitySlots: state.availabilitySlots.filter((slot) => slot.id !== id),
        isLoading: false,
        pendingChanges: false
      }));
    } catch (error) {
      console.error("Error deleting student availability slot:", error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : "Failed to delete availability slot"
      });
      throw error;
    }
  },

  setSelectedDay: (day) => {
    set({ selectedDay: day });
  },

  setSelectedSlot: (slotId) => {
    set({ selectedSlot: slotId });
  },

  setIsCreatingSlot: (isCreating) => {
    set({ isCreatingSlot: isCreating });
  },

  // Grid UI actions
  setStartHour: (hour) => {
    set({ startHour: hour });
  },

  setEndHour: (hour) => {
    set({ endHour: hour });
  },

  setIsDragging: (isDragging) => {
    set({ isDragging });
  },

  setDragStart: (dragStart) => {
    set({ dragStart });
  },

  setDragEnd: (dragEnd) => {
    set({ dragEnd });
  },

  setSlotStatus: (status) => {
    set({ slotStatus: status });
  },

  handleCreateSlot: async () => {
    const { dragStart, dragEnd, slotStatus } = get();

    if (!dragStart || !dragEnd) return;

    // Ensure start time is before end time
    const startTime = dragStart.time <= dragEnd.time ? dragStart.time : dragEnd.time;
    const endTime = dragStart.time <= dragEnd.time ? dragEnd.time : dragStart.time;
    const day = dragStart.day;

    // Calculate the end time (30 minutes after the selected end time)
    // This ensures that dragging over N cells creates a slot covering exactly those N cells
    const [hours, minutes] = endTime.split(':').map(Number);
    let endHours = hours;
    let endMinutes = minutes + 30;

    if (endMinutes >= 60) {
      endHours += 1;
      endMinutes -= 60;
    }

    const finalEndTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;

    try {
      // Add the new slot
      await get().addAvailabilitySlot({
        day,
        startTime,
        endTime: finalEndTime,
        status: slotStatus,
      });

      // Reset drag state
      set({
        isDragging: false,
        dragStart: null,
        dragEnd: null,
      });
    } catch (error) {
      console.error("Error creating slot:", error);
      // Reset drag state even on error
      set({
        isDragging: false,
        dragStart: null,
        dragEnd: null,
      });
    }
  },

  resetPendingChanges: () => {
    set({ pendingChanges: false });
  },

  // Data loading functions
  fetchAvailabilityData: async () => {
    const { studentId } = get();
    if (!studentId) {
      set({ error: "Student ID is required to fetch availability data" });
      return;
    }

    set({ isLoading: true, error: null });

    try {
      // Fetch availability slots from database
      const dbSlots = await studentAvailabilityService.fetchAvailabilitySlots(studentId);

      // Transform to UI format
      const uiSlots = dbSlots.map(transformStudentAvailabilitySlotForUI);

      set({
        availabilitySlots: uiSlots,
        scheduledSessions: sampleScheduledSessions, // Keep sample data for now
        assignedTutors: sampleAssignedTutors, // Keep sample data for now
        isLoading: false,
        pendingChanges: false,
      });
    } catch (error) {
      console.error("Error fetching student availability data:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load availability data. Please try again.",
        isLoading: false,
      });
    }
  },

  saveAvailabilityData: async () => {
    const { studentId, availabilitySlots } = get();
    if (!studentId) {
      throw new Error("Student ID is required to save availability data");
    }

    set({ isLoading: true, error: null });

    try {
      // Transform UI slots to database format
      const dbSlots = availabilitySlots.map(slot => ({
        id: slot.id,
        student_id: studentId,
        day_of_week: dayNameToDayOfWeek(slot.day),
        start_time: slot.startTime,
        end_time: slot.endTime,
        status: slot.status,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      // Bulk update all slots
      await studentAvailabilityService.bulkUpdateAvailabilitySlots(dbSlots);

      set({
        isLoading: false,
        pendingChanges: false
      });
      return true;
    } catch (error) {
      console.error("Error saving student availability data:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to save availability data. Please try again.",
        isLoading: false,
      });
      throw error;
    }
  },
}));

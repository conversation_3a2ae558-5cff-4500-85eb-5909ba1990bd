import { create } from "zustand";
import { supabase } from "@/lib/supabaseClient";
import { User, Session } from "@supabase/supabase-js";
import { UserType, UserStatus, IsOnboarded } from "@/constants/auth";
import { handleAuthError } from "@/services/errorHandler";

interface AuthState {
  // State
  user: User | null;
  session: Session | null;
  userType: string | null; // Keep as string for compatibility
  userStatus: UserStatus;
  isOnboarded: IsOnboarded;
  isInitialized: boolean;
  isLoading: boolean;
  authError: string | null;
  loginSuccess: boolean;
  formError: string | null;
  timeoutId: NodeJS.Timeout | null;

  // Email confirmation states
  emailConfirmed: boolean;
  confirmationMessage: string;
  confirmationError: string;

  // Actions
  initializeAuth: () => Promise<void>;
  setAuthError: (error: string | null) => void;
  retryAuth: () => Promise<void>;
  signIn: (
    email: string,
    password: string
  ) => Promise<{ error?: any; data?: any }>;
  signOut: () => Promise<{ error?: any }>;
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setUserData: (userData: {
    userType: UserType | null;
    userStatus: UserStatus;
    isOnboarded: boolean;
  }) => void;
  setIsInitialized: (initialized: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  setLoginSuccess: (success: boolean) => void;
  resetAuthState: () => void;
  fetchUserData: (userId: string, retryCount?: number) => Promise<any>;
  isUserAdmin: () => boolean;
  setFormError: (error: string | null) => void;
  clearErrors: () => void;

  // Email confirmation actions
  setEmailConfirmed: (value: boolean) => void;
  setConfirmationMessage: (message: string) => void;
  setConfirmationError: (error: string) => void;
  clearEmailConfirmation: () => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  user: null,
  session: null,
  userType: null,
  userStatus: null,
  isOnboarded: null,
  isInitialized: false,
  isLoading: false,
  authError: null,
  loginSuccess: false,
  formError: null,
  timeoutId: null,

  // Email confirmation states
  emailConfirmed: false,
  confirmationMessage: "",
  confirmationError: "",

  // Actions
  setAuthError: (error) => set({ authError: error }),

  // Add missing methods
  setUser: (user) => set({ user }),
  setSession: (session) => set({ session }),
  setUserData: (userData) => {
    console.log("Setting user data in authStore:", userData);
    set({
      userType: userData.userType,
      userStatus: userData.userStatus,
      isOnboarded: userData.isOnboarded,
    });
  },
  setIsInitialized: (initialized) => set({ isInitialized: initialized }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setLoginSuccess: (success) => set({ loginSuccess: success }),
  setFormError: (error) => set({ formError: error }),
  clearErrors: () => set({ authError: null, formError: null }),

  // Email confirmation actions
  setEmailConfirmed: (value) => set({ emailConfirmed: value }),
  setConfirmationMessage: (message) => set({ confirmationMessage: message }),
  setConfirmationError: (error) => set({ confirmationError: error }),
  clearEmailConfirmation: () =>
    set({
      emailConfirmed: false,
      confirmationMessage: "",
      confirmationError: "",
    }),

  resetAuthState: () =>
    set({
      user: null,
      session: null,
      userType: null,
      userStatus: null,
      isOnboarded: null,
      loginSuccess: false,
      authError: null,
      formError: null,
      // Don't reset isInitialized to prevent loading spinner
    }),

  initializeAuth: async () => {
    set({ isLoading: true });

    try {
      console.log("Initializing auth from store");

      // Get current session
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session?.user) {
        console.log(
          "Found existing session during initialization:",
          session.user.id
        );
        set({ user: session.user, session });

        // Fetch user data
        await get().fetchUserData(session.user.id);
      } else {
        console.log("No session found during initialization");
        // Reset auth state to ensure clean state
        get().resetAuthState();
      }

      // Set up auth state change listener
      supabase.auth.onAuthStateChange(async (event, session) => {
        console.log(`Auth state changed: ${event}`, {
          sessionExists: !!session,
          userId: session?.user?.id,
        });

        if (event === "SIGNED_IN" && session?.user) {
          set({ user: session.user, session });

          // Fetch user data
          await get().fetchUserData(session.user.id);
        } else if (event === "SIGNED_OUT") {
          get().resetAuthState();
        } else if (event === "TOKEN_REFRESHED" && session) {
          // Just update the session, don't reset everything
          set({ session });
        }
      });

      // Mark as initialized
      set({ isInitialized: true, isLoading: false });
    } catch (error) {
      console.error("Error initializing auth:", error);
      const errorMessage = handleAuthError(error);
      set({
        authError: errorMessage || "Failed to initialize authentication",
        isInitialized: true, // Still mark as initialized to prevent loading spinner
        isLoading: false,
      });
    }
  },

  fetchUserData: async (userId, retryCount = 0) => {
    try {
      console.log(
        `Fetching user data for ID: ${userId} (attempt ${retryCount + 1})`
      );

      const { data, error } = await supabase.rpc("get_user_data", {
        user_id: userId,
      });

      if (error) {
        console.error("Error fetching user data:", error);

        if (retryCount < 2) {
          console.log(`Retrying fetchUserData (${retryCount + 1}/3)...`);
          return get().fetchUserData(userId, retryCount + 1);
        }

        set({ authError: "Failed to load user data" });
        return null;
      }

      if (!data || !data.userType) {
        console.error("User data incomplete:", data);

        if (retryCount < 2) {
          console.log(
            `Retrying fetchUserData due to incomplete data (${
              retryCount + 1
            }/3)...`
          );
          return get().fetchUserData(userId, retryCount + 1);
        }

        set({ authError: "User profile data is incomplete" });
        return null;
      }

      console.log("User data fetched successfully:", data);
      console.log("Onboarding status from DB:", data.onboarding_completed);

      // Update all user data at once to prevent race conditions
      get().setUserData({
        userType: data.userType,
        userStatus: data.userStatus || "new",
        isOnboarded: data.onboarding_completed || false,
      });

      return data;
    } catch (err) {
      console.error("Error in fetchUserData:", err);

      if (retryCount < 2) {
        console.log(
          `Retrying fetchUserData after error (${retryCount + 1}/3)...`
        );
        return get().fetchUserData(userId, retryCount + 1);
      }

      set({ authError: "Unexpected error loading user data" });
      return null;
    }
  },

  signIn: async (email, password) => {
    set({ isLoading: true, authError: null });

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        set({ authError: error.message, isLoading: false });
        return { error };
      }

      set({
        user: data.user,
        session: data.session,
        loginSuccess: true,
        isLoading: false,
      });

      // Fetch user data
      if (data.user) {
        await get().fetchUserData(data.user.id);
      }

      return { data };
    } catch (error: any) {
      set({
        authError: error.message || "Login failed",
        isLoading: false,
      });
      return { error };
    }
  },

  signOut: async () => {
    set({ isLoading: true });

    try {
      // Call Supabase signOut
      const { error } = await supabase.auth.signOut();

      // Reset state regardless of error
      get().resetAuthState();

      // Clear all Supabase related items from localStorage
      for (const key in localStorage) {
        if (key.startsWith("supabase.auth.")) {
          localStorage.removeItem(key);
        }
      }

      return { error };
    } catch (error) {
      console.error("Sign out error:", error);

      // Reset state even on error
      get().resetAuthState();

      return { error };
    } finally {
      set({ isLoading: false });
    }
  },

  retryAuth: async () => {
    const { user } = get();
    set({ authError: null, isLoading: true });

    if (user) {
      try {
        console.log("Retrying auth for user:", user.id);

        // First, check if the session is still valid
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session) {
          console.log("No valid session found during retry");
          get().resetAuthState();
          set({ isLoading: false });
          return;
        }

        // Update session and user
        set({ session });

        // Force refresh user data
        await get().fetchUserData(user.id);
        set({ isLoading: false });
      } catch (error) {
        console.error("Retry failed:", error);
        const errorMessage = handleAuthError(error);
        set({
          authError:
            errorMessage ||
            "Unable to refresh your session. Please try logging in again.",
          isLoading: false,
        });
      }
    } else {
      console.log("No user to retry auth for");
      set({ isLoading: false });
    }
  },

  isUserAdmin: () => {
    const { user, userType } = get();
    return userType === "admin" || user?.app_metadata?.role === "admin";
  },
}));



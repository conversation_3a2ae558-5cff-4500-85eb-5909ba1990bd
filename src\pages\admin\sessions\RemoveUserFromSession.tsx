import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import AdminSidebar from "@/components/admin/Sidebar";
import UserProfileMenu from "@/components/UserProfileMenu";
import { useProfileData } from "@/hooks/useProfileData";
import { UserMinus, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";

const RemoveUserFromSession = () => {
  const navigate = useNavigate();
  const profileData = useProfileData();
  const [userTo<PERSON><PERSON><PERSON>, setUserTo<PERSON>emove] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  // Mock session data
  const session = {
    id: "123",
    subject: "Mathematics",
    date: "2023-12-15",
    time: "14:00",
    tutor: "<PERSON>",
  };

  // Mock participants data
  const participants = [
    {
      id: "1",
      name: "Alice Johnson",
      email: "<EMAIL>",
      role: "Student",
    },
    {
      id: "2",
      name: "Bob Williams",
      email: "<EMAIL>",
      role: "Student",
    },
    {
      id: "3",
      name: "Carol Davis",
      email: "<EMAIL>",
      role: "Student",
    },
  ];

  const openConfirmDialog = (user) => {
    setUserToRemove(user);
    setConfirmDialogOpen(true);
  };

  const handleRemoveUser = () => {
    console.log("Removing user from session:", userToRemove);
    setConfirmDialogOpen(false);
    // In a real app, you would make an API call here
    // Then navigate back or refresh the list
    navigate("/admin/sessions");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Remove User from Session</h1>
            <UserProfileMenu
              isAdmin={true}
              isAdminPage={true}
            />
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Session Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Subject</p>
                  <p>{session.subject}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Tutor</p>
                  <p>{session.tutor}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Date</p>
                  <p>{session.date}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Time</p>
                  <p>{session.time}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UserMinus className="mr-2 h-5 w-5 text-rfpurple-600" />
                Current Participants
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="w-24">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {participants.map((participant) => (
                    <TableRow key={participant.id}>
                      <TableCell>{participant.name}</TableCell>
                      <TableCell>{participant.email}</TableCell>
                      <TableCell>{participant.role}</TableCell>
                      <TableCell>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => openConfirmDialog(participant)}
                        >
                          Remove
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                onClick={() => navigate("/admin/sessions")}
                className="ml-auto"
              >
                Back to Sessions
              </Button>
            </CardFooter>
          </Card>

          <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  <AlertCircle className="mr-2 h-5 w-5 text-red-500" />
                  Confirm Removal
                </DialogTitle>
                <DialogDescription>
                  Are you sure you want to remove {userToRemove?.name} from this
                  session? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setConfirmDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button variant="destructive" onClick={handleRemoveUser}>
                  Remove User
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default RemoveUserFromSession;

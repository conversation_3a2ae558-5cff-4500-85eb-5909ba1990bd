-- =====================================================
-- TIMEZONE NOTIFICATION SYSTEM
-- =====================================================
-- This script creates a comprehensive system for managing timezone missing notifications
-- that are automatically created when users complete onboarding without a timezone
-- and automatically removed when they set their timezone.

-- =====================================================
-- 1. UPDATE NOTIFICATIONS TABLE SCHEMA
-- =====================================================

-- Add a new field to track system-managed notifications that users cannot delete
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS is_system_managed BOOLEAN DEFAULT false;
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS notification_key TEXT;

-- Create index for better performance on system-managed notifications
CREATE INDEX IF NOT EXISTS idx_notifications_system_managed ON notifications(user_id, is_system_managed, notification_key);

-- =====================================================
-- 2. TIMEZONE NOTIFICATION FUNCTIONS
-- =====================================================

-- Function to check if a timezone notification already exists for a user
CREATE OR REPLACE FUNCTION has_timezone_notification(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM notifications 
        WHERE user_id = p_user_id 
        AND notification_key = 'timezone_missing'
        AND is_system_managed = true
    );
END;
$$;

-- Function to create timezone missing notification
CREATE OR REPLACE FUNCTION create_timezone_notification(
    p_user_id UUID,
    p_user_type TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    notification_id UUID;
    notification_message TEXT;
BEGIN
    -- Check if notification already exists
    IF has_timezone_notification(p_user_id) THEN
        RAISE NOTICE 'Timezone notification already exists for user %', p_user_id;
        RETURN NULL;
    END IF;
    
    -- Create the message with internal link based on user type
    notification_message := 'Please set your timezone in account preferences to ensure accurate session scheduling and notifications. <a href="/' || p_user_type || '/account-preferences" style="color: #7c3aed; text-decoration: underline; font-weight: 500;">Click here to set your timezone</a>.';
    
    -- Insert the notification
    INSERT INTO notifications (
        user_id, 
        title, 
        message, 
        type, 
        is_system_managed,
        notification_key,
        is_read
    ) VALUES (
        p_user_id,
        'Set Your Timezone',
        notification_message,
        'system',
        true,
        'timezone_missing',
        false
    ) RETURNING id INTO notification_id;
    
    RAISE NOTICE 'Created timezone notification % for user %', notification_id, p_user_id;
    RETURN notification_id;
END;
$$;

-- Function to remove timezone notification when timezone is set
CREATE OR REPLACE FUNCTION remove_timezone_notification(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM notifications 
    WHERE user_id = p_user_id 
    AND notification_key = 'timezone_missing'
    AND is_system_managed = true;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count > 0 THEN
        RAISE NOTICE 'Removed timezone notification for user %', p_user_id;
        RETURN true;
    ELSE
        RAISE NOTICE 'No timezone notification found for user %', p_user_id;
        RETURN false;
    END IF;
END;
$$;

-- =====================================================
-- 3. TRIGGER FUNCTIONS
-- =====================================================

-- Function to handle timezone notifications when profile is created/updated
CREATE OR REPLACE FUNCTION handle_timezone_notification()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Handle INSERT (new profile created)
    IF TG_OP = 'INSERT' THEN
        -- If timezone is null or empty, create notification
        IF NEW.timezone IS NULL OR NEW.timezone = '' THEN
            PERFORM create_timezone_notification(NEW.id, NEW.user_type);
        END IF;
        RETURN NEW;
    END IF;
    
    -- Handle UPDATE (profile updated)
    IF TG_OP = 'UPDATE' THEN
        -- If timezone was null/empty and now has a value, remove notification
        IF (OLD.timezone IS NULL OR OLD.timezone = '') AND 
           (NEW.timezone IS NOT NULL AND NEW.timezone != '') THEN
            PERFORM remove_timezone_notification(NEW.id);
        -- If timezone was set and now is null/empty, create notification
        ELSIF (OLD.timezone IS NOT NULL AND OLD.timezone != '') AND 
              (NEW.timezone IS NULL OR NEW.timezone = '') THEN
            PERFORM create_timezone_notification(NEW.id, NEW.user_type);
        END IF;
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$;

-- =====================================================
-- 4. CREATE TRIGGERS
-- =====================================================

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS timezone_notification_trigger ON profiles;

-- Create trigger for timezone notifications
CREATE TRIGGER timezone_notification_trigger
    AFTER INSERT OR UPDATE OF timezone
    ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION handle_timezone_notification();

-- =====================================================
-- 5. UPDATE RLS POLICIES
-- =====================================================

-- Update the delete policy to prevent users from deleting system-managed notifications
DROP POLICY IF EXISTS "Users can delete their own notifications" ON notifications;

CREATE POLICY "Users can delete their own notifications" ON notifications
    FOR DELETE USING (
        auth.uid() = user_id 
        AND is_system_managed = false
    );

-- =====================================================
-- 6. UTILITY FUNCTIONS
-- =====================================================

-- Function to manually create timezone notifications for existing users without timezone
CREATE OR REPLACE FUNCTION create_missing_timezone_notifications()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_record RECORD;
    created_count INTEGER := 0;
BEGIN
    -- Find all users without timezone who don't have a timezone notification
    FOR user_record IN 
        SELECT id, user_type 
        FROM profiles 
        WHERE (timezone IS NULL OR timezone = '')
        AND user_type IN ('student', 'tutor')
        AND NOT has_timezone_notification(id)
    LOOP
        PERFORM create_timezone_notification(user_record.id, user_record.user_type);
        created_count := created_count + 1;
    END LOOP;
    
    RAISE NOTICE 'Created % timezone notifications for existing users', created_count;
    RETURN created_count;
END;
$$;

-- Function to clean up orphaned timezone notifications (users who now have timezone set)
CREATE OR REPLACE FUNCTION cleanup_timezone_notifications()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    DELETE FROM notifications 
    WHERE notification_key = 'timezone_missing'
    AND is_system_managed = true
    AND user_id IN (
        SELECT id FROM profiles 
        WHERE timezone IS NOT NULL AND timezone != ''
    );
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    RAISE NOTICE 'Cleaned up % orphaned timezone notifications', cleaned_count;
    RETURN cleaned_count;
END;
$$;

-- =====================================================
-- 7. INITIAL SETUP
-- =====================================================

-- Create timezone notifications for existing users who need them
SELECT create_missing_timezone_notifications();

-- Clean up any orphaned notifications
SELECT cleanup_timezone_notifications();

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================

-- Query to check timezone notifications
-- SELECT 
--     p.id,
--     p.first_name,
--     p.last_name,
--     p.user_type,
--     p.timezone,
--     n.title,
--     n.is_system_managed,
--     n.notification_key,
--     n.created_at
-- FROM profiles p
-- LEFT JOIN notifications n ON p.id = n.user_id AND n.notification_key = 'timezone_missing'
-- WHERE p.user_type IN ('student', 'tutor')
-- ORDER BY p.created_at DESC;

import { 
  PaymentProvider, 
  PaymentResult, 
  CreateOrderParams, 
  ProcessPaymentParams, 
  VerifyPaymentParams,
  RefundPaymentParams,
  PaymentStatus,
  PaymentMethod 
} from '../types';

// Razorpay types
interface RazorpayConfig {
  keyId: string;
  enabled: boolean;
}



interface RazorpayPayment {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

// Declare Razorpay global (will be loaded via script)
declare global {
  interface Window {
    Razorpay: any;
  }
}

export class RazorpayProvider implements PaymentProvider {
  name = 'razorpay';
  displayName = 'Razorpay';
  supportedCurrencies = ['usd', 'inr'];
  supportedMethods: PaymentMethod[] = ['card', 'netbanking', 'wallet', 'upi', 'emi'];

  private config: RazorpayConfig;
  private isScriptLoaded = false;

  constructor(config: RazorpayConfig) {
    this.config = config;
    this.loadRazorpayScript();
  }

  /**
   * Load Razorpay script dynamically
   */
  private async loadRazorpayScript(): Promise<void> {
    if (this.isScriptLoaded || typeof window === 'undefined') {
      return;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        this.isScriptLoaded = true;
        resolve();
      };
      script.onerror = () => {
        reject(new Error('Failed to load Razorpay script'));
      };
      document.head.appendChild(script);
    });
  }

  /**
   * Create Razorpay order
   */
  async createOrder(params: CreateOrderParams): Promise<PaymentResult> {
    try {
      // Convert amount to paise (Razorpay expects amount in smallest currency unit)
      const amountInPaise = Math.round(params.amount * 100);

      const orderData = {
        amount: amountInPaise,
        currency: params.currency.toUpperCase(),
        receipt: `receipt_${Date.now()}`,
        notes: {
          description: params.description || '',
          customer_email: params.customerEmail || '',
          ...params.metadata
        }
      };

      // Call Supabase Edge Function to create Razorpay order
      const response = await this.callBackendAPI('/functions/v1/razorpay-create-order', orderData);

      if (!response.success) {
        throw new Error(response.error || 'Failed to create order');
      }

      return {
        success: true,
        paymentId: '', // Will be set after payment
        orderId: response.order.id,
        status: 'pending' as PaymentStatus,
        amount: params.amount,
        currency: params.currency,
        metadata: {
          razorpay_order_id: response.order.id,
          amount_in_paise: amountInPaise
        }
      };
    } catch (error) {
      return {
        success: false,
        paymentId: '',
        status: 'failed' as PaymentStatus,
        amount: params.amount,
        currency: params.currency,
        error: {
          code: 'ORDER_CREATION_FAILED',
          message: error instanceof Error ? error.message : 'Failed to create order',
          retryable: true,
          provider: this.name
        }
      };
    }
  }

  /**
   * Process payment using Razorpay checkout
   */
  async processPayment(params: ProcessPaymentParams): Promise<PaymentResult> {
    try {
      await this.loadRazorpayScript();

      if (!window.Razorpay) {
        throw new Error('Razorpay script not loaded');
      }

      return new Promise((resolve) => {
        const options = {
          key: this.config.keyId,
          order_id: params.orderId,
          name: 'RF Learn',
          description: 'Subscription Purchase',
          image: '/logo.png', // Your logo
          handler: async (response: RazorpayPayment) => {
            // Verify payment on backend
            const verificationResult = await this.verifyPayment({
              paymentId: response.razorpay_payment_id,
              orderId: response.razorpay_order_id,
              signature: response.razorpay_signature,
              metadata: params.metadata
            });
            resolve(verificationResult);
          },
          modal: {
            ondismiss: () => {
              resolve({
                success: false,
                paymentId: '',
                orderId: params.orderId,
                status: 'canceled' as PaymentStatus,
                amount: 0,
                currency: 'usd',
                error: {
                  code: 'PAYMENT_CANCELED',
                  message: 'Payment was canceled by user',
                  retryable: true,
                  provider: this.name
                }
              });
            }
          },
          theme: {
            color: '#3B82F6'
          }
        };

        const rzp = new window.Razorpay(options);
        rzp.open();
      });
    } catch (error) {
      return {
        success: false,
        paymentId: '',
        orderId: params.orderId,
        status: 'failed' as PaymentStatus,
        amount: 0,
        currency: 'usd',
        error: {
          code: 'PAYMENT_PROCESSING_FAILED',
          message: error instanceof Error ? error.message : 'Failed to process payment',
          retryable: true,
          provider: this.name
        }
      };
    }
  }

  /**
   * Verify payment signature
   */
  async verifyPayment(params: VerifyPaymentParams): Promise<PaymentResult> {
    try {
      const verificationData = {
        razorpay_payment_id: params.paymentId,
        razorpay_order_id: params.orderId,
        razorpay_signature: params.signature,
        metadata: params.metadata
      };

      // Call Supabase Edge Function to verify payment signature
      const response = await this.callBackendAPI('/functions/v1/razorpay-verify-payment', verificationData);

      if (!response.success) {
        throw new Error(response.error || 'Payment verification failed');
      }

      return {
        success: true,
        paymentId: params.paymentId,
        orderId: params.orderId,
        status: 'succeeded' as PaymentStatus,
        amount: response.amount || 0,
        currency: response.currency || 'usd',
        metadata: {
          razorpay_payment_id: params.paymentId,
          razorpay_order_id: params.orderId,
          verified: true
        }
      };
    } catch (error) {
      return {
        success: false,
        paymentId: params.paymentId,
        orderId: params.orderId,
        status: 'failed' as PaymentStatus,
        amount: 0,
        currency: 'usd',
        error: {
          code: 'PAYMENT_VERIFICATION_FAILED',
          message: error instanceof Error ? error.message : 'Payment verification failed',
          retryable: false,
          provider: this.name
        }
      };
    }
  }

  /**
   * Refund payment (optional)
   */
  async refundPayment(params: RefundPaymentParams): Promise<PaymentResult> {
    try {
      const refundData = {
        payment_id: params.paymentId,
        amount: params.amount ? Math.round(params.amount * 100) : undefined, // Convert to paise
        notes: {
          reason: params.reason || 'Refund requested'
        }
      };

      const response = await this.callBackendAPI('/functions/v1/razorpay-refund', refundData);

      if (!response.success) {
        throw new Error(response.error || 'Refund failed');
      }

      return {
        success: true,
        paymentId: params.paymentId,
        status: 'succeeded' as PaymentStatus,
        amount: response.amount || 0,
        currency: 'usd',
        metadata: {
          refund_id: response.refund_id,
          refund_status: response.status
        }
      };
    } catch (error) {
      return {
        success: false,
        paymentId: params.paymentId,
        status: 'failed' as PaymentStatus,
        amount: 0,
        currency: 'usd',
        error: {
          code: 'REFUND_FAILED',
          message: error instanceof Error ? error.message : 'Refund failed',
          retryable: true,
          provider: this.name
        }
      };
    }
  }

  /**
   * Call Supabase Edge Function API
   */
  private async callBackendAPI(endpoint: string, data: any): Promise<any> {
    // Import supabase client
    const { supabase } = await import('@/lib/supabaseClient');

    // Get current session for authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('No active session');
    }

    // Get Supabase URL from environment
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    if (!supabaseUrl) {
      throw new Error('Supabase URL not configured');
    }

    // Map endpoint to Supabase Edge Function
    const functionName = endpoint.split('/').pop(); // Extract function name from endpoint
    const functionUrl = `${supabaseUrl}/functions/v1/${functionName}`;

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API call failed: ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }
}

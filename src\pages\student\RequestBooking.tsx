import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { create } from "zustand";
import { getAssignedTutorForSession, getAvailableTutorsExceptAssigned, Tutor } from "@/services/tutorAssignmentService";
import TutorAvailabilityViewer from "@/components/student/scheduling/TutorAvailabilityViewer";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/TextArea";
import { Calendar } from "@/components/ui/Calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/Dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/Form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";
import { Label } from "@/components/ui/Label";
import { Badge } from "@/components/ui/Badge";
import { useToast } from "@/components/ui/UseToast";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  BookOpen,
  Calendar as CalendarIcon,
  Clock,
  FileText,
  Info,
  User,
  Users,
  AlertCircle,
  RefreshCw,
  X,
} from "lucide-react";

// Define types for our data
// Using Tutor interface from tutorAssignmentService

interface Batch {
  id: string;
  name: string;
  packageType: string;
  packageName: string;
}

interface Topic {
  id: string;
  name: string;
  subtopics: Subtopic[];
  batchId: string;
}

interface Subtopic {
  id: string;
  name: string;
  topicId: string;
}

// Create a Zustand store for the booking page
interface BookingState {
  batches: Batch[];
  topics: Topic[];
  tutors: Tutor[];
  selectedBatch: string | null;
  selectedTopic: string | null;
  selectedSubtopic: string | null;
  assignedTutor: Tutor | null;
  selectedTutor: string | null;
  isLoading: boolean;
  error: string | null;
  selectionComplete: boolean;
  showChangeTutor: boolean;

  // Actions
  setSelectedBatch: (batchId: string | null) => void;
  setSelectedTopic: (topicId: string | null) => void;
  setSelectedSubtopic: (subtopicId: string | null) => void;
  setSelectedTutor: (tutorId: string | null) => void;
  setSelectionComplete: (complete: boolean) => void;
  setShowChangeTutor: (show: boolean) => void;
  fetchBatchesTopicsAndSubtopics: () => Promise<void>;
  getAssignedTutor: (topicId: string, subtopicId: string) => Promise<void>;
  loadAlternativeTutors: () => Promise<void>;
}

// Sample data for development
const sampleBatches: Batch[] = [
  {
    id: "batch1",
    name: "Fall 2023 - Machine Learning",
    packageType: "complete_booster",
    packageName: "Machine Learning Booster",
  },
  {
    id: "batch2",
    name: "Summer 2023 - Data Science",
    packageType: "preparation",
    packageName: "Data Science Prep",
  },
  {
    id: "batch3",
    name: "Winter 2023 - AI Fundamentals",
    packageType: "customized",
    packageName: "AI Fundamentals Custom",
  },
];

const sampleTopics: Topic[] = [
  {
    id: "topic1",
    name: "Neural Networks",
    batchId: "batch1",
    subtopics: [
      { id: "subtopic1", name: "Convolutional Neural Networks", topicId: "topic1" },
      { id: "subtopic2", name: "Recurrent Neural Networks", topicId: "topic1" },
      { id: "subtopic3", name: "Transformers", topicId: "topic1" },
    ],
  },
  {
    id: "topic2",
    name: "Reinforcement Learning",
    batchId: "batch1",
    subtopics: [
      { id: "subtopic4", name: "Q-Learning", topicId: "topic2" },
      { id: "subtopic5", name: "Policy Gradients", topicId: "topic2" },
    ],
  },
  {
    id: "topic3",
    name: "Data Visualization",
    batchId: "batch2",
    subtopics: [
      { id: "subtopic6", name: "Matplotlib & Seaborn", topicId: "topic3" },
      { id: "subtopic7", name: "Interactive Visualizations", topicId: "topic3" },
    ],
  },
  {
    id: "topic4",
    name: "AI Ethics",
    batchId: "batch3",
    subtopics: [
      { id: "subtopic8", name: "Bias in AI", topicId: "topic4" },
      { id: "subtopic9", name: "Responsible AI Development", topicId: "topic4" },
    ],
  },
];

// Using tutors from tutorAssignmentService

// Create the store
export const useBookingStore = create<BookingState>((set, get) => ({
  batches: [],
  topics: [],
  tutors: [],
  selectedBatch: null,
  selectedTopic: null,
  selectedSubtopic: null,
  assignedTutor: null,
  selectedTutor: null,
  isLoading: false,
  error: null,
  selectionComplete: false,
  showChangeTutor: false,

  setSelectedBatch: (batchId) => {
    set({
      selectedBatch: batchId,
      selectedTopic: null,
      selectedSubtopic: null,
      assignedTutor: null,
      selectedTutor: null,
      selectionComplete: false
    });
  },

  setSelectedTopic: (topicId) => {
    set({
      selectedTopic: topicId,
      selectedSubtopic: null,
      assignedTutor: null,
      selectedTutor: null,
      selectionComplete: false
    });
  },

  setSelectedSubtopic: (subtopicId) => {
    set({
      selectedSubtopic: subtopicId,
      assignedTutor: null,
      selectedTutor: null,
      selectionComplete: false
    });

    if (subtopicId && get().selectedTopic) {
      get().getAssignedTutor(get().selectedTopic!, subtopicId);
    }
  },

  setSelectedTutor: (tutorId) => {
    set({
      selectedTutor: tutorId,
      selectionComplete: Boolean(tutorId && get().selectedBatch && get().selectedTopic && get().selectedSubtopic)
    });
  },

  setSelectionComplete: (complete) => {
    set({ selectionComplete: complete });
  },

  setShowChangeTutor: (show) => {
    set({ showChangeTutor: show });
  },

  fetchBatchesTopicsAndSubtopics: async () => {
    set({ isLoading: true, error: null });

    try {
      // In a real app, you would fetch data from an API here
      // For now, we'll use the sample data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay

      set({
        batches: sampleBatches,
        topics: sampleTopics,
        isLoading: false
      });
    } catch (error) {
      set({
        error: "Failed to load batches, topics and subtopics. Please try again.",
        isLoading: false
      });
    }
  },

  getAssignedTutor: async (topicId, subtopicId) => {
    set({ isLoading: true, error: null });

    try {
      // Get the selected batch
      const selectedBatchId = get().selectedBatch;

      if (!selectedBatchId) {
        throw new Error("No batch selected");
      }

      // Call the service function to get the assigned tutor
      const assignedTutor = await getAssignedTutorForSession(selectedBatchId, topicId, subtopicId);

      // If we have an assigned tutor, set it in the state
      if (assignedTutor) {
        set({
          tutors: [assignedTutor], // Initially only show the assigned tutor
          assignedTutor: assignedTutor,
          selectedTutor: assignedTutor.id,
          isLoading: false,
          showChangeTutor: false, // Reset the change tutor flag
          selectionComplete: Boolean(assignedTutor && get().selectedBatch && get().selectedTopic && get().selectedSubtopic)
        });
      } else {
        set({
          assignedTutor: null,
          selectedTutor: null,
          isLoading: false
        });
      }
    } catch (error) {
      console.error("Error getting assigned tutor:", error);
      set({
        error: "Failed to get assigned tutor. Please try again.",
        isLoading: false
      });
    }
  },

  loadAlternativeTutors: async () => {
    set({ isLoading: true });

    try {
      const { selectedBatch, selectedTopic, selectedSubtopic, assignedTutor } = get();

      if (!selectedBatch || !selectedTopic || !selectedSubtopic || !assignedTutor) {
        throw new Error("Missing required selection data");
      }

      // Get all available tutors except the assigned one
      const alternativeTutors = await getAvailableTutorsExceptAssigned(
        assignedTutor.id,
        selectedBatch,
        selectedTopic,
        selectedSubtopic
      );

      // Combine the assigned tutor with alternatives
      set({
        tutors: [assignedTutor, ...alternativeTutors],
        isLoading: false,
        showChangeTutor: true
      });
    } catch (error) {
      console.error("Error loading alternative tutors:", error);
      set({
        error: "Failed to load alternative tutors. Please try again.",
        isLoading: false
      });
    }
  }
}));

// Form schema
const formSchema = z.object({
  batch: z.string({
    required_error: "Please select a batch",
  }),
  topic: z.string({
    required_error: "Please select a topic",
  }),
  subtopic: z.string({
    required_error: "Please select a subtopic",
  }),
  tutor: z.string({
    required_error: "Please select a tutor",
  }),
  tutorChangeReason: z.string().optional(),
  notes: z.string().optional(),
});

const RequestBooking: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [showTutorChangeDialog, setShowTutorChangeDialog] = useState(false);
  const [tutorChangeReason, setTutorChangeReason] = useState("");

  // Get state from Zustand store
  const {
    batches,
    topics,
    tutors,
    selectedBatch,
    selectedTopic,
    selectedSubtopic,
    assignedTutor,
    selectedTutor,
    isLoading,
    error,
    selectionComplete,
    showChangeTutor,
    setSelectedBatch,
    setSelectedTopic,
    setSelectedSubtopic,
    setSelectedTutor,
    setSelectionComplete,
    setShowChangeTutor,
    fetchBatchesTopicsAndSubtopics,
    getAssignedTutor,
    loadAlternativeTutors,
  } = useBookingStore();

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      batch: "",
      topic: "",
      subtopic: "",
      tutor: "",
      tutorChangeReason: "",
      notes: "",
    },
  });

  // Check for topic and subtopic from URL params (from Learning Journey page)
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const topicParam = searchParams.get('topic');
    const subtopicParam = searchParams.get('subtopic');
    const batchParam = searchParams.get('batch');

    fetchBatchesTopicsAndSubtopics().then(() => {
      // If batch is specified in URL, select it
      if (batchParam) {
        const batch = batches.find(b => b.name === batchParam || b.id === batchParam);
        if (batch) {
          setSelectedBatch(batch.id);
          form.setValue("batch", batch.id);
        }
      }

      // If topic is specified in URL, select it
      if (topicParam) {
        const topic = topics.find(t => t.name === topicParam || t.id === topicParam);
        if (topic) {
          // If we have a batch from URL, make sure topic belongs to that batch
          if (batchParam) {
            const batch = batches.find(b => b.name === batchParam || b.id === batchParam);
            if (batch && topic.batchId === batch.id) {
              setSelectedTopic(topic.id);
              form.setValue("topic", topic.id);
            }
          } else {
            // If no batch in URL, select the topic's batch first
            const topicBatch = batches.find(b => b.id === topic.batchId);
            if (topicBatch) {
              setSelectedBatch(topicBatch.id);
              form.setValue("batch", topicBatch.id);
              setSelectedTopic(topic.id);
              form.setValue("topic", topic.id);
            }
          }

          // If subtopic is specified, select it
          if (subtopicParam) {
            const subtopic = topic.subtopics.find(s => s.name === subtopicParam || s.id === subtopicParam);
            if (subtopic) {
              setSelectedSubtopic(subtopic.id);
              form.setValue("subtopic", subtopic.id);
            }
          }
        }
      }
    });
  }, [location.search, batches.length, topics.length]);

  // Update form when assigned tutor changes
  useEffect(() => {
    if (assignedTutor) {
      form.setValue("tutor", assignedTutor.id);
    }
  }, [assignedTutor]);

  // State to track if a tutor change request has been submitted
  const [tutorChangeRequestSubmitted, setTutorChangeRequestSubmitted] = useState(false);

  // Handle form submission
  const onSubmit = (data: z.infer<typeof formSchema>) => {
    // In a real app, you would submit the form data to an API
    console.log("Form submitted:", data);

    // Get the full batch, topic, and subtopic objects for better logging
    const selectedBatchObj = batches.find(b => b.id === data.batch);
    const selectedTopicObj = topics.find(t => t.id === data.topic);
    const selectedSubtopicObj = selectedTopicObj?.subtopics.find(s => s.id === data.subtopic);

    console.log("Selected batch:", selectedBatchObj?.name);
    console.log("Selected topic:", selectedTopicObj?.name);
    console.log("Selected subtopic:", selectedSubtopicObj?.name);

    // If the tutor change request notice is already shown, don't do anything
    // This is because the request was already submitted when the user clicked "Confirm Change"
    if (tutorChangeRequestSubmitted) {
      return;
    }

    // Note: We're not handling regular session requests here anymore
    // as they are now handled by the TutorAvailabilityViewer component
    // This form submission is only used for tutor change requests
  };

  // Handle tutor change
  const handleTutorChange = (tutorId: string) => {
    const isAssigned = assignedTutor?.id === tutorId;

    if (!isAssigned) {
      setShowTutorChangeDialog(true);
      setSelectedTutor(tutorId);
    } else {
      setSelectedTutor(tutorId);
      form.setValue("tutor", tutorId);
      form.setValue("tutorChangeReason", "");
    }
  };

  // Confirm tutor change with reason
  const confirmTutorChange = () => {
    if (selectedTutor) {
      // Mark the selected tutor as requested
      const updatedTutors = tutors.map(tutor =>
        tutor.id === selectedTutor
          ? { ...tutor, isRequested: true }
          : tutor
      );

      // Update the store with the updated tutors
      useBookingStore.setState({ tutors: updatedTutors });

      form.setValue("tutor", selectedTutor);
      form.setValue("tutorChangeReason", tutorChangeReason);
      setShowTutorChangeDialog(false);

      // Show the tutor change request notice immediately
      setTutorChangeRequestSubmitted(true);

      // Show a toast notification
      toast({
        title: "Tutor change request submitted",
        description: "Your request has been sent to the admin for approval.",
      });
    }
  };

  return (
    <StudentPageLayout
      title="Request a Session"
      description="Request a tutoring session for a specific topic and subtopic"
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rfpurple-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading...</p>
          </div>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => fetchBatchesTopicsAndSubtopics()}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pb-8">
            <Card>
              <CardHeader>
                <CardTitle>Learning Path Selection</CardTitle>
                <CardDescription>
                  Select the batch, topic, and subtopic you want to focus on in this session
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectionComplete ? (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">Selected Learning Path</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectionComplete(false)}
                        className="text-xs"
                      >
                        Edit
                      </Button>
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div>
                        <p className="text-gray-500">Batch</p>
                        <p className="font-medium">{batches.find(b => b.id === selectedBatch)?.name}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Topic</p>
                        <p className="font-medium">{topics.find(t => t.id === selectedTopic)?.name}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Subtopic</p>
                        <p className="font-medium">
                          {topics.find(t => t.id === selectedTopic)?.subtopics.find(s => s.id === selectedSubtopic)?.name}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Batch Selection */}
                    <FormField
                      control={form.control}
                      name="batch"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Batch</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              setSelectedBatch(value);
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a batch" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {batches.map((batch) => (
                                <SelectItem key={batch.id} value={batch.id}>
                                  {batch.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Topic Selection */}
                    <FormField
                      control={form.control}
                      name="topic"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Topic</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              setSelectedTopic(value);
                            }}
                            value={field.value}
                            disabled={!selectedBatch}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={selectedBatch ? "Select a topic" : "Select a batch first"} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {selectedBatch &&
                                topics
                                  .filter((topic) => topic.batchId === selectedBatch)
                                  .map((topic) => (
                                    <SelectItem key={topic.id} value={topic.id}>
                                      {topic.name}
                                    </SelectItem>
                                  ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Subtopic Selection */}
                    <FormField
                      control={form.control}
                      name="subtopic"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subtopic</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              setSelectedSubtopic(value);
                            }}
                            value={field.value}
                            disabled={!selectedTopic}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder={selectedTopic ? "Select a subtopic" : "Select a topic first"} />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {selectedTopic &&
                                topics
                                  .find((t) => t.id === selectedTopic)
                                  ?.subtopics.map((subtopic) => (
                                    <SelectItem key={subtopic.id} value={subtopic.id}>
                                      {subtopic.name}
                                    </SelectItem>
                                  ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
              </CardContent>
            </Card>

            {/* Tutor Selection */}
            {assignedTutor && selectedBatch && selectedTopic && selectedSubtopic && (
              <Card>
                <CardHeader>
                  <CardTitle>Tutor Selection</CardTitle>
                  <CardDescription>
                    The system has assigned a tutor based on your selection
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="tutor"
                    render={({ field }) => (
                      <FormItem>
                        <div className="space-y-4">
                          <RadioGroup
                            onValueChange={(value) => {
                              handleTutorChange(value);
                            }}
                            value={field.value}
                            className="space-y-4"
                          >
                            {/* Assigned Tutor */}
                            <div className="flex items-start space-x-4 border rounded-lg p-4 bg-green-50 border-green-200">
                              <RadioGroupItem value={assignedTutor.id} id={`tutor-${assignedTutor.id}`} className="mt-1" />
                              <div className="flex-1">
                                <Label htmlFor={`tutor-${assignedTutor.id}`} className="flex items-center">
                                  <div className="flex items-center">
                                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                                      {assignedTutor.photoUrl ? (
                                        <img src={assignedTutor.photoUrl} alt={assignedTutor.name} className="w-full h-full object-cover" />
                                      ) : (
                                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                          <User className="h-6 w-6 text-gray-500" />
                                        </div>
                                      )}
                                    </div>
                                    <div>
                                      <div className="font-medium">{assignedTutor.name}</div>
                                      <div className="text-sm text-gray-500 flex items-center">
                                        <Badge className="mr-2 bg-green-100 text-green-800 hover:bg-green-100">Assigned</Badge>
                                        <span>Rating: {assignedTutor.rating}/5</span>
                                      </div>
                                    </div>
                                  </div>
                                </Label>
                                <div className="mt-2 text-sm text-gray-600">
                                  <div>Specialties: {assignedTutor.specialties.join(", ")}</div>
                                </div>
                              </div>
                            </div>

                            {/* Other Tutors - Only shown when Change Tutor is clicked */}
                            {showChangeTutor && tutors
                              .filter((tutor) => tutor.id !== assignedTutor.id)
                              .map((tutor) => (
                                <div key={tutor.id} className="flex items-start space-x-4 border rounded-lg p-4">
                                  <RadioGroupItem value={tutor.id} id={`tutor-${tutor.id}`} className="mt-1" />
                                  <div className="flex-1">
                                    <Label htmlFor={`tutor-${tutor.id}`} className="flex items-center">
                                      <div className="flex items-center">
                                        <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                                          {tutor.photoUrl ? (
                                            <img src={tutor.photoUrl} alt={tutor.name} className="w-full h-full object-cover" />
                                          ) : (
                                            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                              <User className="h-6 w-6 text-gray-500" />
                                            </div>
                                          )}
                                        </div>
                                        <div>
                                          <div className="font-medium">{tutor.name}</div>
                                          <div className="text-sm text-gray-500 flex items-center">
                                            {tutor.isRequested && (
                                              <Badge className="mr-2 bg-orange-100 text-orange-800 hover:bg-orange-100">Requested</Badge>
                                            )}
                                            <span>Rating: {tutor.rating}/5</span>
                                          </div>
                                        </div>
                                      </div>
                                    </Label>
                                    <div className="mt-2 text-sm text-gray-600">
                                      <div>Specialties: {tutor.specialties.join(", ")}</div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                          </RadioGroup>
                        </div>
                        <FormMessage />

                        {/* Change Tutor Button */}
                        {!showChangeTutor ? (
                          <Button
                            type="button"
                            variant="outline"
                            className="mt-4"
                            onClick={() => loadAlternativeTutors()}
                          >
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Change Tutor
                          </Button>
                        ) : (
                          <Button
                            type="button"
                            variant="outline"
                            className="mt-4"
                            onClick={() => setShowChangeTutor(false)}
                          >
                            <X className="mr-2 h-4 w-4" />
                            Hide Alternative Tutors
                          </Button>
                        )}
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            )}

            {/* Tutor Availability Viewer - Only shown when tutor is selected */}
            {selectedBatch && selectedTopic && selectedSubtopic && assignedTutor && selectedTutor && !tutorChangeRequestSubmitted && (
              <TutorAvailabilityViewer
                tutorId={selectedTutor}
                studentId={user?.id || "student-001"}
                batchId={selectedBatch}
                topicId={selectedTopic}
                subtopicId={selectedSubtopic}
                onRequestSent={() => {
                  toast({
                    title: "Request sent",
                    description: "Session request has been sent successfully.",
                  });

                  // Navigate back to the learning journey page
                  setTimeout(() => {
                    navigate("/student/journey");
                  }, 1500);
                }}
              />
            )}

            {/* Tutor Change Request Notice */}
            {tutorChangeRequestSubmitted ? (
              <Card>
                <CardHeader>
                  <CardTitle>Tutor Change Request Submitted</CardTitle>
                  <CardDescription>
                    Your request is being processed
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                    <div className="flex items-start">
                      <Info className="h-6 w-6 text-blue-500 mr-3 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-blue-900 mb-2">Next Steps</h3>
                        <p className="text-blue-800 mb-3">
                          Your tutor change request has been sent to the admin for assessment and approval.
                        </p>
                        <p className="text-blue-800 mb-3">
                          Once approved, you will be notified and can book a session with your requested tutor.
                        </p>
                        <p className="text-blue-800">
                          You will receive a notification when a decision has been made.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/student/journey")}
                  >
                    Return to Learning Journey
                  </Button>
                </CardFooter>
              </Card>
            ) : null}
          </form>
        </Form>
      )}

      {/* Tutor Change Reason Dialog */}
      <Dialog open={showTutorChangeDialog} onOpenChange={setShowTutorChangeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reason for Tutor Change</DialogTitle>
            <DialogDescription>
              Please provide a reason for requesting a different tutor. This information will be sent to the admin.
            </DialogDescription>
          </DialogHeader>
          <Textarea
            value={tutorChangeReason}
            onChange={(e) => setTutorChangeReason(e.target.value)}
            placeholder="Enter your reason here..."
            className="min-h-[100px]"
          />
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowTutorChangeDialog(false);
              setSelectedTutor(assignedTutor?.id || null);
              form.setValue("tutor", assignedTutor?.id || "");
            }}>
              Cancel
            </Button>
            <Button onClick={confirmTutorChange} disabled={!tutorChangeReason.trim()}>
              Confirm Change
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </StudentPageLayout>
  );
};

export default RequestBooking;

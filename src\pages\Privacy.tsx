import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import useScrollToTop from "@/hooks/useScrollToTop";

const Privacy = () => {
  // Use the scroll to top hook
  useScrollToTop();
  
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl mb-8">
            Privacy Policy
          </h1>

          <div className="prose prose-lg max-w-none">
            <h2>1. Information We Collect</h2>
            <p>
              We collect information to provide better services to our users.
              This includes information you provide to us, such as your name,
              email address, and phone number.
            </p>

            <h2>2. How We Use Information</h2>
            <p>
              We use the information we collect to provide, maintain, and
              improve our services, to develop new ones, and to protect our
              users.
            </p>

            <h2>3. Information We Share</h2>
            <p>
              We do not share your personal information with companies,
              organizations, or individuals outside of rfLearn except in the
              following cases: with your consent, with domain administrators,
              for legal reasons, or in case of a merger or acquisition.
            </p>

            <h2>4. Information Security</h2>
            <p>
              We work hard to protect our users from unauthorized access to or
              unauthorized alteration, disclosure, or destruction of information
              we hold.
            </p>

            <h2>5. Changes</h2>
            <p>
              Our Privacy Policy may change from time to time. We will post any
              privacy policy changes on this page.
            </p>

            <h2>6. Contact Us</h2>
            <p>
              If you have any questions about our Privacy Policy, please contact
              us.
            </p>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Privacy;


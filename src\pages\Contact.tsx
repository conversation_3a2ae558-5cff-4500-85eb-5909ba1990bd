import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/Button';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { 
  Mail, 
  Phone, 
  MapPin, 
  ArrowRight, 
  CheckCircle, 
  MessageSquare,
  Clock,
  Users,
  BookOpen
} from 'lucide-react';

const Contact = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [progressWidth, setProgressWidth] = useState(0);

  const steps = [
    {
      id: 1,
      title: "Get in Touch",
      description: "Choose your preferred way to contact our sales team",
      icon: <MessageSquare className="w-6 h-6" />,
      content: "Select from email, phone, or visit us in person. Our team is ready to understand your learning needs and provide personalized guidance.",
      action: "Choose Contact Method"
    },
    {
      id: 2,
      title: "Schedule a Call",
      description: "Book a personalized consultation with our education experts",
      icon: <Clock className="w-6 h-6" />,
      content: "Schedule a convenient time for a detailed discussion about your educational goals, learning preferences, and how we can help you succeed.",
      action: "Book Consultation"
    },
    {
      id: 3,
      title: "Get Started",
      description: "Begin your personalized learning journey with rfLearn",
      icon: <BookOpen className="w-6 h-6" />,
      content: "Start your learning journey with a customized plan, matched tutors, and ongoing support to achieve your academic goals.",
      action: "Begin Learning"
    }
  ];

  // Auto-progress through steps with smooth progress bar
  useEffect(() => {
    const progressTimer = setInterval(() => {
      setProgressWidth(prev => {
        const increment = 100 / (4000 / 50); // 50ms intervals over 4 seconds
        const newWidth = prev + increment;

        if (newWidth >= 100) {
          // Complete current step and move to next
          setCompletedSteps(prevCompleted => {
            if (!prevCompleted.includes(currentStep)) {
              return [...prevCompleted, currentStep];
            }
            return prevCompleted;
          });

          if (currentStep < 3) {
            setCurrentStep(prev => prev + 1);
            return 0; // Reset progress for next step
          }
          return 100; // Keep at 100% for final step
        }

        return newWidth;
      });
    }, 50); // Update every 50ms for smooth animation

    return () => clearInterval(progressTimer);
  }, [currentStep]);



  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-grow bg-white">
      {/* Header Section */}
      <div className="py-16 pb-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">
            Contact Our Sales Team
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8">
            Ready to transform your learning experience? Let's discuss how rfLearn can help you achieve your educational goals.
          </p>
          <div className="flex items-center justify-center space-x-2 text-gray-500">
            <Users className="w-5 h-5" />
            <span>Trusted by thousands of students and parents worldwide</span>
          </div>
        </div>
      </div>

      {/* Steps Progress */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center mb-12">
          <div className="flex items-center space-x-4 md:space-x-8">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center">
                  <div className={`flex items-center justify-center w-16 h-16 rounded-full border-2 transition-all duration-300 ${
                    completedSteps.includes(step.id)
                      ? 'bg-green-600 border-green-600 text-white shadow-lg'
                      : currentStep === step.id
                      ? 'bg-rfpurple-600 border-rfpurple-600 text-white shadow-lg'
                      : 'border-gray-300 text-gray-400 bg-white'
                  }`}>
                    {completedSteps.includes(step.id) ? (
                      <CheckCircle className="w-8 h-8" />
                    ) : (
                      step.icon
                    )}
                  </div>
                  <div className="mt-3 text-center">
                    <p className={`text-sm font-medium ${
                      completedSteps.includes(step.id) ? 'text-green-600' :
                      currentStep === step.id ? 'text-rfpurple-600' : 'text-gray-500'
                    }`}>
                      Step {step.id}
                    </p>
                    <p className={`text-xs mt-1 max-w-20 ${
                      completedSteps.includes(step.id) ? 'text-green-700' :
                      currentStep === step.id ? 'text-gray-900' : 'text-gray-400'
                    }`}>
                      {step.title}
                    </p>
                  </div>

                  {/* Progress bar for current step - hide if all steps are completed */}
                  {currentStep === step.id && !completedSteps.includes(3) && (
                    <div className="mt-2 w-20 h-1 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-rfpurple-600 transition-all duration-100 ease-linear"
                        style={{ width: `${progressWidth}%` }}
                      />
                    </div>
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`hidden sm:block w-16 md:w-24 h-1 transition-all duration-300 ${
                    completedSteps.includes(step.id) ? 'bg-green-600' : 'bg-gray-300'
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>



        {/* Contact Methods */}
        <div className="grid md:grid-cols-3 gap-8 mb-12 px-4">
          {/* Email */}
          <div className="bg-white rounded-xl shadow-lg p-6 pt-8 text-center hover:shadow-xl transition-shadow border border-gray-100">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6 mt-2">
              <Mail className="w-8 h-8 text-rfpurple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Email Us</h3>
            <p className="text-gray-600 mb-6">
              Send us your questions and we'll respond within 24 hours
            </p>
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center text-rfpurple-600 hover:text-rfpurple-700 font-medium"
            >
              <EMAIL>
              <ArrowRight className="ml-2 w-4 h-4" />
            </a>
          </div>

          {/* Phone */}
          <div className="bg-white rounded-xl shadow-lg p-6 pt-8 text-center hover:shadow-xl transition-shadow border border-gray-100">
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6 mt-2">
              <Phone className="w-8 h-8 text-[#ff7f52]" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Call Us</h3>
            <p className="text-gray-600 mb-6">
              Speak directly with our education consultants
            </p>
            <a 
              href="tel:+918690845205"
              className="inline-flex items-center text-[#ff7f52] hover:text-[#d96c46] font-medium"
            >
              (+91) 8690845205
              <ArrowRight className="ml-2 w-4 h-4" />
            </a>
            <p className="text-sm text-gray-500 mt-2">
              Mon-Fri: 9 AM - 6 PM IST
            </p>
          </div>

          {/* Visit */}
          <div className="bg-white rounded-xl shadow-lg p-6 pt-8 text-center hover:shadow-xl transition-shadow border border-gray-100">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6 mt-2">
              <MapPin className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Visit Us</h3>
            <p className="text-gray-600 mb-6">
              Meet our team at our Delhi office
            </p>
            <div className="text-gray-700">
              <p className="font-medium">D-213, 4th Floor, 244/6, Sangam Vihar,</p>
              <p>Wazirabad, Delhi-110084, India</p>
            </div>
          </div>
        </div>

        {/* What to Expect */}
        <div className="bg-gradient-to-r from-purple-50 to-orange-50 rounded-xl p-8 mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            What to Expect When You Contact Us
          </h3>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900">Personalized Consultation</h4>
                  <p className="text-gray-600">We'll understand your specific learning goals and challenges</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900">Custom Learning Plan</h4>
                  <p className="text-gray-600">Receive a tailored roadmap for your educational journey</p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900">Tutor Matching</h4>
                  <p className="text-gray-600">Get matched with expert tutors in your subject areas</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-6 h-6 text-green-600 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900">Pricing Options</h4>
                  <p className="text-gray-600">Explore flexible pricing plans that fit your budget</p>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
      </div>
      <Footer />
    </div>
  );
};

export default Contact;

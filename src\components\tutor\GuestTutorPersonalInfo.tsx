import React, { useEffect } from "react";
import {
  UseFormReturn,
} from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/Form";
import { Input } from "@/components/ui/Input";
import { InfoIcon } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { Loader2, CheckCircle, XCircle, Mail } from "lucide-react";
import { useGuestTutorFormStore } from "@/store/guestTutorFormStore";

// Schema for personal information
export {
  firstNameSchema,
  lastNameSchema,
  emailSchema,
  phoneNumberSchema,
} from "@/services/errorHandler";

interface TutorFormPersonalInfoProps {
  form: UseFormReturn<any>;
  isCheckingEmail: boolean;
  emailChecked: boolean;
  emailValid: boolean;
  isVerifyingEmail: boolean;
  emailVerified: boolean;
  verificationCountdown: number;
  handleVerifyEmail: () => void;
  storeValidationState?: {
    firstName: { value: string; isValid: boolean; error: string | null };
    lastName: { value: string; isValid: boolean; error: string | null };
    email: { value: string; isValid: boolean; error: string | null };
    phoneNumber: { value: string; isValid: boolean; error: string | null };
    isFormValid: boolean;
  };
  onValidationChange?: (
    isValid: boolean,
    errors: Record<string, string>
  ) => void;
}

const GuestTutorPersonalInfo: React.FC<TutorFormPersonalInfoProps> = ({
  form,
  isCheckingEmail,
  emailChecked,
  emailValid,
  isVerifyingEmail,
  emailVerified,
  verificationCountdown,
  handleVerifyEmail,
  storeValidationState,
  onValidationChange,
}) => {
  // Get the store actions
  const { setFirstName, validateForm } = useGuestTutorFormStore();

  // Use storeValidationState for debugging or additional logic if needed
  useEffect(() => {
    if (storeValidationState) {
      // You can use this to log or debug the store state
      // console.log("Store validation state:", storeValidationState);
    }
  }, [storeValidationState]);

  // Watch for first name changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      // Only process firstName changes or form initialization
      if (name === "firstName" || name === undefined) {
        const firstNameValue = form.getValues("firstName");
        const firstNameState = form.getFieldState("firstName");

        // Update the store with React Hook Form's validation state
        setFirstName(
          firstNameValue,
          !firstNameState.invalid, // Just use RHF's validation result
          firstNameState.error?.message || null
        );

        // Validate the overall form
        validateForm();
      }
    });

    // Cleanup subscription
    return () => subscription.unsubscribe();
  }, [form, setFirstName, validateForm]);

  // Keep the original useEffect for backward compatibility
  useEffect(() => {
    if (!onValidationChange) {
      return;
    }

    const subscription = form.watch((value) => {
      // Original validation logic...
      // ...
    });

    return () => subscription.unsubscribe();
  }, [form, emailVerified, emailValid, onValidationChange]);

  // Render verification link based on email verification status
  const renderVerificationLink = () => {
    if (emailVerified) {
      return (
        <div className="mt-2 flex items-center text-sm text-green-600">
          <CheckCircle className="mr-1 h-4 w-4" />
          Email verified
        </div>
      );
    }

    if (emailChecked && emailValid && !emailVerified) {
      return (
        <div className="mt-2">
          <button
            type="button"
            onClick={handleVerifyEmail}
            disabled={isVerifyingEmail || verificationCountdown > 0}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center disabled:text-gray-400 disabled:cursor-not-allowed"
          >
            {isVerifyingEmail ? (
              <>
                <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                Sending...
              </>
            ) : verificationCountdown > 0 ? (
              `Resend in ${verificationCountdown}s`
            ) : (
              <>
                <Mail className="mr-2 h-3 w-3" />
                Verify Email
              </>
            )}
          </button>
        </div>
      );
    }
    // render nothing if email is not checked or not valid
    return null;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex">
                First Name <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex">
                Last Name <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Email
                <span className="text-red-500 ml-1">*</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground ml-2 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      You need to verify your email before submitting the tutor
                      application
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    {...field}
                    placeholder="<EMAIL>"
                    disabled={isVerifyingEmail}
                  />
                </FormControl>
                {isCheckingEmail && (
                  <div className="absolute right-3 top-2.5">
                    <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                  </div>
                )}
                {emailChecked && emailValid && !isVerifyingEmail && (
                  <div className="absolute right-3 top-2.5">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                )}
                {emailChecked && !emailValid && !isVerifyingEmail && (
                  <div className="absolute right-3 top-2.5">
                    <XCircle className="h-5 w-5 text-red-500" />
                  </div>
                )}
              </div>
              <FormMessage />
              {renderVerificationLink()}
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Phone Number <span className="text-red-500 ml-1">*</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <InfoIcon className="h-4 w-4 text-muted-foreground ml-2 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      We're asking for phone number to get in touch with you for
                      the initial phone screening round
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </FormLabel>
              <FormControl>
                <Input placeholder="+****************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default GuestTutorPersonalInfo;

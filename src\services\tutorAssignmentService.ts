import { supabase } from "@/lib/supabaseClient";

// Define the Tutor interface
export interface Tutor {
  id: string;
  name: string;
  photoUrl?: string;
  specialties: string[];
  rating: number;
  isAssigned?: boolean;
  isRequested?: boolean;
}

// Sample tutors data for development
const sampleTutors: <PERSON><PERSON>[] = [
  {
    id: "tutor1",
    name: "Dr. <PERSON>",
    photoUrl: "https://randomuser.me/api/portraits/women/1.jpg",
    specialties: ["Neural Networks", "Deep Learning"],
    rating: 4.9,
    isAssigned: true,
  },
  {
    id: "tutor2",
    name: "Prof<PERSON> <PERSON>",
    photoUrl: "https://randomuser.me/api/portraits/men/2.jpg",
    specialties: ["Reinforcement Learning", "Machine Learning"],
    rating: 4.8,
  },
  {
    id: "tutor3",
    name: "Dr. <PERSON>",
    photoUrl: "https://randomuser.me/api/portraits/women/3.jpg",
    specialties: ["Computer Vision", "Neural Networks"],
    rating: 4.7,
  },
];

// Sample mapping of topics and subtopics to tutors
const sampleAssignments = {
  // Format: batchId_topicId: tutorId
  "batch1_topic1": "tutor1", // Neural Networks in Machine Learning Booster
  "batch1_topic2": "tutor2", // Reinforcement Learning in Machine Learning Booster
  "batch2_topic3": "tutor3", // Data Visualization in Data Science Prep
  "batch3_topic4": "tutor2", // AI Ethics in AI Fundamentals Custom

  // Format: batchId_topicId_subtopicId: tutorId
  "batch1_topic1_subtopic3": "tutor3", // Transformers in Neural Networks
};

/**
 * Get the assigned tutor for a session based on batch, topic, and subtopic
 *
 * This is a mock implementation that simulates the database function get_assigned_tutor_for_session
 * When the database is set up, this should be replaced with an actual API call to the function
 *
 * @param batchId The batch ID
 * @param topicId The topic ID
 * @param subtopicId The subtopic ID (optional)
 * @returns The assigned tutor object or null if no tutor is assigned
 */
export const getAssignedTutorForSession = async (
  batchId: string,
  topicId: string,
  subtopicId: string | null = null
): Promise<Tutor | null> => {
  // In development mode, use sample data
  if (process.env.NODE_ENV === 'development') {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    let assignedTutorId: string | null = null;

    // Check if there's a specific assignment for this subtopic
    if (subtopicId) {
      assignedTutorId = sampleAssignments[`${batchId}_${topicId}_${subtopicId}`] || null;
    }

    // If no subtopic-specific assignment, check for topic-level assignment
    if (!assignedTutorId) {
      assignedTutorId = sampleAssignments[`${batchId}_${topicId}`] || null;
    }

    // If no topic-specific assignment, use a default based on the batch
    // In a real implementation, this would check the default_tutor_id in the batches table
    if (!assignedTutorId) {
      if (batchId === "batch1") assignedTutorId = "tutor1";
      else if (batchId === "batch2") assignedTutorId = "tutor3";
      else if (batchId === "batch3") assignedTutorId = "tutor2";
    }

    // Find the tutor in our sample data
    const assignedTutor = assignedTutorId
      ? sampleTutors.find(tutor => tutor.id === assignedTutorId) || null
      : null;

    // Mark the tutor as assigned
    if (assignedTutor) {
      assignedTutor.isAssigned = true;
    }

    return assignedTutor;
  }

  // In production mode, call the actual database function
  try {
    // Call the database function to get the assigned tutor ID
    const { data: tutorId, error } = await supabase
      .rpc('get_assigned_tutor_for_session', {
        p_batch_id: batchId,
        p_topic_id: topicId,
        p_subtopic_id: subtopicId
      });

    if (error) throw error;

    // If no tutor was assigned, return null
    if (!tutorId) return null;

    // Get the tutor profile details
    const { data: tutorProfile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        id,
        first_name,
        last_name,
        avatar_url,
        tutor_specialties:tutor_specialties(specialty),
        tutor_ratings:session_feedback(rating)
      `)
      .eq('id', tutorId)
      .single();

    if (profileError) throw profileError;

    // Calculate average rating
    const ratings = tutorProfile.tutor_ratings || [];
    const avgRating = ratings.length > 0
      ? ratings.reduce((sum, item) => sum + item.rating, 0) / ratings.length
      : 0;

    // Format specialties
    const specialties = (tutorProfile.tutor_specialties || []).map(s => s.specialty);

    // Return the tutor object
    return {
      id: tutorProfile.id,
      name: `${tutorProfile.first_name} ${tutorProfile.last_name}`,
      photoUrl: tutorProfile.avatar_url,
      specialties,
      rating: parseFloat(avgRating.toFixed(1)),
      isAssigned: true
    };
  } catch (error) {
    console.error("Error getting assigned tutor:", error);
    return null;
  }
};

/**
 * Get all available tutors for a specific batch, topic, and subtopic
 *
 * @param batchId The batch ID
 * @param topicId The topic ID
 * @param subtopicId The subtopic ID (optional)
 * @returns An array of tutor objects
 */
export const getAvailableTutors = async (
  batchId: string,
  topicId: string,
  subtopicId: string | null = null
): Promise<Tutor[]> => {
  // In development mode, use sample data
  if (process.env.NODE_ENV === 'development') {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Return a copy of the sample tutors
    return [...sampleTutors];
  }

  // In production mode, fetch tutors from the database
  try {
    // First get qualified tutor IDs for this topic/subtopic
    const { data: qualifiedTutorIds, error: rpcError } = await supabase
      .rpc('get_qualified_tutors_for_topic', {
        p_topic_id: topicId,
        p_subtopic_id: subtopicId
      });

    if (rpcError) throw rpcError;

    // Then get the tutor profiles
    const { data: tutors, error } = await supabase
      .from('profiles')
      .select(`
        id,
        first_name,
        last_name,
        avatar_url,
        tutor_specialties:tutor_specialties(specialty),
        tutor_ratings:session_feedback(rating)
      `)
      .eq('user_type', 'tutor')
      .in('id', qualifiedTutorIds || []);

    if (error) throw error;

    // Format the tutors
    return tutors.map(tutor => {
      // Calculate average rating
      const ratings = tutor.tutor_ratings || [];
      const avgRating = ratings.length > 0
        ? ratings.reduce((sum, item) => sum + item.rating, 0) / ratings.length
        : 0;

      // Format specialties
      const specialties = (tutor.tutor_specialties || []).map(s => s.specialty);

      return {
        id: tutor.id,
        name: `${tutor.first_name} ${tutor.last_name}`,
        photoUrl: tutor.avatar_url,
        specialties,
        rating: parseFloat(avgRating.toFixed(1))
      };
    });
  } catch (error) {
    console.error("Error getting available tutors:", error);
    return [];
  }
};

/**
 * Get all available tutors except the assigned one for a specific batch, topic, and subtopic
 *
 * @param assignedTutorId The ID of the assigned tutor to exclude
 * @param batchId The batch ID
 * @param topicId The topic ID
 * @param subtopicId The subtopic ID (optional)
 * @returns An array of tutor objects excluding the assigned tutor
 */
export const getAvailableTutorsExceptAssigned = async (
  assignedTutorId: string,
  batchId: string,
  topicId: string,
  subtopicId: string | null = null
): Promise<Tutor[]> => {
  // Get all available tutors
  const allTutors = await getAvailableTutors(batchId, topicId, subtopicId);

  // Filter out the assigned tutor
  return allTutors.filter(tutor => tutor.id !== assignedTutorId);
};

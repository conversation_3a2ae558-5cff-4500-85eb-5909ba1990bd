
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Check, Circle } from "lucide-react";

type StepProps = {
  title: string;
  isActive: boolean;
  isCompleted: boolean;
};

const Step = ({ title, isActive, isCompleted }: StepProps) => {
  return (
    <div className="flex flex-col items-center">
      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
        isCompleted ? 'bg-red-500 text-white' : 
        isActive ? 'border-2 border-red-500' : 'border-2 border-gray-200'
      }`}>
        {isCompleted ? <Check className="w-6 h-6" /> : null}
      </div>
      <span className="mt-2 text-sm font-medium">{title}</span>
    </div>
  );
};

const BookingSteps = ({ currentStep }: { currentStep: number }) => {
  const steps = [
    { title: "CHOOSE TIME", id: 1 },
    { title: "YOUR INFO", id: 2 },
    { title: "CONFIRMATION", id: 3 }
  ];

  return (
    <div className="w-full max-w-3xl mx-auto mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center flex-grow">
            <Step 
              title={step.title}
              isActive={step.id === currentStep}
              isCompleted={step.id < currentStep}
            />
            {index < steps.length - 1 && (
              <div className="h-[2px] flex-grow mx-2 bg-gray-200">
                {step.id < currentStep && (
                  <div className="h-full bg-red-500" style={{ width: '100%' }}></div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default BookingSteps;

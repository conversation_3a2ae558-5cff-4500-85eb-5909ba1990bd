import React, { useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useNotificationStore, Notification } from '@/store/notificationStore';
import { useNotificationPageStore } from '@/store/notificationPageStore';
import { useAuth } from '@/context/AuthContext';
import TutorPageLayout from '@/components/layouts/TutorPageLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import LoadingSpinner from '@/components/LoadingSpinner';
import {
  Bell,
  Check,
  CheckCheck,
  Trash2,
  AlertCircle,
  CreditCard,
  BookOpen,
  Settings,
  Search,
  Filter,
  X
} from 'lucide-react';

const NotificationsPage: React.FC = () => {
  const { user } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const notificationRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Zustand stores
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    subscribeToNotifications,
    unsubscribeFromNotifications,
    clearError
  } = useNotificationStore();

  const {
    searchQuery,
    filterType,
    filterStatus,
    highlightedNotificationId,
    setSearchQuery,
    setFilterType,
    setFilterStatus,
    setHighlightedNotificationId,
    clearHighlight,
    resetState
  } = useNotificationPageStore();

  // Initialize notifications when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchNotifications(user.id);
      subscribeToNotifications(user.id);
    }

    // Cleanup subscription and reset state on unmount
    return () => {
      unsubscribeFromNotifications();
      resetState();
    };
  }, [user?.id, fetchNotifications, subscribeToNotifications, unsubscribeFromNotifications, resetState]);

  // Handle URL parameters for highlighting specific notifications
  useEffect(() => {
    const notificationId = searchParams.get('highlight');
    if (notificationId) {
      setHighlightedNotificationId(notificationId);
      
      // Scroll to the notification after a short delay
      setTimeout(() => {
        const element = notificationRefs.current[notificationId];
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 500);

      // Clear the highlight after 3 seconds
      setTimeout(() => {
        clearHighlight();
        setSearchParams(prev => {
          prev.delete('highlight');
          return prev;
        });
      }, 3000);
    }
  }, [searchParams, setHighlightedNotificationId, clearHighlight, setSearchParams]);

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = searchQuery === '' || 
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = filterType === 'all' || notification.type === filterType;
    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'read' && notification.is_read) ||
      (filterStatus === 'unread' && !notification.is_read);

    return matchesSearch && matchesType && matchesStatus;
  });

  const unreadNotifications = filteredNotifications.filter(n => !n.is_read);
  const readNotifications = filteredNotifications.filter(n => n.is_read);

  // Event handlers
  const handleMarkAsRead = async (notificationId: string) => {
    await markAsRead(notificationId);
  };

  const handleMarkAllAsRead = async () => {
    if (user?.id) {
      await markAllAsRead(user.id);
    }
  };

  const handleDelete = async (notificationId: string, isSystemManaged?: boolean) => {
    // Prevent deletion of system-managed notifications
    if (isSystemManaged) {
      return;
    }
    await deleteNotification(notificationId);
  };

  // Helper function to safely render HTML content for system notifications
  const renderNotificationMessage = (notification: Notification) => {
    if (notification.is_system_managed && notification.message.includes('<a href=')) {
      return (
        <div
          dangerouslySetInnerHTML={{ __html: notification.message }}
          className={`text-sm ${!notification.is_read ? 'text-gray-700' : 'text-gray-500'} mb-2`}
        />
      );
    }
    return (
      <p className={`text-sm ${!notification.is_read ? 'text-gray-700' : 'text-gray-500'} mb-2`}>
        {notification.message}
      </p>
    );
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setFilterType('all');
    setFilterStatus('all');
  };

  // Helper functions for notification display
  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'billing':
        return <CreditCard className="h-5 w-5" />;
      case 'academic':
        return <BookOpen className="h-5 w-5" />;
      case 'system':
        return <Settings className="h-5 w-5" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const getNotificationTypeColor = (type: Notification['type']) => {
    switch (type) {
      case 'billing':
        return 'text-green-600 bg-green-100';
      case 'academic':
        return 'text-blue-600 bg-blue-100';
      case 'system':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  // Show error state
  if (error) {
    return (
      <TutorPageLayout
        title="Notifications"
        description="View and manage all your notifications"
      >
        <Card>
          <CardContent className="py-12 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading notifications</h3>
            <p className="text-gray-500 mb-4">{error}</p>
            <Button onClick={clearError} variant="outline">
              Try again
            </Button>
          </CardContent>
        </Card>
      </TutorPageLayout>
    );
  }

  return (
    <TutorPageLayout
      title="Notifications"
      description="View and manage all your notifications"
    >
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-gray-600" />
              <span className="text-lg font-semibold">
                {unreadCount > 0 ? `${unreadCount} unread` : 'All caught up!'}
              </span>
            </div>
            {unreadCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <CheckCheck className="h-4 w-4 mr-1" />
                Mark all read
              </Button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Type Filter */}
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Types</option>
                <option value="academic">Academic</option>
                <option value="billing">Billing</option>
                <option value="system">System</option>
              </select>

              {/* Status Filter */}
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Status</option>
                <option value="unread">Unread</option>
                <option value="read">Read</option>
              </select>

              {/* Clear Filters */}
              {(searchQuery || filterType !== 'all' || filterStatus !== 'all') && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFilters}
                  className="flex items-center gap-1"
                >
                  <X className="h-4 w-4" />
                  Clear
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : filteredNotifications.length === 0 ? (
          <Card>
            <CardContent className="py-12 text-center">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
              <p className="text-gray-500">
                {searchQuery || filterType !== 'all' || filterStatus !== 'all'
                  ? 'Try adjusting your filters to see more notifications.'
                  : 'You\'re all caught up! New notifications will appear here.'}
              </p>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue="all" className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">
                All ({filteredNotifications.length})
              </TabsTrigger>
              <TabsTrigger value="unread">
                Unread ({unreadNotifications.length})
              </TabsTrigger>
              <TabsTrigger value="read">
                Read ({readNotifications.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              <NotificationList
                notifications={filteredNotifications}
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDelete}
                getNotificationIcon={getNotificationIcon}
                getNotificationTypeColor={getNotificationTypeColor}
                highlightedNotificationId={highlightedNotificationId}
                notificationRefs={notificationRefs}
                formatDate={formatDate}
                renderNotificationMessage={renderNotificationMessage}
              />
            </TabsContent>

            <TabsContent value="unread">
              <NotificationList
                notifications={unreadNotifications}
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDelete}
                getNotificationIcon={getNotificationIcon}
                getNotificationTypeColor={getNotificationTypeColor}
                highlightedNotificationId={highlightedNotificationId}
                notificationRefs={notificationRefs}
                formatDate={formatDate}
                renderNotificationMessage={renderNotificationMessage}
              />
            </TabsContent>

            <TabsContent value="read">
              <NotificationList
                notifications={readNotifications}
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDelete}
                getNotificationIcon={getNotificationIcon}
                getNotificationTypeColor={getNotificationTypeColor}
                highlightedNotificationId={highlightedNotificationId}
                notificationRefs={notificationRefs}
                formatDate={formatDate}
                renderNotificationMessage={renderNotificationMessage}
              />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </TutorPageLayout>
  );
};

// Notification List Component
interface NotificationListProps {
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string, isSystemManaged?: boolean) => void;
  getNotificationIcon: (type: Notification['type']) => React.ReactNode;
  getNotificationTypeColor: (type: Notification['type']) => string;
  highlightedNotificationId: string | null;
  notificationRefs: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>;
  formatDate: (dateString: string) => string;
  renderNotificationMessage: (notification: Notification) => React.ReactNode;
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  onMarkAsRead,
  onDelete,
  getNotificationIcon,
  getNotificationTypeColor,
  highlightedNotificationId,
  notificationRefs,
  formatDate,
  renderNotificationMessage
}) => {
  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read when clicked if it's unread
    if (!notification.is_read) {
      await onMarkAsRead(notification.id);
    }
  };

  return (
    <div className="space-y-4">
      {notifications.map((notification) => {
        const isHighlighted = highlightedNotificationId === notification.id;

        return (
          <Card
            key={notification.id}
            ref={(el) => {
              notificationRefs.current[notification.id] = el;
            }}
            className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
              !notification.is_read ? 'bg-blue-50 border-blue-200' : 'bg-white'
            } ${isHighlighted ? 'ring-2 ring-blue-500 shadow-lg' : ''}`}
            onClick={() => handleNotificationClick(notification)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  {/* Icon */}
                  <div className={`p-2 rounded-full ${getNotificationTypeColor(notification.type)}`}>
                    {getNotificationIcon(notification.type)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className={`text-sm font-medium ${!notification.is_read ? 'text-gray-900' : 'text-gray-700'}`}>
                        {notification.title}
                      </h4>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="text-xs">
                          {notification.type}
                        </Badge>
                        {!notification.is_read && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        )}
                      </div>
                    </div>
                    {renderNotificationMessage(notification)}
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-400">
                        {formatDate(notification.created_at)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-1 ml-2">
                  {!notification.is_read && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onMarkAsRead(notification.id);
                      }}
                      className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                  )}
                  {!notification.is_system_managed && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDelete(notification.id, notification.is_system_managed);
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default NotificationsPage;

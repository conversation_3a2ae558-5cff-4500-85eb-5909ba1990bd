import React, { useEffect } from "react";
import { useTutorAvailabilityStore } from "@/store/tutorAvailabilityStore";
import AvailabilityGrid from "@/components/tutor/availability/AvailabilityGrid";
import AutoAcceptRules from "@/components/tutor/availability/AutoAcceptRules";
import GoogleCalendarIntegration from "@/components/tutor/availability/GoogleCalendarIntegration";
import TutorPreferences from "@/components/tutor/availability/TutorPreferences";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import { Button } from "@/components/ui/Button";
import { Calendar, Settings, Clock, RefreshCw } from "lucide-react";
import { useToast } from "@/components/ui/UseToast";
import { useProfileData } from "@/hooks/useProfileData";
import { useAuth } from "@/context/AuthContext";
import LoadingSpinner from "@/components/LoadingSpinner";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";

const TutorSchedulePage: React.FC = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const profileData = useProfileData();
  const {
    fetchAvailabilityData,
    setTutorId,
    isLoading: availabilityLoading,
    error: availabilityError,
  } = useTutorAvailabilityStore();

  // Load availability data on component mount
  useEffect(() => {
    // Get tutor ID from auth user
    const tutorId = user?.id;

    if (tutorId) {
      // Set tutor ID in the availability store
      setTutorId(tutorId);

      // Fetch availability data from database
      fetchAvailabilityData();
    }
  }, [setTutorId, fetchAvailabilityData, user?.id]);

  // Determine if there's any loading or error state
  const isLoading = availabilityLoading;
  const error = availabilityError;

  return (
    <TutorPageLayout
      title="Availability Management"
      profileData={profileData}
      description="Manage your teaching availability, set auto-accept rules, and integrate with your calendar."
    >
      {isLoading ? (
        <div className="flex justify-center items-center h-full">
          <LoadingSpinner size="lg" fullScreen={false} />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <h3 className="text-red-800 font-medium">Error loading data</h3>
          <p className="text-red-700 mt-1">{error}</p>
          <Button
            variant="outline"
            className="mt-2"
            onClick={() => fetchAvailabilityData()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      ) : (
        <Tabs defaultValue="availability" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-3xl">
            <TabsTrigger value="availability" className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Availability Grid
            </TabsTrigger>
            <TabsTrigger value="auto-accept" className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Auto-Accept Rules
            </TabsTrigger>
            <TabsTrigger value="calendar" className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Calendar Integration
            </TabsTrigger>
            <TabsTrigger value="preferences" className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Preferences
            </TabsTrigger>
          </TabsList>

          <TabsContent value="availability" className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-4">
                Interactive Availability Grid
              </h2>
              <p className="text-gray-600 mb-6">
                Click and drag to create availability windows. Click on
                existing slots to change their status.
              </p>
              <AvailabilityGrid />
            </div>
          </TabsContent>

          <TabsContent value="auto-accept" className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <AutoAcceptRules />
            </div>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <GoogleCalendarIntegration />
            </div>
          </TabsContent>

          <TabsContent value="preferences" className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <TutorPreferences />
            </div>
          </TabsContent>
        </Tabs>
      )}
    </TutorPageLayout>
  );
};

export default TutorSchedulePage;

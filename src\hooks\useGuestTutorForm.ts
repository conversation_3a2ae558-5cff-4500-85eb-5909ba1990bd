import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { guestTutorFormSchema } from "@/services/errorHandler";
import { useGuestTutorFormStore } from "@/store/guestTutorFormStore";
import { useEffect } from "react";

export type TutorFormValues = z.infer<typeof guestTutorFormSchema>;

export function useGuestTutorForm() {
  const { touchField } = useGuestTutorFormStore();

  const form = useForm<TutorFormValues>({
    resolver: zodResolver(guestTutorFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      subjects: [],
      hourlyRate: "",
      cvFile: undefined,
    },
    mode: "onBlur", // Triggers validation on blur for all fields
  });

  // Use a different approach to sync with Zustand store
  useEffect(() => {
    // Subscribe to form state changes
    const subscription = form.watch((value, { name, type }) => {
      // When a field is blurred, mark it as touched in Zustand
      if (type === "blur" && name) {
        touchField(name as string);
      }
    });

    // Cleanup subscription on unmount
    return () => subscription.unsubscribe();
  }, [form, touchField]);

  return form;
}

import React, { useState, useEffect } from "react";
import { useProfileData } from "@/hooks/useProfileData";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/Tabs";
import {
  AlertCircle,
  BarChart3,
  BookOpen,
  Calendar,
  CheckCircle,
  Clock,
  Star,
  TrendingUp,
  User,
  XCircle,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { Progress } from "@/components/ui/Progress";

// Sample batch data
const batchesData = [
  { id: "BATCH-001", name: "Fall 2023" },
  { id: "BATCH-002", name: "Summer 2023" },
  { id: "BATCH-003", name: "Winter 2023" },
];

// Sample student data
const studentsData = [
  {
    id: "STU-1001",
    name: "Alice Johnson",
    email: "<EMAIL>",
    batchId: "BATCH-001",
    avatar: "https://ui-avatars.com/api/?name=Alice+Johnson&background=random",
    performance: {
      completionRate: 42, // percentage
      attendanceRate: 95, // percentage
      feedbackScore: 4.8, // out of 5
      lastActive: "2023-11-20", // date
      inactivityFlag: false,
      topicsCompleted: 5,
      totalTopics: 12,
      sessionsAttended: 10,
      totalSessions: 24,
      upcomingSessions: 2,
      averageEngagement: 8.5, // out of 10
      topicProgress: [
        { topic: "Neural Networks", progress: 75 },
        { topic: "Reinforcement Learning", progress: 40 },
        { topic: "Natural Language Processing", progress: 20 },
      ],
      recentSessions: [
        {
          id: "S-1001",
          date: "2023-11-15",
          topic: "Neural Networks",
          subtopic: "CNN Architecture",
          duration: 60,
          participationScore: 9.2,
          feedback: 5,
        },
        {
          id: "S-1008",
          date: "2023-11-08",
          topic: "Neural Networks",
          subtopic: "Backpropagation",
          duration: 60,
          participationScore: 8.5,
          feedback: 4.5,
        },
        {
          id: "S-1015",
          date: "2023-11-01",
          topic: "Reinforcement Learning",
          subtopic: "Q-Learning",
          duration: 60,
          participationScore: 7.8,
          feedback: 4,
        },
      ],
      strengths: ["Active participation", "Asks insightful questions", "Completes assignments on time"],
      areasForImprovement: ["Could benefit from more practice exercises", "Occasional difficulty with complex math concepts"],
    },
  },
  {
    id: "STU-1002",
    name: "Bob Smith",
    email: "<EMAIL>",
    batchId: "BATCH-001",
    avatar: "https://ui-avatars.com/api/?name=Bob+Smith&background=random",
    performance: {
      completionRate: 100, // percentage
      attendanceRate: 100, // percentage
      feedbackScore: 4.9, // out of 5
      lastActive: "2023-11-18", // date
      inactivityFlag: false,
      topicsCompleted: 18,
      totalTopics: 18,
      sessionsAttended: 18,
      totalSessions: 18,
      upcomingSessions: 0,
      averageEngagement: 9.2, // out of 10
      topicProgress: [
        { topic: "Probability Theory", progress: 100 },
        { topic: "Statistical Inference", progress: 100 },
        { topic: "Data Visualization", progress: 100 },
      ],
      recentSessions: [
        {
          id: "S-2001",
          date: "2023-08-25",
          topic: "Data Visualization",
          subtopic: "Interactive Dashboards",
          duration: 60,
          participationScore: 9.5,
          feedback: 5,
        },
        {
          id: "S-2008",
          date: "2023-08-18",
          topic: "Data Visualization",
          subtopic: "Storytelling with Data",
          duration: 60,
          participationScore: 9.0,
          feedback: 5,
        },
        {
          id: "S-2015",
          date: "2023-08-11",
          topic: "Statistical Inference",
          subtopic: "Hypothesis Testing",
          duration: 60,
          participationScore: 9.2,
          feedback: 4.8,
        },
      ],
      strengths: ["Exceptional grasp of concepts", "Helps other students", "Goes beyond required material"],
      areasForImprovement: ["Could benefit from more real-world applications"],
    },
  },
  {
    id: "STU-1003",
    name: "Charlie Davis",
    email: "<EMAIL>",
    batchId: "BATCH-001",
    avatar: "https://ui-avatars.com/api/?name=Charlie+Davis&background=random",
    performance: {
      completionRate: 40, // percentage
      attendanceRate: 80, // percentage
      feedbackScore: 4.0, // out of 5
      lastActive: "2023-11-05", // date
      inactivityFlag: true,
      topicsCompleted: 8,
      totalTopics: 20,
      sessionsAttended: 8,
      totalSessions: 20,
      upcomingSessions: 1,
      averageEngagement: 7.0, // out of 10
      topicProgress: [
        { topic: "Image Classification", progress: 80 },
        { topic: "Object Detection", progress: 40 },
        { topic: "Image Segmentation", progress: 0 },
      ],
      recentSessions: [
        {
          id: "S-3001",
          date: "2023-11-05",
          topic: "Object Detection",
          subtopic: "YOLO Architecture",
          duration: 60,
          participationScore: 6.5,
          feedback: 3.5,
        },
        {
          id: "S-3008",
          date: "2023-10-29",
          topic: "Object Detection",
          subtopic: "R-CNN Family",
          duration: 60,
          participationScore: 7.0,
          feedback: 4.0,
        },
        {
          id: "S-3015",
          date: "2023-10-22",
          topic: "Image Classification",
          subtopic: "Transfer Learning",
          duration: 60,
          participationScore: 7.5,
          feedback: 4.5,
        },
      ],
      strengths: ["Strong practical skills", "Creative problem-solving"],
      areasForImprovement: ["Attendance needs improvement", "Often misses deadlines", "Needs to engage more in discussions"],
    },
  },
  {
    id: "STU-1004",
    name: "Diana Wang",
    email: "<EMAIL>",
    batchId: "BATCH-003",
    avatar: "https://ui-avatars.com/api/?name=Diana+Wang&background=random",
    performance: {
      completionRate: 33, // percentage
      attendanceRate: 60, // percentage
      feedbackScore: 3.5, // out of 5
      lastActive: "2023-03-15", // date
      inactivityFlag: true,
      topicsCompleted: 5,
      totalTopics: 15,
      sessionsAttended: 5,
      totalSessions: 15,
      upcomingSessions: 0,
      averageEngagement: 5.5, // out of 10
      topicProgress: [
        { topic: "Text Classification", progress: 80 },
        { topic: "Language Modeling", progress: 20 },
        { topic: "Machine Translation", progress: 0 },
      ],
      recentSessions: [
        {
          id: "S-4001",
          date: "2023-03-15",
          topic: "Language Modeling",
          subtopic: "N-grams",
          duration: 60,
          participationScore: 5.0,
          feedback: 3.0,
        },
        {
          id: "S-4008",
          date: "2023-03-08",
          topic: "Language Modeling",
          subtopic: "Neural Language Models",
          duration: 60,
          participationScore: 5.5,
          feedback: 3.5,
        },
        {
          id: "S-4015",
          date: "2023-03-01",
          topic: "Text Classification",
          subtopic: "Sentiment Analysis",
          duration: 60,
          participationScore: 6.0,
          feedback: 4.0,
        },
      ],
      strengths: ["Good theoretical understanding", "Written assignments are well-researched"],
      areasForImprovement: ["Severe attendance issues", "Needs to communicate about absences", "Participation in sessions is minimal"],
    },
  },
];

const Performance = () => {
  const profileData = useProfileData();
  const [selectedBatchId, setSelectedBatchId] = useState<string>("");
  const [selectedStudentId, setSelectedStudentId] = useState<string>("");
  const [filteredStudents, setFilteredStudents] = useState<any[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("overview");

  // Filter students when batch changes
  useEffect(() => {
    if (selectedBatchId) {
      const students = studentsData.filter(student => student.batchId === selectedBatchId);
      setFilteredStudents(students);
      
      // Reset selected student if not in this batch
      if (selectedStudentId && !students.some(s => s.id === selectedStudentId)) {
        setSelectedStudentId("");
        setSelectedStudent(null);
      }
    } else {
      setFilteredStudents([]);
      setSelectedStudentId("");
      setSelectedStudent(null);
    }
  }, [selectedBatchId]);

  // Update selected student when student ID changes
  useEffect(() => {
    if (selectedStudentId) {
      const student = studentsData.find(s => s.id === selectedStudentId);
      setSelectedStudent(student || null);
    } else {
      setSelectedStudent(null);
    }
  }, [selectedStudentId]);

  // Format date to display in a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  // Calculate days since last activity
  const getDaysSinceLastActive = (lastActiveDate: string) => {
    const lastActive = new Date(lastActiveDate);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - lastActive.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Get color based on score
  const getScoreColor = (score: number, max: number) => {
    const percentage = (score / max) * 100;
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  // Get progress color based on percentage
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-500";
    if (percentage >= 60) return "bg-yellow-500";
    if (percentage >= 40) return "bg-orange-500";
    return "bg-red-500";
  };

  return (
    <TutorPageLayout
      title="Performance & Activity"
      profileData={profileData}
      description="Monitor student learning progress and engagement metrics"
    >
      {/* Student Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Select Batch</label>
          <Select value={selectedBatchId} onValueChange={setSelectedBatchId}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a batch" />
            </SelectTrigger>
            <SelectContent>
              {batchesData.map(batch => (
                <SelectItem key={batch.id} value={batch.id}>
                  {batch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Select Student</label>
          <Select 
            value={selectedStudentId} 
            onValueChange={setSelectedStudentId}
            disabled={filteredStudents.length === 0}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={filteredStudents.length === 0 ? "Select a batch first" : "Select a student"} />
            </SelectTrigger>
            <SelectContent>
              {filteredStudents.map(student => (
                <SelectItem key={student.id} value={student.id}>
                  {student.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Performance Content */}
      {selectedStudent ? (
        <div className="space-y-6">
          {/* Student Header with Key Metrics */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-6 items-start md:items-center">
                <div className="flex items-center gap-4">
                  <img 
                    src={selectedStudent.avatar} 
                    alt={selectedStudent.name} 
                    className="h-16 w-16 rounded-full"
                  />
                  <div>
                    <h2 className="text-xl font-bold">{selectedStudent.name}</h2>
                    <p className="text-gray-500">{selectedStudent.email}</p>
                  </div>
                </div>
                
                <div className="flex-grow grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-2 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">Completion</p>
                    <p className={`text-xl font-bold ${getScoreColor(selectedStudent.performance.completionRate, 100)}`}>
                      {selectedStudent.performance.completionRate}%
                    </p>
                  </div>
                  
                  <div className="text-center p-2 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">Attendance</p>
                    <p className={`text-xl font-bold ${getScoreColor(selectedStudent.performance.attendanceRate, 100)}`}>
                      {selectedStudent.performance.attendanceRate}%
                    </p>
                  </div>
                  
                  <div className="text-center p-2 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">Feedback</p>
                    <p className={`text-xl font-bold ${getScoreColor(selectedStudent.performance.feedbackScore, 5)}`}>
                      {selectedStudent.performance.feedbackScore}/5
                    </p>
                  </div>
                  
                  <div className="text-center p-2 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">Engagement</p>
                    <p className={`text-xl font-bold ${getScoreColor(selectedStudent.performance.averageEngagement, 10)}`}>
                      {selectedStudent.performance.averageEngagement}/10
                    </p>
                  </div>
                </div>
                
                {selectedStudent.performance.inactivityFlag && (
                  <div className="flex items-center gap-2 bg-red-50 text-red-700 px-3 py-2 rounded-md">
                    <AlertCircle size={16} />
                    <span>Inactive for {getDaysSinceLastActive(selectedStudent.performance.lastActive)} days</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          {/* Tabs for different performance views */}
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="progress">Topic Progress</TabsTrigger>
              <TabsTrigger value="sessions">Recent Sessions</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-6">
              {/* Overview content will be added in the next step */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <BookOpen size={18} className="text-purple-500" />
                      Topics & Sessions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Topics Completed</span>
                          <span className="text-sm font-medium">
                            {selectedStudent.performance.topicsCompleted}/{selectedStudent.performance.totalTopics}
                          </span>
                        </div>
                        <Progress 
                          value={(selectedStudent.performance.topicsCompleted / selectedStudent.performance.totalTopics) * 100} 
                          className={getProgressColor((selectedStudent.performance.topicsCompleted / selectedStudent.performance.totalTopics) * 100)}
                        />
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">Sessions Attended</span>
                          <span className="text-sm font-medium">
                            {selectedStudent.performance.sessionsAttended}/{selectedStudent.performance.totalSessions}
                          </span>
                        </div>
                        <Progress 
                          value={(selectedStudent.performance.sessionsAttended / selectedStudent.performance.totalSessions) * 100} 
                          className={getProgressColor((selectedStudent.performance.sessionsAttended / selectedStudent.performance.totalSessions) * 100)}
                        />
                      </div>
                      
                      <div className="pt-2">
                        <p className="text-sm font-medium mb-2">Upcoming Sessions</p>
                        {selectedStudent.performance.upcomingSessions > 0 ? (
                          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                            {selectedStudent.performance.upcomingSessions} scheduled
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-gray-500">
                            None scheduled
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <TrendingUp size={18} className="text-purple-500" />
                      Strengths
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {selectedStudent.performance.strengths.map((strength: string, index: number) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{strength}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <AlertCircle size={18} className="text-purple-500" />
                      Areas for Improvement
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {selectedStudent.performance.areasForImprovement.map((area: string, index: number) => (
                        <li key={index} className="flex items-start gap-2">
                          <AlertCircle size={16} className="text-yellow-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{area}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="progress" className="space-y-6">
              {/* Topic Progress content will be added in the next step */}
              <Card>
                <CardHeader>
                  <CardTitle>Topic Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {selectedStudent.performance.topicProgress.map((topic: any) => (
                      <div key={topic.topic} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <h3 className="font-medium">{topic.topic}</h3>
                          <span className={`text-sm font-medium ${
                            topic.progress >= 80 ? "text-green-600" : 
                            topic.progress >= 40 ? "text-yellow-600" : "text-red-600"
                          }`}>
                            {topic.progress}%
                          </span>
                        </div>
                        <Progress 
                          value={topic.progress} 
                          className={getProgressColor(topic.progress)}
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="sessions" className="space-y-6">
              {/* Recent Sessions content will be added in the next step */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Sessions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedStudent.performance.recentSessions.map((session: any) => (
                      <div key={session.id} className="border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-medium">{session.topic}: {session.subtopic}</h3>
                            <p className="text-sm text-gray-500">{formatDate(session.date)} • {session.duration} min</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={`${
                              session.participationScore >= 8 ? "bg-green-100 text-green-800" : 
                              session.participationScore >= 6 ? "bg-yellow-100 text-yellow-800" : 
                              "bg-red-100 text-red-800"
                            }`}>
                              {session.participationScore.toFixed(1)}/10
                            </Badge>
                            <div className="flex items-center">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <Star 
                                  key={i} 
                                  size={14} 
                                  className={i < Math.round(session.feedback) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          {/* Action Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            <Button variant="outline">
              Export Report
            </Button>
            <Button>
              Schedule Follow-up
            </Button>
          </div>
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <User size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-1">No Student Selected</h3>
          <p className="text-gray-500 max-w-md mx-auto">
            Please select a batch and student to view their performance and activity metrics.
          </p>
        </div>
      )}
    </TutorPageLayout>
  );
};

export default Performance;

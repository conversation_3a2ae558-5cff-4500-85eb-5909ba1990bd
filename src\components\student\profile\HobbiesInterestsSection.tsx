import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Edit } from "lucide-react";
import EditSectionModal, { FormInput } from "@/components/student/profile/EditSectionModal";
import { StudentExtendedProfileData } from "@/pages/student/Profile";

interface HobbiesInterestsSectionProps {
  profileData: StudentExtendedProfileData;
  editData: Partial<StudentExtendedProfileData>;
  activeSectionEdit: string | null;
  setActiveSectionEdit: (section: string | null) => void;
  updateEditData: (field: string, value: any) => void;
  updateProfile: (data: Partial<StudentExtendedProfileData>) => Promise<void>;
}

const HobbiesInterestsSection: React.FC<HobbiesInterestsSectionProps> = ({
  profileData,
  editData,
  activeSectionEdit,
  setActiveSectionEdit,
  updateEditData,
  updateProfile
}) => {
  return (
    <>
      {/* Hobbies & Interests Section */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Hobbies & Interests</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setActiveSectionEdit('hobbies')}
          >
            <Edit className="h-3.5 w-3.5 mr-1" />
            Edit
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Hobbies:</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {profileData.hobbies.length > 0 ? (
                  profileData.hobbies.map((hobby, index) => (
                    <Badge key={index} variant="outline" className="bg-red-50">
                      {hobby}
                    </Badge>
                  ))
                ) : (
                  <p className="text-gray-500">No hobbies specified</p>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Interests:</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {profileData.interests.length > 0 ? (
                  profileData.interests.map((interest, index) => (
                    <Badge key={index} variant="outline" className="bg-green-50">
                      {interest}
                    </Badge>
                  ))
                ) : (
                  <p className="text-gray-500">No interests specified</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hobbies & Interests Edit Modal */}
      <EditSectionModal
        isOpen={activeSectionEdit === 'hobbies'}
        onClose={() => setActiveSectionEdit(null)}
        title="Edit Hobbies & Interests"
        onSubmit={() => {
          // Save changes
          updateProfile(editData);
          setActiveSectionEdit(null);
        }}
        layout="default"
      >
        <FormInput
          label="Hobbies"
          id="hobbies"
          placeholder="Add hobbies separated by commas"
          value={editData.hobbies?.join(', ') || ''}
          onChange={(e) => {
            const hobbiesArray = e.target.value.split(',').map(item => item.trim()).filter(Boolean);
            updateEditData("hobbies", hobbiesArray);
          }}
          helpText="Enter activities you enjoy in your free time"
        />

        <FormInput
          label="Interests"
          id="interests"
          placeholder="Add interests separated by commas"
          value={editData.interests?.join(', ') || ''}
          onChange={(e) => {
            const interestsArray = e.target.value.split(',').map(item => item.trim()).filter(Boolean);
            updateEditData("interests", interestsArray);
          }}
          helpText="Enter topics or subjects that interest you"
        />
      </EditSectionModal>
    </>
  );
};

export default HobbiesInterestsSection;

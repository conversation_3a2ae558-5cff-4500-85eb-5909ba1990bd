import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import LoginForm from "@/components/auth/LoginForm";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useToast } from "@/components/ui/UseToast";
import useScrollToTop from "@/hooks/useScrollToTop";
import { getRouteForUser } from "@/routes/RouteConfig";
import { useAuthStore } from "@/store/authStore";
import AuthErrorHandler from "@/components/auth/AuthErrorHandler";
import { UserType } from "@/context/AuthContext";
import { loginSchema, handleAuthError } from "@/services/errorHandler";
import { authErrors } from "@/constants/errorMessages";
import { useRetryOperation } from "@/hooks/useRetryOperation";

const Login = () => {
  console.log("Login component mounting");

  // Use Zustand store exclusively
  const {
    signIn,
    user,
    isInitialized,
    userType,
    isOnboarded,
    isLoading,
    loginSuccess,
    authError,
    setAuthError,
    retryAuth,
    setLoginSuccess,
    // Add the new Zustand state and actions
    formError,
    setFormError,
    clearErrors,
    emailConfirmed,
    confirmationMessage,
    confirmationError,
    clearEmailConfirmation,
    setEmailConfirmed,
    setConfirmationMessage,
    setConfirmationError,
  } = useAuthStore();

  const navigate = useNavigate();
  const { toast } = useToast();

  useScrollToTop();

  // Create a retry operation for navigation after login
  const navigationRetry = useRetryOperation(
    () => {
      // Make sure we have all required data before attempting navigation
      if (!user || userType === null || !isInitialized) {
        console.log("Navigation retry: Missing required data", {
          user: !!user,
          userType,
          isInitialized,
        });
        return false;
      }

      console.log("Navigation retry: User data is available", {
        userType,
        isOnboarded,
        userId: user.id,
      });

      // Get the appropriate route based on user type and onboarding status
      const redirectPath = getRouteForUser(userType as UserType, isOnboarded);
      console.log(`Redirecting to: ${redirectPath}`);

      // Use replace: true to prevent back button from returning to login page
      navigate(redirectPath, { replace: true });
      return true;
    },
    {
      maxAttempts: 10,
      retryInterval: 1000,
      persistKey: "login_navigation",
    }
  );

  // Handle login using Zustand actions
  const handleLogin = async (email: string, password: string) => {
    try {
      clearErrors();
      navigationRetry.reset();

      const validationResult = loginSchema.safeParse({ email, password });

      if (!validationResult.success) {
        const formattedErrors = validationResult.error.format();
        const errorMessage =
          formattedErrors.email?._errors[0] ||
          formattedErrors.password?._errors[0] ||
          authErrors.login.invalidCredentials;

        setFormError(errorMessage);
        return;
      }

      // Use Zustand signIn action
      const { error, data } = await signIn(email, password);

      if (error) {
        if (error.onboardingRequired && error.user) {
          setFormError("Please complete your profile setup to continue.");
          const userType = error.user.user_metadata?.user_type || "student";
          const redirectPath = getRouteForUser(userType as UserType, false);
          navigate(redirectPath, { state: { user: error.user } });
          return;
        }
        throw error;
      }

      console.log("Login successful, initiating navigation retry mechanism");
      setLoginSuccess(true);

      // Add a small delay before executing the navigation retry
      setTimeout(() => {
        console.log("Executing navigation retry after delay");
        navigationRetry.execute();
      }, 500);
    } catch (error: any) {
      console.error("Login error:", error);
      const errorMessage = handleAuthError(error);
      setAuthError(errorMessage);
    }
  };

  // Monitor user data changes and trigger navigation if needed
  useEffect(() => {
    if (loginSuccess && user && userType !== null && isInitialized) {
      console.log(
        "User data changed after successful login, checking navigation",
        {
          userType,
          isOnboarded,
          navigationRetryState: {
            isRetrying: navigationRetry.isRetrying,
            success: navigationRetry.success,
            error: navigationRetry.error,
          },
        }
      );

      if (!navigationRetry.success && !navigationRetry.isRetrying) {
        console.log("Executing navigation retry due to user data change");
        navigationRetry.execute();
      }
    }
  }, [user, userType, isOnboarded, isInitialized, loginSuccess]);

  // Check for email confirmation data in localStorage
  useEffect(() => {
    console.log("Checking localStorage for confirmation data");

    // Check if we're coming from email confirmation
    const confirmedFromStorage =
      localStorage.getItem("emailConfirmed") === "true";

    if (confirmedFromStorage) {
      const message =
        localStorage.getItem("confirmationMessage") ||
        "Email confirmed successfully!";

      // Use Zustand actions to update state
      setEmailConfirmed(true);
      setConfirmationMessage(message);

      // Clear localStorage
      localStorage.removeItem("emailConfirmed");
      localStorage.removeItem("confirmationMessage");

      // Show toast notification
      toast({
        title: "Email Confirmed",
        description: message,
        duration: 5000,
      });
    }

    // Check for confirmation error
    const errorFromStorage = localStorage.getItem("confirmationError");
    if (errorFromStorage) {
      // Use Zustand action to update state
      setConfirmationError(errorFromStorage);

      // Clear localStorage
      localStorage.removeItem("confirmationError");

      // Show toast notification
      toast({
        title: "Confirmation Error",
        description: errorFromStorage,
        variant: "destructive",
        duration: 5000,
      });
    }
  }, [toast]);

  // Add debugging for auth state changes
  useEffect(() => {
    console.log("Auth state in Login component:", {
      user: user ? user.id : null,
      userType,
      isOnboarded,
      isInitialized,
      loginSuccess,
    });
  }, [user, userType, isOnboarded, isInitialized, loginSuccess]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Display success message if email was confirmed */}
          {emailConfirmed && (
            <div className="mb-6 p-6 bg-gray-50 border border-green-200 rounded-lg shadow-sm">
              <p className="font-medium text-lg">
                Email confirmed successfully!
              </p>
              <p>{confirmationMessage}</p>
            </div>
          )}

          {/* Display confirmation error message if there was an issue */}
          {confirmationError && (
            <AuthErrorHandler message={confirmationError} />
          )}

          {/* Display auth error with retry option */}
          {authError && (
            <AuthErrorHandler message={authError} onRetry={retryAuth} />
          )}

          {/* Display form validation errors from Zustand */}
          {formError && <AuthErrorHandler message={formError} />}

          <LoginForm onSubmit={handleLogin} isLoading={isLoading} />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Login;

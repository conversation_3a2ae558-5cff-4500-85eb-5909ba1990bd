-- Setup OAuth Pending Metadata System
-- This creates the infrastructure for pre-OAuth metadata setup

-- =====================================================
-- STEP 1: CREATE PENDING METADATA TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS oauth_pending_metadata (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT,
  session_id TEXT, -- For cases where we don't know email yet
  user_type TEXT NOT NULL CHECK (user_type IN ('student', 'tutor')),
  provider TEXT NOT NULL DEFAULT 'google',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  processed BOOLEAN DEFAULT FALSE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  metadata JSONB DEFAULT '{}'::jsonb,
  
  -- Ensure we have either email or session_id
  CONSTRAINT oauth_pending_metadata_identifier_check 
    CHECK (email IS NOT NULL OR session_id IS NOT NULL)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_oauth_pending_email ON oauth_pending_metadata(email);
CREATE INDEX IF NOT EXISTS idx_oauth_pending_session_id ON oauth_pending_metadata(session_id);
CREATE INDEX IF NOT EXISTS idx_oauth_pending_expires ON oauth_pending_metadata(expires_at);
CREATE INDEX IF NOT EXISTS idx_oauth_pending_processed ON oauth_pending_metadata(processed);
CREATE INDEX IF NOT EXISTS idx_oauth_pending_user_id ON oauth_pending_metadata(user_id);

-- =====================================================
-- STEP 2: CREATE TRIGGER TO APPLY PENDING METADATA
-- =====================================================

CREATE OR REPLACE FUNCTION apply_pending_oauth_metadata()
RETURNS TRIGGER AS $$
DECLARE
    pending_record RECORD;
    current_metadata JSONB;
BEGIN
    -- Only for OAuth users (not email signups)
    IF NEW.raw_app_meta_data->>'provider' != 'email' AND NEW.raw_app_meta_data->>'provider' IS NOT NULL THEN
        
        -- Look for pending metadata by email first, then by session_id
        SELECT * INTO pending_record
        FROM oauth_pending_metadata
        WHERE (email = NEW.email OR session_id IS NOT NULL)
          AND NOT processed
          AND expires_at > NOW()
        ORDER BY 
          CASE WHEN email = NEW.email THEN 1 ELSE 2 END, -- Prioritize email matches
          created_at DESC
        LIMIT 1;
        
        IF FOUND THEN
            -- Get current metadata or initialize empty object
            current_metadata := COALESCE(NEW.raw_user_meta_data, '{}'::jsonb);
            
            -- Apply the pending metadata
            NEW.raw_user_meta_data := current_metadata ||
                jsonb_build_object(
                    'user_type', pending_record.user_type,
                    'onboarding_completed', false,
                    'applied_from_pending', true,
                    'pending_metadata_id', pending_record.id,
                    'oauth_setup_timestamp', extract(epoch from now())
                ) || COALESCE(pending_record.metadata, '{}'::jsonb);
            
            -- Mark as processed and update user_id
            UPDATE oauth_pending_metadata
            SET processed = TRUE,
                user_id = NEW.id,  -- Link to the newly created user
                metadata = metadata || jsonb_build_object('applied_at', NOW(), 'user_id', NEW.id)
            WHERE id = pending_record.id;
            
            RAISE LOG 'Applied pending metadata for user %: % (pending_id: %)', NEW.id, pending_record.user_type, pending_record.id;
        ELSE
            RAISE LOG 'No pending metadata found for user % with email %', NEW.id, NEW.email;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS apply_pending_oauth_metadata_trigger ON auth.users;

-- Create trigger that fires on INSERT (new user creation)
CREATE TRIGGER apply_pending_oauth_metadata_trigger
    BEFORE INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION apply_pending_oauth_metadata();

-- =====================================================
-- STEP 3: CREATE CLEANUP FUNCTION FOR EXPIRED RECORDS
-- =====================================================

CREATE OR REPLACE FUNCTION cleanup_expired_oauth_metadata()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete expired and processed records older than 1 hour
    DELETE FROM oauth_pending_metadata
    WHERE (expires_at < NOW() OR processed = TRUE)
      AND created_at < NOW() - INTERVAL '1 hour';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE LOG 'Cleaned up % expired OAuth metadata records', deleted_count;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION cleanup_expired_oauth_metadata() TO authenticated;

-- =====================================================
-- STEP 4: CREATE RLS POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE oauth_pending_metadata ENABLE ROW LEVEL SECURITY;

-- Policy 1: Users can insert their own pending metadata
CREATE POLICY "users_can_insert_pending_metadata" ON oauth_pending_metadata
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' OR auth.role() = 'service_role'
    );

-- Policy 2: Users can view their own pending metadata
CREATE POLICY "users_can_view_own_pending_metadata" ON oauth_pending_metadata
    FOR SELECT USING (
        email = (auth.jwt()::jsonb ->> 'email')::text OR
        auth.role() = 'service_role'
    );

-- Policy 3: Service role can manage all records
CREATE POLICY "service_role_can_manage_pending_metadata" ON oauth_pending_metadata
    FOR ALL USING (
        auth.role() = 'service_role'
    );

-- Policy 4: System can update processed status
CREATE POLICY "system_can_update_processed_status" ON oauth_pending_metadata
    FOR UPDATE USING (
        auth.role() = 'service_role' OR
        auth.role() = 'authenticated'
    );

-- =====================================================
-- STEP 5: CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to manually set pending metadata
CREATE OR REPLACE FUNCTION set_pending_oauth_metadata(
    p_user_type TEXT,
    p_email TEXT DEFAULT NULL,
    p_session_id TEXT DEFAULT NULL,
    p_provider TEXT DEFAULT 'google',
    p_expires_minutes INTEGER DEFAULT 10
)
RETURNS UUID AS $$
DECLARE
    new_id UUID;
BEGIN
    -- Validate input
    IF p_user_type NOT IN ('student', 'tutor') THEN
        RAISE EXCEPTION 'Invalid user_type: %. Must be student or tutor', p_user_type;
    END IF;
    
    IF p_email IS NULL AND p_session_id IS NULL THEN
        RAISE EXCEPTION 'Either email or session_id must be provided';
    END IF;
    
    -- Insert pending metadata
    INSERT INTO oauth_pending_metadata (email, session_id, user_type, provider, expires_at)
    VALUES (
        p_email,
        p_session_id,
        p_user_type,
        p_provider,
        NOW() + (p_expires_minutes || ' minutes')::INTERVAL
    )
    RETURNING id INTO new_id;
    
    RAISE LOG 'Set pending OAuth metadata: email=%, session_id=%, user_type=% (id: %)', p_email, p_session_id, p_user_type, new_id;
    RETURN new_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION set_pending_oauth_metadata(TEXT, TEXT, TEXT, TEXT, INTEGER) TO authenticated;

-- Function to check pending metadata
CREATE OR REPLACE FUNCTION get_pending_oauth_metadata(
    p_email TEXT DEFAULT NULL,
    p_session_id TEXT DEFAULT NULL
)
RETURNS TABLE(
    id UUID,
    email TEXT,
    session_id TEXT,
    user_type TEXT,
    provider TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    processed BOOLEAN
) AS $$
BEGIN
    IF p_email IS NULL AND p_session_id IS NULL THEN
        RAISE EXCEPTION 'Either email or session_id must be provided';
    END IF;
    
    RETURN QUERY
    SELECT 
        pom.id,
        pom.email,
        pom.session_id,
        pom.user_type,
        pom.provider,
        pom.created_at,
        pom.expires_at,
        pom.processed
    FROM oauth_pending_metadata pom
    WHERE (p_email IS NOT NULL AND pom.email = p_email)
       OR (p_session_id IS NOT NULL AND pom.session_id = p_session_id)
      AND pom.expires_at > NOW()
    ORDER BY pom.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_pending_oauth_metadata(TEXT, TEXT) TO authenticated;

-- =====================================================
-- STEP 6: TESTING FUNCTIONS
-- =====================================================

-- Function to test the system
CREATE OR REPLACE FUNCTION test_oauth_pending_system()
RETURNS TEXT AS $$
DECLARE
    test_session_id TEXT := 'test_session_' || extract(epoch from now());
    test_id UUID;
    result_record RECORD;
BEGIN
    -- Test setting pending metadata with session_id
    SELECT set_pending_oauth_metadata('tutor', NULL, test_session_id, 'google', 5) INTO test_id;
    
    -- Test retrieving pending metadata
    SELECT * INTO result_record FROM get_pending_oauth_metadata(NULL, test_session_id) LIMIT 1;
    
    -- Clean up test data
    DELETE FROM oauth_pending_metadata WHERE session_id = test_session_id;
    
    IF result_record.user_type = 'tutor' THEN
        RETURN 'OAuth pending metadata system is working correctly';
    ELSE
        RETURN 'OAuth pending metadata system test failed';
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION test_oauth_pending_system() TO authenticated;

-- =====================================================
-- STEP 7: GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON oauth_pending_metadata TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Grant permissions to service role
GRANT ALL ON oauth_pending_metadata TO service_role;

-- =====================================================
-- USAGE EXAMPLES AND TESTING
-- =====================================================

/*
-- Test the system
SELECT test_oauth_pending_system();

-- Set pending metadata manually with session_id
SELECT set_pending_oauth_metadata('tutor', NULL, 'session_123', 'google', 10);

-- Set pending metadata manually with email
SELECT set_pending_oauth_metadata('student', '<EMAIL>', NULL, 'google', 10);

-- Check pending metadata by session_id
SELECT * FROM get_pending_oauth_metadata(NULL, 'session_123');

-- Check pending metadata by email
SELECT * FROM get_pending_oauth_metadata('<EMAIL>', NULL);

-- Clean up expired records
SELECT cleanup_expired_oauth_metadata();

-- View all pending metadata (admin only)
SELECT * FROM oauth_pending_metadata WHERE NOT processed;

-- Check if trigger exists
SELECT trigger_name, event_manipulation, action_timing 
FROM information_schema.triggers 
WHERE trigger_name = 'apply_pending_oauth_metadata_trigger';
*/

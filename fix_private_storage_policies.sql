-- =====================================================
-- FIX PRIVATE STORAGE BUCKET POLICIES
-- =====================================================
-- This script removes public access from private buckets and ensures
-- only authenticated users can access their own files and other users'
-- files as appropriate for the application's security requirements.

-- =====================================================
-- STEP 1: DROP INSECURE PUBLIC POLICIES
-- =====================================================

-- Remove public access policies that should not exist for private buckets
DROP POLICY IF EXISTS "Public can view student uploads" ON storage.objects;
DROP POLICY IF EXISTS "Public can view tutor uploads" ON storage.objects;
DROP POLICY IF EXISTS "Public can view admin uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to student-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access" ON storage.objects;

-- =====================================================
-- STEP 2: ENSURE BUCKETS ARE PROPERLY CONFIGURED
-- =====================================================

-- Ensure student-uploads bucket is private
UPDATE storage.buckets 
SET public = false 
WHERE id = 'student-uploads';

-- Ensure tutor-uploads bucket is private
UPDATE storage.buckets 
SET public = false 
WHERE id = 'tutor-uploads';

-- Ensure admin-uploads bucket is private
UPDATE storage.buckets 
SET public = false 
WHERE id = 'admin-uploads';

-- Keep avatars bucket public if it exists (this can be public for general avatars)
UPDATE storage.buckets 
SET public = true 
WHERE id = 'avatars';

-- =====================================================
-- STEP 3: CREATE SECURE AUTHENTICATED-ONLY POLICIES
-- =====================================================

-- STUDENT-UPLOADS POLICIES
-- Allow authenticated users to view student uploads (for profile pictures, etc.)
CREATE POLICY "Authenticated users can view student uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Allow students to upload to their own folder (profiles and gallery)
CREATE POLICY "Students can upload their own files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'gallery') AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Allow students to update their own files (profiles and gallery)
CREATE POLICY "Students can update their own files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'gallery') AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Allow students to delete their own files (profiles and gallery)
CREATE POLICY "Students can delete their own files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'gallery') AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- TUTOR-UPLOADS POLICIES
-- Allow authenticated users to view tutor uploads (for profile pictures, etc.)
CREATE POLICY "Authenticated users can view tutor uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated'
    );

-- Allow tutors to upload to their own folders
CREATE POLICY "Tutors can upload their own files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- Allow tutors to update their own files
CREATE POLICY "Tutors can update their own files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- Allow tutors to delete their own files
CREATE POLICY "Tutors can delete their own files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'tutor-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'tutor'
        )
    );

-- ADMIN-UPLOADS POLICIES
-- Allow authenticated users to view admin uploads (for profile pictures, etc.)
CREATE POLICY "Authenticated users can view admin uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated'
    );

-- Allow admins to upload to their own folders
CREATE POLICY "Admins can upload their own files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Allow admins to update their own files
CREATE POLICY "Admins can update their own files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Allow admins to delete their own files
CREATE POLICY "Admins can delete their own files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'admin-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] IN ('profiles', 'cv') AND
        (storage.foldername(name))[2] = auth.uid()::text AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- STEP 4: ADMIN OVERRIDE POLICIES (for moderation)
-- =====================================================

-- Allow admins to manage all files across all buckets (for moderation)
CREATE POLICY "Admins can manage all storage files" ON storage.objects
    FOR ALL USING (
        bucket_id IN ('student-uploads', 'tutor-uploads', 'admin-uploads') AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- STEP 5: VERIFICATION
-- =====================================================

-- Check bucket configurations
SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id IN ('student-uploads', 'tutor-uploads', 'admin-uploads', 'avatars')
ORDER BY id;

-- Check policies
SELECT 
    policyname,
    cmd as command,
    permissive,
    roles
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects'
AND policyname LIKE '%student%' OR policyname LIKE '%tutor%' OR policyname LIKE '%admin%'
ORDER BY policyname;

-- =====================================================
-- NOTES
-- =====================================================

/*
This script ensures:

1. SECURITY: No public access to private user data
   - student-uploads: Only authenticated users can view, only owners can modify
   - tutor-uploads: Only authenticated users can view, only owners can modify  
   - admin-uploads: Only authenticated users can view, only owners can modify

2. FUNCTIONALITY: Authenticated users can view profile pictures
   - This allows the app to display user profile pictures to other authenticated users
   - Users can only modify their own files
   - Admins have override access for moderation

3. PRIVACY: Proper folder structure enforcement
   - Student files must be in profiles/{user_id}/ or gallery/{user_id}/ folders
   - Tutor/Admin files must be in profiles/{user_id}/ or cv/{user_id}/ folders
   - Users can only access their own folders for modifications

After running this script:
- The AuthenticatedImage component will work correctly with signed URLs
- No public access to private user data
- Authenticated users can view each other's profile pictures
- Users can only modify their own files
*/

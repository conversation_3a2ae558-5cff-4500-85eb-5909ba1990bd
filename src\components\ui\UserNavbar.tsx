import React, { ReactNode } from "react";
import { Button } from "@/components/ui/Button";
import UserProfileMenu from "@/components/UserProfileMenu";
import HorizontalDivider from "./HorizontalDivider";

interface UserNavbarProps {
  title: string;
  isAdmin?: boolean;
  isAdminPage?: boolean;
  actions?: ReactNode;
  className?: string;
}

/**
 * A generic navbar component for user interfaces that can be used across all user types
 * (admin, student, tutor) and their respective pages.
 */
const UserNavbar: React.FC<UserNavbarProps> = ({
  title,
  isAdmin = false,
  isAdminPage = false,
  actions,
  className = "",
}) => {
  return (
    <header className={`bg-white relative ${className}`}>
      <div className="px-4 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">{title}</h1>
            {actions}
          </div>
          <div className="flex items-center space-x-4">
            <UserProfileMenu
              isAdmin={isAdmin}
              isAdminPage={isAdminPage}
            />
          </div>
        </div>
      </div>
      <HorizontalDivider noMargin={true} />
    </header>
  );
};

export default UserNavbar;

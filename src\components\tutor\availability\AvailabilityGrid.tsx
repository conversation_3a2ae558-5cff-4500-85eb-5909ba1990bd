import React, { useEffect, useRef } from "react";
import { useTutorAvailabilityStore } from "@/store/tutorAvailabilityStore";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { CheckCircle, Settings, Clock } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";

// Time slots for the grid (30-minute intervals)
const timeSlots = Array.from({ length: 24 * 2 }, (_, i) => {
  const hour = Math.floor(i / 2);
  const minute = i % 2 === 0 ? "00" : "30";
  return `${hour.toString().padStart(2, "0")}:${minute}`;
});

// Days of the week
const daysOfWeek = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

interface AvailabilityGridProps {
  // No props needed for now
}

const AvailabilityGrid: React.FC<AvailabilityGridProps> = () => {
  // Create a ref to track newly created slots to prevent immediate status cycling
  const newlyCreatedSlotRef = useRef<string | null>(null);

  // Get all the state and actions from the Zustand store
  const {
    availabilitySlots,
    updateAvailabilitySlot,
    deleteAvailabilitySlot,
    addAvailabilitySlot,
    startHour,
    endHour,
    isDragging,
    dragStart,
    dragEnd,
    slotStatus,
    setStartHour,
    setEndHour,
    setIsDragging,
    setDragStart,
    setDragEnd,
    setSlotStatus
  } = useTutorAvailabilityStore();

  // Filter time slots based on selected start and end hours
  const filteredTimeSlots = timeSlots.filter((time) => {
    const hour = parseInt(time.split(":")[0]);
    const endHourInt = parseInt(endHour);

    // If end hour is 24, include all hours from start hour up to 23:30
    if (endHourInt === 24) {
      return hour >= parseInt(startHour);
    }

    // Normal case: include hours between start (inclusive) and end (exclusive)
    return hour >= parseInt(startHour) && hour < endHourInt;
  });

  // Check if a cell is part of an availability slot
  const getSlotForCell = (day: string, time: string) => {
    const matchingSlot = availabilitySlots.find((slot) => {
      const slotStartHour = parseInt(slot.startTime.split(":")[0]);
      const slotStartMinute = parseInt(slot.startTime.split(":")[1]);
      const slotEndHour = parseInt(slot.endTime.split(":")[0]);
      const slotEndMinute = parseInt(slot.endTime.split(":")[1]);

      const cellHour = parseInt(time.split(":")[0]);
      const cellMinute = parseInt(time.split(":")[1]);

      const cellTime = cellHour * 60 + cellMinute;
      const slotStartTime = slotStartHour * 60 + slotStartMinute;
      const slotEndTime = slotEndHour * 60 + slotEndMinute;

      const isMatch = slot.day === day && cellTime >= slotStartTime && cellTime < slotEndTime;

      if (isMatch) {
        console.log(`Cell ${day} ${time} matches slot:`, {
          slotId: slot.id,
          slotDay: slot.day,
          slotTime: `${slot.startTime}-${slot.endTime}`,
          slotStatus: slot.status
        });
      }

      return isMatch;
    });

    return matchingSlot;
  };

  // Note: We don't need this function anymore as we're using inline checks
  // with `const isContinuation = slot && !isSlotStart;`

  // We no longer need global refs for event handlers since we're using inline handlers
  // that capture the current state

  // Handle mouse down on a cell
  const handleMouseDown = (day: string, time: string) => {
    // Check if there's already a slot at this position
    const existingSlot = getSlotForCell(day, time);
    if (existingSlot) {
      console.log(`Mouse down on existing slot: day=${day}, time=${time}`);
      return;
    }

    console.log(`Mouse down on empty cell: day=${day}, time=${time}`);

    // Set up for potential dragging
    setIsDragging(true);
    setDragStart({ day, time });
    setDragEnd({ day, time });

    // Track the current drag state
    let dragStartTime = time;
    let dragEndTime = time;
    let isDraggingNow = true;

    // Define inline handlers that use the current drag state
    const moveHandler = (e: MouseEvent) => {
      if (!isDraggingNow) return;

      // Find the element under the cursor
      const elementUnderCursor = document.elementFromPoint(e.clientX, e.clientY);
      if (!elementUnderCursor) return;

      // Check if the element has data attributes for day and time
      const cellElement = elementUnderCursor.closest('[data-day][data-time]');
      if (cellElement) {
        const cellDay = cellElement.getAttribute('data-day');
        const cellTime = cellElement.getAttribute('data-time');

        if (cellDay && cellTime && cellDay === day) {
          console.log(`Move handler - updating drag end: day=${cellDay}, time=${cellTime}`);
          dragEndTime = cellTime;
          setDragEnd({ day: cellDay, time: cellTime });
          e.preventDefault();
        }
      }
    };

    const upHandler = () => {
      // Get the current slot status at the time of the mouse up event
      const currentSlotStatus = slotStatus;

      console.log("Up handler called with drag state:", {
        isDragging: isDraggingNow,
        startTime: dragStartTime,
        endTime: dragEndTime,
        day,
        slotStatus: currentSlotStatus // Log the current slot status
      });

      // Remove the event listeners
      document.removeEventListener('mousemove', moveHandler);
      document.removeEventListener('mouseup', upHandler);

      // Only proceed if we're still dragging
      if (isDraggingNow) {
        // Create a slot directly without using React state
        // This ensures we use the most up-to-date values
        console.log("Creating slot directly with:", {
          day,
          startTime: dragStartTime,
          endTime: dragEndTime,
          status: currentSlotStatus // Log the status being used
        });

        // Calculate start and end times in minutes
        const startMinutes = parseInt(dragStartTime.split(":")[0]) * 60 + parseInt(dragStartTime.split(":")[1]);
        const endMinutes = parseInt(dragEndTime.split(":")[0]) * 60 + parseInt(dragEndTime.split(":")[1]);

        // Ensure start time is before end time
        let finalStartTime = dragStartTime;
        let finalEndTime = dragEndTime;

        if (startMinutes > endMinutes) {
          finalStartTime = dragEndTime;
          finalEndTime = dragStartTime;
        }

        // Add 30 minutes to end time
        const endHour = parseInt(finalEndTime.split(":")[0]);
        const endMinute = parseInt(finalEndTime.split(":")[1]);

        // Special handling for 23:30 slot - set end time to 24:00
        let adjustedEndTime: string;
        if (endHour === 23 && endMinute === 30) {
          console.log("Creating special slot ending at 24:00");
          adjustedEndTime = "24:00";
        } else {
          // Normal case - calculate end time (30 minutes after start time)
          const newEndMinute = endMinute + 30;
          const newEndHour = endHour + Math.floor(newEndMinute / 60);
          adjustedEndTime = `${newEndHour.toString().padStart(2, "0")}:${(newEndMinute % 60).toString().padStart(2, "0")}`;
        }

        // Create the slot directly with the captured slot status
        // Force the status to be the correct type
        const slotToAdd = {
          day,
          startTime: finalStartTime,
          endTime: adjustedEndTime,
          status: currentSlotStatus as "available" | "auto_accept" | "manual_approval"
        };

        console.log("About to add slot with explicit status:", slotToAdd);

        // Add the slot and get the new slot ID from the store (now async)
        addAvailabilitySlot(slotToAdd)
          .then((newSlotId) => {
            // Set the newly created slot ID in the ref to prevent immediate status cycling
            newlyCreatedSlotRef.current = newSlotId;
            console.log("Slot created successfully with status:", currentSlotStatus, "ID:", newSlotId);
          })
          .catch((error) => {
            console.error("Failed to create slot:", error);
          });
      }

      // Reset the drag state
      isDraggingNow = false;
      setIsDragging(false);
      setDragStart(null);
      setDragEnd(null);
    };

    // Add event listeners using the inline handlers
    document.addEventListener('mousemove', moveHandler);
    document.addEventListener('mouseup', upHandler);
  };

  // Handle mouse enter on a cell during drag
  const handleMouseEnter = (day: string, time: string) => {
    // This is now handled by the moveHandler in handleMouseDown
    // We'll keep this for compatibility but it's not the primary drag mechanism anymore
    if (isDragging && dragStart && dragStart.day === day) {
      console.log(`Mouse entered cell via onMouseEnter: day=${day}, time=${time}`);
      setDragEnd({ day, time });
    }
  };

  // Handle clicking on an existing slot
  const handleSlotClick = (e: React.MouseEvent, slotId: string) => {
    // Stop event propagation to prevent triggering handleMouseDown
    e.stopPropagation();
    e.preventDefault();

    // Always reset any drag operation when clicking on a slot
    setIsDragging(false);
    setDragStart(null);
    setDragEnd(null);

    // Check if this is a newly created slot that we should ignore for status cycling
    if (newlyCreatedSlotRef.current === slotId) {
      console.log("Ignoring click on newly created slot:", slotId);
      // Clear the ref so future clicks will work normally
      newlyCreatedSlotRef.current = null;
      return;
    }

    // We don't need to remove event listeners here anymore
    // since we're using inline handlers that are scoped to each drag operation

    const slot = availabilitySlots.find((s) => s.id === slotId);
    if (slot) {
      // Cycle through statuses: available -> auto_accept -> manual_approval -> available
      const statusMap: Record<string, "available" | "auto_accept" | "manual_approval"> = {
        "available": "auto_accept",
        "auto_accept": "manual_approval",
        "manual_approval": "available",
      };

      console.log("Updating slot status:", slotId, statusMap[slot.status]);
      updateAvailabilitySlot(slotId, { status: statusMap[slot.status] })
        .catch((error) => {
          console.error("Failed to update slot status:", error);
        });
    }
  };

  // Handle deleting a slot
  const handleDeleteSlot = (e: React.MouseEvent, slotId: string) => {
    e.stopPropagation();
    e.preventDefault();

    // Always reset any drag operation when deleting a slot
    setIsDragging(false);
    setDragStart(null);
    setDragEnd(null);

    // We don't need to remove event listeners here anymore
    // since we're using inline handlers that are scoped to each drag operation

    console.log("Deleting slot:", slotId); // Debug log
    deleteAvailabilitySlot(slotId)
      .catch((error) => {
        console.error("Failed to delete slot:", error);
      });
  };

  // Get status color for a slot
  const getStatusColor = (status: string, isContinuation: boolean = false) => {
    console.log("Getting color for status:", status, isContinuation ? "(continuation cell)" : "");

    // For continuation cells, use a lighter background color
    const intensity = isContinuation ? "50" : "100";
    const borderIntensity = isContinuation ? "200" : "300";

    switch (status) {
      case "available":
        return `bg-green-${intensity} ${!isContinuation ? `border-green-${borderIntensity} text-green-800` : ""}`;
      case "auto_accept":
        return `bg-blue-${intensity} ${!isContinuation ? `border-blue-${borderIntensity} text-blue-800` : ""}`;
      case "manual_approval":
        return `bg-yellow-${intensity} ${!isContinuation ? `border-yellow-${borderIntensity} text-yellow-800` : ""}`;
      default:
        console.log("Unknown status, using default color:", status);
        return `bg-gray-${intensity} ${!isContinuation ? `border-gray-${borderIntensity} text-gray-800` : ""}`;
    }
  };

  // Get status icon for a slot
  const getStatusIcon = (status: string, isContinuation: boolean = false) => {
    // Make icons slightly larger for continuation cells for better visibility
    const size = isContinuation ? 4 : 3;
    const marginRight = isContinuation ? 0 : 1;

    switch (status) {
      case "available":
        return <CheckCircle className={`h-${size} w-${size} ${marginRight ? `mr-${marginRight}` : ''}`} />;
      case "auto_accept":
        return <Settings className={`h-${size} w-${size} ${marginRight ? `mr-${marginRight}` : ''}`} />;
      case "manual_approval":
        return <Clock className={`h-${size} w-${size} ${marginRight ? `mr-${marginRight}` : ''}`} />;
      default:
        return null;
    }
  };

  // Clean up event listeners when component unmounts
  useEffect(() => {
    return () => {
      console.log("Cleaning up on component unmount");
      // No global event listeners to clean up anymore
      // Each drag operation manages its own event listeners
    };
  }, []);

  return (
    <div className="space-y-4">
      <div className="relative mb-16">
        {/* Legend positioned at top right */}
        <div className="absolute -top-20 right-0 flex flex-col space-y-2 border border-gray-200 rounded-md p-3 bg-white shadow-sm z-10">
          <div className="text-sm font-medium mb-1">Slot Type</div>
          <div className="flex items-center">
            <Badge className="bg-green-100 border-green-300 text-green-800 mr-2">
              <CheckCircle className="h-3 w-3 mr-1" /> Available
            </Badge>
            <span className="text-sm text-gray-500">Open for bookings</span>
          </div>
          <div className="flex items-center">
            <Badge className="bg-blue-100 border-blue-300 text-blue-800 mr-2">
              <Settings className="h-3 w-3 mr-1" /> Auto-Accept
            </Badge>
            <span className="text-sm text-gray-500">Auto-accept if rules match</span>
          </div>
          <div className="flex items-center">
            <Badge className="bg-yellow-100 border-yellow-300 text-yellow-800 mr-2">
              <Clock className="h-3 w-3 mr-1" /> Manual
            </Badge>
            <span className="text-sm text-gray-500">Manual approval required</span>
          </div>
        </div>

        {/* Main controls */}
        <div className="flex flex-wrap gap-4 items-center">
          <div>
            <label className="block text-sm font-medium mb-1">View Hours</label>
            <div className="flex items-center space-x-2">
              <Select value={startHour} onValueChange={setStartHour}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="Start" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 24 }, (_, i) => (
                    <SelectItem key={i} value={i.toString().padStart(2, "0")}>
                      {i.toString().padStart(2, "0")}:00
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <span>to</span>
              <Select value={endHour} onValueChange={setEndHour}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="End" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 25 }, (_, i) => (
                    <SelectItem key={i} value={i.toString().padStart(2, "0")}>
                      {i === 24 ? "24:00" : `${i.toString().padStart(2, "0")}:00`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">New Slot Type</label>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant={slotStatus === "available" ? "default" : "outline"}
                className={slotStatus === "available" ? "bg-green-500" : ""}
                onClick={() => setSlotStatus("available")}
              >
                <CheckCircle className="h-4 w-4 mr-1" /> Available
              </Button>
              <Button
                size="sm"
                variant={slotStatus === "auto_accept" ? "default" : "outline"}
                className={slotStatus === "auto_accept" ? "bg-blue-500" : ""}
                onClick={() => setSlotStatus("auto_accept")}
              >
                <Settings className="h-4 w-4 mr-1" /> Auto-Accept
              </Button>
              <Button
                size="sm"
                variant={slotStatus === "manual_approval" ? "default" : "outline"}
                className={slotStatus === "manual_approval" ? "bg-yellow-500" : ""}
                onClick={() => setSlotStatus("manual_approval")}
              >
                <Clock className="h-4 w-4 mr-1" /> Manual
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-max">
          <div className="grid grid-cols-[100px_repeat(7,1fr)]">
            {/* Header row with days */}
            <div className="bg-gray-100 p-2 font-medium border-b border-r"></div>
            {daysOfWeek.map((day) => (
              <div
                key={day}
                className="bg-gray-100 p-2 font-medium text-center border-b border-r"
              >
                {day}
              </div>
            ))}

            {/* Time slots rows */}
            {filteredTimeSlots.map((time) => (
              <React.Fragment key={time}>
                {/* Time label */}
                <div className="p-2 text-xs text-gray-500 border-b border-r">
                  {time}
                </div>

                {/* Day cells */}
                {daysOfWeek.map((day) => {
                  const slot = getSlotForCell(day, time);
                  // Fix time format comparison - database returns HH:MM:SS, grid uses HH:MM
                  const normalizeTime = (timeStr: string) => timeStr.substring(0, 5); // Get HH:MM part
                  const isSlotStart = slot && normalizeTime(slot.startTime) === time;
                  const isContinuation = slot && !isSlotStart;

                  return (
                    <div
                      key={`${day}-${time}`}
                      data-day={day}
                      data-time={time}
                      className={`border-b border-r h-10 relative ${
                        // Apply slot background color if this is a continuation cell
                        isContinuation
                          ? getStatusColor(slot.status, true)
                          : ""
                      } ${
                        // Only apply gray background if:
                        // 1. We're actively dragging
                        // 2. This cell is in the same day as the drag operation
                        // 3. This cell is between the start and end of the drag
                        // 4. This cell doesn't already have a slot
                        isDragging &&
                        dragStart &&
                        dragEnd &&
                        !slot && // Don't highlight cells that already have a slot
                        dragStart.day === day &&
                        dragStart.day === dragEnd.day &&
                        ((time >= dragStart.time && time <= dragEnd.time) ||
                          (time >= dragEnd.time && time <= dragStart.time))
                          ? "bg-gray-200"
                          : ""
                      }`}
                      onClick={(e) => {
                        if (slot) {
                          // If there's already a slot, handle clicking on it
                          handleSlotClick(e, slot.id);
                        } else {
                          // If there's no slot, create one immediately
                          // Capture the current slot status to ensure we use the correct value
                          const currentSlotStatus = slotStatus;
                          console.log("Creating slot on click with status:", currentSlotStatus);

                          // Calculate end time (30 minutes after start time)
                          const startHour = parseInt(time.split(":")[0]);
                          const startMinute = parseInt(time.split(":")[1]);

                          // Special handling for 23:30 slot - set end time to 24:00
                          let endTime: string;
                          if (startHour === 23 && startMinute === 30) {
                            console.log("Creating special slot ending at 24:00");
                            endTime = "24:00";
                          } else {
                            // Normal case - calculate end time (30 minutes after start time)
                            const newEndMinute = startMinute + 30;
                            const newEndHour = startHour + Math.floor(newEndMinute / 60);
                            endTime = `${newEndHour.toString().padStart(2, "0")}:${(newEndMinute % 60).toString().padStart(2, "0")}`;
                          }

                          // Add the slot directly with the captured slot status
                          // Force the status to be the correct type
                          const slotToAdd = {
                            day,
                            startTime: time,
                            endTime,
                            status: currentSlotStatus as "available" | "auto_accept" | "manual_approval"
                          };

                          console.log("About to add slot with explicit status (click):", slotToAdd);

                          // Add the slot and get the new slot ID from the store (now async)
                          addAvailabilitySlot(slotToAdd)
                            .then((newSlotId) => {
                              // Set the newly created slot ID in the ref to prevent immediate status cycling
                              newlyCreatedSlotRef.current = newSlotId;
                              console.log("Slot created successfully with status:", currentSlotStatus, "ID:", newSlotId);
                            })
                            .catch((error) => {
                              console.error("Failed to create slot:", error);
                            });

                          // Make sure to reset any drag states
                          setIsDragging(false);
                          setDragStart(null);
                          setDragEnd(null);
                        }
                      }}
                      onMouseDown={() => {
                        // Only start dragging if we're not clicking on an existing slot
                        if (!slot) {
                          handleMouseDown(day, time);
                        }
                      }}
                      onMouseEnter={() => handleMouseEnter(day, time)}
                    >
                      {/* Show detailed information only for the start cell of each slot */}
                      {slot && isSlotStart && (
                        <div
                          className={`absolute top-0 left-0 right-0 z-10 px-1 py-0.5 text-xs flex items-center justify-between ${getStatusColor(
                            slot.status
                          )}`}
                          data-status={slot.status} // Add data attribute for debugging
                        >
                          <span className="flex items-center">
                            {getStatusIcon(slot.status, false)}
                            {normalizeTime(slot.startTime)} - {normalizeTime(slot.endTime)} ({slot.status})
                          </span>
                          <button
                            type="button"
                            className="bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-800 font-bold text-lg rounded-full w-5 h-5 flex items-center justify-center leading-none"
                            onClick={(e) => {
                              e.stopPropagation();
                              console.log("Delete button clicked for slot:", slot.id);
                              handleDeleteSlot(e, slot.id);
                            }}
                          >
                            ×
                          </button>
                        </div>
                      )}

                      {/* For continuation cells, show the status icon */}
                      {isContinuation && (
                        <div
                          className="w-full h-full flex items-center justify-center cursor-pointer"
                          onClick={(e) => {
                            // Make sure clicking on the indicator also triggers the slot click handler
                            e.stopPropagation();
                            handleSlotClick(e, slot.id);
                          }}
                        >
                          <div className="flex items-center text-current opacity-70">
                            {getStatusIcon(slot.status, true)}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>


    </div>
  );
};

export default AvailabilityGrid;

-- Test script to verify the trigger fix works correctly
-- This script tests both INSERT and UPDATE scenarios

-- First, let's check the current state of triggers
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition
FROM information_schema.triggers 
WHERE trigger_name IN ('on_student_candidate_completed', 'on_student_candidate_updated')
ORDER BY trigger_name;

-- Test 1: INSERT scenario - Insert a new candidate with onboarding_completed = true
-- This should trigger the INSERT trigger
INSERT INTO candidate_student (
    id,
    first_name,
    last_name,
    email,
    phone,
    date_of_birth,
    education_level,
    onboarding_completed,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'Test',
    'Student',
    '<EMAIL>',
    '+1234567890',
    '2000-01-01',
    'high_school',
    true,  -- This should trigger the INSERT trigger
    NOW(),
    NOW()
);

-- Check if a student record was created for the INSERT test
SELECT 
    cs.email,
    cs.onboarding_completed,
    s.id as student_id,
    s.created_at as student_created_at
FROM candidate_student cs
LEFT JOIN students s ON s.candidate_id = cs.id
WHERE cs.email = '<EMAIL>';

-- Test 2: UPDATE scenario - Insert a candidate with onboarding_completed = false, then update to true
-- First insert with onboarding_completed = false
INSERT INTO candidate_student (
    id,
    first_name,
    last_name,
    email,
    phone,
    date_of_birth,
    education_level,
    onboarding_completed,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'Test',
    'Student2',
    '<EMAIL>',
    '+1234567891',
    '2000-01-01',
    'high_school',
    false,  -- Start with false
    NOW(),
    NOW()
);

-- Now update to true - this should trigger the UPDATE trigger
UPDATE candidate_student 
SET onboarding_completed = true, updated_at = NOW()
WHERE email = '<EMAIL>';

-- Check if a student record was created for the UPDATE test
SELECT 
    cs.email,
    cs.onboarding_completed,
    s.id as student_id,
    s.created_at as student_created_at
FROM candidate_student cs
LEFT JOIN students s ON s.candidate_id = cs.id
WHERE cs.email = '<EMAIL>';

-- Clean up test data
DELETE FROM students WHERE candidate_id IN (
    SELECT id FROM candidate_student 
    WHERE email IN ('<EMAIL>', '<EMAIL>')
);

DELETE FROM candidate_student 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Final verification: Check that both triggers exist and are properly configured
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition,
    'Trigger should fire on INSERT when onboarding_completed = true' as insert_trigger_note
FROM information_schema.triggers 
WHERE trigger_name = 'on_student_candidate_completed'

UNION ALL

SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition,
    'Trigger should fire on UPDATE when onboarding_completed changes from false to true' as update_trigger_note
FROM information_schema.triggers 
WHERE trigger_name = 'on_student_candidate_updated';

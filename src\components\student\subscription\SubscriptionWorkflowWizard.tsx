import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Progress } from "@/components/ui/Progress";
import { Alert, AlertDescription } from "@/components/ui/Alert";
import {
  CheckCircle,
  Circle,
  ArrowRight,
  ArrowLeft,
  Package,
  BookOpen,
  Calculator,
  CreditCard,
  HelpCircle,
  Clock,
  User
} from "lucide-react";
import { useSubscriptionWorkflowStore } from "@/store/subscriptionWorkflowStore";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/useToast";

// Step components
import ProductSelectionStep from "./steps/ProductSelectionStep";

interface SubscriptionWorkflowWizardProps {
  onComplete?: () => void;
  onCancel?: () => void;
}

const SubscriptionWorkflowWizard: React.FC<SubscriptionWorkflowWizardProps> = ({
  onComplete,
  onCancel
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const {
    currentWorkflow,
    isLoading,
    error,
    getActiveWorkflow,
    createWorkflow,
    resetWorkflow,
    goToStep,
    requestAdminAssistance
  } = useSubscriptionWorkflowStore();

  const [adminAssistanceMessage, setAdminAssistanceMessage] = useState("");
  const [showAdminAssistanceDialog, setShowAdminAssistanceDialog] = useState(false);

  // Load or create workflow on component mount
  useEffect(() => {
    console.log('SubscriptionWorkflowWizard useEffect triggered, user?.id:', user?.id);

    const initializeWorkflow = async () => {
      if (!user?.id) {
        console.log('No user ID, skipping workflow initialization');
        return;
      }

      console.log('Starting workflow initialization for user:', user.id);

      // Try to get existing active workflow
      const existingWorkflow = await getActiveWorkflow(user.id);

      console.log('getActiveWorkflow completed, result:', existingWorkflow);

      if (!existingWorkflow) {
        console.log('No active workflow found, calling resetWorkflow');
        // No active workflow, user needs to start a new one
        resetWorkflow();
      }
    };

    initializeWorkflow();
  }, [user?.id]); // Remove function dependencies to prevent re-runs

  // Step configuration
  const steps = [
    {
      number: 1,
      title: "Select Package",
      description: "Choose your learning package",
      icon: Package,
      required: true
    },
    {
      number: 2,
      title: "Configure Curriculum",
      description: "Select topics and subtopics",
      icon: BookOpen,
      required: (productType: string) => productType === 'custom' || productType === 'preparation'
    },
    {
      number: 3,
      title: "Price Calculation",
      description: "Review pricing details",
      icon: Calculator,
      required: true
    },
    {
      number: 4,
      title: "Purchase",
      description: "Complete your subscription",
      icon: CreditCard,
      required: true
    }
  ];

  // Calculate progress percentage
  const getProgressPercentage = () => {
    if (!currentWorkflow) return 0;

    const completedSteps = [
      currentWorkflow.step_completions.step_1_completed,
      currentWorkflow.step_completions.step_2_completed ||
        (currentWorkflow.product_type === 'booster'), // Step 2 not required for booster
      currentWorkflow.step_completions.step_3_completed,
      currentWorkflow.step_completions.step_4_completed
    ].filter(Boolean).length;

    return (completedSteps / 4) * 100;
  };

  // Handle step navigation
  const handleStepClick = (stepNumber: number) => {
    if (!currentWorkflow) return;

    // Allow navigation to completed steps or current step
    const canNavigate = stepNumber <= currentWorkflow.current_step ||
                       currentWorkflow.step_completions[`step_${stepNumber}_completed` as keyof typeof currentWorkflow.step_completions];

    if (canNavigate) {
      goToStep(stepNumber);
    }
  };

  // Handle admin assistance request
  const handleRequestAdminAssistance = async () => {
    if (!currentWorkflow || !adminAssistanceMessage.trim()) return;

    const success = await requestAdminAssistance(currentWorkflow.id, adminAssistanceMessage);

    if (success) {
      toast({
        title: "Admin Assistance Requested",
        description: "An admin will help you with your subscription configuration.",
        variant: "default"
      });
      setShowAdminAssistanceDialog(false);
      setAdminAssistanceMessage("");
    } else {
      toast({
        title: "Request Failed",
        description: "Failed to request admin assistance. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle new workflow creation
  const handleStartNewWorkflow = async (productType: 'booster' | 'custom' | 'preparation') => {
    if (!user?.id) return;

    const workflowId = await createWorkflow(user.id, productType);

    if (workflowId) {
      toast({
        title: "Subscription Process Started",
        description: `Started ${productType} subscription workflow`,
        variant: "default"
      });
    }
  };

  // Render rich visual step indicator
  const renderStepIndicator = () => {
    if (!currentWorkflow) return null;

    const visibleSteps = steps.filter(step => {
      // Skip step 2 for booster packages
      if (step.number === 2 && currentWorkflow.product_type === 'booster') {
        return false;
      }
      return true;
    });

    return (
      <div className="mb-12">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {currentWorkflow.product_type.charAt(0).toUpperCase() + currentWorkflow.product_type.slice(1)} Subscription
          </h1>
          <p className="text-lg text-gray-600">
            Complete your learning journey in {visibleSteps.length} simple steps
          </p>
          <Badge
            variant={currentWorkflow.status === 'completed' ? 'default' : 'secondary'}
            className="mt-2 px-4 py-1 text-sm"
          >
            {currentWorkflow.status.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Progress</span>
            <span>{Math.round(getProgressPercentage())}% Complete</span>
          </div>
          <Progress value={getProgressPercentage()} className="h-3 bg-gray-100" />
        </div>

        {/* Rich Visual Steps */}
        <div className="relative">
          {/* Connection Lines */}
          <div className="absolute top-16 left-0 right-0 h-0.5 bg-gray-200 z-0"
               style={{
                 left: `${100 / (visibleSteps.length * 2)}%`,
                 right: `${100 / (visibleSteps.length * 2)}%`
               }}
          />

          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${visibleSteps.length}, 1fr)` }}>
            {visibleSteps.map((step, index) => {
              const isRequired = typeof step.required === 'function'
                ? step.required(currentWorkflow.product_type)
                : step.required;

              const isCompleted = currentWorkflow.step_completions[`step_${step.number}_completed` as keyof typeof currentWorkflow.step_completions];
              const isCurrent = currentWorkflow.current_step === step.number;
              const isAccessible = step.number <= currentWorkflow.current_step || isCompleted;

              return (
                <div key={step.number} className="relative z-10">
                  <button
                    onClick={() => handleStepClick(step.number)}
                    disabled={!isAccessible}
                    className={`
                      w-full p-6 rounded-xl border-2 transition-all duration-300 transform hover:scale-105
                      ${isCompleted
                        ? 'bg-gradient-to-br from-green-50 to-green-100 border-green-300 shadow-lg'
                        : isCurrent
                          ? 'bg-gradient-to-br from-blue-50 to-blue-100 border-blue-300 shadow-xl ring-4 ring-blue-100'
                          : isAccessible
                            ? 'bg-white border-gray-200 hover:border-blue-300 hover:shadow-md'
                            : 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60'
                      }
                    `}
                  >
                    {/* Step Number Circle */}
                    <div className={`
                      w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 transition-all
                      ${isCompleted
                        ? 'bg-green-500 text-white shadow-lg'
                        : isCurrent
                          ? 'bg-blue-500 text-white shadow-lg animate-pulse'
                          : isAccessible
                            ? 'bg-gray-100 text-gray-600 border-2 border-gray-300'
                            : 'bg-gray-100 text-gray-400 border-2 border-gray-200'
                      }
                    `}>
                      {isCompleted ? (
                        <CheckCircle className="h-8 w-8" />
                      ) : (
                        <step.icon className="h-8 w-8" />
                      )}
                    </div>

                    {/* Step Content */}
                    <div className="text-center">
                      <h3 className={`text-lg font-bold mb-2 ${
                        isCurrent ? 'text-blue-700' : isCompleted ? 'text-green-700' : 'text-gray-700'
                      }`}>
                        Step {step.number}: {step.title}
                      </h3>
                      <p className={`text-sm ${
                        isCurrent ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                      }`}>
                        {step.description}
                      </p>

                      {/* Status Indicators */}
                      <div className="mt-3 flex justify-center">
                        {isCompleted && (
                          <Badge variant="default" className="bg-green-500 text-white">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Completed
                          </Badge>
                        )}
                        {isCurrent && !isCompleted && (
                          <Badge variant="default" className="bg-blue-500 text-white">
                            <Circle className="h-3 w-3 mr-1" />
                            Current Step
                          </Badge>
                        )}
                        {!isRequired && (
                          <Badge variant="outline" className="text-xs ml-2">
                            Optional
                          </Badge>
                        )}
                      </div>
                    </div>
                  </button>

                  {/* Connection Arrow */}
                  {index < visibleSteps.length - 1 && (
                    <div className="absolute top-16 -right-4 z-20">
                      <ArrowRight className={`h-6 w-6 ${
                        isCompleted ? 'text-green-400' : 'text-gray-300'
                      }`} />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            You can save your progress at any step and return later to complete your subscription.
          </p>
        </div>
      </div>
    );
  };

  // Render current step content
  const renderStepContent = () => {
    if (!currentWorkflow) {
      return (
        <div className="max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-6">
              <Package className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Start Your Learning Journey
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Choose the perfect learning package for your educational goals.
              Our 4-step process makes it easy to get started.
            </p>
          </div>

          {/* Package Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {[
              {
                type: 'booster',
                title: 'Complete Booster',
                description: 'Comprehensive subject coverage with all topics and subtopics included',
                icon: '🚀',
                features: ['Complete subject coverage', 'All topics included', 'Structured learning path', 'Fixed pricing'],
                popular: true,
                color: 'from-green-400 to-green-600'
              },
              {
                type: 'custom',
                title: 'Custom Package',
                description: 'Build your own learning path by selecting specific topics and subtopics',
                icon: '🎯',
                features: ['Choose specific topics', 'Select subtopics', 'Flexible curriculum', 'Pay for what you need'],
                popular: false,
                color: 'from-blue-400 to-blue-600'
              },
              {
                type: 'preparation',
                title: 'Exam Preparation',
                description: 'Targeted preparation with exam-focused content and practice materials',
                icon: '📚',
                features: ['Exam-focused content', 'Practice materials', 'Performance tracking', 'Flexible scheduling'],
                popular: false,
                color: 'from-purple-400 to-purple-600'
              }
            ].map((pkg) => (
              <Card
                key={pkg.type}
                className="relative overflow-hidden cursor-pointer hover:shadow-2xl transition-all duration-300 transform hover:scale-105 border-2 hover:border-blue-300"
                onClick={() => handleStartNewWorkflow(pkg.type as any)}
              >
                {pkg.popular && (
                  <div className="absolute top-0 right-0 bg-gradient-to-r from-orange-400 to-red-500 text-white px-3 py-1 text-xs font-bold rounded-bl-lg">
                    POPULAR
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br ${pkg.color} flex items-center justify-center text-2xl`}>
                    {pkg.icon}
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-900">
                    {pkg.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    {pkg.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0">
                  <ul className="space-y-2 mb-6">
                    {pkg.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Button
                    className={`w-full bg-gradient-to-r ${pkg.color} hover:opacity-90 text-white font-semibold py-3 rounded-lg transition-all duration-200`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleStartNewWorkflow(pkg.type as any);
                    }}
                  >
                    Start {pkg.title}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Process Overview */}
          <Card className="bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                Our Simple 4-Step Process
              </CardTitle>
              <CardDescription className="text-gray-600">
                We've made subscribing to our learning programs as easy as 1-2-3-4
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {[
                  { step: 1, title: 'Select Package', desc: 'Choose your learning package', icon: Package },
                  { step: 2, title: 'Configure Curriculum', desc: 'Customize your learning content', icon: BookOpen },
                  { step: 3, title: 'Review Pricing', desc: 'See transparent pricing details', icon: Calculator },
                  { step: 4, title: 'Complete Purchase', desc: 'Secure payment and get started', icon: CreditCard }
                ].map((item, index) => (
                  <div key={item.step} className="text-center relative">
                    <div className="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                      {item.step}
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-1">{item.title}</h3>
                    <p className="text-sm text-gray-600">{item.desc}</p>

                    {index < 3 && (
                      <ArrowRight className="hidden md:block absolute top-6 -right-3 h-4 w-4 text-gray-400" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    switch (currentWorkflow.current_step) {
      case 1:
        return <ProductSelectionStep />;
      case 2:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Configure Curriculum</CardTitle>
              <CardDescription>Select your topics and subtopics</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Curriculum configuration step - Coming soon</p>
            </CardContent>
          </Card>
        );
      case 3:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Price Calculation</CardTitle>
              <CardDescription>Review your pricing details</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Price calculation step - Coming soon</p>
            </CardContent>
          </Card>
        );
      case 4:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Complete Purchase</CardTitle>
              <CardDescription>Finalize your subscription</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Purchase confirmation step - Coming soon</p>
            </CardContent>
          </Card>
        );
      default:
        return <div>Invalid step</div>;
    }
  };

  // Render admin assistance section
  const renderAdminAssistanceSection = () => {
    if (!currentWorkflow) return null;

    const { admin_assistance } = currentWorkflow;

    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center text-sm">
            <HelpCircle className="h-4 w-4 mr-2" />
            Need Help?
          </CardTitle>
        </CardHeader>
        <CardContent>
          {admin_assistance.admin_assistance_requested ? (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Admin assistance has been requested.
                {admin_assistance.assigned_admin_id && " An admin has been assigned to help you."}
                {admin_assistance.student_confirmation_required && (
                  <div className="mt-2">
                    <Badge variant="outline">Confirmation Required</Badge>
                    <p className="text-sm mt-1">Please review the admin's configuration and confirm to proceed.</p>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          ) : (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Having trouble with curriculum selection or pricing? Request help from our admin team.
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdminAssistanceDialog(true)}
              >
                <User className="h-4 w-4 mr-2" />
                Request Admin Assistance
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Add debugging for store state
  console.log('SubscriptionWorkflowWizard render - Store state:', {
    isLoading,
    currentWorkflow: currentWorkflow ? 'exists' : 'null',
    error: error || 'none'
  });

  // Direct store access for debugging
  const storeState = useSubscriptionWorkflowStore.getState();
  console.log('Direct store state access:', {
    isLoading: storeState.isLoading,
    currentWorkflow: storeState.currentWorkflow ? 'exists' : 'null',
    error: storeState.error || 'none'
  });

  if (isLoading) {
    console.log('SubscriptionWorkflowWizard: isLoading is true, showing loading state');
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading subscription workflow...</p>
          <p className="text-sm text-gray-500 mt-2">
            Debug: currentWorkflow={currentWorkflow ? 'exists' : 'null'}, error={error || 'none'}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="w-full">
      {renderStepIndicator()}
      {renderStepContent()}
      {renderAdminAssistanceSection()}

      {/* Admin Assistance Dialog */}
      {showAdminAssistanceDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Request Admin Assistance</CardTitle>
              <CardDescription>
                Describe what you need help with and an admin will assist you.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <textarea
                className="w-full p-3 border rounded-md resize-none"
                rows={4}
                placeholder="Please describe what you need help with..."
                value={adminAssistanceMessage}
                onChange={(e) => setAdminAssistanceMessage(e.target.value)}
              />
            </CardContent>
            <div className="flex justify-end gap-2 p-6 pt-0">
              <Button
                variant="outline"
                onClick={() => setShowAdminAssistanceDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleRequestAdminAssistance}
                disabled={!adminAssistanceMessage.trim()}
              >
                Send Request
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default SubscriptionWorkflowWizard;

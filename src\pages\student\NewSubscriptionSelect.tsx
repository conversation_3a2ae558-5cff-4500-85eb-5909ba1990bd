import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useSubscriptionWorkflowStore } from "@/store/subscriptionWorkflowStore";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";
import { Label } from "@/components/ui/Label";
import { Badge } from "@/components/ui/Badge";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage
} from "@/components/ui/Breadcrumb";
import {
  Package,
  CheckCircle,
  ArrowR<PERSON>,
  ArrowLeft,
  Home
} from "lucide-react";
import { Link } from "react-router-dom";
import { ROUTES } from "@/routes/RouteConfig";
import { useToast } from "@/hooks/useToast";

const NewSubscriptionSelect: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const profileData = useProfileData();
  const { toast } = useToast();
  const [productWorkflowStatus, setProductWorkflowStatus] = useState<Record<string, { status: string; currentStep?: number }>>({});
  const {
    currentWorkflow,
    availableProducts,
    selectedProduct,
    isLoadingProducts,
    error,
    fetchAvailableProducts,
    getActiveWorkflowForProduct,
    selectProduct,
    completeStep1,
    goToStep,
    createWorkflow,
    updateWorkflowStep
  } = useSubscriptionWorkflowStore();

  useEffect(() => {
    // Fetch products on component mount
    fetchAvailableProducts();
  }, [fetchAvailableProducts]);

  // Check workflow status for all products
  useEffect(() => {
    const checkProductWorkflowStatus = async () => {
      if (!user?.id || !availableProducts.length) return;

      const statusMap: Record<string, { status: string; currentStep?: number }> = {};

      for (const product of availableProducts) {
        const workflow = await getActiveWorkflowForProduct(user.id, product.id);
        if (workflow) {
          statusMap[product.id] = {
            status: workflow.status,
            currentStep: workflow.current_step
          };
        }
      }

      setProductWorkflowStatus(statusMap);
    };

    checkProductWorkflowStatus();
  }, [user?.id, availableProducts, getActiveWorkflowForProduct]);

  // Get product type from location state or current workflow
  const productType = location.state?.productType || currentWorkflow?.product_type;
  const resumingWorkflowId = location.state?.resumingWorkflow;

  // Filter products by workflow type
  const getFilteredProducts = () => {
    if (!productType) return [];
    return availableProducts.filter(product => product.type === productType);
  };

  const filteredProducts = getFilteredProducts();

  // Handle product selection
  const handleProductSelect = async (product: any) => {
    if (!user?.id) return;

    // If we're resuming a workflow, allow selection even if there's an active workflow
    if (resumingWorkflowId) {
      selectProduct(product);
      return;
    }

    // Check workflow status from our cached data
    const workflowStatus = productWorkflowStatus[product.id];

    if (workflowStatus) {
      if (workflowStatus.status === 'completed') {
        toast({
          title: "Product Already Purchased",
          description: `You have already completed the subscription for ${product.name}. Check your subscriptions page.`,
          variant: "destructive"
        });
        return;
      }

      if (['in_progress', 'admin_assistance_requested'].includes(workflowStatus.status)) {
        toast({
          title: "Active Workflow Found",
          description: `You already have an active workflow for ${product.name}. Please complete or cancel it first.`,
          variant: "destructive"
        });
        return;
      }
    }

    // No existing workflow for this product, proceed with selection
    selectProduct(product);
  };

  // Handle step completion
  const handleContinue = async () => {
    if (!selectedProduct || !user?.id) {
      toast({
        title: "Selection Required",
        description: "Please select a product to continue",
        variant: "destructive"
      });
      return;
    }

    // If we're resuming a workflow, update the existing workflow instead of creating a new one
    if (resumingWorkflowId) {
      // Update the existing workflow with the selected product
      const success = await updateWorkflowStep(resumingWorkflowId, 1, { product_id: selectedProduct.id });

      if (success) {
        toast({
          title: "Workflow Updated",
          description: `${selectedProduct.name} has been selected for your existing workflow`,
          variant: "default"
        });

        // Move to next step based on product type
        if (selectedProduct.type === 'booster') {
          // For booster, go directly to pricing (step 3)
          navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PRICING.path);
        } else {
          // For custom/preparation, go to curriculum configuration (step 2)
          navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_CONFIGURE.path);
        }
      } else {
        toast({
          title: "Error",
          description: "Failed to update workflow with selected product",
          variant: "destructive"
        });
      }
      return;
    }

    // Check if there's already an active workflow for this specific product
    const existingWorkflow = await getActiveWorkflowForProduct(user.id, selectedProduct.id);
    if (existingWorkflow) {
      toast({
        title: "Existing Workflow Found",
        description: `You already have an active workflow for ${selectedProduct.name}. Please complete or cancel that workflow first.`,
        variant: "destructive"
      });
      return;
    }

    // Create a new workflow with the selected product
    const workflowId = await createWorkflow(user.id, selectedProduct.type as any, selectedProduct.id);

    if (workflowId) {
      toast({
        title: "Product Selected",
        description: `${selectedProduct.name} has been selected and workflow created`,
        variant: "default"
      });

      // Move to next step based on product type
      if (selectedProduct.type === 'booster') {
        // For booster, go directly to pricing (step 3)
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PRICING.path);
      } else {
        // For custom/preparation, go to curriculum configuration (step 2)
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_CONFIGURE.path);
      }
    } else {
      toast({
        title: "Error",
        description: "Failed to create workflow with selected product",
        variant: "destructive"
      });
    }
  };

  // Get product features
  const getProductFeatures = (product: any) => {
    if (!product.features) return [];
    return Object.entries(product.features).map(([key, value]) => ({
      key,
      value,
      label: key.split('_').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ')
    }));
  };

  const breadcrumbItems = [
    { label: "Dashboard", href: ROUTES.STUDENT_DASHBOARD.path },
    { label: "Subscriptions", href: ROUTES.STUDENT_SUBSCRIPTIONS.path },
    { label: "New Subscription", href: ROUTES.STUDENT_NEW_SUBSCRIPTION.path },
    { label: "Select Package", href: null }
  ];

  if (!productType) {
    return (
      <StudentPageLayout
        title="Select Package"
        profileData={profileData}
      >
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No product type specified.</p>
          <Button onClick={() => navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION.path)}>
            Start New Subscription
          </Button>
        </div>
      </StudentPageLayout>
    );
  }

  return (
    <StudentPageLayout
      title="Select Package"
      profileData={profileData}
      description={`Choose your ${productType?.charAt(0).toUpperCase()}${productType?.slice(1)} package`}
    >
      {/* Breadcrumb Navigation */}
      <div className="mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_DASHBOARD.path} className="flex items-center">
                  <Home className="h-4 w-4 mr-1" />
                  Dashboard
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_SUBSCRIPTIONS.path}>
                  Subscriptions
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_NEW_SUBSCRIPTION.path}>
                  New Subscription
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Select Package</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Select Your {productType?.charAt(0).toUpperCase()}{productType?.slice(1)} Package
            </CardTitle>
            <CardDescription>
              {resumingWorkflowId
                ? "You are resuming an existing workflow. Select the product to continue."
                : "Choose the package that best fits your learning needs"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Product Selection */}
            {isLoadingProducts ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading products...</p>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No products available for this package type.</p>
              </div>
            ) : (
              <RadioGroup
                value={selectedProduct?.id || ''}
                onValueChange={(value) => {
                  const product = filteredProducts.find(p => p.id === value);
                  if (product) handleProductSelect(product);
                }}
              >
                <div className="grid grid-cols-1 gap-4">
                  {filteredProducts.map((product) => {
                    const features = getProductFeatures(product);
                    const isSelected = selectedProduct?.id === product.id;
                    const workflowStatus = productWorkflowStatus[product.id];
                    const isCompleted = workflowStatus?.status === 'completed';
                    const hasActiveWorkflow = workflowStatus && ['in_progress', 'admin_assistance_requested'].includes(workflowStatus.status);
                    const isDisabled = isCompleted || (hasActiveWorkflow && !resumingWorkflowId);

                    return (
                      <div key={product.id} className="relative">
                        <Label
                          htmlFor={product.id}
                          className={`
                            block rounded-lg border-2 p-6 transition-all
                            ${isDisabled
                              ? 'border-gray-300 bg-gray-50 opacity-75 cursor-not-allowed'
                              : isSelected
                                ? 'border-blue-500 bg-blue-50 cursor-pointer'
                                : 'border-gray-200 hover:border-gray-300 cursor-pointer'
                            }
                          `}
                        >
                          {/* Status Badge */}
                          {workflowStatus && (
                            <div className="absolute top-2 right-2">
                              {isCompleted ? (
                                <Badge variant="default" className="bg-green-600">
                                  Purchased
                                </Badge>
                              ) : hasActiveWorkflow ? (
                                <Badge variant="outline" className="border-orange-500 text-orange-600">
                                  Active Workflow (Step {workflowStatus.currentStep})
                                </Badge>
                              ) : null}
                            </div>
                          )}

                          <div className="flex items-start space-x-4">
                            <RadioGroupItem
                              value={product.id}
                              id={product.id}
                              className="mt-1"
                              disabled={isDisabled}
                            />
                            <div className="flex-1">
                              <div className="flex justify-between items-start mb-2">
                                <h3 className={`text-lg font-semibold ${isDisabled ? 'text-gray-500' : ''}`}>
                                  {product.name}
                                </h3>
                                <div className="text-right">
                                  <p className={`text-2xl font-bold ${isDisabled ? 'text-gray-500' : 'text-blue-600'}`}>
                                    ${product.price.toFixed(2)}
                                  </p>
                                  <p className="text-sm text-gray-500">{product.duration_days} days</p>
                                </div>
                              </div>
                              <p className={`mb-4 ${isDisabled ? 'text-gray-500' : 'text-gray-600'}`}>
                                {product.description}
                              </p>

                              {isCompleted && (
                                <div className="mb-4 p-3 bg-green-100 rounded-lg">
                                  <p className="text-green-800 text-sm font-medium">
                                    ✓ You have already purchased this product. Check your subscriptions page for access.
                                  </p>
                                </div>
                              )}

                              {hasActiveWorkflow && !isCompleted && !resumingWorkflowId && (
                                <div className="mb-4 p-3 bg-orange-100 rounded-lg">
                                  <p className="text-orange-800 text-sm font-medium">
                                    🚫 You have an active workflow for this product at step {workflowStatus.currentStep}.
                                    Please complete or cancel it before selecting this product again.
                                  </p>
                                </div>
                              )}

                              {resumingWorkflowId && hasActiveWorkflow && (
                                <div className="mb-4 p-3 bg-blue-100 rounded-lg">
                                  <p className="text-blue-800 text-sm font-medium">
                                    ℹ️ You are resuming your existing workflow. Select this product to continue from step {workflowStatus.currentStep}.
                                  </p>
                                </div>
                              )}

                              {features.length > 0 && (
                                <div className="grid grid-cols-2 gap-2">
                                  {features.map((feature, index) => (
                                    <div key={index} className="flex items-center text-sm">
                                      <CheckCircle className={`h-3 w-3 mr-2 ${isDisabled ? 'text-gray-400' : 'text-green-500'}`} />
                                      <span className={isDisabled ? 'text-gray-500' : ''}>
                                        {feature.label}: {String(feature.value)}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </Label>
                      </div>
                    );
                  })}
                </div>
              </RadioGroup>
            )}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION.path)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button
            onClick={handleContinue}
            disabled={!selectedProduct}
          >
            Continue
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </StudentPageLayout>
  );
};

export default NewSubscriptionSelect;

import React, { useState } from "react";
import { useStudentPastSessionsStore, SessionParticipant } from "@/store/studentPastSessionsStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import { Badge } from "@/components/ui/Badge";
import { Checkbox } from "@/components/ui/Checkbox";
import {
  Search,
  Filter,
  ArrowUpDown,
  Columns,
  Clipboard,
  Check,
  Download,
  AlertTriangle,
  FileText,
  Info,
  Calendar,
  BarChart3,
  MessageSquare,
  Star,
  Clock,
  ChevronDown,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";

const StudentPastSessions = () => {
  // Use the Zustand store
  const {
    searchTerm,
    setSearchTerm,
    visibleColumns,
    setVisibleColumns,
    selectedSessions,
    toggleSessionSelection,
    selectAllSessions,
    clearSelection,
    filteredSessions
  } = useStudentPastSessionsStore();

  // Get profile data for the UserNavbar
  const profileData = useProfileData();

  // Local state for sorting
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Completed": { color: "bg-green-100 text-green-800", icon: <Check size={12} /> },
      "Missed": { color: "bg-red-100 text-red-800", icon: <AlertTriangle size={12} /> },
      "Cancelled": { color: "bg-gray-100 text-gray-800", icon: <Clock size={12} /> },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      color: "bg-gray-100 text-gray-800", 
      icon: null 
    };

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        {config.icon}
        {status}
      </Badge>
    );
  };

  const getParticipationBadge = (level: string, score: number) => {
    const levelConfig = {
      "Highly Engaged": "bg-green-100 text-green-800",
      "Engaged": "bg-blue-100 text-blue-800",
      "Moderately Engaged": "bg-yellow-100 text-yellow-800",
      "Low Engagement": "bg-orange-100 text-orange-800",
      "Not Attended": "bg-red-100 text-red-800",
    };

    const color = levelConfig[level as keyof typeof levelConfig] || "bg-gray-100 text-gray-800";

    return (
      <div className="flex flex-col items-center">
        <Badge className={color}>
          {level}
        </Badge>
        {score > 0 && (
          <span className="text-xs text-gray-500 mt-1">{score}/10</span>
        )}
      </div>
    );
  };

  const getRatingStars = (rating: number) => {
    if (rating === 0) return <span className="text-gray-400">No rating</span>;

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={14}
            className={star <= rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}
          />
        ))}
        <span className="text-sm text-gray-600 ml-1">{rating.toFixed(1)}</span>
      </div>
    );
  };

  const getAttendanceBadge = (attendance: string, missedReason?: string) => {
    const attendanceConfig = {
      "Attended": { color: "bg-green-100 text-green-800", icon: <Check size={12} /> },
      "Late": { color: "bg-yellow-100 text-yellow-800", icon: <Clock size={12} /> },
      "Missed": { color: "bg-red-100 text-red-800", icon: <AlertTriangle size={12} /> },
      "Left Early": { color: "bg-orange-100 text-orange-800", icon: <Clock size={12} /> },
    };

    const config = attendanceConfig[attendance as keyof typeof attendanceConfig] || {
      color: "bg-gray-100 text-gray-800",
      icon: null
    };

    // If there's a missed reason, wrap in tooltip
    if (missedReason) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                className={`${config.color} flex items-center gap-1 text-xs cursor-pointer`}
                title={missedReason} // Fallback native tooltip
              >
                {config.icon}
                {attendance}
              </Badge>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs">
              <p>{missedReason}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return (
      <Badge className={`${config.color} flex items-center gap-1 text-xs`}>
        {config.icon}
        {attendance}
      </Badge>
    );
  };

  const renderParticipationCells = (participants: SessionParticipant[], columnIndex: number) => {
    return (
      <div className="space-y-1">
        {participants.map((participant, index) => (
          <div key={index} className="text-xs py-1 border-b border-gray-100 last:border-b-0">
            {columnIndex === 0 && (
              <span className="font-medium text-gray-600">
                {participant.role === "Instructor" ? "Tutor" : "Student"}
              </span>
            )}
            {columnIndex === 1 && (
              <span className="truncate" title={participant.name}>
                {participant.name}
              </span>
            )}
            {columnIndex === 2 && (
              <span className="text-gray-500 whitespace-nowrap">
                {participant.joined || "—"}
              </span>
            )}
            {columnIndex === 3 && (
              <span className="text-gray-500 whitespace-nowrap">
                {participant.left || "—"}
              </span>
            )}
            {columnIndex === 4 && (
              <div>
                {getAttendanceBadge(participant.attendance, participant.missedReason)}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const handleSelectAll = () => {
    if (selectedSessions.size === filteredSessions.length) {
      clearSelection();
    } else {
      selectAllSessions();
    }
  };

  const isAllSelected = selectedSessions.size === filteredSessions.length && filteredSessions.length > 0;

  return (
    <StudentPageLayout
      title="Session History"
      description="Review your completed learning sessions and track your progress"
    >
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-4 border-b border-gray-200 flex flex-wrap justify-between items-center gap-4">
          <div className="relative flex-grow max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by tutor, subject, topic, or notes"
              className="pl-10 pr-4 py-2 w-full border-gray-200 rounded-md focus:border-rfpurple-500 focus:ring focus:ring-rfpurple-200 focus:ring-opacity-50 transition-colors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Filter size={16} />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <div className="p-2 text-sm font-medium text-gray-700 border-b">
                  Filter Options
                </div>
                <div className="p-2 text-sm text-gray-500">
                  Coming soon...
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Columns size={16} />
                  Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <div className="p-2 text-sm font-medium text-gray-700 border-b">
                  Toggle Columns
                </div>
                {Object.entries(visibleColumns).map(([key, value]) => (
                  <DropdownMenuCheckboxItem
                    key={key}
                    checked={value}
                    onCheckedChange={(checked) =>
                      setVisibleColumns({ [key]: checked })
                    }
                    className="capitalize"
                  >
                    {key === "participationScore" ? "Participation Score" :
                     key === "feedbackRating" ? "Rating" :
                     key === "participation" ? "Participation Details" : key}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {selectedSessions.size > 0 && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Download size={16} />
                  Export ({selectedSessions.size})
                </Button>
                <Button variant="outline" size="sm" onClick={clearSelection}>
                  Clear Selection
                </Button>
              </div>
            )}
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              {/* First row - Main headers */}
              <TableRow>
                <TableHead className="w-12" rowSpan={2}>
                  <Checkbox
                    checked={isAllSelected}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                {visibleColumns.id && (
                  <TableHead className="cursor-pointer" rowSpan={2} onClick={() => handleSort('id')}>
                    <div className="flex items-center gap-1">
                      Session ID
                      <ArrowUpDown size={14} />
                    </div>
                  </TableHead>
                )}
                {visibleColumns.tutor && <TableHead rowSpan={2}>Tutor</TableHead>}
                {visibleColumns.subject && <TableHead rowSpan={2}>Subject</TableHead>}
                {visibleColumns.topic && <TableHead rowSpan={2}>Topic</TableHead>}
                {visibleColumns.subtopic && <TableHead rowSpan={2}>Subtopic</TableHead>}
                {visibleColumns.sessionType && <TableHead rowSpan={2}>Type</TableHead>}
                {visibleColumns.status && <TableHead rowSpan={2}>Status</TableHead>}
                {visibleColumns.date && (
                  <TableHead className="cursor-pointer" rowSpan={2} onClick={() => handleSort('date')}>
                    <div className="flex items-center gap-1">
                      Date
                      <ArrowUpDown size={14} />
                    </div>
                  </TableHead>
                )}
                {visibleColumns.time && <TableHead rowSpan={2}>Time</TableHead>}
                {visibleColumns.duration && <TableHead rowSpan={2}>Duration</TableHead>}
                {visibleColumns.participation && (
                  <TableHead colSpan={5} className="text-center bg-gray-100 border-b-0">
                    <div className="text-sm font-medium text-gray-700">Participation</div>
                  </TableHead>
                )}
                {visibleColumns.participationScore && <TableHead rowSpan={2}>Participation Score</TableHead>}
                {visibleColumns.feedbackRating && <TableHead rowSpan={2}>Rating</TableHead>}
                {visibleColumns.actions && <TableHead rowSpan={2}>Actions</TableHead>}
              </TableRow>

              {/* Second row - Sub-headers for Participation */}
              {visibleColumns.participation && (
                <TableRow>
                  <TableHead className="text-xs font-medium text-gray-600 bg-gray-50 w-16">ROLE</TableHead>
                  <TableHead className="text-xs font-medium text-gray-600 bg-gray-50 w-32">ATTENDEE</TableHead>
                  <TableHead className="text-xs font-medium text-gray-600 bg-gray-50 w-20">JOINED</TableHead>
                  <TableHead className="text-xs font-medium text-gray-600 bg-gray-50 w-20">LEFT</TableHead>
                  <TableHead className="text-xs font-medium text-gray-600 bg-gray-50 w-28">ATTENDANCE</TableHead>
                </TableRow>
              )}
            </TableHeader>
            <TableBody>
              {filteredSessions.map((session) => (
                <TableRow key={session.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedSessions.has(session.id)}
                      onCheckedChange={() => toggleSessionSelection(session.id)}
                    />
                  </TableCell>
                  {visibleColumns.id && (
                    <TableCell className="font-medium">{session.id}</TableCell>
                  )}
                  {visibleColumns.tutor && (
                    <TableCell>
                      <div>
                        <div className="font-medium">{session.tutor.name}</div>
                        <div className="text-sm text-gray-500">{session.tutor.email}</div>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.subject && (
                    <TableCell>{session.subject}</TableCell>
                  )}
                  {visibleColumns.topic && (
                    <TableCell>{session.topic}</TableCell>
                  )}
                  {visibleColumns.subtopic && (
                    <TableCell>{session.subtopic}</TableCell>
                  )}
                  {visibleColumns.sessionType && (
                    <TableCell>{session.sessionType}</TableCell>
                  )}
                  {visibleColumns.status && (
                    <TableCell>{getStatusBadge(session.status)}</TableCell>
                  )}
                  {visibleColumns.date && (
                    <TableCell>{formatDate(session.date)}</TableCell>
                  )}
                  {visibleColumns.time && (
                    <TableCell>{session.time}</TableCell>
                  )}
                  {visibleColumns.duration && (
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{session.duration} min</span>
                        {session.duration !== session.scheduledDuration && (
                          <span className="text-xs text-gray-500">
                            (scheduled: {session.scheduledDuration} min)
                          </span>
                        )}
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.participation && (
                    <>
                      <TableCell className="p-2 w-16">
                        {renderParticipationCells(session.participants, 0)}
                      </TableCell>
                      <TableCell className="p-2 w-32">
                        {renderParticipationCells(session.participants, 1)}
                      </TableCell>
                      <TableCell className="p-2 w-20">
                        {renderParticipationCells(session.participants, 2)}
                      </TableCell>
                      <TableCell className="p-2 w-20">
                        {renderParticipationCells(session.participants, 3)}
                      </TableCell>
                      <TableCell className="p-2 w-28">
                        {renderParticipationCells(session.participants, 4)}
                      </TableCell>
                    </>
                  )}
                  {visibleColumns.participationScore && (
                    <TableCell>
                      {getParticipationBadge(session.participationLevel, session.participationScore)}
                    </TableCell>
                  )}
                  {visibleColumns.feedbackRating && (
                    <TableCell>{getRatingStars(session.feedbackRating)}</TableCell>
                  )}
                  {visibleColumns.actions && (
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <FileText size={14} />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MessageSquare size={14} />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <BarChart3 size={14} />
                        </Button>
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {filteredSessions.length === 0 && (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 mx-auto text-gray-300 mb-2" />
            <p className="text-gray-500">No past sessions found.</p>
          </div>
        )}
      </div>
    </StudentPageLayout>
  );
};

export default StudentPastSessions;

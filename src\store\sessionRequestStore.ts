import { create } from 'zustand';

// Types for session requests
export interface SessionRequest {
  id: string;
  batchId: string;
  topicId: string;
  subtopicId?: string;
  requestedTutorId: string;
  studentId: string;
  requestedDate: string;
  requestedTime: string;
  durationMin: number;
  notes?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'cancelled';
  urgency: 'high' | 'medium' | 'low';
  autoAccepted: boolean;
  hasConflict: boolean;
  conflictDetails?: string;
  createdAt: string;
}

// Interface for the store state
interface SessionRequestState {
  // State
  requests: SessionRequest[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  createSessionRequest: (request: Omit<SessionRequest, 'id' | 'status' | 'autoAccepted' | 'hasConflict' | 'createdAt'>) => Promise<SessionRequest>;
  acceptSessionRequest: (requestId: string) => Promise<void>;
  rejectSessionRequest: (requestId: string, reason: string) => Promise<void>;
  cancelSessionRequest: (requestId: string) => Promise<void>;
  fetchSessionRequests: (filters?: { tutorId?: string; studentId?: string; status?: SessionRequest['status'] }) => Promise<void>;
  checkForConflicts: (date: string, time: string, durationMin: number) => Promise<{ hasConflict: boolean; conflictDetails?: string }>;
}

// Sample data for development
const sampleRequests: SessionRequest[] = [
  {
    id: "req-001",
    batchId: "batch-001",
    topicId: "topic-001",
    subtopicId: "subtopic-001",
    requestedTutorId: "tutor-001",
    studentId: "student-001",
    requestedDate: "2023-07-15",
    requestedTime: "14:00",
    durationMin: 60,
    notes: "I need help understanding backpropagation algorithms.",
    status: "pending",
    urgency: "high",
    autoAccepted: false,
    hasConflict: false,
    createdAt: "2023-07-14T10:30:00Z",
  },
  {
    id: "req-002",
    batchId: "batch-001",
    topicId: "topic-002",
    subtopicId: "subtopic-002",
    requestedTutorId: "tutor-001",
    studentId: "student-002",
    requestedDate: "2023-07-16",
    requestedTime: "10:30",
    durationMin: 60,
    notes: "Would like to review tree traversal methods.",
    status: "pending",
    urgency: "medium",
    autoAccepted: false,
    hasConflict: true,
    conflictDetails: "Another session at 11:00",
    createdAt: "2023-07-14T09:15:00Z",
  },
];

// Create the store
export const useSessionRequestStore = create<SessionRequestState>((set, get) => ({
  // Initial state
  requests: sampleRequests,
  isLoading: false,
  error: null,

  // Actions
  createSessionRequest: async (requestData) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check for conflicts
      const { hasConflict, conflictDetails } = await get().checkForConflicts(
        requestData.requestedDate,
        requestData.requestedTime,
        requestData.durationMin
      );
      
      // Create new request with generated ID
      const newRequest: SessionRequest = {
        ...requestData,
        id: `req-${Date.now()}`,
        status: 'pending',
        autoAccepted: false,
        hasConflict,
        conflictDetails,
        createdAt: new Date().toISOString(),
      };
      
      // Update state with new request
      set(state => ({
        requests: [...state.requests, newRequest],
        isLoading: false,
      }));
      
      return newRequest;
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  acceptSessionRequest: async (requestId) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update request status
      set(state => ({
        requests: state.requests.map(req => 
          req.id === requestId 
            ? { ...req, status: 'accepted' } 
            : req
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  rejectSessionRequest: async (requestId, reason) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update request status
      set(state => ({
        requests: state.requests.map(req => 
          req.id === requestId 
            ? { ...req, status: 'rejected', notes: reason } 
            : req
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  cancelSessionRequest: async (requestId) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update request status
      set(state => ({
        requests: state.requests.map(req => 
          req.id === requestId 
            ? { ...req, status: 'cancelled' } 
            : req
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  fetchSessionRequests: async (filters) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Filter requests based on provided filters
      let filteredRequests = [...sampleRequests];
      
      if (filters) {
        if (filters.tutorId) {
          filteredRequests = filteredRequests.filter(req => req.requestedTutorId === filters.tutorId);
        }
        if (filters.studentId) {
          filteredRequests = filteredRequests.filter(req => req.studentId === filters.studentId);
        }
        if (filters.status) {
          filteredRequests = filteredRequests.filter(req => req.status === filters.status);
        }
      }
      
      set({ requests: filteredRequests, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  checkForConflicts: async (date, time, durationMin) => {
    // In a real app, this would check against the database of booked sessions
    // For now, we'll simulate some conflicts
    
    // Convert time to minutes for easier comparison
    const [hours, minutes] = time.split(':').map(Number);
    const requestTimeInMinutes = hours * 60 + minutes;
    const requestEndTimeInMinutes = requestTimeInMinutes + durationMin;
    
    // Sample booked sessions for the same date
    const bookedSessions = [
      { date: "2023-07-15", startTime: "13:00", durationMin: 60 },
      { date: "2023-07-16", startTime: "10:00", durationMin: 45 },
    ];
    
    // Check for conflicts
    const conflictingSession = bookedSessions.find(session => {
      if (session.date !== date) return false;
      
      const [sessionHours, sessionMinutes] = session.startTime.split(':').map(Number);
      const sessionStartTimeInMinutes = sessionHours * 60 + sessionMinutes;
      const sessionEndTimeInMinutes = sessionStartTimeInMinutes + session.durationMin;
      
      // Check if there's an overlap
      return (
        (requestTimeInMinutes < sessionEndTimeInMinutes && requestEndTimeInMinutes > sessionStartTimeInMinutes) ||
        (sessionStartTimeInMinutes < requestEndTimeInMinutes && sessionEndTimeInMinutes > requestTimeInMinutes)
      );
    });
    
    if (conflictingSession) {
      return {
        hasConflict: true,
        conflictDetails: `Another session at ${conflictingSession.startTime}`
      };
    }
    
    return { hasConflict: false };
  }
}));

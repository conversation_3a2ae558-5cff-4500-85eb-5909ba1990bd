-- RLS Policies for subscription_workflows table
-- This file contains Row Level Security policies for the subscription_workflows table

-- Enable RLS on subscription_workflows table
ALTER TABLE public.subscription_workflows ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STUDENT POLICIES
-- =====================================================

-- RLS Policy: Students can view their own subscription workflows
CREATE POLICY "Students can view own subscription workflows"
ON public.subscription_workflows
FOR SELECT
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- RLS Policy: Students can create their own subscription workflows
CREATE POLICY "Students can create own subscription workflows"
ON public.subscription_workflows
FOR INSERT
TO authenticated
WITH CHECK (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- RLS Policy: Students can update their own subscription workflows (limited fields)
CREATE POLICY "Students can update own subscription workflows"
ON public.subscription_workflows
FOR UPDATE
TO authenticated
USING (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
)
WITH CHECK (
  student_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'student'
  )
);

-- =====================================================
-- ADMIN POLICIES
-- =====================================================

-- RLS Policy: Admins can view all subscription workflows
CREATE POLICY "Admins can view all subscription workflows"
ON public.subscription_workflows
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- RLS Policy: Admins can create subscription workflows for any student
CREATE POLICY "Admins can create subscription workflows"
ON public.subscription_workflows
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- RLS Policy: Admins can update all subscription workflows
CREATE POLICY "Admins can update all subscription workflows"
ON public.subscription_workflows
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- RLS Policy: Admins can delete subscription workflows
CREATE POLICY "Admins can delete subscription workflows"
ON public.subscription_workflows
FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- =====================================================
-- ASSIGNED ADMIN POLICIES
-- =====================================================

-- RLS Policy: Assigned admins can view their assigned workflows
CREATE POLICY "Assigned admins can view assigned workflows"
ON public.subscription_workflows
FOR SELECT
TO authenticated
USING (
  assigned_admin_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- RLS Policy: Assigned admins can update their assigned workflows
CREATE POLICY "Assigned admins can update assigned workflows"
ON public.subscription_workflows
FOR UPDATE
TO authenticated
USING (
  assigned_admin_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  assigned_admin_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- =====================================================
-- TUTOR POLICIES
-- =====================================================

-- RLS Policy: Tutors can view workflows for students they have sessions with
CREATE POLICY "Tutors can view workflows for their students"
ON public.subscription_workflows
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'tutor'
  )
  AND EXISTS (
    SELECT 1 FROM public.sessions s
    WHERE s.student_id = subscription_workflows.student_id
    AND s.tutor_id = auth.uid()
  )
);

-- =====================================================
-- SERVICE ROLE POLICIES
-- =====================================================

-- RLS Policy: Service role can perform all operations (for backend processes)
CREATE POLICY "Service role full access"
ON public.subscription_workflows
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- ADDITIONAL SECURITY CONSIDERATIONS
-- =====================================================

-- Create function to check if user can modify payment-related fields
CREATE OR REPLACE FUNCTION can_modify_payment_fields(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Only admins and service role can modify payment-related fields
  RETURN EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = user_id 
    AND user_type = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to validate workflow step progression
CREATE OR REPLACE FUNCTION validate_step_progression(
  old_step INTEGER,
  new_step INTEGER,
  user_type TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Admins can set any step
  IF user_type = 'admin' THEN
    RETURN true;
  END IF;
  
  -- Students can only progress forward one step at a time
  IF user_type = 'student' THEN
    RETURN new_step = old_step + 1 OR new_step = old_step;
  END IF;
  
  -- Default: no modification allowed
  RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for student_id lookups (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_student_id 
ON public.subscription_workflows(student_id);

-- Index for assigned_admin_id lookups
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_assigned_admin_id 
ON public.subscription_workflows(assigned_admin_id);

-- Index for status filtering
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_status 
ON public.subscription_workflows(status);

-- Index for product_type filtering
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_product_type 
ON public.subscription_workflows(product_type);

-- Composite index for admin queries (status + created_at)
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_status_created_at 
ON public.subscription_workflows(status, created_at DESC);

-- Composite index for student queries (student_id + status)
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_student_status
ON public.subscription_workflows(student_id, status);

-- =====================================================
-- FIELD-LEVEL SECURITY FUNCTIONS
-- =====================================================

-- Create a trigger function to prevent non-admins from modifying payment fields
CREATE OR REPLACE FUNCTION prevent_payment_field_modification()
RETURNS TRIGGER AS $$
DECLARE
  user_type TEXT;
BEGIN
  -- Get the user type of the current user
  SELECT p.user_type INTO user_type
  FROM public.profiles p
  WHERE p.id = auth.uid();

  -- If user is admin or service role, allow all changes
  IF user_type = 'admin' OR current_setting('role') = 'service_role' THEN
    RETURN NEW;
  END IF;

  -- For non-admins, prevent modification of payment-sensitive fields
  IF (
    OLD.stripe_payment_intent_id IS DISTINCT FROM NEW.stripe_payment_intent_id
    OR OLD.stripe_subscription_id IS DISTINCT FROM NEW.stripe_subscription_id
    OR OLD.stripe_customer_id IS DISTINCT FROM NEW.stripe_customer_id
    OR OLD.payment_status IS DISTINCT FROM NEW.payment_status
    OR OLD.total_amount IS DISTINCT FROM NEW.total_amount
    OR OLD.currency IS DISTINCT FROM NEW.currency
    OR OLD.payment_method_type IS DISTINCT FROM NEW.payment_method_type
    OR OLD.payment_completed_at IS DISTINCT FROM NEW.payment_completed_at
  ) THEN
    RAISE EXCEPTION 'Access denied: Only administrators can modify payment-related fields'
      USING ERRCODE = '42501';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to enforce payment field protection
DROP TRIGGER IF EXISTS prevent_payment_modification_trigger ON public.subscription_workflows;
CREATE TRIGGER prevent_payment_modification_trigger
  BEFORE UPDATE ON public.subscription_workflows
  FOR EACH ROW EXECUTE FUNCTION prevent_payment_field_modification();

-- Create a trigger function to validate workflow step progression
CREATE OR REPLACE FUNCTION validate_workflow_step_progression()
RETURNS TRIGGER AS $$
DECLARE
  user_type TEXT;
BEGIN
  -- Get the user type of the current user
  SELECT p.user_type INTO user_type
  FROM public.profiles p
  WHERE p.id = auth.uid();

  -- If user is admin or service role, allow all step changes
  IF user_type = 'admin' OR current_setting('role') = 'service_role' THEN
    RETURN NEW;
  END IF;

  -- For students, validate step progression
  IF user_type = 'student' THEN
    -- Students can only progress forward one step at a time or stay on same step
    IF NEW.current_step > OLD.current_step + 1 THEN
      RAISE EXCEPTION 'Invalid step progression: Students can only advance one step at a time'
        USING ERRCODE = '42501';
    END IF;

    -- Students cannot go backwards in steps
    IF NEW.current_step < OLD.current_step THEN
      RAISE EXCEPTION 'Invalid step progression: Students cannot go backwards in workflow steps'
        USING ERRCODE = '42501';
    END IF;
  END IF;

  -- For tutors, prevent any step modifications
  IF user_type = 'tutor' THEN
    IF NEW.current_step IS DISTINCT FROM OLD.current_step THEN
      RAISE EXCEPTION 'Access denied: Tutors cannot modify workflow steps'
        USING ERRCODE = '42501';
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to enforce step progression validation
DROP TRIGGER IF EXISTS validate_step_progression_trigger ON public.subscription_workflows;
CREATE TRIGGER validate_step_progression_trigger
  BEFORE UPDATE ON public.subscription_workflows
  FOR EACH ROW EXECUTE FUNCTION validate_workflow_step_progression();

-- Create a trigger function to automatically update timestamps
CREATE OR REPLACE FUNCTION update_workflow_timestamps()
RETURNS TRIGGER AS $$
BEGIN
  -- Always update the updated_at timestamp
  NEW.updated_at = timezone('UTC'::text, now());

  -- Update step completion timestamps when steps are completed
  IF NEW.step_1_completed = true AND OLD.step_1_completed = false THEN
    NEW.step_1_completed_at = timezone('UTC'::text, now());
  END IF;

  IF NEW.step_2_completed = true AND OLD.step_2_completed = false THEN
    NEW.step_2_completed_at = timezone('UTC'::text, now());
  END IF;

  IF NEW.step_3_completed = true AND OLD.step_3_completed = false THEN
    NEW.step_3_completed_at = timezone('UTC'::text, now());
  END IF;

  IF NEW.step_4_completed = true AND OLD.step_4_completed = false THEN
    NEW.step_4_completed_at = timezone('UTC'::text, now());
  END IF;

  -- Update completed_at when status changes to completed
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    NEW.completed_at = timezone('UTC'::text, now());
  END IF;

  -- Update payment_completed_at when payment status changes to succeeded
  IF NEW.payment_status = 'succeeded' AND OLD.payment_status != 'succeeded' THEN
    NEW.payment_completed_at = timezone('UTC'::text, now());
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update timestamps
DROP TRIGGER IF EXISTS update_timestamps_trigger ON public.subscription_workflows;
CREATE TRIGGER update_timestamps_trigger
  BEFORE UPDATE ON public.subscription_workflows
  FOR EACH ROW EXECUTE FUNCTION update_workflow_timestamps();



-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get user's accessible workflows
CREATE OR REPLACE FUNCTION get_accessible_workflows(user_id UUID)
RETURNS TABLE (
  workflow_id UUID,
  access_type TEXT -- 'owner', 'assigned_admin', 'admin', 'tutor'
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    sw.id as workflow_id,
    CASE
      WHEN sw.student_id = user_id THEN 'owner'
      WHEN sw.assigned_admin_id = user_id THEN 'assigned_admin'
      WHEN p.user_type = 'admin' THEN 'admin'
      WHEN EXISTS (
        SELECT 1 FROM sessions s
        WHERE s.student_id = sw.student_id
        AND s.tutor_id = user_id
      ) THEN 'tutor'
      ELSE 'none'
    END as access_type
  FROM subscription_workflows sw
  JOIN profiles p ON p.id = user_id
  WHERE
    sw.student_id = user_id
    OR sw.assigned_admin_id = user_id
    OR p.user_type = 'admin'
    OR EXISTS (
      SELECT 1 FROM sessions s
      WHERE s.student_id = sw.student_id
      AND s.tutor_id = user_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check workflow permissions
CREATE OR REPLACE FUNCTION check_workflow_permission(
  workflow_id UUID,
  user_id UUID,
  required_permission TEXT -- 'read', 'write', 'admin'
)
RETURNS BOOLEAN AS $$
DECLARE
  user_type TEXT;
  is_owner BOOLEAN := false;
  is_assigned_admin BOOLEAN := false;
  has_tutor_access BOOLEAN := false;
BEGIN
  -- Get user type
  SELECT p.user_type INTO user_type
  FROM profiles p
  WHERE p.id = user_id;

  -- Check ownership
  SELECT EXISTS (
    SELECT 1 FROM subscription_workflows sw
    WHERE sw.id = workflow_id AND sw.student_id = user_id
  ) INTO is_owner;

  -- Check assigned admin
  SELECT EXISTS (
    SELECT 1 FROM subscription_workflows sw
    WHERE sw.id = workflow_id AND sw.assigned_admin_id = user_id
  ) INTO is_assigned_admin;

  -- Check tutor access
  SELECT EXISTS (
    SELECT 1 FROM subscription_workflows sw
    JOIN sessions s ON s.student_id = sw.student_id
    WHERE sw.id = workflow_id AND s.tutor_id = user_id
  ) INTO has_tutor_access;

  -- Determine permissions
  CASE required_permission
    WHEN 'read' THEN
      RETURN (
        is_owner
        OR is_assigned_admin
        OR user_type = 'admin'
        OR (user_type = 'tutor' AND has_tutor_access)
      );
    WHEN 'write' THEN
      RETURN (
        is_owner
        OR is_assigned_admin
        OR user_type = 'admin'
      );
    WHEN 'admin' THEN
      RETURN (user_type = 'admin');
    ELSE
      RETURN false;
  END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

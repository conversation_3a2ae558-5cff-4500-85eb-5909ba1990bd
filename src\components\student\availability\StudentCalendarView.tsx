import React, { useState } from "react";
import { useStudentAvailabilityStore } from "@/store/studentAvailabilityStore";
import { Button } from "@/components/ui/Button";
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { format, addDays, startOfWeek, endOfWeek, isToday, isSameDay, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, getDay } from "date-fns";

const StudentCalendarView: React.FC = () => {
  const { scheduledSessions, availabilitySlots } = useStudentAvailabilityStore();

  const [calendarView, setCalendarView] = useState<"day" | "week" | "month">("week");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string[]>(["scheduled", "completed"]);

  // Filter sessions based on search query and status filter
  const filteredSessions = scheduledSessions.filter(session => {
    const matchesSearch = searchQuery === "" ||
      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.tutorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (session.subtopic && session.subtopic.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = statusFilter.includes(session.status);

    return matchesSearch && matchesStatus;
  });

  // Navigation functions
  const nextPeriod = () => {
    if (calendarView === "day") {
      setSelectedDate(addDays(selectedDate, 1));
    } else if (calendarView === "week") {
      setSelectedDate(addDays(selectedDate, 7));
    } else if (calendarView === "month") {
      setCurrentMonth(addMonths(currentMonth, 1));
    }
  };

  const prevPeriod = () => {
    if (calendarView === "day") {
      setSelectedDate(addDays(selectedDate, -1));
    } else if (calendarView === "week") {
      setSelectedDate(addDays(selectedDate, -7));
    } else if (calendarView === "month") {
      setCurrentMonth(subMonths(currentMonth, 1));
    }
  };

  // Toggle status filter
  const toggleStatusFilter = (status: string) => {
    if (statusFilter.includes(status)) {
      setStatusFilter(statusFilter.filter(s => s !== status));
    } else {
      setStatusFilter([...statusFilter, status]);
    }
  };

  // Render day view
  const DayView = () => {
    // Get all sessions for the selected day
    const dayEvents = filteredSessions.filter(session =>
      isSameDay(session.date, selectedDate)
    );

    // Get availability for the day of week
    const dayOfWeek = [
      "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
    ][selectedDate.getDay()];

    const dayAvailability = availabilitySlots.filter(slot =>
      slot.day === dayOfWeek
    );

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium">
          {format(selectedDate, "EEEE, MMMM d, yyyy")}
        </h3>

        <div className="relative border rounded-lg overflow-hidden bg-white" style={{ height: "1200px" }}>
          {/* Time indicators */}
          <div className="absolute left-0 top-0 bottom-0 w-16 border-r bg-gray-50 flex flex-col">
            {/* Empty block at the top */}
            <div className="h-6"></div>
            {Array.from({ length: 24 }).map((_, i) => (
              <div key={i} className="flex items-center justify-center h-12 text-xs text-gray-500">
                {i === 0 ? "12:00 AM" : i < 12 ? `${i}:00 AM` : i === 12 ? "12:00 PM" : `${i - 12}:00 PM`}
              </div>
            ))}
          </div>

          {/* Calendar content */}
          <div className="ml-16 h-full relative">
            {/* Horizontal time lines */}
            {Array.from({ length: 24 }).map((_, i) => (
              <div key={i} className="absolute w-full border-t border-gray-100" style={{ top: `${i * 48 + 46}px` }}></div>
            ))}

            {/* Availability blocks */}
            {dayAvailability.map((slot, index) => {
              // Calculate position based on time
              const startHour = parseInt(slot.startTime.split(':')[0]);
              const startMinutes = parseInt(slot.startTime.split(':')[1]) || 0;

              // Position calculation (12AM is the start of our grid, offset by 46px to align with time labels)
              const topPosition = startHour * 48 + (startMinutes / 60) * 48 + 46;

              // Calculate height based on duration
              const endHour = parseInt(slot.endTime.split(':')[0]);
              const endMinutes = parseInt(slot.endTime.split(':')[1]) || 0;

              // Height calculation
              const durationHours = endHour - startHour;
              const durationMinutes = endMinutes - startMinutes;
              const height = durationHours * 48 + (durationMinutes / 60) * 48;

              // Get color based on status
              const getColor = (status: string) => {
                switch (status) {
                  case "available": return "bg-green-100 border-green-300 text-green-800";
                  case "preferred": return "bg-yellow-100 border-yellow-300 text-yellow-800";
                  case "unavailable": return "bg-red-100 border-red-300 text-red-800";
                  default: return "bg-gray-100 border-gray-300 text-gray-800";
                }
              };

              return (
                <div
                  key={`avail-${index}`}
                  className={`absolute left-0 right-0 border-l-4 px-2 py-1 opacity-50 ${getColor(slot.status)}`}
                  style={{
                    top: `${topPosition}px`,
                    height: `${height}px`,
                  }}
                >
                  <div className="text-xs font-medium">
                    {slot.status.charAt(0).toUpperCase() + slot.status.slice(1)} {slot.startTime} - {slot.endTime}
                  </div>
                </div>
              );
            })}

            {/* Session events */}
            {dayEvents.map((event) => {
              // Calculate position based on time
              const startHour = parseInt(event.startTime.split(':')[0]);
              const startMinutes = parseInt(event.startTime.split(':')[1]) || 0;
              const isPM = event.startTime.includes('PM') && startHour !== 12;
              const hour24 = isPM ? startHour + 12 : startHour;

              // Position calculation (12AM is the start of our grid, offset by 46px to align with time labels)
              const topPosition = hour24 * 48 + (startMinutes / 60) * 48 + 46;

              // Calculate height based on duration
              const endHour = parseInt(event.endTime.split(':')[0]);
              const endMinutes = parseInt(event.endTime.split(':')[1]) || 0;
              const endIsPM = event.endTime.includes('PM') && endHour !== 12;
              const endHour24 = endIsPM ? endHour + 12 : endHour;

              // Height calculation
              const durationHours = endHour24 - hour24;
              const durationMinutes = endMinutes - startMinutes;
              const height = durationHours * 48 + (durationMinutes / 60) * 48;

              // Get color based on status
              const getColor = (status: string) => {
                switch (status) {
                  case "scheduled": return "bg-blue-100 border-blue-500 text-blue-800";
                  case "completed": return "bg-green-100 border-green-500 text-green-800";
                  case "cancelled": return "bg-red-100 border-red-500 text-red-800";
                  default: return "bg-gray-100 border-gray-500 text-gray-800";
                }
              };

              return (
                <div
                  key={event.id}
                  className={`absolute left-0 right-0 border-l-4 px-2 py-1 ${getColor(event.status)}`}
                  style={{
                    top: `${topPosition}px`,
                    height: `${Math.max(height, 24)}px`,
                    zIndex: 10,
                  }}
                >
                  <div className="text-xs font-medium">{event.title}</div>
                  <div className="text-xs">{event.tutorName}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // Render week view
  const WeekView = () => {
    // Get the start and end of the week
    const weekStart = startOfWeek(selectedDate, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 1 });

    // Get all days in the week
    const weekDates = eachDayOfInterval({ start: weekStart, end: weekEnd });

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium">
          {format(weekStart, "MMM d")} - {format(weekEnd, "MMM d, yyyy")}
        </h3>

        <div className="relative border rounded-lg overflow-hidden bg-white" style={{ height: "1200px" }}>
          {/* Time indicators */}
          <div className="absolute left-0 top-0 bottom-0 w-16 border-r bg-gray-50 flex flex-col">
            {/* Empty block at the top */}
            <div className="h-6"></div>
            {Array.from({ length: 24 }).map((_, i) => (
              <div key={i} className="flex items-center justify-center h-12 text-xs text-gray-500">
                {i === 0 ? "12:00 AM" : i < 12 ? `${i}:00 AM` : i === 12 ? "12:00 PM" : `${i - 12}:00 PM`}
              </div>
            ))}
          </div>

          {/* Calendar content */}
          <div className="ml-16 grid grid-cols-7 h-full">
            {/* Header with day names */}
            <div className="absolute top-0 left-16 right-0 grid grid-cols-7 border-b bg-gray-50 z-10">
              {weekDates.map((date, index) => (
                <div
                  key={index}
                  className={`text-center py-1 text-sm font-medium ${isToday(date) ? 'bg-blue-50 text-blue-700' : ''}`}
                >
                  {format(date, "EEE")}<br />
                  <span className={`text-xs ${isToday(date) ? 'text-blue-700' : 'text-gray-500'}`}>
                    {format(date, "MMM d")}
                  </span>
                </div>
              ))}
            </div>

            {/* Grid for each day */}
            {weekDates.map((date, dayIndex) => {
              // Get day of week name for availability lookup
              const dayOfWeek = [
                "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
              ][date.getDay()];

              // Get availability for this day
              const dayAvailability = availabilitySlots.filter(slot =>
                slot.day === dayOfWeek
              );

              return (
                <div key={dayIndex} className="relative border-r h-full" style={{ marginTop: "40px" }}>
                  {/* Horizontal time lines */}
                  {Array.from({ length: 24 }).map((_, i) => (
                    <div key={i} className="absolute w-full border-t border-gray-100" style={{ top: `${i * 48 + 46 - 40}px` }}></div>
                  ))}

                  {/* Availability blocks */}
                  {dayAvailability.map((slot, index) => {
                    // Calculate position based on time
                    const startHour = parseInt(slot.startTime.split(':')[0]);
                    const startMinutes = parseInt(slot.startTime.split(':')[1]) || 0;

                    // Position calculation (12AM is the start of our grid, offset by 46px to align with time labels, minus 40px for header offset)
                    const topPosition = startHour * 48 + (startMinutes / 60) * 48 + 46 - 40;

                    // Calculate height based on duration
                    const endHour = parseInt(slot.endTime.split(':')[0]);
                    const endMinutes = parseInt(slot.endTime.split(':')[1]) || 0;

                    // Height calculation
                    const durationHours = endHour - startHour;
                    const durationMinutes = endMinutes - startMinutes;
                    const height = durationHours * 48 + (durationMinutes / 60) * 48;

                    // Get color based on status
                    const getColor = (status: string) => {
                      switch (status) {
                        case "available": return "bg-green-100 border-green-300 text-green-800";
                        case "preferred": return "bg-yellow-100 border-yellow-300 text-yellow-800";
                        case "unavailable": return "bg-red-100 border-red-300 text-red-800";
                        default: return "bg-gray-100 border-gray-300 text-gray-800";
                      }
                    };

                    return (
                      <div
                        key={`avail-${dayIndex}-${index}`}
                        className={`absolute left-0 right-0 border-l-2 px-1 opacity-50 ${getColor(slot.status)}`}
                        style={{
                          top: `${topPosition}px`,
                          height: `${height}px`,
                        }}
                      >
                        <div className="text-xs truncate">
                          {slot.status.charAt(0).toUpperCase() + slot.status.slice(1)} {slot.startTime} - {slot.endTime}
                        </div>
                      </div>
                    );
                  })}

                  {/* Events for this day */}
                  {filteredSessions
                    .filter(event => isSameDay(event.date, date))
                    .map(event => {
                      // Calculate position based on time
                      const startHour = parseInt(event.startTime.split(':')[0]);
                      const startMinutes = parseInt(event.startTime.split(':')[1]) || 0;
                      const isPM = event.startTime.includes('PM') && startHour !== 12;
                      const hour24 = isPM ? startHour + 12 : startHour;

                      // Position calculation (12AM is the start of our grid, offset by 46px to align with time labels, minus 40px for header offset)
                      const topPosition = hour24 * 48 + (startMinutes / 60) * 48 + 46 - 40;

                      // Calculate height based on duration
                      const endHour = parseInt(event.endTime.split(':')[0]);
                      const endMinutes = parseInt(event.endTime.split(':')[1]) || 0;
                      const endIsPM = event.endTime.includes('PM') && endHour !== 12;
                      const endHour24 = endIsPM ? endHour + 12 : endHour;

                      // Height calculation
                      const durationHours = endHour24 - hour24;
                      const durationMinutes = endMinutes - startMinutes;
                      const height = durationHours * 48 + (durationMinutes / 60) * 48;

                      // Get color based on status
                      const getColor = (status: string) => {
                        switch (status) {
                          case "scheduled": return "bg-blue-100 border-blue-500 text-blue-800";
                          case "completed": return "bg-green-100 border-green-500 text-green-800";
                          case "cancelled": return "bg-red-100 border-red-500 text-red-800";
                          default: return "bg-gray-100 border-gray-500 text-gray-800";
                        }
                      };

                      return (
                        <div
                          key={`event-${dayIndex}-${event.id}`}
                          className={`absolute left-0 right-0 border-l-2 px-1 ${getColor(event.status)}`}
                          style={{
                            top: `${topPosition}px`,
                            height: `${Math.max(height, 24)}px`,
                            zIndex: 10,
                          }}
                        >
                          <div className="text-xs font-medium truncate">{event.title}</div>
                          <div className="text-xs truncate">{event.tutorName}</div>
                        </div>
                      );
                    })}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // Render month view
  const MonthView = () => {
    // Get the start and end of the month
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);

    // Get all days to display (including days from previous/next month to fill the grid)
    const calendarStart = startOfWeek(monthStart, { weekStartsOn: 0 }); // Start on Sunday
    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 0 });

    // Get all days in the calendar view
    const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

    // Group days into weeks
    const weeks = [];
    for (let i = 0; i < calendarDays.length; i += 7) {
      weeks.push(calendarDays.slice(i, i + 7));
    }

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium">
          {format(currentMonth, "MMMM yyyy")}
        </h3>

        <div className="border rounded-lg overflow-hidden bg-white">
          {/* Days of week header */}
          <div className="grid grid-cols-7 border-b bg-gray-50">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
              <div key={day} className="p-3 text-center text-sm font-medium text-gray-700">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar grid */}
          <div className="grid grid-cols-7">
            {weeks.map((week, weekIndex) =>
              week.map((date, dayIndex) => {
                const isCurrentMonth = date.getMonth() === currentMonth.getMonth();
                const isTodayDate = isToday(date);
                const isSelected = isSameDay(date, selectedDate);

                // Get day of week name for availability lookup
                const dayOfWeek = [
                  "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"
                ][date.getDay()];

                // Get availability for this day
                const dayAvailability = availabilitySlots.filter(slot =>
                  slot.day === dayOfWeek
                );

                // Get sessions for this day
                const dayEvents = filteredSessions.filter(session =>
                  isSameDay(session.date, date)
                );

                return (
                  <div
                    key={`${weekIndex}-${dayIndex}`}
                    className={`
                      min-h-[120px] border-r border-b p-2 cursor-pointer hover:bg-gray-50 transition-colors
                      ${!isCurrentMonth ? 'bg-gray-50 text-gray-400' : 'bg-white'}
                      ${isTodayDate ? 'bg-blue-50' : ''}
                      ${isSelected ? 'ring-2 ring-blue-500' : ''}
                    `}
                    onClick={() => {
                      setSelectedDate(date);
                      setCalendarView("day");
                    }}
                  >
                    {/* Date number */}
                    <div className={`text-right mb-2 ${isTodayDate ? 'font-bold text-blue-600' : ''}`}>
                      {format(date, "d")}
                    </div>

                    {/* Availability slots */}
                    <div className="space-y-1 mb-2">
                      {dayAvailability.slice(0, 2).map((slot, index) => {
                        const getColor = (status: string) => {
                          switch (status) {
                            case "available": return "bg-green-100 text-green-800 border-green-200";
                            case "preferred": return "bg-yellow-100 text-yellow-800 border-yellow-200";
                            case "unavailable": return "bg-red-100 text-red-800 border-red-200";
                            default: return "bg-gray-100 text-gray-800 border-gray-200";
                          }
                        };

                        return (
                          <div
                            key={`avail-${index}`}
                            className={`text-xs px-1 py-0.5 rounded border truncate ${getColor(slot.status)}`}
                          >
                            {slot.status.charAt(0).toUpperCase() + slot.status.slice(1)} {slot.startTime}-{slot.endTime}
                          </div>
                        );
                      })}
                      {dayAvailability.length > 2 && (
                        <div className="text-xs text-gray-500 px-1">
                          +{dayAvailability.length - 2} more
                        </div>
                      )}
                    </div>

                    {/* Scheduled sessions */}
                    <div className="space-y-1">
                      {dayEvents.slice(0, 2).map((event) => {
                        const getColor = (status: string) => {
                          switch (status) {
                            case "scheduled": return "bg-blue-100 text-blue-800 border-blue-200";
                            case "completed": return "bg-green-100 text-green-800 border-green-200";
                            case "cancelled": return "bg-red-100 text-red-800 border-red-200";
                            default: return "bg-gray-100 text-gray-800 border-gray-200";
                          }
                        };

                        return (
                          <div
                            key={event.id}
                            className={`text-xs px-1 py-0.5 rounded border truncate ${getColor(event.status)}`}
                          >
                            <div className="font-medium truncate">{event.title}</div>
                            <div className="truncate">{event.startTime} - {event.tutorName}</div>
                          </div>
                        );
                      })}
                      {dayEvents.length > 2 && (
                        <div className="text-xs text-gray-500 px-1">
                          +{dayEvents.length - 2} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap justify-between items-center gap-2">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            className={calendarView === "day" ? "bg-blue-50" : ""}
            onClick={() => setCalendarView("day")}
          >
            Day
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={calendarView === "week" ? "bg-blue-50" : ""}
            onClick={() => setCalendarView("week")}
          >
            Week
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={calendarView === "month" ? "bg-blue-50" : ""}
            onClick={() => setCalendarView("month")}
          >
            Month
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={prevPeriod}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" onClick={() => setSelectedDate(new Date())}>
            Today
          </Button>
          <Button variant="outline" size="icon" onClick={nextPeriod}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search sessions..."
              className="pl-8 h-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? "bg-blue-50" : ""}
          >
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {showFilters && (
        <div className="p-3 bg-gray-50 rounded-md flex flex-wrap gap-2">
          <span className="text-sm font-medium mr-2">Status:</span>
          <Badge
            variant={statusFilter.includes("scheduled") ? "default" : "outline"}
            className="cursor-pointer"
            onClick={() => toggleStatusFilter("scheduled")}
          >
            Scheduled
          </Badge>
          <Badge
            variant={statusFilter.includes("completed") ? "default" : "outline"}
            className="cursor-pointer"
            onClick={() => toggleStatusFilter("completed")}
          >
            Completed
          </Badge>
          <Badge
            variant={statusFilter.includes("cancelled") ? "default" : "outline"}
            className="cursor-pointer"
            onClick={() => toggleStatusFilter("cancelled")}
          >
            Cancelled
          </Badge>
        </div>
      )}

      {calendarView === "day" && <DayView />}
      {calendarView === "week" && <WeekView />}
      {calendarView === "month" && <MonthView />}
    </div>
  );
};

export default StudentCalendarView;

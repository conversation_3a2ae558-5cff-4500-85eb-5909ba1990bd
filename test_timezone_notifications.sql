-- =====================================================
-- TEST SCRIPT FOR TIMEZONE NOTIFICATION SYSTEM
-- =====================================================
-- This script tests the timezone notification system to ensure it works correctly

-- =====================================================
-- 1. SETUP TEST DATA
-- =====================================================

-- Create test users (if they don't exist)
DO $$
BEGIN
    -- Test student without timezone
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>') THEN
        INSERT INTO profiles (
            id,
            first_name,
            last_name,
            user_type,
            email,
            timezone,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            'Test',
            'Student',
            'student',
            '<EMAIL>',
            NULL, -- No timezone set
            NOW(),
            NOW()
        );
    END IF;

    -- Test tutor without timezone
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>') THEN
        INSERT INTO profiles (
            id,
            first_name,
            last_name,
            user_type,
            email,
            timezone,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            'Test',
            'Tutor',
            'tutor',
            '<EMAIL>',
            NULL, -- No timezone set
            NOW(),
            NOW()
        );
    END IF;

    -- Test user with timezone (should not get notification)
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>') THEN
        INSERT INTO profiles (
            id,
            first_name,
            last_name,
            user_type,
            email,
            timezone,
            created_at,
            updated_at
        ) VALUES (
            gen_random_uuid(),
            'Test',
            'UserWithTimezone',
            'student',
            '<EMAIL>',
            'America/New_York', -- Has timezone
            NOW(),
            NOW()
        );
    END IF;
END $$;

-- =====================================================
-- 2. TEST NOTIFICATION CREATION
-- =====================================================

-- Check if notifications were created for users without timezone
SELECT 
    'Test 1: Notification Creation' as test_name,
    p.email,
    p.user_type,
    p.timezone,
    CASE 
        WHEN n.id IS NOT NULL THEN 'PASS - Notification created'
        ELSE 'FAIL - No notification found'
    END as result
FROM profiles p
LEFT JOIN notifications n ON p.id = n.user_id 
    AND n.notification_key = 'timezone_missing'
    AND n.is_system_managed = true
WHERE p.email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY p.email;

-- =====================================================
-- 3. TEST NOTIFICATION CONTENT
-- =====================================================

-- Check notification content and links
SELECT 
    'Test 2: Notification Content' as test_name,
    p.email,
    p.user_type,
    n.title,
    CASE 
        WHEN n.title = 'Set Your Timezone' THEN 'PASS - Correct title'
        ELSE 'FAIL - Wrong title: ' || COALESCE(n.title, 'NULL')
    END as title_check,
    CASE 
        WHEN n.message LIKE '%/' || p.user_type || '/account-preferences%' THEN 'PASS - Correct link'
        ELSE 'FAIL - Wrong link in message'
    END as link_check,
    CASE 
        WHEN n.is_system_managed = true THEN 'PASS - System managed'
        ELSE 'FAIL - Not system managed'
    END as system_managed_check
FROM profiles p
JOIN notifications n ON p.id = n.user_id 
    AND n.notification_key = 'timezone_missing'
WHERE p.email IN (
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY p.email;

-- =====================================================
-- 4. TEST TIMEZONE UPDATE (NOTIFICATION REMOVAL)
-- =====================================================

-- Update timezone for test student
UPDATE profiles 
SET timezone = 'America/Los_Angeles', updated_at = NOW()
WHERE email = '<EMAIL>';

-- Check if notification was removed
SELECT 
    'Test 3: Notification Removal' as test_name,
    p.email,
    p.timezone,
    CASE 
        WHEN n.id IS NULL THEN 'PASS - Notification removed'
        ELSE 'FAIL - Notification still exists'
    END as result
FROM profiles p
LEFT JOIN notifications n ON p.id = n.user_id 
    AND n.notification_key = 'timezone_missing'
    AND n.is_system_managed = true
WHERE p.email = '<EMAIL>';

-- =====================================================
-- 5. TEST TIMEZONE CLEARING (NOTIFICATION RECREATION)
-- =====================================================

-- Clear timezone for test student (should recreate notification)
UPDATE profiles 
SET timezone = NULL, updated_at = NOW()
WHERE email = '<EMAIL>';

-- Check if notification was recreated
SELECT 
    'Test 4: Notification Recreation' as test_name,
    p.email,
    p.timezone,
    CASE 
        WHEN n.id IS NOT NULL THEN 'PASS - Notification recreated'
        ELSE 'FAIL - Notification not recreated'
    END as result
FROM profiles p
LEFT JOIN notifications n ON p.id = n.user_id 
    AND n.notification_key = 'timezone_missing'
    AND n.is_system_managed = true
WHERE p.email = '<EMAIL>';

-- =====================================================
-- 6. TEST DUPLICATE PREVENTION
-- =====================================================

-- Try to create duplicate notification manually
DO $$
DECLARE
    test_user_id UUID;
    notification_count INTEGER;
BEGIN
    -- Get test user ID
    SELECT id INTO test_user_id 
    FROM profiles 
    WHERE email = '<EMAIL>';
    
    -- Try to create duplicate notification
    PERFORM create_timezone_notification(test_user_id, 'tutor');
    
    -- Count notifications for this user
    SELECT COUNT(*) INTO notification_count
    FROM notifications 
    WHERE user_id = test_user_id 
    AND notification_key = 'timezone_missing';
    
    -- Output result
    RAISE NOTICE 'Test 5: Duplicate Prevention - User has % timezone notifications (should be 1)', notification_count;
END $$;

-- =====================================================
-- 7. TEST UTILITY FUNCTIONS
-- =====================================================

-- Test cleanup function
SELECT 
    'Test 6: Cleanup Function' as test_name,
    cleanup_timezone_notifications() as cleaned_count;

-- Test bulk creation function
SELECT 
    'Test 7: Bulk Creation Function' as test_name,
    create_missing_timezone_notifications() as created_count;

-- =====================================================
-- 8. FINAL VERIFICATION
-- =====================================================

-- Show all timezone notifications in the system
SELECT 
    'Final State: All Timezone Notifications' as summary,
    p.email,
    p.user_type,
    p.timezone,
    n.title,
    n.is_system_managed,
    n.notification_key,
    n.created_at
FROM profiles p
LEFT JOIN notifications n ON p.id = n.user_id 
    AND n.notification_key = 'timezone_missing'
WHERE p.email LIKE '<EMAIL>'
ORDER BY p.email;

-- =====================================================
-- 9. CLEANUP TEST DATA (OPTIONAL)
-- =====================================================

-- Uncomment the following lines to clean up test data
/*
-- Delete test notifications
DELETE FROM notifications 
WHERE user_id IN (
    SELECT id FROM profiles 
    WHERE email LIKE '<EMAIL>'
);

-- Delete test profiles
DELETE FROM profiles 
WHERE email LIKE '<EMAIL>';
*/

-- =====================================================
-- 10. PERFORMANCE CHECK
-- =====================================================

-- Check index usage for timezone notifications
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM notifications 
WHERE user_id = (SELECT id FROM profiles LIMIT 1)
AND notification_key = 'timezone_missing'
AND is_system_managed = true;

SELECT 'Test completed successfully!' as status;

import React from "react";
import { Input } from "@/components/ui/input";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

interface NameFieldsProps {
  form: any;
  firstNameLabel?: string;
  lastNameLabel?: string;
  firstNamePlaceholder?: string;
  lastNamePlaceholder?: string;
}

const NameFields: React.FC<NameFieldsProps> = ({
  form,
  firstNameLabel = "First Name",
  lastNameLabel = "Last Name",
  firstNamePlaceholder = "John",
  lastNamePlaceholder = "Doe",
}) => {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
      <FormField
        control={form.control}
        name="firstName"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{firstNameLabel}</FormLabel>
            <FormControl>
              <Input placeholder={firstNamePlaceholder} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="lastName"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{lastNameLabel}</FormLabel>
            <FormControl>
              <Input placeholder={lastNamePlaceholder} {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default NameFields;

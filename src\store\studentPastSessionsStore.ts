import { create } from "zustand";

// Define the past session interface for student perspective
export interface PastSessionTutor {
  name: string;
  email: string;
  country: string;
}

export interface SessionParticipant {
  name: string;
  role: "Instructor" | "Student";
  joined: string;
  left: string;
  attendance: "Attended" | "Late" | "Missed" | "Left Early";
  missedReason?: string;
}

export interface StudentPastSession {
  id: string;
  externalId: string;
  tutor: PastSessionTutor;
  subject: string;
  topic: string;
  subtopic: string;
  sessionType: string;
  status: string;
  date: string;
  time: string;
  duration: number;
  scheduledDuration: number;
  studentTalkTime: number;
  whiteboardInteractions: number;
  messagesSent: number;
  messagesReceived: number;
  participationScore: number;
  participationLevel: string;
  notes: string;
  feedbackRating: number;
  tutorFeedback?: string;
  participants: SessionParticipant[];
}

// Define the store interface
interface StudentPastSessionsState {
  // Data
  sessions: StudentPastSession[];
  filteredSessions: StudentPastSession[];
  
  // UI State
  searchTerm: string;
  visibleColumns: {
    id: boolean;
    externalId: boolean;
    tutor: boolean;
    country: boolean;
    subject: boolean;
    topic: boolean;
    subtopic: boolean;
    sessionType: boolean;
    status: boolean;
    date: boolean;
    time: boolean;
    duration: boolean;
    participation: boolean;
    participationScore: boolean;
    feedbackRating: boolean;
    actions: boolean;
  };
  selectedSessions: Set<string>;
  
  // Actions
  setSearchTerm: (term: string) => void;
  setVisibleColumns: (columns: Partial<StudentPastSessionsState['visibleColumns']>) => void;
  setSelectedSessions: (sessions: Set<string>) => void;
  toggleSessionSelection: (sessionId: string) => void;
  selectAllSessions: () => void;
  clearSelection: () => void;
  filterSessions: () => void;
}

// Sample data for student past sessions
const studentPastSessionsData: StudentPastSession[] = [
  {
    id: "S-1001",
    externalId: "EXT-917",
    tutor: {
      name: "Dr. Sarah Johnson",
      email: "<EMAIL>",
      country: "United States",
    },
    subject: "Mathematics",
    topic: "Number Systems",
    subtopic: "Consolidation of Numbers up to 120",
    sessionType: "One-on-One",
    status: "Completed",
    date: "2023-11-15",
    time: "10:00 AM",
    duration: 60,
    scheduledDuration: 60,
    studentTalkTime: 22,
    whiteboardInteractions: 8,
    messagesSent: 22,
    messagesReceived: 15,
    participationScore: 8.2,
    participationLevel: "Highly Engaged",
    notes: "Great session on number consolidation. Practiced counting and grouping numbers up to 120.",
    feedbackRating: 4.8,
    tutorFeedback: "Excellent understanding of number concepts. Keep practicing counting!",
    participants: [
      {
        name: "Dr. Sarah Johnson",
        role: "Instructor",
        joined: "10:00 AM",
        left: "11:00 AM",
        attendance: "Attended",
      },
      {
        name: "You",
        role: "Student",
        joined: "10:02 AM",
        left: "11:00 AM",
        attendance: "Late",
        missedReason: "Technical issues",
      },
    ],
  },
  {
    id: "S-1002",
    externalId: "EXT-918",
    tutor: {
      name: "Prof. Michael Wilson",
      email: "<EMAIL>",
      country: "United Kingdom",
    },
    subject: "Mathematics",
    topic: "Addition and Subtraction",
    subtopic: "Addition of Two-Digit Numbers",
    sessionType: "One-on-One",
    status: "Completed",
    date: "2023-11-14",
    time: "2:30 PM",
    duration: 90,
    scheduledDuration: 90,
    studentTalkTime: 35,
    whiteboardInteractions: 15,
    messagesSent: 18,
    messagesReceived: 12,
    participationScore: 7.8,
    participationLevel: "Engaged",
    notes: "Covered addition strategies for two-digit numbers with and without regrouping.",
    feedbackRating: 4.6,
    tutorFeedback: "Good progress on addition techniques. Practice more word problems.",
    participants: [
      {
        name: "Prof. Michael Wilson",
        role: "Instructor",
        joined: "2:30 PM",
        left: "4:00 PM",
        attendance: "Attended",
      },
      {
        name: "You",
        role: "Student",
        joined: "2:30 PM",
        left: "4:00 PM",
        attendance: "Attended",
      },
    ],
  },
  {
    id: "S-1003",
    externalId: "EXT-919",
    tutor: {
      name: "Dr. Emily Chen",
      email: "<EMAIL>",
      country: "Canada",
    },
    subject: "Mathematics",
    topic: "An Introduction to Multiplication",
    subtopic: "Multiplication as Repeated Addition",
    sessionType: "Group",
    status: "Completed",
    date: "2023-11-13",
    time: "11:00 AM",
    duration: 120,
    scheduledDuration: 120,
    studentTalkTime: 28,
    whiteboardInteractions: 10,
    messagesSent: 25,
    messagesReceived: 20,
    participationScore: 8.5,
    participationLevel: "Highly Engaged",
    notes: "Great introduction to multiplication concepts. Learned how multiplication relates to repeated addition.",
    feedbackRating: 4.9,
    tutorFeedback: "Excellent questions and participation in group discussion about multiplication.",
    participants: [
      {
        name: "Dr. Emily Chen",
        role: "Instructor",
        joined: "11:00 AM",
        left: "1:00 PM",
        attendance: "Attended",
      },
      {
        name: "You",
        role: "Student",
        joined: "11:00 AM",
        left: "1:00 PM",
        attendance: "Attended",
      },
      {
        name: "Alex Thompson",
        role: "Student",
        joined: "11:05 AM",
        left: "1:00 PM",
        attendance: "Late",
        missedReason: "Traffic delay",
      },
      {
        name: "Maria Garcia",
        role: "Student",
        joined: "11:00 AM",
        left: "12:45 PM",
        attendance: "Left Early",
        missedReason: "Emergency call",
      },
    ],
  },
  {
    id: "S-1004",
    externalId: "EXT-920",
    tutor: {
      name: "Dr. James Rodriguez",
      email: "<EMAIL>",
      country: "Spain",
    },
    subject: "Mathematics",
    topic: "Number Systems",
    subtopic: "Comparing and Ordering Numbers up to 99",
    sessionType: "One-on-One",
    status: "Completed",
    date: "2023-11-12",
    time: "4:00 PM",
    duration: 75,
    scheduledDuration: 60,
    studentTalkTime: 30,
    whiteboardInteractions: 12,
    messagesSent: 20,
    messagesReceived: 16,
    participationScore: 7.5,
    participationLevel: "Engaged",
    notes: "Practiced comparing numbers using greater than, less than, and equal to symbols.",
    feedbackRating: 4.4,
    tutorFeedback: "Good understanding of number comparison concepts.",
    participants: [
      {
        name: "Dr. James Rodriguez",
        role: "Instructor",
        joined: "4:00 PM",
        left: "5:15 PM",
        attendance: "Attended",
      },
      {
        name: "You",
        role: "Student",
        joined: "4:00 PM",
        left: "5:15 PM",
        attendance: "Attended",
      },
    ],
  },
  {
    id: "S-1005",
    externalId: "EXT-921",
    tutor: {
      name: "Dr. Lisa Park",
      email: "<EMAIL>",
      country: "South Korea",
    },
    subject: "Mathematics",
    topic: "Number Systems",
    subtopic: "Numbers up to 99",
    sessionType: "One-on-One",
    status: "Missed",
    date: "2023-11-11",
    time: "9:00 AM",
    duration: 0,
    scheduledDuration: 60,
    studentTalkTime: 0,
    whiteboardInteractions: 0,
    messagesSent: 0,
    messagesReceived: 0,
    participationScore: 0,
    participationLevel: "Not Attended",
    notes: "Missed session on number recognition and counting up to 99. Need to reschedule.",
    feedbackRating: 0,
    participants: [
      {
        name: "Dr. Lisa Park",
        role: "Instructor",
        joined: "9:00 AM",
        left: "9:15 AM",
        attendance: "Attended",
      },
      {
        name: "You",
        role: "Student",
        joined: "",
        left: "",
        attendance: "Missed",
        missedReason: "Overslept",
      },
    ],
  },
];

// Create the store
export const useStudentPastSessionsStore = create<StudentPastSessionsState>((set, get) => ({
  // Initial data
  sessions: studentPastSessionsData,
  filteredSessions: studentPastSessionsData,
  
  // Initial UI state
  searchTerm: "",
  visibleColumns: {
    id: true,
    externalId: false,
    tutor: true,
    country: false,
    subject: true,
    topic: true,
    subtopic: true,
    sessionType: true,
    status: true,
    date: true,
    time: true,
    duration: true,
    participation: true,
    participationScore: false,
    feedbackRating: true,
    actions: true,
  },
  selectedSessions: new Set(),
  
  // Actions
  setSearchTerm: (term) => {
    set({ searchTerm: term });
    get().filterSessions();
  },
  
  setVisibleColumns: (columns) => {
    set({ visibleColumns: { ...get().visibleColumns, ...columns } });
  },
  
  setSelectedSessions: (sessions) => {
    set({ selectedSessions: sessions });
  },
  
  toggleSessionSelection: (sessionId) => {
    const { selectedSessions } = get();
    const newSelection = new Set(selectedSessions);
    
    if (newSelection.has(sessionId)) {
      newSelection.delete(sessionId);
    } else {
      newSelection.add(sessionId);
    }
    
    set({ selectedSessions: newSelection });
  },
  
  selectAllSessions: () => {
    const { filteredSessions } = get();
    const allIds = new Set(filteredSessions.map(session => session.id));
    set({ selectedSessions: allIds });
  },
  
  clearSelection: () => {
    set({ selectedSessions: new Set() });
  },
  
  filterSessions: () => {
    const { sessions, searchTerm } = get();
    const searchLower = searchTerm.toLowerCase();

    const filtered = sessions.filter((session) => {
      return (
        session.id.toLowerCase().includes(searchLower) ||
        session.tutor.name.toLowerCase().includes(searchLower) ||
        session.subject.toLowerCase().includes(searchLower) ||
        session.topic.toLowerCase().includes(searchLower) ||
        session.subtopic.toLowerCase().includes(searchLower) ||
        session.sessionType.toLowerCase().includes(searchLower) ||
        session.status.toLowerCase().includes(searchLower)
      );
    });

    set({ filteredSessions: filtered });
  },
}));

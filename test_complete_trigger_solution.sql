-- Comprehensive test script for the trigger solution
-- This script tests both the function creation and trigger functionality

-- Step 1: Verify the function exists
SELECT
    routine_name,
    routine_type,
    'Function exists and is ready' as status
FROM information_schema.routines
WHERE routine_name = 'handle_student_candidate_completion';

-- Step 2: Verify both functions exist
SELECT
    routine_name,
    routine_type,
    'Function exists and is ready' as status
FROM information_schema.routines
WHERE routine_name IN ('handle_student_candidate_completion', 'handle_tutor_candidate_completion');

-- Step 3: Verify all triggers exist
SELECT
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition
FROM information_schema.triggers
WHERE trigger_name IN (
    'on_student_candidate_completed',
    'on_student_candidate_updated',
    'on_tutor_candidate_completed',
    'on_tutor_candidate_updated'
)
ORDER BY trigger_name;

-- Step 3: Test INSERT scenario
-- Insert a new candidate with onboarding_completed = true (should trigger INSERT trigger)
DO $$
DECLARE
    test_id UUID := gen_random_uuid();
    test_email TEXT := 'test.insert.' || extract(epoch from now()) || '@example.com';
BEGIN
    -- Insert test candidate
    INSERT INTO candidate_student (
        id,
        first_name,
        last_name,
        email,
        phone,
        date_of_birth,
        education_level,
        subjects_of_interest,
        learning_goals,
        onboarding_completed,
        created_at,
        updated_at
    ) VALUES (
        test_id,
        'Test',
        'Student',
        test_email,
        '+1234567890',
        '2000-01-01',
        'high_school',
        ARRAY['mathematics', 'science'],
        ARRAY['improve grades', 'prepare for college'],
        true,  -- This should trigger the INSERT trigger
        NOW(),
        NOW()
    );

    -- Check if profile was created
    IF EXISTS(SELECT 1 FROM profiles WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Profile created for INSERT test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Profile NOT created for INSERT test (ID: %)', test_id;
    END IF;

    -- Check if student record was created
    IF EXISTS(SELECT 1 FROM students WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Student record created for INSERT test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Student record NOT created for INSERT test (ID: %)', test_id;
    END IF;

    -- Clean up
    DELETE FROM students WHERE id = test_id;
    DELETE FROM profiles WHERE id = test_id;
    DELETE FROM candidate_student WHERE id = test_id;
    DELETE FROM logs WHERE user_id = test_id;

    RAISE NOTICE 'INSERT test completed and cleaned up';
END $$;

-- Step 4: Test UPDATE scenario
-- Insert candidate with onboarding_completed = false, then update to true
DO $$
DECLARE
    test_id UUID := gen_random_uuid();
    test_email TEXT := 'test.update.' || extract(epoch from now()) || '@example.com';
BEGIN
    -- First insert with onboarding_completed = false
    INSERT INTO candidate_student (
        id,
        first_name,
        last_name,
        email,
        phone,
        date_of_birth,
        education_level,
        subjects_of_interest,
        learning_goals,
        onboarding_completed,
        created_at,
        updated_at
    ) VALUES (
        test_id,
        'Test',
        'Student2',
        test_email,
        '+1234567891',
        '2000-01-01',
        'high_school',
        ARRAY['mathematics', 'science'],
        ARRAY['improve grades', 'prepare for college'],
        false,  -- Start with false
        NOW(),
        NOW()
    );

    -- Now update to true (should trigger UPDATE trigger)
    UPDATE candidate_student
    SET onboarding_completed = true, updated_at = NOW()
    WHERE id = test_id;

    -- Check if profile was created
    IF EXISTS(SELECT 1 FROM profiles WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Profile created for UPDATE test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Profile NOT created for UPDATE test (ID: %)', test_id;
    END IF;

    -- Check if student record was created
    IF EXISTS(SELECT 1 FROM students WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Student record created for UPDATE test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Student record NOT created for UPDATE test (ID: %)', test_id;
    END IF;

    -- Clean up
    DELETE FROM students WHERE id = test_id;
    DELETE FROM profiles WHERE id = test_id;
    DELETE FROM candidate_student WHERE id = test_id;
    DELETE FROM logs WHERE user_id = test_id;

    RAISE NOTICE 'UPDATE test completed and cleaned up';
END $$;

-- Step 5: Test TUTOR INSERT scenario
DO $$
DECLARE
    test_id UUID := gen_random_uuid();
    test_email TEXT := 'test.tutor.insert.' || extract(epoch from now()) || '@example.com';
BEGIN
    -- Insert test tutor candidate
    INSERT INTO candidate_tutor (
        id,
        first_name,
        last_name,
        email,
        bio,
        hourly_rate,
        subjects_taught,
        education_level,
        teaching_experience,
        onboarding_completed,
        created_at,
        updated_at
    ) VALUES (
        test_id,
        'Test',
        'Tutor',
        test_email,
        'Experienced tutor in mathematics and science',
        50.00,
        ARRAY['mathematics', 'science'],
        'masters',
        '5+ years',
        true,  -- This should trigger the INSERT trigger
        NOW(),
        NOW()
    );

    -- Check if profile was created
    IF EXISTS(SELECT 1 FROM profiles WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Profile created for TUTOR INSERT test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Profile NOT created for TUTOR INSERT test (ID: %)', test_id;
    END IF;

    -- Check if tutor record was created
    IF EXISTS(SELECT 1 FROM tutors WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Tutor record created for INSERT test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Tutor record NOT created for INSERT test (ID: %)', test_id;
    END IF;

    -- Clean up
    DELETE FROM tutors WHERE id = test_id;
    DELETE FROM profiles WHERE id = test_id;
    DELETE FROM candidate_tutor WHERE id = test_id;
    DELETE FROM logs WHERE user_id = test_id;

    RAISE NOTICE 'TUTOR INSERT test completed and cleaned up';
END $$;

-- Step 6: Test TUTOR UPDATE scenario
DO $$
DECLARE
    test_id UUID := gen_random_uuid();
    test_email TEXT := 'test.tutor.update.' || extract(epoch from now()) || '@example.com';
BEGIN
    -- First insert with onboarding_completed = false
    INSERT INTO candidate_tutor (
        id,
        first_name,
        last_name,
        email,
        bio,
        hourly_rate,
        subjects_taught,
        education_level,
        teaching_experience,
        onboarding_completed,
        created_at,
        updated_at
    ) VALUES (
        test_id,
        'Test',
        'Tutor2',
        test_email,
        'Experienced tutor in mathematics and science',
        45.00,
        ARRAY['mathematics', 'physics'],
        'bachelors',
        '3+ years',
        false,  -- Start with false
        NOW(),
        NOW()
    );

    -- Now update to true (should trigger UPDATE trigger)
    UPDATE candidate_tutor
    SET onboarding_completed = true, updated_at = NOW()
    WHERE id = test_id;

    -- Check if profile was created
    IF EXISTS(SELECT 1 FROM profiles WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Profile created for TUTOR UPDATE test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Profile NOT created for TUTOR UPDATE test (ID: %)', test_id;
    END IF;

    -- Check if tutor record was created
    IF EXISTS(SELECT 1 FROM tutors WHERE id = test_id) THEN
        RAISE NOTICE 'SUCCESS: Tutor record created for UPDATE test (ID: %)', test_id;
    ELSE
        RAISE NOTICE 'FAILED: Tutor record NOT created for UPDATE test (ID: %)', test_id;
    END IF;

    -- Clean up
    DELETE FROM tutors WHERE id = test_id;
    DELETE FROM profiles WHERE id = test_id;
    DELETE FROM candidate_tutor WHERE id = test_id;
    DELETE FROM logs WHERE user_id = test_id;

    RAISE NOTICE 'TUTOR UPDATE test completed and cleaned up';
END $$;

-- Step 7: Check logs for any errors during testing
SELECT
    created_at,
    level,
    message,
    context->>'source' as source,
    context->>'action' as action
FROM logs
WHERE context->>'source' IN ('handle_student_candidate_completion', 'handle_tutor_candidate_completion')
ORDER BY created_at DESC
LIMIT 20;

-- Step 8: Final verification
SELECT
    'Student Trigger Function' as component,
    CASE
        WHEN EXISTS(SELECT 1 FROM information_schema.routines WHERE routine_name = 'handle_student_candidate_completion')
        THEN 'EXISTS'
        ELSE 'MISSING'
    END as status

UNION ALL

SELECT
    'Tutor Trigger Function' as component,
    CASE
        WHEN EXISTS(SELECT 1 FROM information_schema.routines WHERE routine_name = 'handle_tutor_candidate_completion')
        THEN 'EXISTS'
        ELSE 'MISSING'
    END as status

UNION ALL

SELECT
    'Student INSERT Trigger' as component,
    CASE
        WHEN EXISTS(SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_student_candidate_completed')
        THEN 'EXISTS'
        ELSE 'MISSING'
    END as status

UNION ALL

SELECT
    'Student UPDATE Trigger' as component,
    CASE
        WHEN EXISTS(SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_student_candidate_updated')
        THEN 'EXISTS'
        ELSE 'MISSING'
    END as status

UNION ALL

SELECT
    'Tutor INSERT Trigger' as component,
    CASE
        WHEN EXISTS(SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_tutor_candidate_completed')
        THEN 'EXISTS'
        ELSE 'MISSING'
    END as status

UNION ALL

SELECT
    'Tutor UPDATE Trigger' as component,
    CASE
        WHEN EXISTS(SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_tutor_candidate_updated')
        THEN 'EXISTS'
        ELSE 'MISSING'
    END as status;

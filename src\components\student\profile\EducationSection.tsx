import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Edit, School } from "lucide-react";
import EditSectionModal, { FormInput } from "@/components/student/profile/EditSectionModal";
import { StudentExtendedProfileData } from "@/pages/student/Profile";

interface EducationSectionProps {
  profileData: StudentExtendedProfileData;
  editData: Partial<StudentExtendedProfileData>;
  activeSectionEdit: string | null;
  setActiveSectionEdit: (section: string | null) => void;
  updateEditData: (field: string, value: any) => void;
  updateProfile: (data: Partial<StudentExtendedProfileData>) => Promise<void>;
  getEducationLevelText: (level: string) => string;
}

const EducationSection: React.FC<EducationSectionProps> = ({
  profileData,
  editData,
  activeSectionEdit,
  setActiveSectionEdit,
  updateEditData,
  update<PERSON>ro<PERSON><PERSON>,
  getEducationLevelText
}) => {
  return (
    <>
      {/* Education Section */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Education</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setActiveSectionEdit('education')}
          >
            <Edit className="h-3.5 w-3.5 mr-1" />
            Edit
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                <School className="h-6 w-6 text-gray-500" />
              </div>
              <div>
                <h3 className="font-medium">{profileData.academic_history.school_name || "School not specified"}</h3>
                <p className="text-sm text-gray-500">
                  {getEducationLevelText(profileData.education_level) || 'Grade not specified'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Education Edit Modal */}
      <EditSectionModal
        isOpen={activeSectionEdit === 'education'}
        onClose={() => setActiveSectionEdit(null)}
        title="Edit Education"
        onSubmit={() => {
          updateProfile(editData);
          setActiveSectionEdit(null);
        }}
        layout="default"
      >
        <FormInput
          label="School Name"
          id="school_name"
          placeholder="Enter your school name"
          helpText="The name of your current school or institution"
          value={editData.academic_history?.school_name || ""}
          onChange={(e) => updateEditData("academic_history", {
            ...editData.academic_history,
            school_name: e.target.value
          })}
        />

        <FormInput
          label="Education Level"
          id="education_level"
          placeholder="Enter your highest pursuing grade"
          helpText="Your current grade or education level"
          value={editData.education_level || ""}
          onChange={(e) => updateEditData("education_level", e.target.value)}
        />
      </EditSectionModal>
    </>
  );
};

export default EducationSection;

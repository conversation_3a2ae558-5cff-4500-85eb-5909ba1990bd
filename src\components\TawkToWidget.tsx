import { useEffect, useRef } from "react";
import { config } from "@/config/environment";
import { useAuth } from "@/context/AuthContext";

type TawkToWidgetProps = {
  name?: string;
  email?: string;
};

const TawkToWidget = ({ name, email }: TawkToWidgetProps) => {
  const isLoadedRef = useRef(false);
  const { user, isInitialized } = useAuth();

  useEffect(() => {
    console.log("TawkTo: Component effect running...");

    // Only show widget for logged-out users
    if (user) {
      console.log("TawkTo: User is logged in, hiding widget");
      // Hide the widget if it exists
      if ((window as any).Tawk_API && (window as any).Tawk_API.hideWidget) {
        (window as any).Tawk_API.hideWidget();
      }
      return;
    }

    // Wait for auth to be initialized before proceeding
    if (!isInitialized) {
      console.log("TawkTo: Auth not initialized yet, waiting...");
      return;
    }

    console.log("TawkTo: User is logged out, showing widget");

    // Check if TawkTo is enabled and configured
    if (!config.tawkTo.enabled) {
      console.log("TawkTo: Widget is disabled in configuration");
      return;
    }

    if (!config.tawkTo.propertyId || !config.tawkTo.widgetId) {
      console.warn("TawkTo: Missing propertyId or widgetId in configuration. Please check your .env file and TAWKTO_SETUP.md for setup instructions.");
      return;
    }

    // Function to update visitor info
    const updateVisitorInfo = () => {
      if (name && email && (window as any).Tawk_API) {
        console.log("TawkTo: Setting visitor info", { name, email });
        (window as any).Tawk_API.visitor = { name, email };
      }
    };

    // Prevent multiple script loads
    if (isLoadedRef.current || document.getElementById("tawkto-script")) {
      console.log("TawkTo: Script already loaded, updating visitor info only");
      updateVisitorInfo();
      return;
    }

    console.log("TawkTo: Loading widget script...");

    // Initialize Tawk_API
    (window as any).Tawk_API = (window as any).Tawk_API || {};
    (window as any).Tawk_LoadStart = new Date();

    // Create and load script
    const script = document.createElement("script");
    script.src = `https://embed.tawk.to/${config.tawkTo.propertyId}/${config.tawkTo.widgetId}`;
    script.async = true;
    script.setAttribute("crossorigin", "*");
    script.id = "tawkto-script";

    // Handle script load success
    script.onload = () => {
      console.log("TawkTo: Script loaded successfully");
      isLoadedRef.current = true;
      updateVisitorInfo();
    };

    // Handle script load error
    script.onerror = (error) => {
      console.error("TawkTo: Failed to load script", error);
      console.error("TawkTo: This usually means the Property ID or Widget ID is incorrect.");
      console.error("TawkTo: Please check your .env file and verify your TawkTo configuration.");
      console.error("TawkTo: Current configuration:", {
        propertyId: config.tawkTo.propertyId,
        widgetId: config.tawkTo.widgetId,
        scriptUrl: `https://embed.tawk.to/${config.tawkTo.propertyId}/${config.tawkTo.widgetId}`
      });
      isLoadedRef.current = false;
    };

    document.body.appendChild(script);

    // Show the widget if it was previously hidden
    if ((window as any).Tawk_API && (window as any).Tawk_API.showWidget) {
      (window as any).Tawk_API.showWidget();
    }

    // Don't cleanup on unmount to prevent widget from disappearing
    // The widget should persist across page navigation
    return () => {
      // Only update visitor info on prop changes, don't remove the widget
      console.log("TawkTo: Component unmounting, keeping widget active");
    };
  }, [user, isInitialized]); // Include auth state in dependencies

  // Separate effect for updating visitor info when props change
  useEffect(() => {
    // Only update visitor info if user is logged out
    if (!user && isLoadedRef.current && (window as any).Tawk_API) {
      console.log("TawkTo: Updating visitor info due to prop change", { name, email });
      if (name && email) {
        (window as any).Tawk_API.visitor = { name, email };
      }
    }
  }, [name, email, user]);

  return null;
};

export default TawkToWidget;

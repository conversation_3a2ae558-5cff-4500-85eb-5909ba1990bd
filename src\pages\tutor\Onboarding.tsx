import React, { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import OnboardingLayout from "@/components/onboarding/OnboardingLayout";
import TutorOnboardingStepOne from "@/components/onboarding/tutor/TutorOnboardingStepOne";
import TutorOnboardingStepTwo from "@/components/onboarding/tutor/TutorOnboardingStepTwo";
import TutorOnboardingStepThree from "@/components/onboarding/tutor/TutorOnboardingStepThree";
import TutorOnboardingStepFour from "@/components/onboarding/tutor/TutorOnboardingStepFour";
import { useAuth } from "@/context/AuthContext";
import { AlertCircle, RefreshCw } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import useScrollToTop from "@/hooks/useScrollToTop";
import { useTutorOnboardingStore } from "@/store/tutorOnboardingStore";
import { ROUTES } from "@/routes/RouteConfig";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useRetryOperation } from "@/hooks/useRetryOperation";
import ProcessStepsModal from "@/components/ui/ProcessStepsModal";
import { useProcessStepsStore } from "@/store/processStepsStore";
import { useAuthStore } from "@/store/authStore";
import { Button } from "@/components/ui/button";
import { create } from "zustand";

// Create a Zustand store for the onboarding UI state
interface OnboardingUIState {
  error: string | null;
  isSubmitting: boolean;
  showRefreshButton: boolean;
  setError: (error: string | null) => void;
  setIsSubmitting: (isSubmitting: boolean) => void;
  setShowRefreshButton: (show: boolean) => void;
  resetState: () => void;
}

const useOnboardingUIStore = create<OnboardingUIState>((set) => ({
  error: null,
  isSubmitting: false,
  showRefreshButton: false,
  setError: (error) => set({ error }),
  setIsSubmitting: (isSubmitting) => set({ isSubmitting }),
  setShowRefreshButton: (show) => set({ showRefreshButton: show }),
  resetState: () =>
    set({ error: null, isSubmitting: false, showRefreshButton: false }),
}));

const TutorOnboarding = () => {
  const { user, refreshUserData, isOnboarded } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Use Zustand store for UI state
  const {
    error,
    isSubmitting,
    showRefreshButton,
    setError,
    setIsSubmitting,
    setShowRefreshButton,
    resetState,
  } = useOnboardingUIStore();

  // Use Zustand store for all state management
  const {
    currentStep,
    nextStep,
    prevStep,
    isNextDisabled,
    resetStore,
    selectedSubjects,
    educationLevel,
    teachingExperience,
    firstName,
    lastName,
    bio,
    hourlyRate,
  } = useTutorOnboardingStore();

  // Also get the onboarding status from the Zustand store
  const storeIsOnboarded = useAuthStore((state) => state.isOnboarded);

  // Get actions from the process steps store
  const { startProcess, updateStep, open, close, timedOut } =
    useProcessStepsStore();

  // Function to handle page refresh
  const handleRefresh = () => {
    console.log("Forcing complete page reload");
    // Clear any persisted state that might be causing issues
    sessionStorage.removeItem("retry_tutor_onboarding_completion");
    localStorage.removeItem("processStepsState");
    
    // Force a hard reload from the server, not from cache
    window.location.href = window.location.href.split('#')[0];
    // Use a TypeScript-compatible approach for the fallback
    setTimeout(() => window.location.reload(), 100);
  };

  // Check for timeout and show refresh button if needed
  useEffect(() => {
    if (timedOut) {
      console.log("Process timed out, showing refresh button");
      setError(
        "The operation is taking longer than expected. Please refresh the page and try again."
      );
      setShowRefreshButton(true);
      setIsSubmitting(false);
      
      // Reset the process steps store to ensure modal can be closed
      useProcessStepsStore.getState().resetState();
    }
  }, [timedOut, setError, setShowRefreshButton, setIsSubmitting]);

  // Helper function to create a delay promise
  const delay = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms));

  // Complete the onboarding process
  const completeOnboarding = async () => {
    if (!user) {
      setError("User not authenticated. Please log in again.");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setShowRefreshButton(false);

    // Initialize the process steps modal with our steps and prevent resets
    const processStore = useProcessStepsStore.getState();
    processStore.startProcess("Onboarding Status", [
      { id: "saving", label: "Saving details", status: "pending" },
      {
        id: "onboarding",
        label: "Updating onboarding status",
        status: "pending",
      },
      { id: "redirect", label: "Redirect to dashboard", status: "pending" },
    ]);
    
    // Ensure the process state persists during navigation
    processStore.persistForNavigation();

    try {
      // Update first step to loading
      processStore.updateStep("saving", "loading");

      // Save tutor data to candidate_tutor table
      const { error: supabaseError } = await supabase
        .from("candidate_tutor")
        .upsert({
          id: user.id,
          email: user.email,
          first_name: firstName,
          last_name: lastName,
          bio: bio,
          hourly_rate: parseFloat(hourlyRate),
          subjects_taught: selectedSubjects,
          education_level: educationLevel,
          teaching_experience: teachingExperience,
          onboarding_completed: true,
        });

      if (supabaseError) throw supabaseError;

      // Update saving step to complete
      processStore.updateStep("saving", "complete");
      await delay(500);

      // Update onboarding step to loading
      processStore.updateStep("onboarding", "loading");

      // Refresh user data to get latest onboarding status
      await refreshUserData();

      // Get the latest values from both context and store
      const contextIsOnboarded = isOnboarded;
      const storeIsOnboarded = useAuthStore.getState().isOnboarded;
      const effectiveIsOnboarded = contextIsOnboarded || storeIsOnboarded;

      console.log("Checking onboarding status after refresh:", {
        contextIsOnboarded,
        storeIsOnboarded,
        effectiveIsOnboarded,
      });

      // If onboarding is already completed, proceed with UI updates
      if (effectiveIsOnboarded) {
        await handleOnboardingComplete();
      } else {
        // Otherwise, start the retry operation
        console.log(
          "Onboarding status not yet updated, starting retry operation"
        );
        onboardingStatusRetry.execute();
      }
    } catch (error: any) {
      console.error("Error completing onboarding:", error);
      processStore.updateStep("saving", "error");
      setError(
        error.message || "Failed to complete onboarding. Please try again."
      );
      setIsSubmitting(false);
      
      // Allow resets again and close the modal
      processStore.preventStateReset(false);
      processStore.close();
    }
  };

  // Helper function to handle onboarding completion
  const handleOnboardingComplete = async () => {
    try {
      console.log("Starting onboarding completion UI sequence");
      
      // Get the process store
      const processStore = useProcessStepsStore.getState();
      
      // Ensure the modal is open with the correct steps
      const { isOpen, steps } = processStore;
      console.log("Current modal state:", { isOpen, steps: steps.map(s => s.id) });
      
      // If modal is not open or steps are missing, reinitialize
      if (!isOpen || steps.length < 3) {
        console.log("Modal needs to be reinitialized");
        processStore.startProcess("Onboarding Status", [
          { id: "saving", label: "Saving details", status: "complete" },
          { id: "onboarding", label: "Updating onboarding status", status: "pending" },
          { id: "redirect", label: "Redirect to dashboard", status: "pending" },
        ]);
        
        // Ensure the process state persists during navigation
        processStore.persistForNavigation();
        
        // Short delay to ensure the modal is rendered
        await delay(100);
      }
      
      // Now update the steps
      console.log("Setting onboarding step to complete");
      processStore.updateStep("onboarding", "complete");
      await delay(1000);
      
      // Update redirect step to loading
      console.log("Setting redirect step to loading");
      processStore.updateStep("redirect", "loading");
      await delay(1000);
      
      // Update redirect step to complete
      console.log("Setting redirect step to complete");
      processStore.updateStep("redirect", "complete");
      await delay(1000);
      
      // Make sure the state persists during navigation
      processStore.persistForNavigation();
      
      // Allow resets after navigation is complete (will be handled by Dashboard)
      processStore.preventStateReset(false);
      
      // Navigate to dashboard with simple state
      console.log("All steps completed, redirecting to dashboard");
      navigate(ROUTES.TUTOR_DASHBOARD.path, {
        state: {
          onboardingComplete: true,
          showWelcome: true
        },
      });
    } catch (error) {
      console.error("Error during redirect sequence:", error);
      setError("An error occurred during the final steps. Please try again.");
      setIsSubmitting(false);
      
      // Allow resets again in case of error
      useProcessStepsStore.getState().preventStateReset(false);
    }
  };

  // Create a retry operation for checking onboarding status
  const onboardingStatusRetry = useRetryOperation(
    async () => {
      // Refresh user data to get latest onboarding status
      await refreshUserData();

      // Get the latest values from both context and store
      const contextIsOnboarded = isOnboarded;
      const storeIsOnboarded = useAuthStore.getState().isOnboarded;
      const effectiveIsOnboarded = contextIsOnboarded || storeIsOnboarded;

      console.log("Checking onboarding status in retry:", {
        contextIsOnboarded,
        storeIsOnboarded,
        effectiveIsOnboarded,
      });

      // Check if onboarding is now completed
      if (effectiveIsOnboarded) {
        await handleOnboardingComplete();
        return true;
      }

      console.log("Onboarding status not yet updated, retrying...");
      return false;
    },
    {
      maxAttempts: 5,
      retryInterval: 2000,
      persistKey: "tutor_onboarding_completion",
    }
  );

  useScrollToTop();

  // Total steps constant
  const totalSteps = 4;

  // If user is not authenticated, redirect to login
  useEffect(() => {
    if (!user && !location.state?.user) {
      navigate(ROUTES.LOGIN.path);
    }

    // Reset store when component unmounts
    return () => {
      resetStore();
      useProcessStepsStore.getState().resetState();
      resetState();
    };
  }, [user, location.state, navigate, resetStore, resetState]);

  // Handle the next button click with completion logic
  const handleNext = () => {
    // Clear any previous errors when moving between steps
    setError(null);

    if (currentStep < totalSteps) {
      nextStep();
    } else {
      completeOnboarding();
    }
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <TutorOnboardingStepOne />;
      case 2:
        return <TutorOnboardingStepTwo />;
      case 3:
        return <TutorOnboardingStepThree />;
      case 4:
        return (
          <TutorOnboardingStepFour
            firstName={firstName}
            setFirstName={(name) =>
              useTutorOnboardingStore.getState().setFirstName(name)
            }
            lastName={lastName}
            setLastName={(name) =>
              useTutorOnboardingStore.getState().setLastName(name)
            }
            bio={bio}
            setBio={(bio) => useTutorOnboardingStore.getState().setBio(bio)}
            hourlyRate={hourlyRate}
            setHourlyRate={(rate) =>
              useTutorOnboardingStore.getState().setHourlyRate(rate)
            }
          />
        );
      default:
        return null;
    }
  };

  // Show error if retry operation fails
  useEffect(() => {
    if (onboardingStatusRetry.error) {
      setError(
        "Failed to verify onboarding completion. Please try again or contact support."
      );
      setIsSubmitting(false);
      setShowRefreshButton(true);
    }
  }, [
    onboardingStatusRetry.error,
    setError,
    setIsSubmitting,
    setShowRefreshButton,
  ]);

  // Clean up retry operation when component unmounts
  useEffect(() => {
    return () => {
      // Reset the retry operation when component unmounts
      onboardingStatusRetry.reset();
    };
  }, []);

  return (
    <>
      <OnboardingLayout
        currentStep={currentStep}
        totalSteps={totalSteps}
        nextStep={handleNext}
        prevStep={prevStep}
        isNextDisabled={isNextDisabled() || isSubmitting}
        nextButtonText={
          currentStep === totalSteps
            ? isSubmitting
              ? "Submitting..."
              : "Complete"
            : "Next"
        }
      >
        {/* Error message alert */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
            {showRefreshButton && (
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={handleRefresh}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Page
              </Button>
            )}
          </Alert>
        )}

        {renderStep()}
      </OnboardingLayout>

      {/* New Process Steps Modal */}
      <ProcessStepsModal />
    </>
  );
};

export default TutorOnboarding;











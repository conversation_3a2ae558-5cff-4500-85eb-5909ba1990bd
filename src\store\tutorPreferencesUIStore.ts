import { create } from 'zustand';

// Type for tutor preferences (matching the structure from tutorAvailabilityStore)
interface TutorPreferences {
  defaultWorkingHours: {
    startTime: string;
    endTime: string;
  };
  sessionDurations: number[];
  bufferTime: number;
  doNotDisturbHours: {
    startTime: string;
    endTime: string;
  };
  timezone: string;
}

interface TutorPreferencesUIState {
  // UI state
  isEditing: boolean;
  selectedTimezone: string;
  isSavingTimezone: boolean;
  isDetecting: boolean;
  editedPreferences: TutorPreferences;
  newDuration: string;

  // Actions
  setIsEditing: (isEditing: boolean) => void;
  setSelectedTimezone: (timezone: string) => void;
  setIsSavingTimezone: (isSaving: boolean) => void;
  setIsDetecting: (isDetecting: boolean) => void;
  setEditedPreferences: (preferences: TutorPreferences) => void;
  setNewDuration: (duration: string) => void;

  // Combined actions
  initializePreferences: (preferences: TutorPreferences) => void;
  resetToDefaults: (preferences: TutorPreferences) => void;
  cancelEditing: (originalPreferences: TutorPreferences) => void;
}

export const useTutorPreferencesUIStore = create<TutorPreferencesUIState>((set, get) => ({
  // Initial state
  isEditing: false,
  selectedTimezone: '',
  isSavingTimezone: false,
  isDetecting: false,
  editedPreferences: {
    defaultWorkingHours: {
      startTime: "09:00",
      endTime: "17:00",
    },
    sessionDurations: [30, 45, 60],
    bufferTime: 15,
    doNotDisturbHours: {
      startTime: "22:00",
      endTime: "08:00",
    },
    timezone: '',
  },
  newDuration: '',

  // Basic setters
  setIsEditing: (isEditing: boolean) => set({ isEditing }),
  setSelectedTimezone: (timezone: string) => set({ selectedTimezone: timezone }),
  setIsSavingTimezone: (isSaving: boolean) => set({ isSavingTimezone: isSaving }),
  setIsDetecting: (isDetecting: boolean) => set({ isDetecting }),
  setEditedPreferences: (preferences: TutorPreferences) => set({ editedPreferences: preferences }),
  setNewDuration: (duration: string) => set({ newDuration: duration }),

  // Combined actions
  initializePreferences: (preferences: TutorPreferences) => {
    set({
      selectedTimezone: preferences.timezone || '',
      editedPreferences: preferences,
    });
  },

  resetToDefaults: (preferences: TutorPreferences) => {
    set({
      isEditing: false,
      selectedTimezone: preferences.timezone || '',
      isSavingTimezone: false,
      isDetecting: false,
      editedPreferences: preferences,
      newDuration: '',
    });
  },

  cancelEditing: (originalPreferences: TutorPreferences) => {
    set({
      isEditing: false,
      selectedTimezone: originalPreferences.timezone || '',
      editedPreferences: originalPreferences,
      newDuration: '',
    });
  },
}));

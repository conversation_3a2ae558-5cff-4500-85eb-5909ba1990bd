-- Razorpay Integration Migration Script
-- This script applies the multi-provider payment schema with Razorpay as the primary provider

BEGIN;

-- =====================================================
-- 1. CREATE PAYMENT PROVIDERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS payment_providers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    configuration JSONB,
    supported_currencies TEXT[],
    supported_methods TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Insert payment providers with <PERSON><PERSON>pay as primary
INSERT INTO payment_providers (name, display_name, supported_currencies, supported_methods, is_active) VALUES
('razorpay', 'Razorpay', ARRAY['inr'], ARRAY['card', 'netbanking', 'wallet', 'upi', 'emi'], true),
('stripe', 'Stripe', ARRAY['usd', 'eur', 'gbp', 'cad'], ARRAY['card', 'bank_transfer', 'apple_pay', 'google_pay'], false),
('paypal', 'PayPal', ARRAY['usd', 'eur', 'gbp', 'cad'], ARRAY['paypal_account', 'card'], false)
ON CONFLICT (name) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    supported_currencies = EXCLUDED.supported_currencies,
    supported_methods = EXCLUDED.supported_methods,
    is_active = EXCLUDED.is_active,
    updated_at = now();

-- =====================================================
-- 2. ENHANCE EXISTING TABLES
-- =====================================================

-- Add provider support to payments table
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
ADD COLUMN IF NOT EXISTS provider_payment_id TEXT,
ADD COLUMN IF NOT EXISTS provider_customer_id TEXT,
ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS provider_created_at TIMESTAMP;

-- Make stripe fields nullable for backward compatibility
ALTER TABLE payments ALTER COLUMN stripe_payment_intent_id DROP NOT NULL;

-- Add constraint to ensure either stripe or generic provider fields are used
ALTER TABLE payments DROP CONSTRAINT IF EXISTS check_payment_provider;
ALTER TABLE payments ADD CONSTRAINT check_payment_provider 
CHECK (
    (stripe_payment_intent_id IS NOT NULL AND provider_id IS NULL) OR
    (stripe_payment_intent_id IS NULL AND provider_id IS NOT NULL AND provider_payment_id IS NOT NULL)
);

-- Add provider support to subscription_workflows
ALTER TABLE subscription_workflows 
ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
ADD COLUMN IF NOT EXISTS provider_order_id TEXT,
ADD COLUMN IF NOT EXISTS provider_customer_id TEXT,
ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}';

-- Update currency default to INR for Razorpay
ALTER TABLE subscription_workflows ALTER COLUMN currency SET DEFAULT 'inr';
ALTER TABLE payments ALTER COLUMN currency SET DEFAULT 'inr';

-- Add provider support to subscriptions table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions') THEN
        ALTER TABLE subscriptions 
        ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
        ADD COLUMN IF NOT EXISTS provider_subscription_id TEXT,
        ADD COLUMN IF NOT EXISTS provider_customer_id TEXT,
        ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}';
        
        -- Update currency default
        ALTER TABLE subscriptions ALTER COLUMN currency SET DEFAULT 'inr';
    END IF;
END $$;

-- Add provider support to invoices table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoices') THEN
        ALTER TABLE invoices 
        ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
        ADD COLUMN IF NOT EXISTS provider_invoice_id TEXT,
        ADD COLUMN IF NOT EXISTS provider_customer_id TEXT,
        ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}';
        
        -- Update currency default
        ALTER TABLE invoices ALTER COLUMN currency SET DEFAULT 'inr';
    END IF;
END $$;

-- Add provider support to payment_methods table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payment_methods') THEN
        ALTER TABLE payment_methods 
        ADD COLUMN IF NOT EXISTS provider_id UUID REFERENCES payment_providers(id),
        ADD COLUMN IF NOT EXISTS provider_payment_method_id TEXT,
        ADD COLUMN IF NOT EXISTS provider_customer_id TEXT,
        ADD COLUMN IF NOT EXISTS provider_metadata JSONB DEFAULT '{}';
        
        -- Make stripe fields nullable
        ALTER TABLE payment_methods ALTER COLUMN stripe_payment_method_id DROP NOT NULL;
        ALTER TABLE payment_methods ALTER COLUMN stripe_customer_id DROP NOT NULL;
    END IF;
END $$;

-- =====================================================
-- 3. CREATE INDEXES
-- =====================================================

-- Payment providers indexes
CREATE INDEX IF NOT EXISTS idx_payment_providers_name ON payment_providers(name);
CREATE INDEX IF NOT EXISTS idx_payment_providers_active ON payment_providers(is_active);

-- Payments table indexes
CREATE INDEX IF NOT EXISTS idx_payments_provider_id ON payments(provider_id);
CREATE INDEX IF NOT EXISTS idx_payments_provider_payment_id ON payments(provider_payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_provider_customer_id ON payments(provider_customer_id);

-- Subscription workflows indexes
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_provider_id ON subscription_workflows(provider_id);
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_provider_order_id ON subscription_workflows(provider_order_id);

-- =====================================================
-- 4. HELPER FUNCTIONS
-- =====================================================

-- Function to get payment provider by name
CREATE OR REPLACE FUNCTION get_payment_provider(provider_name TEXT)
RETURNS UUID AS $$
DECLARE
    provider_id UUID;
BEGIN
    SELECT id INTO provider_id 
    FROM payment_providers 
    WHERE name = provider_name AND is_active = TRUE;
    
    IF provider_id IS NULL THEN
        RAISE EXCEPTION 'Payment provider % not found or inactive', provider_name;
    END IF;
    
    RETURN provider_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create payment with provider
CREATE OR REPLACE FUNCTION create_payment_with_provider(
    p_workflow_id UUID,
    p_student_id UUID,
    p_provider_name TEXT,
    p_provider_payment_id TEXT,
    p_amount DECIMAL(10,2),
    p_currency TEXT DEFAULT 'inr',
    p_description TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    payment_id UUID;
    provider_id UUID;
BEGIN
    -- Get provider ID
    provider_id := get_payment_provider(p_provider_name);
    
    -- Create payment record
    INSERT INTO payments (
        workflow_id,
        student_id,
        provider_id,
        provider_payment_id,
        amount,
        currency,
        status,
        description
    ) VALUES (
        p_workflow_id,
        p_student_id,
        provider_id,
        p_provider_payment_id,
        p_amount,
        p_currency,
        'pending',
        p_description
    ) RETURNING id INTO payment_id;
    
    RETURN payment_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update payment status (provider agnostic)
CREATE OR REPLACE FUNCTION update_payment_status_generic(
    p_provider_payment_id TEXT,
    p_provider_name TEXT,
    p_status TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    provider_id UUID;
BEGIN
    -- Get provider ID
    provider_id := get_payment_provider(p_provider_name);
    
    -- Update payment status
    UPDATE payments 
    SET 
        status = p_status,
        succeeded_at = CASE WHEN p_status = 'succeeded' THEN NOW() ELSE succeeded_at END,
        failed_at = CASE WHEN p_status = 'failed' THEN NOW() ELSE failed_at END,
        updated_at = NOW()
    WHERE provider_payment_id = p_provider_payment_id 
    AND provider_id = provider_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. MIGRATE EXISTING STRIPE DATA (IF ANY)
-- =====================================================

DO $$
DECLARE
    stripe_provider_id UUID;
BEGIN
    -- Get Stripe provider ID
    SELECT id INTO stripe_provider_id FROM payment_providers WHERE name = 'stripe';
    
    IF stripe_provider_id IS NOT NULL THEN
        -- Update existing payments
        UPDATE payments 
        SET provider_id = stripe_provider_id,
            provider_payment_id = stripe_payment_intent_id,
            provider_customer_id = stripe_customer_id,
            provider_created_at = stripe_created_at
        WHERE stripe_payment_intent_id IS NOT NULL AND provider_id IS NULL;
        
        RAISE NOTICE 'Migrated existing Stripe payment data to multi-provider schema';
    END IF;
END $$;

-- =====================================================
-- 6. UPDATE RLS POLICIES (IF ENABLED)
-- =====================================================

-- Enable RLS on payment_providers table
ALTER TABLE payment_providers ENABLE ROW LEVEL SECURITY;

-- Allow all users to read active payment providers
CREATE POLICY IF NOT EXISTS "Allow read access to active payment providers" ON payment_providers
    FOR SELECT USING (is_active = true);

-- Allow admins to manage payment providers
CREATE POLICY IF NOT EXISTS "Allow admin access to payment providers" ON payment_providers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

COMMIT;

-- =====================================================
-- 7. VERIFICATION QUERIES
-- =====================================================

-- Verify payment providers
SELECT 'Payment Providers:' as info;
SELECT name, display_name, is_active, supported_currencies, supported_methods 
FROM payment_providers 
ORDER BY is_active DESC, name;

-- Verify table structure
SELECT 'Payment table columns:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'payments' 
AND column_name LIKE '%provider%'
ORDER BY column_name;

-- Success message
SELECT '✅ Razorpay integration migration completed successfully!' as result;

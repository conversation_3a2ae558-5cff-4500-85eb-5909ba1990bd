import React, { useRef } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  GraduationCap,
  Users,
  BookOpen,
  Calendar,
  Target,
  Award,
  CheckCircle,
  ArrowRight,
  Star,
  Clock,
  MessageCircle,
  TrendingUp,
  Shield,
  Zap,
  Heart,
  Globe,
  PlayCircle,
  FileText,
  BarChart3,
  <PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON>,
  Sparkles
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import AnimatedFavicon from '@/components/AnimatedFavicon';
import { useCountryPricing } from '@/hooks/useCountryPricing';

// Icon mapping for dynamic icon rendering
const iconMap = {
  Zap,
  Target,
  Award
};

const HowItWorks = () => {
  const heroSectionRef = useRef<HTMLElement>(null);
  const { localizedPlans } = useCountryPricing();

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="py-12">
        {/* Hero Section */}
        <section ref={heroSectionRef} className="relative py-20 px-4 text-center overflow-hidden">
          <AnimatedFavicon containerRef={heroSectionRef} />

        <div className="relative max-w-6xl mx-auto">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Zap className="w-4 h-4" />
            Personalized Learning Platform
          </div>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            How <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">rfLearn</span> Works
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Experience personalized learning with expert tutors, structured curriculam, and AI-powered progress tracking.
            Join thousands of students achieving their academic goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Link to="/register" className="flex items-center gap-2">
                Start Learning Today <ArrowRight className="w-5 h-5" />
              </Link>
            </Button>
            <Button variant="outline" size="lg">
              <Link to="/tutor/search" className="flex items-center gap-2">
                <PlayCircle className="w-5 h-5" />
                Watch Demo
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* For Students Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
              <GraduationCap className="w-4 h-4" />
              For Students
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Your Learning Journey, Simplified</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From subscription to mastery - discover how our platform transforms your learning experience
            </p>
          </div>

          {/* Student Journey Steps */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {[
              {
                step: "01",
                title: "Choose Your Package",
                description: "Select from Complete Booster, Custom, or Preparation packages tailored to your needs",
                icon: <Target className="w-8 h-8" />,
                color: "from-blue-500 to-blue-600"
              },
              {
                step: "02",
                title: "Customize Curriculum",
                description: "Pick specific topics and subtopics or get comprehensive subject coverage",
                icon: <BookOpen className="w-8 h-8" />,
                color: "from-purple-500 to-purple-600"
              },
              {
                step: "03",
                title: "Get Matched",
                description: "Our AI matches you with expert tutors based on your subjects and learning style",
                icon: <UserCheck className="w-8 h-8" />,
                color: "from-green-500 to-green-600"
              },
              {
                step: "04",
                title: "Start Learning",
                description: "Attend sessions, track progress, and achieve your academic goals",
                icon: <TrendingUp className="w-8 h-8" />,
                color: "from-orange-500 to-orange-600"
              }
            ].map((item, index) => (
              <Card key={index} className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${item.color}`}></div>
                <CardHeader className="text-center pb-4">
                  <div className={`w-16 h-16 mx-auto rounded-full bg-gradient-to-r ${item.color} flex items-center justify-center text-white mb-4`}>
                    {item.icon}
                  </div>
                  <Badge variant="secondary" className="mb-2">{item.step}</Badge>
                  <CardTitle className="text-xl">{item.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-center">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Student Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Calendar className="w-6 h-6" />,
                title: "Flexible Scheduling",
                description: "Book sessions that fit your schedule with 24/7 availability",
                features: ["Real-time booking", "Conflict resolution", "Automatic reminders"]
              },
              {
                icon: <BarChart3 className="w-6 h-6" />,
                title: "Progress Tracking",
                description: "Monitor your learning journey with detailed analytics and insights",
                features: ["Visual progress maps", "Performance metrics", "Goal tracking"]
              },
              {
                icon: <FileText className="w-6 h-6" />,
                title: "Learning Materials",
                description: "Access comprehensive resources and session recordings",
                features: ["Digital materials", "Session recordings", "Practice exercises"]
              },
              {
                icon: <MessageCircle className="w-6 h-6" />,
                title: "Direct Communication",
                description: "Stay connected with your tutors through integrated messaging",
                features: ["In-app messaging", "Session feedback", "Quick questions"]
              },
              {
                icon: <Award className="w-6 h-6" />,
                title: "Certified Tutors",
                description: "Learn from verified experts with proven track records",
                features: ["Background verified", "Subject specialists", "Continuous training"]
              },
              {
                icon: <Shield className="w-6 h-6" />,
                title: "Secure Platform",
                description: "Safe and secure learning environment with privacy protection",
                features: ["Data encryption", "Secure payments", "Privacy controls"]
              }
            ].map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600">
                      {feature.icon}
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-gray-500">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Subscription Packages Preview */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Learning Path</h2>
            <p className="text-xl text-gray-600">Flexible packages designed for every learning goal</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {localizedPlans.map((plan) => {
              const IconComponent = iconMap[plan.icon as keyof typeof iconMap];
              return (
                <Card
                  key={plan.id}
                  className={`relative overflow-hidden ${
                    plan.popular ? 'ring-2 ring-green-500 scale-105' : ''
                  } hover:shadow-xl transition-all duration-300`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 left-0 w-full bg-green-500 text-white text-center py-2 text-sm font-medium flex items-center justify-center gap-1">
                      <Sparkles className="w-4 h-4" />
                      Most Popular
                    </div>
                  )}

                  <CardHeader className={`text-center ${plan.popular ? 'pt-12' : 'pt-6'} pb-4`}>
                    <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center mx-auto mb-3`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{plan.name}</CardTitle>
                    <p className="text-gray-600 text-sm">{plan.description}</p>

                    {/* Compact Pricing Display */}
                    <div className="mt-3">
                      <div className="flex items-center justify-center space-x-2">
                        {plan.formattedOriginalPrice && (
                          <span className="text-sm text-gray-400 line-through">
                            {plan.formattedOriginalPrice}
                          </span>
                        )}
                        <span className="text-2xl font-bold text-gray-900">
                          {plan.formattedPrice}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        {plan.priceType === 'per_session' ? '/session' : plan.duration}
                      </p>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    {/* Key Features - Limited to 4 for compact display */}
                    <ul className="space-y-2">
                      {plan.features.slice(0, 4).map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-sm">
                          <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                      {plan.features.length > 4 && (
                        <li className="text-xs text-gray-500 text-center pt-1">
                          +{plan.features.length - 4} more features
                        </li>
                      )}
                    </ul>

                    {/* Sessions Info */}
                    <div className="flex items-center justify-center space-x-2 mt-4 text-xs text-gray-600 bg-gray-50 rounded-lg py-2">
                      <Clock className="w-3 h-3" />
                      <span>{plan.sessions}</span>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* View Full Pricing Link */}
          <div className="text-center mt-8">
            <Button variant="outline" asChild>
              <Link to="/pricing" className="flex items-center gap-2">
                View Detailed Pricing
                <ArrowRight className="w-4 h-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* For Tutors Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Users className="w-4 h-4" />
              For Tutors
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Empower Students, Grow Your Career</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Join our community of expert tutors and make a meaningful impact while building your teaching career
            </p>
          </div>

          {/* Tutor Benefits Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {[
              {
                icon: <Globe className="w-6 h-6" />,
                title: "Global Reach",
                description: "Teach students from around the world with our online platform",
                highlight: "Unlimited potential"
              },
              {
                icon: <Clock className="w-6 h-6" />,
                title: "Flexible Schedule",
                description: "Set your own availability and work when it suits you best",
                highlight: "Work-life balance"
              },
              {
                icon: <TrendingUp className="w-6 h-6" />,
                title: "Competitive Rates",
                description: "Earn competitive hourly rates with transparent payment system",
                highlight: "Fair compensation"
              },
              {
                icon: <UserCheck className="w-6 h-6" />,
                title: "Student Matching",
                description: "Get matched with students who need your specific expertise",
                highlight: "Perfect fit"
              },
              {
                icon: <BarChart3 className="w-6 h-6" />,
                title: "Performance Insights",
                description: "Track your teaching effectiveness with detailed analytics",
                highlight: "Continuous improvement"
              },
              {
                icon: <Heart className="w-6 h-6" />,
                title: "Meaningful Impact",
                description: "Help students achieve their academic goals and dreams",
                highlight: "Make a difference"
              }
            ].map((benefit, index) => (
              <Card key={index} className="hover:shadow-lg transition-all duration-300 group">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center text-purple-600 group-hover:bg-purple-600 group-hover:text-white transition-colors">
                      {benefit.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{benefit.title}</CardTitle>
                      <Badge variant="secondary" className="text-xs">{benefit.highlight}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Tutor Journey */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-center mb-8">Your Journey as a Tutor</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[
                {
                  step: "Apply",
                  description: "Submit your application with credentials and expertise",
                  icon: <FileText className="w-6 h-6" />
                },
                {
                  step: "Verify",
                  description: "Complete our verification process and background check",
                  icon: <Shield className="w-6 h-6" />
                },
                {
                  step: "Onboard",
                  description: "Complete training and set up your tutor profile",
                  icon: <UserCheck className="w-6 h-6" />
                },
                {
                  step: "Teach",
                  description: "Start teaching and building your reputation",
                  icon: <GraduationCap className="w-6 h-6" />
                }
              ].map((step, index) => (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-purple-600 mx-auto mb-4 shadow-md">
                    {step.icon}
                  </div>
                  <h4 className="font-semibold text-lg mb-2">{step.step}</h4>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                  {index < 3 && (
                    <ArrowRight className="w-5 h-5 text-gray-400 mx-auto mt-4 hidden md:block" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 px-4 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Success Stories</h2>
            <p className="text-xl text-gray-600">Real results from real students and tutors</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                type: "student",
                name: "Sarah Chen",
                role: "Computer Science Student",
                quote: "rf helped me master machine learning concepts that seemed impossible before. My tutor's personalized approach made all the difference.",
                rating: 5,
                achievement: "Improved grades by 40%"
              },
              {
                type: "tutor",
                name: "Dr. Michael Rodriguez",
                role: "Mathematics Tutor",
                quote: "Teaching on rf has been incredibly rewarding. The platform's tools help me track student progress and adapt my teaching methods effectively.",
                rating: 5,
                achievement: "500+ successful sessions"
              },
              {
                type: "student",
                name: "Alex Thompson",
                role: "High School Student",
                quote: "The flexible scheduling and expert tutors helped me prepare for my SATs while managing my busy schedule. Highly recommended!",
                rating: 5,
                achievement: "SAT score increased by 200 points"
              }
            ].map((story, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                      {story.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div>
                      <h4 className="font-semibold">{story.name}</h4>
                      <p className="text-sm text-gray-600">{story.role}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 mb-2">
                    {[...Array(story.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4 italic">"{story.quote}"</p>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    {story.achievement}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Learning?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of students and tutors who are already experiencing the future of education
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
              <Link to="/register" className="flex items-center gap-2">
                <GraduationCap className="w-5 h-5" />
                Start as Student
              </Link>
            </Button>
            <Button size="lg" className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 transition-colors">
              <Link to="/become-tutor" className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Become a Tutor
              </Link>
            </Button>
          </div>
        </div>
      </section>
      </main>
      <Footer />
    </div>
  );
};

export default HowItWorks;
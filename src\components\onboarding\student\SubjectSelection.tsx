import React, { useState } from "react";
import { Check } from "lucide-react";

interface Subject {
  id: string;
  name: string;
  icon: React.ReactNode;
}

interface SubjectSelectionProps {
  selectedSubjects: string[];
  setSelectedSubjects: (subjects: string[]) => void;
}

const SubjectSelection = React.forwardRef<HTMLDivElement, SubjectSelectionProps>(({
  selectedSubjects,
  setSelectedSubjects,
}, ref) => {
  const subjects: Subject[] = [
    {
      id: "maths",
      name: "Maths",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 text-blue-500"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M2 12h20M12 2v20M17 8l-10 8M7 8l10 8" />
        </svg>
      ),
    },
    {
      id: "coding",
      name: "Coding",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 text-red-500"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <polyline points="16 18 22 12 16 6"></polyline>
          <polyline points="8 6 2 12 8 18"></polyline>
        </svg>
      ),
    },
    {
      id: "science",
      name: "Science",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 text-purple-500"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M10 2v7.31"></path>
          <path d="M14 9.3V2"></path>
          <path d="M8.5 2h7"></path>
          <path d="M14 9.3a6 6 0 1 1-4 0"></path>
        </svg>
      ),
    },
    {
      id: "languages",
      name: "Languages",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8 text-green-500"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M5 8h14M5 12h14M5 16h6"></path>
        </svg>
      ),
    },
  ];

  const toggleSubject = (subjectId: string) => {
    if (selectedSubjects.includes(subjectId)) {
      setSelectedSubjects(selectedSubjects.filter((id) => id !== subjectId));
    } else {
      setSelectedSubjects([...selectedSubjects, subjectId]);
    }
  };

  return (
    <div ref={ref}>
      <h2 className="text-2xl font-bold mb-2">
        What will you be using rfLearn for?
      </h2>
      <p className="text-gray-600 mb-6">
        We'll use this to personalize courses and tutors for you.
      </p>

      <div className="grid grid-cols-2 gap-4">
        {subjects.map((subject) => (
          <div
            key={subject.id}
            onClick={() => toggleSubject(subject.id)}
            className={`
              p-6 rounded-lg border-2 cursor-pointer transition-all
              flex flex-col items-center justify-center
              ${
                selectedSubjects.includes(subject.id)
                  ? "border-green-500 bg-green-50"
                  : "border-gray-200 hover:border-gray-300"
              }
            `}
          >
            <div className="relative">
              {selectedSubjects.includes(subject.id) && (
                <div className="absolute -top-2 -right-2 bg-green-500 rounded-full p-1">
                  <Check className="h-3 w-3 text-white" />
                </div>
              )}
              {subject.icon}
            </div>
            <span className="mt-2 font-medium">{subject.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
});

SubjectSelection.displayName = "SubjectSelection";

export default SubjectSelection;

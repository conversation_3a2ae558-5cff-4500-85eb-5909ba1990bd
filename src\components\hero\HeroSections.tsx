import React from "react";
import HeroSVGPairComponent from "./HeroSVGPair";

// Add a responsive container for the hero buttons
export const HeroButtonsContainer: React.FC<
  React.HTMLAttributes<HTMLDivElement>
> = ({ children, className, ...props }) => {
  return (
    <div
      className={`hero-buttons-container ${className || ""}`}
      {...props}
    >
      {children}
    </div>
  );
};

// Add button components for consistent styling
export const HeroButton: React.FC<
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    primary?: boolean;
  }
> = ({ children, className, primary = false, ...props }) => {
  return (
    <button
      className={`hero-button rounded-md transition-all cursor-pointer text-center flex-1
        ${
          primary
            ? "bg-[#ff7f52] hover:bg-[#ff6a3a] text-white border-none"
            : "bg-white text-[#ff7f52] border border-[#ff7f52] hover:bg-[#fff8f6]"
        } ${className || ""}`}
      {...props}
    >
      {children}
    </button>
  );
};

// Hero section with SVG graphics
export const HeroSVGPair: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={`hero-section ${className || ""}`}>
      <HeroSVGPairComponent />
    </div>
  );
};
import { supabase } from '@/lib/supabaseClient';

// Types matching the database schema
export interface TutorAvailabilitySlot {
  id: string;
  tutor_id: string;
  day_of_week: number; // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  status: 'available' | 'auto_accept' | 'manual_approval';
  created_at: string;
  updated_at: string;
}

export interface AutoAcceptRule {
  id: string;
  tutor_id: string;
  name: string;
  is_active: boolean;
  existing_students_only: boolean;
  created_at: string;
  updated_at: string;
}

export interface RuleTimeRange {
  id: string;
  rule_id: string;
  day_of_week: number;
  start_time: string | null;
  end_time: string | null;
  created_at: string;
}

export interface RuleTopic {
  id: string;
  rule_id: string;
  topic_id: string;
  created_at: string;
}

// Helper function to convert day name to day_of_week number
export const dayNameToDayOfWeek = (dayName: string): number => {
  const dayMap: Record<string, number> = {
    'Sunday': 0,
    'Monday': 1,
    'Tuesday': 2,
    'Wednesday': 3,
    'Thursday': 4,
    'Friday': 5,
    'Saturday': 6
  };
  return dayMap[dayName] ?? 1; // Default to Monday if not found
};

// Helper function to convert day_of_week number to day name
export const dayOfWeekToDayName = (dayOfWeek: number): string => {
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return dayNames[dayOfWeek] ?? 'Monday';
};

// Tutor Availability CRUD operations
export const tutorAvailabilityService = {
  // Fetch all availability slots for a tutor
  async fetchAvailabilitySlots(tutorId: string): Promise<TutorAvailabilitySlot[]> {
    const { data, error } = await supabase
      .from('tutor_availability')
      .select('*')
      .eq('tutor_id', tutorId)
      .order('day_of_week', { ascending: true })
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching availability slots:', error);
      throw new Error(`Failed to fetch availability slots: ${error.message}`);
    }

    return data || [];
  },

  // Create a new availability slot
  async createAvailabilitySlot(slot: Omit<TutorAvailabilitySlot, 'id' | 'created_at' | 'updated_at'>): Promise<TutorAvailabilitySlot> {
    const { data, error } = await supabase
      .from('tutor_availability')
      .insert([slot])
      .select()
      .single();

    if (error) {
      console.error('Error creating availability slot:', error);
      throw new Error(`Failed to create availability slot: ${error.message}`);
    }

    return data;
  },

  // Update an availability slot
  async updateAvailabilitySlot(id: string, updates: Partial<Omit<TutorAvailabilitySlot, 'id' | 'created_at' | 'updated_at'>>): Promise<TutorAvailabilitySlot> {
    const { data, error } = await supabase
      .from('tutor_availability')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating availability slot:', error);
      throw new Error(`Failed to update availability slot: ${error.message}`);
    }

    return data;
  },

  // Delete an availability slot
  async deleteAvailabilitySlot(id: string): Promise<void> {
    const { error } = await supabase
      .from('tutor_availability')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting availability slot:', error);
      throw new Error(`Failed to delete availability slot: ${error.message}`);
    }
  }
};

// Auto Accept Rules CRUD operations
export const autoAcceptRulesService = {
  // Fetch all auto accept rules for a tutor with related data
  async fetchAutoAcceptRules(tutorId: string): Promise<(AutoAcceptRule & { 
    time_ranges: RuleTimeRange[], 
    topics: (RuleTopic & { topic: { id: string, name: string } })[] 
  })[]> {
    const { data, error } = await supabase
      .from('tutor_auto_accept_rules')
      .select(`
        *,
        rule_time_ranges:rule_time_ranges(*),
        rule_topics:rule_topics(
          *,
          topic:topics(id, name)
        )
      `)
      .eq('tutor_id', tutorId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching auto accept rules:', error);
      throw new Error(`Failed to fetch auto accept rules: ${error.message}`);
    }

    return data || [];
  },

  // Create a new auto accept rule
  async createAutoAcceptRule(rule: Omit<AutoAcceptRule, 'id' | 'created_at' | 'updated_at'>): Promise<AutoAcceptRule> {
    const { data, error } = await supabase
      .from('tutor_auto_accept_rules')
      .insert([rule])
      .select()
      .single();

    if (error) {
      console.error('Error creating auto accept rule:', error);
      throw new Error(`Failed to create auto accept rule: ${error.message}`);
    }

    return data;
  },

  // Update an auto accept rule
  async updateAutoAcceptRule(id: string, updates: Partial<Omit<AutoAcceptRule, 'id' | 'created_at' | 'updated_at'>>): Promise<AutoAcceptRule> {
    const { data, error } = await supabase
      .from('tutor_auto_accept_rules')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating auto accept rule:', error);
      throw new Error(`Failed to update auto accept rule: ${error.message}`);
    }

    return data;
  },

  // Delete an auto accept rule (cascades to related time ranges and topics)
  async deleteAutoAcceptRule(id: string): Promise<void> {
    const { error } = await supabase
      .from('tutor_auto_accept_rules')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting auto accept rule:', error);
      throw new Error(`Failed to delete auto accept rule: ${error.message}`);
    }
  },

  // Add time range to a rule
  async addTimeRangeToRule(timeRange: Omit<RuleTimeRange, 'id' | 'created_at'>): Promise<RuleTimeRange> {
    const { data, error } = await supabase
      .from('rule_time_ranges')
      .insert([timeRange])
      .select()
      .single();

    if (error) {
      console.error('Error adding time range to rule:', error);
      throw new Error(`Failed to add time range to rule: ${error.message}`);
    }

    return data;
  },

  // Add topic to a rule
  async addTopicToRule(ruleTopic: Omit<RuleTopic, 'id' | 'created_at'>): Promise<RuleTopic> {
    const { data, error } = await supabase
      .from('rule_topics')
      .insert([ruleTopic])
      .select()
      .single();

    if (error) {
      console.error('Error adding topic to rule:', error);
      throw new Error(`Failed to add topic to rule: ${error.message}`);
    }

    return data;
  },

  // Remove time range from a rule
  async removeTimeRangeFromRule(timeRangeId: string): Promise<void> {
    const { error } = await supabase
      .from('rule_time_ranges')
      .delete()
      .eq('id', timeRangeId);

    if (error) {
      console.error('Error removing time range from rule:', error);
      throw new Error(`Failed to remove time range from rule: ${error.message}`);
    }
  },

  // Remove topic from a rule
  async removeTopicFromRule(ruleTopicId: string): Promise<void> {
    const { error } = await supabase
      .from('rule_topics')
      .delete()
      .eq('id', ruleTopicId);

    if (error) {
      console.error('Error removing topic from rule:', error);
      throw new Error(`Failed to remove topic from rule: ${error.message}`);
    }
  }
};

// Helper functions for data transformation
export const transformAvailabilitySlotForUI = (dbSlot: TutorAvailabilitySlot) => ({
  id: dbSlot.id,
  day: dayOfWeekToDayName(dbSlot.day_of_week),
  startTime: dbSlot.start_time,
  endTime: dbSlot.end_time,
  status: dbSlot.status
});

export const transformAvailabilitySlotForDB = (uiSlot: {
  day: string;
  startTime: string;
  endTime: string;
  status: 'available' | 'auto_accept' | 'manual_approval';
}, tutorId: string) => ({
  tutor_id: tutorId,
  day_of_week: dayNameToDayOfWeek(uiSlot.day),
  start_time: uiSlot.startTime,
  end_time: uiSlot.endTime,
  status: uiSlot.status
});

// src/store/adminBatchStore.ts
import { create } from "zustand";
import { supabase } from "@/lib/supabaseClient";
import { Subscription } from "./billingStore";

export interface Batch {
  id: string;
  name: string;
  student_id: string;
  package_type: string;
  package_name: string;
  default_tutor_id: string;
  status: string;
  start_date?: string;
  end_date?: string;
  total_sessions?: number;
  remaining_sessions?: number;
  subscription_id?: string;
  workflow_id?: string;
  created_at: string;
  updated_at: string;
  student_name?: string;
  tutor_name?: string;
}

// Interface for enrolled students without batches
export interface EnrolledStudentForBatch {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  subscriptions: {
    id: string;
    product_id: string;
    product_name: string;
    product_type: string;
    workflow_id: string;
    current_period_end: string;
    days_remaining: number;
    admin_assistance_required?: boolean;
    admin_assistance_notes?: string;
  }[];
}

interface AdminBatchStore {
  batches: Batch[];
  isLoading: boolean;
  error: string | null;

  fetchBatches: () => Promise<void>;
  fetchBatchById: (batchId: string) => Promise<Batch | null>;
  createBatch: (batchData: Partial<Batch>) => Promise<{ success: boolean; batchId?: string; error?: string }>;
  updateBatch: (batchId: string, batchData: Partial<Batch>) => Promise<boolean>;
  deleteBatch: (batchId: string) => Promise<boolean>;
  getStudentSubscriptions: (studentId: string) => Promise<Subscription[]>;
  getEnrolledStudentsForBatch: () => Promise<EnrolledStudentForBatch[]>;
  assignTutorToBatch: (batchId: string, tutorId: string) => Promise<boolean>;
}

export const useAdminBatchStore = create<AdminBatchStore>((set, get) => ({
  batches: [],
  isLoading: false,
  error: null,

  fetchBatches: async () => {
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('batches')
        .select(`
          *,
          students:student_id (first_name, last_name),
          tutors:default_tutor_id (first_name, last_name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Format the data
      const formattedBatches = (data || []).map(batch => ({
        ...batch,
        student_name: `${batch.students?.first_name || ''} ${batch.students?.last_name || ''}`.trim(),
        tutor_name: `${batch.tutors?.first_name || ''} ${batch.tutors?.last_name || ''}`.trim()
      }));

      set({ batches: formattedBatches, isLoading: false });
    } catch (error) {
      console.error("Error fetching batches:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load batches",
        isLoading: false
      });
    }
  },

  fetchBatchById: async (batchId) => {
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('batches')
        .select(`
          *,
          students:student_id (first_name, last_name),
          tutors:default_tutor_id (first_name, last_name)
        `)
        .eq('id', batchId)
        .single();

      if (error) throw error;

      // Format the data
      const formattedBatch = {
        ...data,
        student_name: `${data.students?.first_name || ''} ${data.students?.last_name || ''}`.trim(),
        tutor_name: `${data.tutors?.first_name || ''} ${data.tutors?.last_name || ''}`.trim()
      };

      set({ isLoading: false });
      return formattedBatch;
    } catch (error) {
      console.error("Error fetching batch:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load batch",
        isLoading: false
      });
      return null;
    }
  },

  createBatch: async (batchData) => {
    set({ isLoading: true, error: null });

    try {
      // Validate required fields
      if (!batchData.student_id || !batchData.package_type || !batchData.subscription_id) {
        throw new Error("Missing required fields for batch creation");
      }

      // Generate batch name based on package type and current year
      const batchName = `${batchData.package_name || batchData.package_type} - ${new Date().getFullYear()}`;

      // Use the admin_create_batch function for better validation and security
      const { data: batchId, error } = await supabase
        .rpc('admin_create_batch', {
          student_uuid: batchData.student_id,
          batch_name: batchName,
          product_type_val: batchData.package_type,
          product_name_val: batchData.package_name || batchData.package_type,
          subscription_uuid: batchData.subscription_id,
          workflow_uuid: batchData.workflow_id || null,
          tutor_uuid: batchData.default_tutor_id || null,
          total_sessions_val: batchData.total_sessions || null
        });

      if (error) throw error;

      // Refresh batches
      await get().fetchBatches();

      set({ isLoading: false });
      return { success: true, batchId: batchId };
    } catch (error) {
      console.error("Error creating batch:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to create batch",
        isLoading: false
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to create batch"
      };
    }
  },

  updateBatch: async (batchId, batchData) => {
    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase
        .from('batches')
        .update(batchData)
        .eq('id', batchId);

      if (error) throw error;

      // Refresh batches
      await get().fetchBatches();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error("Error updating batch:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to update batch",
        isLoading: false
      });
      return false;
    }
  },

  deleteBatch: async (batchId) => {
    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase
        .from('batches')
        .delete()
        .eq('id', batchId);

      if (error) throw error;

      // Update local state
      set(state => ({
        batches: state.batches.filter(batch => batch.id !== batchId),
        isLoading: false
      }));

      return true;
    } catch (error) {
      console.error("Error deleting batch:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to delete batch",
        isLoading: false
      });
      return false;
    }
  },

  getStudentSubscriptions: async (studentId) => {
    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .select(`
          *,
          products:product_id (name)
        `)
        .eq('student_id', studentId)
        .eq('status', 'active');

      if (error) throw error;

      // Calculate days remaining for each subscription
      const now = new Date();
      const subscriptionsWithDaysRemaining = (data || []).map(sub => {
        const endDate = new Date(sub.current_period_end);
        const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

        return {
          ...sub,
          product_name: sub.products?.name,
          days_remaining: daysRemaining
        };
      });

      // Filter out expired subscriptions
      return subscriptionsWithDaysRemaining.filter(sub => sub.days_remaining > 0);
    } catch (error) {
      console.error("Error fetching student subscriptions:", error);
      return [];
    }
  },

  getEnrolledStudentsForBatch: async () => {
    try {
      console.log("🔍 Starting getEnrolledStudentsForBatch...");

      // Use the new RPC function for better performance and security
      const { data: subscriptionsData, error: subscriptionsError } = await supabase
        .rpc('admin_get_subscriptions_without_batches');

      if (subscriptionsError) {
        console.error("❌ Error fetching subscriptions without batches:", subscriptionsError);
        throw subscriptionsError;
      }

      console.log("📊 Subscriptions without batches found:", subscriptionsData?.length || 0);

      if (!subscriptionsData || subscriptionsData.length === 0) {
        console.log("⚠️ No subscriptions without batches found");
        return [];
      }

      // Group subscriptions by student
      const studentsMap = new Map<string, EnrolledStudentForBatch>();

      subscriptionsData.forEach(subscription => {
        const studentId = subscription.student_id;

        if (!studentsMap.has(studentId)) {
          studentsMap.set(studentId, {
            id: studentId,
            first_name: subscription.student_name?.split(' ')[0] || '',
            last_name: subscription.student_name?.split(' ').slice(1).join(' ') || '',
            email: subscription.student_email || '',
            subscriptions: []
          });
        }

        const student = studentsMap.get(studentId)!;
        student.subscriptions.push({
          id: subscription.subscription_id,
          product_id: subscription.product_id,
          product_name: subscription.product_name || 'Unknown Product',
          product_type: subscription.product_type || 'unknown',
          workflow_id: subscription.workflow_id,
          current_period_end: subscription.current_period_end,
          days_remaining: subscription.days_remaining,
          admin_assistance_required: subscription.admin_assistance_required || false,
          admin_assistance_notes: subscription.admin_assistance_notes || ''
        });
      });

      const result = Array.from(studentsMap.values());
      console.log("🎯 Final result - students with unbatched subscriptions:", result.length);
      console.log("📋 Final students data:", result);

      return result;
    } catch (error) {
      console.error("❌ Error fetching enrolled students for batch creation:", error);
      return [];
    }
  },

  assignTutorToBatch: async (batchId, tutorId) => {
    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase
        .from('batches')
        .update({ default_tutor_id: tutorId })
        .eq('id', batchId);

      if (error) throw error;

      // Refresh batches
      await get().fetchBatches();

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error("Error assigning tutor to batch:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to assign tutor",
        isLoading: false
      });
      return false;
    }
  }
}));

import { z } from "zod";
import {
  fieldErrors,
  authErrors,
  emailVerificationErrors,
  apiErrors,
  textErrors,
  dateErrors,
  numberErrors,
  amountErrors,
} from "@/constants/errorMessages";
import { USER_TYPE } from "@/constants/auth";

// Create schema builder functions
export const createFormSchema = <T extends z.ZodRawShape>(shape: T) =>
  z.object(shape);

// Password validation schemas
export const passwordSchema = z
  .string()
  .min(8, { message: fieldErrors.password.tooShort })
  .refine(
    (val) => /[A-Z]/.test(val) && /[a-z]/.test(val) && /[0-9]/.test(val),
    { message: fieldErrors.password.weakPassword }
  );

// Numbers and amounts schemas
export const positiveNumberSchema = z
  .string()
  .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: numberErrors.notPositive,
  });

export const currencySchema = z
  .string()
  .refine((val) => !isNaN(Number(val)) && Number(val) >= 0, {
    message: amountErrors.notValid,
  });

// Text content schemas
export const shortTextSchema = z
  .string()
  .max(100, { message: textErrors.shortText });

export const longTextSchema = z
  .string()
  .max(1000, { message: textErrors.longText });

export const richTextSchema = z
  .string()
  .max(5000, { message: textErrors.richText });

// Date schemas
export const futureDateSchema = z.date().refine((date) => date > new Date(), {
  message: dateErrors.futureDate,
});

export const pastDateSchema = z.date().refine((date) => date < new Date(), {
  message: dateErrors.pastDate,
});

// Array schemas
export const nonEmptyArraySchema = <T extends z.ZodTypeAny>(type: T) =>
  z.array(type).min(1, { message: "Please select at least one option" });

// File schemas
export const requiredFileSchema = z
  .any()
  .refine((val) => val !== undefined && val !== "", {
    message: "File upload is required",
  });

// Enhanced individual field schemas for tutors
export const firstNameSchema = z
  .string()
  .min(2, { message: fieldErrors.firstName.tooShort })
  .refine((val) => val.trim() !== "", {
    message: fieldErrors.firstName.empty,
  })
  .refine((val) => /^[a-zA-Z\s-']+$/.test(val), {
    message: fieldErrors.firstName.specialChars,
  });

export const lastNameSchema = z
  .string()
  .min(2, { message: fieldErrors.lastName.tooShort })
  .refine((val) => val.trim() !== "", {
    message: fieldErrors.lastName.empty,
  })
  .refine((val) => /^[a-zA-Z\s-']+$/.test(val), {
    message: fieldErrors.lastName.specialChars,
  });

export const emailSchema = z.string().email({
  message: fieldErrors.email.invalid,
});

export const phoneNumberSchema = z.string().min(10, {
  message: fieldErrors.phoneNumber.tooShort,
});

export const subjectsSchema = z.array(z.string()).min(1, {
  message: fieldErrors.subjects.required,
});

export const hourlyRateSchema = z
  .string()
  .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: fieldErrors.hourlyRate.invalid,
  });

export const cvFileSchema = z
  .any()
  .refine((val) => val !== undefined && val !== "", {
    message: fieldErrors.cvFile.required,
  });

// Authentication schemas
export const loginSchema = createFormSchema({
  email: emailSchema,
  password: z.string().min(1, { message: fieldErrors.password.required }),
}).superRefine((data, ctx) => {
  // Handle login validation errors inline
  if (data.email && data.password && data.email.includes("invalid")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: authErrors.login.invalidCredentials,
      path: ["email"],
    });
  }
});

// Email registration check schema
export const isEmailRegisteredSchema = (
  isEmailRegistered = false,
  existingUserType?: string
) =>
  z
    .object({
      email: emailSchema,
    })
    .superRefine((data, ctx) => {
      if (isEmailRegistered) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            existingUserType === USER_TYPE.ADMIN
              ? "This email is already registered. Please log in instead."
              : `This email is already registered as a ${existingUserType}. Please log in instead.`,
          path: ["email"],
        });
      }
    });

// Registration schema without the email registration check
export const registrationSchema = createFormSchema({
  email: emailSchema,
  password: passwordSchema,
  userType: z.enum(["student", "tutor"], {
    message: fieldErrors.userType.required,
  }),
  acceptTerms: z.literal(true, {
    message: "You must accept the terms and conditions",
  }),
}).superRefine((data, ctx) => {
  if (
    data.password &&
    !(
      /[A-Z]/.test(data.password) &&
      /[a-z]/.test(data.password) &&
      /[0-9]/.test(data.password)
    )
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: fieldErrors.password.weakPassword,
      path: ["password"],
    });
  }
});

export const passwordResetSchema = createFormSchema({
  password: passwordSchema,
  confirmPassword: z.string(),
})
  .refine((data) => data.password === data.confirmPassword, {
    message: authErrors.passwordReset.mismatch,
    path: ["confirmPassword"],
  })
  .superRefine((data, ctx) => {
    // Handle password reset validation errors inline
    if (data.password && data.password.includes("same")) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: authErrors.passwordReset.samePassword,
        path: ["password"],
      });
    }

    // Check for expired token scenarios in the UI component
    // since this is typically determined by API response, not form data
  });

// Compose the guest tutor form schema from individual field schemas
export const guestTutorPISchema = createFormSchema({
  firstName: firstNameSchema,
  lastName: lastNameSchema,
  email: emailSchema,
  phoneNumber: phoneNumberSchema,
});

export const guestTutorFormSchema = createFormSchema({
  ...guestTutorPISchema.shape,
  subjects: subjectsSchema,
  hourlyRate: hourlyRateSchema,
  cvFile: cvFileSchema,
});

// Error handlers
export const handleAuthError = (error: any): string => {
  console.log("handleAuthError received:", error);

  if (!error) return "";

  // Check for specific error types
  if (typeof error === "object") {
    // If it's a Supabase error
    if (error.message) {
      console.log("Error has message property:", error.message);
      return error.message;
    }

    // If it has a code property (like Firebase)
    if (error.code) {
      console.log("Error has code property:", error.code);
      return error.code
        .replace(/auth\/|authentication\//, "")
        .replace(/-/g, " ");
    }
  }

  // If it's a string
  if (typeof error === "string") {
    console.log("Error is a string:", error);
    return error;
  }

  // Generic error fallback
  console.log("Using generic error fallback");
  return apiErrors.generic;
};

// Add other error handlers as needed
export const handleApiError = (error: any): string => {
  // Handle API-specific errors
  return (
    error?.message || "An error occurred while communicating with the server."
  );
};

// Add handler for email verification errors
export const handleEmailVerificationError = (error: any): string => {
  if (!error) return "";

  const errorMessage = error.message || "";

  if (errorMessage.includes("Signups not allowed for otp")) {
    return emailVerificationErrors.signupNotAllowed;
  }

  if (errorMessage.includes("rate limit")) {
    return emailVerificationErrors.rateLimit;
  }

  // Generic error fallback
  return errorMessage || emailVerificationErrors.generic;
};

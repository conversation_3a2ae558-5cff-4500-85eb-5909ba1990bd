import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import Footer from "@/components/Footer";
import AdminSidebar from "@/components/admin/Sidebar";
import { supabase } from "@/lib/supabaseClient";
import { useToast } from "@/components/ui/UseToast";
import UserProfileMenu from "@/components/UserProfileMenu";
import { useProfileData } from "@/hooks/useProfileData";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Button } from "@/components/ui/Button";
import { MessageSquare, CheckCircle, Eye, Trash2 } from "lucide-react";
import { deleteInquiry } from "@/utils/inquiryService";

// Add this at the top of the file to check if the page is being loaded
console.log("Admin Inquiries page module loaded");

interface InquiriesProps {
  preview?: boolean;
  limit?: number;
}

interface Inquiry {
  id: string;
  created_at: string;
  name: string;
  email: string;
  phone: string | null;
  inquiry_type: string;
  message: string;
  status: string;
}

const Inquiries = ({ preview = false, limit = 10 }: InquiriesProps) => {
  console.log("Admin Inquiries page rendering");
  const { toast } = useToast();
  const profileData = useProfileData();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [newCount, setNewCount] = useState(0);

  useEffect(() => {
    const fetchInquiries = async () => {
      try {
        let query = supabase
          .from("inquiries")
          .select("*")
          .order("created_at", { ascending: false });

        if (limit) {
          query = query.limit(limit);
        }

        const { data, error } = await query;

        if (error) throw error;
        setInquiries(data || []);

        // Count new inquiries
        const newInquiries =
          data?.filter((item) => item.status === "new") || [];
        setNewCount(newInquiries.length);

        setLoading(false);
      } catch (error) {
        console.error("Error fetching inquiries:", error);
        setLoading(false);
      }
    };

    fetchInquiries();
  }, [limit]);

  const updateStatus = async (id: string, status: string) => {
    try {
      const { error } = await supabase
        .from("inquiries")
        .update({ status })
        .eq("id", id);

      if (error) throw error;

      setInquiries(
        inquiries.map((inquiry) =>
          inquiry.id === id ? { ...inquiry, status } : inquiry
        )
      );

      // Update new count if status changed from/to new
      if (status === "new") {
        setNewCount((prev) => prev + 1);
      } else {
        const wasNew = inquiries.find((i) => i.id === id)?.status === "new";
        if (wasNew) {
          setNewCount((prev) => prev - 1);
        }
      }

      toast({
        title: "Status Updated",
        description: "Inquiry status has been updated",
      });
    } catch (error: any) {
      console.error("Error updating status:", error);
      toast({
        title: "Error",
        description: "Failed to update status",
        variant: "destructive",
      });
    }
  };

  if (preview) {
    return (
      <div>
        {/* Simplified view for dashboard preview */}
        {loading ? (
          <p>Loading inquiries...</p>
        ) : inquiries.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {inquiries.slice(0, 3).map((inquiry, index) => (
                  <tr key={inquiry.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      #{index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>{inquiry.name}</div>
                      <div className="text-sm text-gray-500">
                        {inquiry.email}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {inquiry.inquiry_type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          inquiry.status === "new"
                            ? "bg-green-100 text-green-800"
                            : inquiry.status === "in_progress"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {inquiry.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p>No inquiries found.</p>
        )}
        <div className="mt-4">
          <Link to="/admin/inquiries">
            <Button>View All Inquiries</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex flex-grow">
        <AdminSidebar />
        <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <div className="flex items-center">
                <h1 className="text-3xl font-extrabold text-gray-900">
                  Inquiry Management
                </h1>
                {newCount > 0 && (
                  <span className="ml-4 px-3 py-1 text-sm font-medium rounded-full bg-red-100 text-red-800">
                    {newCount} New
                  </span>
                )}
              </div>
              <UserProfileMenu
                isAdmin={true}
                isAdminPage={true}
              />
            </div>

            {loading ? (
              <div className="text-center py-12">Loading inquiries...</div>
            ) : inquiries.length === 0 ? (
              <div className="text-center py-12">No inquiries found</div>
            ) : (
              <div className="bg-white shadow overflow-hidden rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {inquiries.map((inquiry, index) => (
                      <TableRow key={inquiry.id}>
                        <TableCell>#{index + 1}</TableCell>
                        <TableCell>
                          <div className="font-medium">{inquiry.name}</div>
                          <div className="text-sm text-gray-500">
                            {inquiry.email}
                          </div>
                        </TableCell>
                        <TableCell>
                          {inquiry.inquiry_type.charAt(0).toUpperCase() +
                            inquiry.inquiry_type.slice(1)}
                        </TableCell>
                        <TableCell>
                          {new Date(inquiry.created_at).toLocaleDateString(
                            "en-US",
                            { month: "short", day: "numeric", year: "numeric" }
                          )}
                        </TableCell>
                        <TableCell>
                          <Select
                            value={inquiry.status}
                            onValueChange={(value) =>
                              updateStatus(inquiry.id, value)
                            }
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="new">New</SelectItem>
                              <SelectItem value="in-progress">
                                In Progress
                              </SelectItem>
                              <SelectItem value="resolved">Resolved</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <button
                              className="p-1 text-gray-500 hover:text-gray-700"
                              title="View Details"
                              onClick={() => {
                                // View details functionality
                                // You could implement a modal here
                              }}
                            >
                              <Eye size={18} />
                            </button>
                            <button
                              className="p-1 text-red-500 hover:text-red-700"
                              title="Delete Inquiry"
                              onClick={() =>
                                deleteInquiry(
                                  inquiry.id,
                                  inquiries,
                                  setInquiries,
                                  setNewCount
                                )
                              }
                            >
                              <Trash2 size={18} />
                            </button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </main>
      </div>
      <Footer />
    </div>
  );
};

export default Inquiries;

import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Link } from "react-router-dom";
import {
  GraduationCap,
  DollarSign,
  Clock,
  Users,
  TrendingUp,
  Star,
  BookOpen,
  Calendar,
  Target,
  Award,
  Zap,
  Heart,
  ArrowRight,
  CheckCircle,
  BarChart3,
  Globe,
  Lightbulb,
  Shield,
  Sparkles
} from "lucide-react";

const Tutors = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="text-gray-900 pt-12 md:pt-16 pb-20 px-4 sm:px-6 lg:px-8 mt-10 md:mt-12 bg-gradient-to-br from-white via-orange-50/20 to-purple-50/30">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <Badge variant="outline" className="bg-rfpurple-100 text-rfpurple-700 border-rfpurple-300 mb-6">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Join Our Elite Tutor Network
                </Badge>
                <h1 className="text-4xl md:text-6xl font-extrabold mb-6 leading-tight">
                  Empower Students,
                  <span className="block text-rfpurple-600">Grow Your Career</span>
                </h1>
                <p className="text-xl mb-8 text-gray-600 leading-relaxed">
                  Join rfLearn's innovative platform where passionate educators connect with motivated students
                  through personalized, AI-enhanced learning experiences.
                </p>
                <div className="flex justify-start">
                  <Button size="lg" className="bg-rfpurple-600 text-white hover:bg-rfpurple-700 font-semibold" asChild>
                    <Link to="/become-tutor">
                      <GraduationCap className="mr-2 h-5 w-5" />
                      Become a Tutor
                    </Link>
                  </Button>
                </div>
              </div>
              <div className="relative">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200 shadow-lg">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold mb-2 text-gray-900">Competitive Pay</div>
                      <div className="text-sm text-gray-600">Salary no bar</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold mb-2 text-gray-900">Coming Soon</div>
                      <div className="text-sm text-gray-600">Active Students</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold mb-2 text-gray-900">Coming Soon</div>
                      <div className="text-sm text-gray-600">Avg Rating</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold mb-2 text-gray-900">24/7</div>
                      <div className="text-sm text-gray-600">Support</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* What We Offer Section */}
        <section id="benefits" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What We Offer Our Tutors
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Join a platform designed to support your success with cutting-edge tools,
                competitive compensation, and meaningful student connections.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: DollarSign,
                  title: 'Competitive Earnings',
                  description: 'Experienced and qualified tutors, especially those with teaching certifications, may earn more',
                  color: 'from-green-400 to-green-600'
                },
                {
                  icon: Clock,
                  title: 'Flexible Scheduling',
                  description: 'Set your own hours and availability to match your lifestyle',
                  color: 'from-blue-400 to-blue-600'
                },
                {
                  icon: Users,
                  title: 'Quality Student Matches',
                  description: 'Work with motivated students who are serious about learning',
                  color: 'from-purple-400 to-purple-600'
                },
                {
                  icon: BarChart3,
                  title: 'Performance Analytics',
                  description: 'Track your impact with detailed student progress insights',
                  color: 'from-orange-400 to-orange-600'
                },
                {
                  icon: BookOpen,
                  title: 'Curriculum Support',
                  description: 'Access comprehensive teaching materials and resources',
                  color: 'from-teal-400 to-teal-600'
                },
                {
                  icon: Shield,
                  title: 'Professional Support',
                  description: '24/7 technical support and dedicated tutor success team',
                  color: 'from-red-400 to-red-600'
                }
              ].map((offer, index) => {
                const IconComponent = offer.icon;
                return (
                  <Card key={index} className="hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                    <CardHeader className="text-center pb-4">
                      <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${offer.color} flex items-center justify-center mx-auto mb-4`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <CardTitle className="text-xl font-bold">{offer.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center text-gray-600">
                        {offer.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  Why Tutors Choose rfLearn
                </h2>
                <p className="text-lg text-gray-600 mb-8">
                  Experience the difference of teaching on a platform built specifically
                  for educator success and student achievement.
                </p>

                <div className="space-y-6">
                  {[
                    {
                      icon: TrendingUp,
                      title: 'Career Growth',
                      description: 'Build your reputation with student reviews and performance metrics'
                    },
                    {
                      icon: Globe,
                      title: 'Global Reach',
                      description: 'Teach students from anywhere with our virtual classroom technology'
                    },
                    {
                      icon: Heart,
                      title: 'Meaningful Impact',
                      description: 'Make a real difference in students\' academic journeys and futures'
                    },
                    {
                      icon: Lightbulb,
                      title: 'Innovation',
                      description: 'Use AI-powered tools to enhance your teaching effectiveness'
                    }
                  ].map((benefit, index) => {
                    const IconComponent = benefit.icon;
                    return (
                      <div key={index} className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-rfpurple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <IconComponent className="w-6 h-6 text-rfpurple-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{benefit.title}</h3>
                          <p className="text-gray-600">{benefit.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="relative">
                <Card className="bg-gradient-to-br from-rfpurple-50 to-blue-50 border-0 shadow-xl">
                  <CardHeader className="text-center">
                    <CardTitle className="text-2xl font-bold text-gray-900">
                      Tutor Success Story
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <div className="mb-6">
                      <div className="w-20 h-20 bg-gradient-to-r from-rfpurple-400 to-blue-400 rounded-full flex items-center justify-center mx-auto mb-4">
                        <GraduationCap className="w-10 h-10 text-white" />
                      </div>
                      <div className="flex justify-center mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    </div>
                    <blockquote className="text-gray-700 italic mb-4">
                      "rfLearn has transformed my tutoring career. The platform's tools help me
                      track student progress effectively, and I've increased my earnings by 40%
                      while working with amazing students."
                    </blockquote>
                    <div className="text-sm text-gray-600">
                      <div className="font-semibold">Sarah Johnson</div>
                      <div>Mathematics Tutor, 2+ years on rfLearn</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* How rfLearn is Different */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                How rfLearn Helps You Grow
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Unlike traditional tutoring platforms, we provide comprehensive support
                for your professional development and teaching excellence.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                {
                  title: 'AI-Enhanced Teaching',
                  description: 'Leverage artificial intelligence to personalize learning paths and identify student needs more effectively.',
                  icon: Zap,
                  features: ['Smart student matching', 'Automated progress tracking', 'Personalized recommendations']
                },
                {
                  title: 'Professional Development',
                  description: 'Access continuous learning opportunities and certification programs to advance your teaching skills.',
                  icon: Award,
                  features: ['Monthly training sessions', 'Certification programs', 'Peer collaboration network']
                },
                {
                  title: 'Student Success Focus',
                  description: 'Work with students who are committed to learning through our structured subscription model.',
                  icon: Target,
                  features: ['Pre-screened students', 'Goal-oriented sessions', 'Long-term relationships']
                },
                {
                  title: 'Comprehensive Analytics',
                  description: 'Understand your impact with detailed insights into student progress and teaching effectiveness.',
                  icon: BarChart3,
                  features: ['Performance dashboards', 'Student feedback analysis', 'Earnings optimization']
                }
              ].map((difference, index) => {
                const IconComponent = difference.icon;
                return (
                  <Card key={index} className="hover:shadow-xl transition-all duration-300">
                    <CardHeader>
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-rfpurple-100 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-6 h-6 text-rfpurple-600" />
                        </div>
                        <CardTitle className="text-xl font-bold">{difference.title}</CardTitle>
                      </div>
                      <CardDescription className="text-gray-600">
                        {difference.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {difference.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span className="text-sm text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Getting Started Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                How to Get Started
              </h2>
              <p className="text-xl text-gray-600">
                Join our tutor community in just a few simple steps
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  step: '01',
                  title: 'Apply Online',
                  description: 'Submit your application with your qualifications and teaching experience',
                  icon: BookOpen
                },
                {
                  step: '02',
                  title: 'Complete Onboarding',
                  description: 'Go through our comprehensive onboarding process and platform training',
                  icon: GraduationCap
                },
                {
                  step: '03',
                  title: 'Start Teaching',
                  description: 'Get matched with students and begin your rewarding tutoring journey',
                  icon: Users
                }
              ].map((step, index) => {
                const IconComponent = step.icon;
                return (
                  <div key={index} className="text-center">
                    <div className="relative mb-6">
                      <div className="w-20 h-20 bg-gradient-to-r from-rfpurple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <IconComponent className="w-10 h-10 text-white" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-sm font-bold text-gray-900">
                        {step.step}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{step.title}</h3>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-rfpurple-600 to-rfpurple-500">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Transform Your Teaching Career?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join hundreds of educators who are making a meaningful impact while growing their careers on rfLearn.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-rfpurple-600 hover:bg-gray-100 font-semibold" asChild>
                <Link to="/become-tutor">
                  <GraduationCap className="mr-2 h-5 w-5" />
                  Apply to Become a Tutor
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-rfpurple-600 bg-transparent" asChild>
                <Link to="/inquiry?type=tutoring">
                  <Calendar className="mr-2 h-5 w-5" />
                  Schedule a Call
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Tutors;

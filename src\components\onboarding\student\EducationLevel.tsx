import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";
import { Label } from "@/components/ui/Label";

interface EducationLevelProps {
  educationLevel: string;
  setEducationLevel: (level: string) => void;
}

const EducationLevel: React.FC<EducationLevelProps> = ({
  educationLevel,
  setEducationLevel,
}) => {
  const levels = [
    { id: "primary", label: "Primary School" },
    { id: "secondary", label: "Secondary School" },
    { id: "high_school", label: "High School" },
    { id: "undergraduate", label: "Undergraduate" },
    { id: "postgraduate", label: "Postgraduate" },
    { id: "professional", label: "Professional" },
  ];

  return (
    <div>
      <h2 className="text-2xl font-bold mb-2">What is your education level?</h2>
      <p className="text-gray-600 mb-6">
        This helps us recommend appropriate learning materials.
      </p>

      <RadioGroup
        value={educationLevel}
        onValueChange={setEducationLevel}
        className="space-y-3"
      >
        {levels.map((level) => (
          <div
            key={level.id}
            className="flex items-center space-x-2 border p-4 rounded-lg hover:bg-gray-50"
          >
            <RadioGroupItem value={level.id} id={level.id} />
            <Label htmlFor={level.id} className="flex-grow cursor-pointer">
              {level.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default EducationLevel;

import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import {
  Briefcase,
  Clock,
  Users,
  Target,
  BookOpen,
  Bell,
  Mail,
  ArrowRight,
  Building,
  Heart,
  Zap
} from "lucide-react";

const Careers = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="mb-8">
              <Badge variant="outline" className="bg-rfpurple-50 text-rfpurple-700 border-rfpurple-200 mb-4">
                <Clock className="w-3 h-3 mr-1" />
                Coming Soon
              </Badge>
            </div>
            <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">
              Join Our Team
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
              We're building something amazing and looking for passionate educators and innovators 
              to help shape the future of personalized learning.
            </p>
          </div>

          {/* Coming Soon Card */}
          <Card className="mb-12 bg-white shadow-lg">
            <CardHeader className="text-center pb-6">
              <div className="w-20 h-20 bg-rfpurple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Briefcase className="w-10 h-10 text-rfpurple-600" />
              </div>
              <CardTitle className="text-2xl mb-2">Career Opportunities Coming Soon</CardTitle>
              <CardDescription className="text-lg text-gray-600">
                We're currently setting up our careers portal and will be posting exciting opportunities soon.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 className="font-semibold text-gray-900 mb-3">What We're Looking For:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center justify-center">
                    <Users className="w-4 h-4 mr-2 text-rfpurple-600" />
                    Passionate Educators
                  </div>
                  <div className="flex items-center justify-center">
                    <Zap className="w-4 h-4 mr-2 text-rfpurple-600" />
                    Tech Innovators
                  </div>
                  <div className="flex items-center justify-center">
                    <Heart className="w-4 h-4 mr-2 text-rfpurple-600" />
                    Student Advocates
                  </div>
                </div>
              </div>
              
              <p className="text-gray-600 mb-6">
                Be the first to know when we start hiring! Get notified about new opportunities 
                that match your skills and passion for education.
              </p>
              
              <Button className="button-gradient text-white" asChild>
                <Link to="/contact">
                  <Mail className="mr-2 h-4 w-4" />
                  Get Notified
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Why Join Us Section */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
              Why You'll Love Working With Us
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="bg-white hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Target className="w-6 h-6 text-green-600" />
                  </div>
                  <CardTitle className="text-lg">Mission-Driven</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    Make a real impact on students' lives through personalized education and innovative learning solutions.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="bg-white hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Building className="w-6 h-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-lg">Growth Opportunities</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    Join a growing company where your ideas matter and your career can flourish in the EdTech space.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="bg-white hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="w-6 h-6 text-purple-600" />
                  </div>
                  <CardTitle className="text-lg">Collaborative Culture</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center">
                    Work with passionate educators and technologists who share your commitment to student success.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-gradient-to-r from-rfpurple-600 to-rfpurple-500 rounded-2xl p-8 md:p-12 text-center text-white">
            <h2 className="text-3xl font-bold mb-4">Ready to Make a Difference?</h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Stay connected with us and be among the first to explore exciting career opportunities 
              when they become available.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-rfpurple-600 hover:bg-gray-100" asChild>
                <Link to="/contact">
                  <Bell className="mr-2 h-5 w-5" />
                  Stay Updated
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-rfpurple-600 bg-transparent" asChild>
                <Link to="/about">
                  <BookOpen className="mr-2 h-5 w-5" />
                  Learn About Us
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Careers;

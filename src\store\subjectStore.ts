import { create } from "zustand";

// Types for subject data - updated to match database schema
export interface Topic {
  id: string;
  subject_id: string;
  name: string;
  description?: string;
  icon?: string;
  display_order?: number;
  created_at: string;
  updated_at: string;
  subtopics?: Subtopic[]; // For UI convenience
}

export interface Subtopic {
  id: string;
  topic_id: string;
  name: string;
  description?: string;
  state_standard?: string;
  display_order?: number;
  created_at: string;
  updated_at: string;
}

export interface Subject {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
  topics?: Topic[]; // For UI convenience
}

// Subject store state and actions
interface SubjectStore {
  // State
  subjects: Subject[];
  selectedSubject: Subject | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchSubjects: () => Promise<void>;
  fetchSubjectById: (id: string) => Promise<Subject | null>;
  fetchSubjectWithTopics: (id: string) => Promise<Subject | null>;
  createSubject: (subject: Omit<Subject, "id" | "created_at" | "updated_at">) => Promise<Subject | null>;
  updateSubject: (id: string, updates: Partial<Subject>) => Promise<boolean>;
  deleteSubject: (id: string) => Promise<boolean>;
  setSelectedSubject: (subject: Subject | null) => void;

  // Topic and subtopic management
  addTopic: (subjectId: string, topic: Omit<Topic, "id">) => Promise<Topic | null>;
  updateTopic: (topicId: string, updates: Partial<Topic>) => Promise<boolean>;
  deleteTopic: (topicId: string) => Promise<boolean>;
  addSubtopic: (topicId: string, subtopic: Omit<Subtopic, "id" | "topic_id">) => Promise<Subtopic | null>;
  updateSubtopic: (subtopicId: string, updates: Partial<Subtopic>) => Promise<boolean>;
  deleteSubtopic: (subtopicId: string) => Promise<boolean>;
}

// Create the store
export const useSubjectStore = create<SubjectStore>((set, get) => ({
  // Initial state
  subjects: [],
  selectedSubject: null,
  isLoading: false,
  error: null,

  // Actions
  fetchSubjects: async () => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { data, error } = await supabase
        .from("subjects")
        .select(`
          id,
          name,
          description,
          icon,
          color,
          is_active,
          created_at,
          updated_at
        `)
        .order("name", { ascending: true });

      if (error) throw error;

      set({ subjects: data || [], isLoading: false });
    } catch (error) {
      console.error("Error fetching subjects:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load subjects",
        isLoading: false
      });
    }
  },

  fetchSubjectById: async (id) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { data, error } = await supabase
        .from("subjects")
        .select(`
          id,
          name,
          description,
          icon,
          color,
          is_active,
          created_at,
          updated_at
        `)
        .eq("id", id)
        .single();

      if (error) throw error;

      set({ selectedSubject: data, isLoading: false });
      return data;
    } catch (error) {
      console.error(`Error fetching subject with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to load subject with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  fetchSubjectWithTopics: async (id) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // First, fetch the subject
      const { data: subject, error: subjectError } = await supabase
        .from("subjects")
        .select(`
          id,
          name,
          description,
          icon,
          color,
          is_active,
          created_at,
          updated_at
        `)
        .eq("id", id)
        .single();

      if (subjectError) throw subjectError;

      // Then, fetch the topics for this subject
      const { data: topics, error: topicsError } = await supabase
        .from("topics")
        .select(`
          id,
          subject_id,
          name,
          description,
          icon,
          display_order,
          created_at,
          updated_at
        `)
        .eq("subject_id", id)
        .order("display_order", { ascending: true });

      if (topicsError) throw topicsError;

      // For each topic, fetch its subtopics
      const topicsWithSubtopics = await Promise.all(
        topics.map(async (topic) => {
          const { data: subtopics, error: subtopicsError } = await supabase
            .from("subtopics")
            .select(`
              id,
              topic_id,
              name,
              description,
              state_standard,
              display_order,
              created_at,
              updated_at
            `)
            .eq("topic_id", topic.id)
            .order("display_order", { ascending: true });

          if (subtopicsError) throw subtopicsError;

          return {
            ...topic,
            subtopics: subtopics || []
          };
        })
      );

      const subjectWithTopics: Subject = {
        ...subject,
        topics: topicsWithSubtopics
      };

      set({ selectedSubject: subjectWithTopics, isLoading: false });
      return subjectWithTopics;
    } catch (error) {
      console.error(`Error fetching subject with topics for ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to load subject with topics for ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  createSubject: async (subject) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { data, error } = await supabase
        .from("subjects")
        .insert(subject)
        .select()
        .single();

      if (error) throw error;

      // Update the subjects list with the new subject
      set(state => ({
        subjects: [...state.subjects, data],
        isLoading: false
      }));

      return data;
    } catch (error) {
      console.error("Error creating subject:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to create subject",
        isLoading: false
      });
      return null;
    }
  },

  updateSubject: async (id, updates) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { error } = await supabase
        .from("subjects")
        .update(updates)
        .eq("id", id);

      if (error) throw error;

      // Update the subjects list with the updated subject
      set(state => ({
        subjects: state.subjects.map(subject =>
          subject.id === id ? { ...subject, ...updates } : subject
        ),
        selectedSubject: state.selectedSubject?.id === id
          ? { ...state.selectedSubject, ...updates }
          : state.selectedSubject,
        isLoading: false
      }));

      return true;
    } catch (error) {
      console.error(`Error updating subject with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update subject with ID ${id}`,
        isLoading: false
      });
      return false;
    }
  },

  deleteSubject: async (id) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { error } = await supabase
        .from("subjects")
        .delete()
        .eq("id", id);

      if (error) throw error;

      // Remove the deleted subject from the subjects list
      set(state => ({
        subjects: state.subjects.filter(subject => subject.id !== id),
        selectedSubject: state.selectedSubject?.id === id ? null : state.selectedSubject,
        isLoading: false
      }));

      return true;
    } catch (error) {
      console.error(`Error deleting subject with ID ${id}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete subject with ID ${id}`,
        isLoading: false
      });
      return false;
    }
  },

  setSelectedSubject: (subject) => {
    set({ selectedSubject: subject });
  },

  // Topic management
  addTopic: async (subjectId, topic) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Ensure we're using the correct field names from the database schema
      const topicToInsert = {
        ...topic,
        subject_id: subjectId,
        // Handle any legacy code that might still use 'order' instead of 'display_order'
        display_order: topic.display_order || (topic as any).order,
      };

      // Remove any fields that don't match the schema
      const finalTopicToInsert = { ...topicToInsert };
      if ('order' in finalTopicToInsert) {
        delete (finalTopicToInsert as any).order;
      }

      const { data, error } = await supabase
        .from("topics")
        .insert(finalTopicToInsert)
        .select()
        .single();

      if (error) throw error;

      // Update the selected subject with the new topic if it's currently selected
      set(state => {
        if (state.selectedSubject?.id === subjectId && state.selectedSubject.topics) {
          return {
            selectedSubject: {
              ...state.selectedSubject,
              topics: [...state.selectedSubject.topics, { ...data, subtopics: [] }]
            },
            isLoading: false
          };
        }
        return { isLoading: false };
      });

      return data;
    } catch (error) {
      console.error(`Error adding topic to subject ${subjectId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to add topic to subject ${subjectId}`,
        isLoading: false
      });
      return null;
    }
  },

  updateTopic: async (topicId, updates) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { error } = await supabase
        .from("topics")
        .update(updates)
        .eq("id", topicId);

      if (error) throw error;

      // Update the selected subject's topics if the topic is part of the selected subject
      set(state => {
        if (state.selectedSubject?.topics) {
          const topicIndex = state.selectedSubject.topics.findIndex(t => t.id === topicId);

          if (topicIndex !== -1) {
            const updatedTopics = [...state.selectedSubject.topics];
            updatedTopics[topicIndex] = {
              ...updatedTopics[topicIndex],
              ...updates
            };

            return {
              selectedSubject: {
                ...state.selectedSubject,
                topics: updatedTopics
              },
              isLoading: false
            };
          }
        }
        return { isLoading: false };
      });

      return true;
    } catch (error) {
      console.error(`Error updating topic with ID ${topicId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update topic with ID ${topicId}`,
        isLoading: false
      });
      return false;
    }
  },

  deleteTopic: async (topicId) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { error } = await supabase
        .from("topics")
        .delete()
        .eq("id", topicId);

      if (error) throw error;

      // Update the selected subject's topics if the topic is part of the selected subject
      set(state => {
        if (state.selectedSubject?.topics) {
          return {
            selectedSubject: {
              ...state.selectedSubject,
              topics: state.selectedSubject.topics.filter(t => t.id !== topicId)
            },
            isLoading: false
          };
        }
        return { isLoading: false };
      });

      return true;
    } catch (error) {
      console.error(`Error deleting topic with ID ${topicId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete topic with ID ${topicId}`,
        isLoading: false
      });
      return false;
    }
  },

  // Subtopic management
  addSubtopic: async (topicId, subtopic) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Ensure we're using the correct field names from the database schema
      const subtopicToInsert = {
        ...subtopic,
        topic_id: topicId,
        // Handle any legacy code that might still use 'order' instead of 'display_order'
        display_order: subtopic.display_order || (subtopic as any).order,
      };

      // Remove any fields that don't match the schema
      const finalSubtopicToInsert = { ...subtopicToInsert };
      if ('order' in finalSubtopicToInsert) {
        delete (finalSubtopicToInsert as any).order;
      }

      const { data, error } = await supabase
        .from("subtopics")
        .insert(finalSubtopicToInsert)
        .select()
        .single();

      if (error) throw error;

      // Update the selected subject's topics if the topic is part of the selected subject
      set(state => {
        if (state.selectedSubject?.topics) {
          const topicIndex = state.selectedSubject.topics.findIndex(t => t.id === topicId);

          if (topicIndex !== -1) {
            const updatedTopics = [...state.selectedSubject.topics];
            const updatedTopic = { ...updatedTopics[topicIndex] };

            updatedTopic.subtopics = updatedTopic.subtopics
              ? [...updatedTopic.subtopics, data]
              : [data];

            updatedTopics[topicIndex] = updatedTopic;

            return {
              selectedSubject: {
                ...state.selectedSubject,
                topics: updatedTopics
              },
              isLoading: false
            };
          }
        }
        return { isLoading: false };
      });

      return data;
    } catch (error) {
      console.error(`Error adding subtopic to topic ${topicId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to add subtopic to topic ${topicId}`,
        isLoading: false
      });
      return null;
    }
  },

  updateSubtopic: async (subtopicId, updates) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { error } = await supabase
        .from("subtopics")
        .update(updates)
        .eq("id", subtopicId);

      if (error) throw error;

      // Update the selected subject's topics if the subtopic is part of the selected subject
      set(state => {
        if (state.selectedSubject?.topics) {
          const updatedTopics = state.selectedSubject.topics.map(topic => {
            if (topic.subtopics) {
              const subtopicIndex = topic.subtopics.findIndex(s => s.id === subtopicId);

              if (subtopicIndex !== -1) {
                const updatedSubtopics = [...topic.subtopics];
                updatedSubtopics[subtopicIndex] = {
                  ...updatedSubtopics[subtopicIndex],
                  ...updates
                };

                return {
                  ...topic,
                  subtopics: updatedSubtopics
                };
              }
            }
            return topic;
          });

          return {
            selectedSubject: {
              ...state.selectedSubject,
              topics: updatedTopics
            },
            isLoading: false
          };
        }
        return { isLoading: false };
      });

      return true;
    } catch (error) {
      console.error(`Error updating subtopic with ID ${subtopicId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update subtopic with ID ${subtopicId}`,
        isLoading: false
      });
      return false;
    }
  },

  deleteSubtopic: async (subtopicId) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { error } = await supabase
        .from("subtopics")
        .delete()
        .eq("id", subtopicId);

      if (error) throw error;

      // Update the selected subject's topics if the subtopic is part of the selected subject
      set(state => {
        if (state.selectedSubject?.topics) {
          const updatedTopics = state.selectedSubject.topics.map(topic => {
            if (topic.subtopics) {
              return {
                ...topic,
                subtopics: topic.subtopics.filter(s => s.id !== subtopicId)
              };
            }
            return topic;
          });

          return {
            selectedSubject: {
              ...state.selectedSubject,
              topics: updatedTopics
            },
            isLoading: false
          };
        }
        return { isLoading: false };
      });

      return true;
    } catch (error) {
      console.error(`Error deleting subtopic with ID ${subtopicId}:`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete subtopic with ID ${subtopicId}`,
        isLoading: false
      });
      return false;
    }
  }
}));

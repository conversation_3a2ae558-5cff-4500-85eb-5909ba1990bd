-- Payment Integration Migration Script
-- Run this script to safely apply payment integration changes to your database
-- This script is idempotent and can be run multiple times safely

BEGIN;

-- =====================================================
-- 1. CREATE EXTENSION IF NOT EXISTS
-- =====================================================

-- Ensure UUID extension is available
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 2. BACKUP EXISTING DATA (if needed)
-- =====================================================

-- Create backup table for subscription_workflows before adding columns
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_workflows_backup_payment_migration') THEN
        CREATE TABLE subscription_workflows_backup_payment_migration AS 
        SELECT * FROM subscription_workflows;
        
        RAISE NOTICE 'Created backup table: subscription_workflows_backup_payment_migration';
    END IF;
END $$;

-- =====================================================
-- 3. EXTEND EXISTING TABLES
-- =====================================================

-- Add payment-related columns to subscription_workflows table
DO $$
BEGIN
    -- Add columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'stripe_payment_intent_id') THEN
        ALTER TABLE subscription_workflows ADD COLUMN stripe_payment_intent_id TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'stripe_subscription_id') THEN
        ALTER TABLE subscription_workflows ADD COLUMN stripe_subscription_id TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'stripe_customer_id') THEN
        ALTER TABLE subscription_workflows ADD COLUMN stripe_customer_id TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'payment_status') THEN
        ALTER TABLE subscription_workflows ADD COLUMN payment_status TEXT DEFAULT 'pending';
        -- Add check constraint
        ALTER TABLE subscription_workflows ADD CONSTRAINT check_payment_status 
        CHECK (payment_status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'total_amount') THEN
        ALTER TABLE subscription_workflows ADD COLUMN total_amount DECIMAL(10,2);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'currency') THEN
        ALTER TABLE subscription_workflows ADD COLUMN currency TEXT DEFAULT 'usd';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'payment_method_type') THEN
        ALTER TABLE subscription_workflows ADD COLUMN payment_method_type TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_workflows' AND column_name = 'payment_completed_at') THEN
        ALTER TABLE subscription_workflows ADD COLUMN payment_completed_at TIMESTAMP;
    END IF;
    
    RAISE NOTICE 'Extended subscription_workflows table with payment columns';
END $$;

-- =====================================================
-- 4. CREATE NEW TABLES
-- =====================================================

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE CASCADE,
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Stripe identifiers
    stripe_payment_intent_id TEXT UNIQUE NOT NULL,
    stripe_charge_id TEXT,
    stripe_customer_id TEXT,
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd' NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action')),
    payment_method_type TEXT,
    
    -- Payment metadata
    description TEXT,
    receipt_email TEXT,
    receipt_url TEXT,
    
    -- Failure information
    failure_code TEXT,
    failure_message TEXT,
    
    -- Timestamps
    stripe_created_at TIMESTAMP,
    succeeded_at TIMESTAMP,
    failed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE RESTRICT,
    workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE SET NULL,
    
    -- Stripe identifiers
    stripe_subscription_id TEXT UNIQUE,
    stripe_customer_id TEXT NOT NULL,
    stripe_price_id TEXT,
    
    -- Subscription details
    status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid')),
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    trial_start TIMESTAMP,
    trial_end TIMESTAMP,
    canceled_at TIMESTAMP,
    ended_at TIMESTAMP,
    
    -- Billing details
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd' NOT NULL,
    interval_type TEXT CHECK (interval_type IN ('one_time', 'monthly', 'yearly')),
    
    -- Access control
    access_granted_at TIMESTAMP,
    access_expires_at TIMESTAMP,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Stripe identifiers
    stripe_invoice_id TEXT UNIQUE NOT NULL,
    stripe_customer_id TEXT NOT NULL,
    stripe_subscription_id TEXT,
    
    -- Invoice details
    invoice_number TEXT,
    amount_due DECIMAL(10,2) NOT NULL,
    amount_paid DECIMAL(10,2) DEFAULT 0,
    amount_remaining DECIMAL(10,2) DEFAULT 0,
    currency TEXT DEFAULT 'usd' NOT NULL,
    
    -- Status and dates
    status TEXT NOT NULL CHECK (status IN ('draft', 'open', 'paid', 'uncollectible', 'void')),
    due_date TIMESTAMP,
    paid_at TIMESTAMP,
    
    -- URLs and metadata
    invoice_pdf_url TEXT,
    hosted_invoice_url TEXT,
    receipt_number TEXT,
    
    -- Timestamps
    stripe_created_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create payment_methods table
CREATE TABLE IF NOT EXISTS payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Stripe identifiers
    stripe_payment_method_id TEXT UNIQUE NOT NULL,
    stripe_customer_id TEXT NOT NULL,
    
    -- Payment method details
    type TEXT NOT NULL,
    card_brand TEXT,
    card_last4 TEXT,
    card_exp_month INTEGER,
    card_exp_year INTEGER,
    
    -- Status
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create payment_events table
CREATE TABLE IF NOT EXISTS payment_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID REFERENCES payments(id) ON DELETE CASCADE,
    workflow_id UUID REFERENCES subscription_workflows(id) ON DELETE CASCADE,
    
    -- Event details
    event_type TEXT NOT NULL,
    stripe_event_id TEXT,
    
    -- Event data
    event_data JSONB,
    processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT,
    
    -- Timestamps
    stripe_created_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP
);

RAISE NOTICE 'Created payment integration tables';

-- =====================================================
-- 5. CREATE INDEXES
-- =====================================================

-- Subscription workflows indexes
CREATE INDEX IF NOT EXISTS idx_subscription_workflows_stripe_payment_intent 
ON subscription_workflows(stripe_payment_intent_id);

CREATE INDEX IF NOT EXISTS idx_subscription_workflows_stripe_customer 
ON subscription_workflows(stripe_customer_id);

CREATE INDEX IF NOT EXISTS idx_subscription_workflows_payment_status 
ON subscription_workflows(payment_status);

-- Payments table indexes
CREATE INDEX IF NOT EXISTS idx_payments_workflow_id ON payments(workflow_id);
CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);
CREATE INDEX IF NOT EXISTS idx_payments_stripe_payment_intent ON payments(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- Subscriptions table indexes
CREATE INDEX IF NOT EXISTS idx_subscriptions_student_id ON subscriptions(student_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_product_id ON subscriptions(product_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_current_period ON subscriptions(current_period_start, current_period_end);

-- Other table indexes
CREATE INDEX IF NOT EXISTS idx_invoices_subscription_id ON invoices(subscription_id);
CREATE INDEX IF NOT EXISTS idx_invoices_student_id ON invoices(student_id);
CREATE INDEX IF NOT EXISTS idx_invoices_stripe_invoice ON invoices(stripe_invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);

CREATE INDEX IF NOT EXISTS idx_payment_methods_student_id ON payment_methods(student_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_stripe_payment_method ON payment_methods(stripe_payment_method_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_stripe_customer ON payment_methods(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_is_default ON payment_methods(student_id, is_default) WHERE is_default = TRUE;

CREATE INDEX IF NOT EXISTS idx_payment_events_payment_id ON payment_events(payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_workflow_id ON payment_events(workflow_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_type ON payment_events(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_events_stripe_event ON payment_events(stripe_event_id);
CREATE INDEX IF NOT EXISTS idx_payment_events_processed ON payment_events(processed);
CREATE INDEX IF NOT EXISTS idx_payment_events_created_at ON payment_events(created_at);

RAISE NOTICE 'Created payment integration indexes';

COMMIT;

RAISE NOTICE 'Payment integration migration completed successfully!';
RAISE NOTICE 'Next steps:';
RAISE NOTICE '1. Run payment_functions.sql to create functions and policies';
RAISE NOTICE '2. Set up Stripe account and configure environment variables';
RAISE NOTICE '3. Implement frontend payment components';

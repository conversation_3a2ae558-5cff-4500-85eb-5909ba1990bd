import React, { useState } from "react";
import { useProfileData } from "@/hooks/useProfileData";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { Card, CardContent } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/TextArea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/Dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/Tabs";
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  Filter,
  MessageSquare,
  MoreHorizontal,
  RefreshCw,
  Search,
  Trash,
  X,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";

// Sample rejection data
const rejectionRequestsData = [
  {
    id: "REJ-1001",
    sessionId: "S-1001",
    student: {
      id: "STU-1001",
      name: "Alice Johnson",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Alice+Johnson&background=random",
    },
    topic: "Neural Networks",
    subtopic: "Convolutional Neural Networks",
    sessionType: "One-on-One",
    date: "2023-12-05",
    time: "10:00 AM",
    duration: 60,
    rejectionReason: "Schedule conflict with another teaching commitment.",
    rejectionDate: "2023-11-20",
    status: "awaiting_approval",
    adminFeedback: null,
    adminAction: null,
    canEdit: true,
  },
  {
    id: "REJ-1002",
    sessionId: "S-1002",
    student: {
      id: "STU-1002",
      name: "Bob Smith",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Bob+Smith&background=random",
    },
    topic: "Reinforcement Learning",
    subtopic: "Q-Learning",
    sessionType: "One-on-One",
    date: "2023-12-06",
    time: "2:00 PM",
    duration: 60,
    rejectionReason: "Not qualified to teach this specific subtopic.",
    rejectionDate: "2023-11-18",
    status: "approved",
    adminFeedback: "Rejection approved. We'll find another tutor for this session.",
    adminAction: "reassigned",
    canEdit: false,
  },
  {
    id: "REJ-1003",
    sessionId: "S-1003",
    student: {
      id: "STU-1003",
      name: "Charlie Davis",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Charlie+Davis&background=random",
    },
    topic: "Computer Vision",
    subtopic: "Object Detection",
    sessionType: "Group",
    date: "2023-12-07",
    time: "11:00 AM",
    duration: 90,
    rejectionReason: "Personal emergency, unable to conduct the session.",
    rejectionDate: "2023-11-15",
    status: "approved",
    adminFeedback: "Understood. Hope everything is okay. We've reassigned the session.",
    adminAction: "reassigned",
    canEdit: false,
  },
  {
    id: "REJ-1004",
    sessionId: "S-1004",
    student: {
      id: "STU-1004",
      name: "Diana Wang",
      email: "<EMAIL>",
      avatar: "https://ui-avatars.com/api/?name=Diana+Wang&background=random",
    },
    topic: "Natural Language Processing",
    subtopic: "Transformers",
    sessionType: "One-on-One",
    date: "2023-12-08",
    time: "9:00 AM",
    duration: 60,
    rejectionReason: "The topic requires more advanced knowledge than I currently possess.",
    rejectionDate: "2023-11-10",
    status: "awaiting_approval",
    adminFeedback: null,
    adminAction: null,
    canEdit: true,
  },
];

// Format date to display in a readable format
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
};

const RejectionRequests = () => {
  const profileData = useProfileData();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedRejection, setSelectedRejection] = useState<any>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showRevokeDialog, setShowRevokeDialog] = useState(false);
  const [editedReason, setEditedReason] = useState("");
  
  // Filter rejections based on search term and status filter
  const filteredRejections = rejectionRequestsData.filter(rejection => {
    const matchesSearch = 
      rejection.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rejection.topic.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rejection.subtopic.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = 
      statusFilter === "all" || 
      (statusFilter === "awaiting" && rejection.status === "awaiting_approval") ||
      (statusFilter === "approved" && rejection.status === "approved");
    
    return matchesSearch && matchesStatus;
  });
  
  // Get status badge
  const getStatusBadge = (status: string, adminAction: string | null) => {
    switch (status) {
      case "awaiting_approval":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Awaiting Admin Approval</Badge>;
      case "approved":
        if (adminAction === "reassigned") {
          return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Reassigned</Badge>;
        } else if (adminAction === "confirmed") {
          return <Badge className="bg-green-100 text-green-800 border-green-200">Confirmed</Badge>;
        } else if (adminAction === "expired") {
          return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Expired</Badge>;
        }
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Approved</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };
  
  // Handle edit rejection reason
  const handleEditReason = () => {
    // In a real app, this would update the rejection reason
    console.log(`Updating rejection reason for ${selectedRejection.id}: ${editedReason}`);
    setShowEditDialog(false);
  };
  
  // Handle revoke rejection
  const handleRevokeRejection = () => {
    // In a real app, this would revoke the rejection
    console.log(`Revoking rejection for ${selectedRejection.id}`);
    setShowRevokeDialog(false);
  };
  
  return (
    <TutorPageLayout
      title="Rejection Approvals"
      profileData={profileData}
      description="Track the status of your rejected session requests"
      actions={
        <Badge className="bg-purple-100 text-purple-800 border-purple-200">
          {filteredRejections.length} Rejections
        </Badge>
      }
    >
      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <div className="relative flex-grow max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search by student, topic, or subtopic..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Tabs defaultValue="all" className="w-full sm:w-auto" onValueChange={setStatusFilter}>
          <TabsList className="grid grid-cols-3 w-full sm:w-auto">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="awaiting">Awaiting</TabsTrigger>
            <TabsTrigger value="approved">Approved</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Rejections List */}
      <div className="space-y-4">
        {filteredRejections.length > 0 ? (
          filteredRejections.map(rejection => (
            <Card key={rejection.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <CardContent className="p-0">
                <div className="p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row justify-between gap-4">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={rejection.student.avatar} alt={rejection.student.name} />
                        <AvatarFallback>{rejection.student.name.split(' ').map((n: string) => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{rejection.student.name}</h3>
                          {getStatusBadge(rejection.status, rejection.adminAction)}
                        </div>
                        <p className="text-sm text-gray-500">{rejection.student.email}</p>
                        <div className="mt-2">
                          <p className="text-sm font-medium">{rejection.topic}: {rejection.subtopic}</p>
                          <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                            <Calendar size={14} />
                            <span>{formatDate(rejection.date)}</span>
                            <Clock size={14} className="ml-2" />
                            <span>{rejection.time} ({rejection.duration} min)</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {rejection.canEdit && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-purple-600 border-purple-200 hover:bg-purple-50"
                            onClick={() => {
                              setSelectedRejection(rejection);
                              setEditedReason(rejection.rejectionReason);
                              setShowEditDialog(true);
                            }}
                          >
                            <Edit size={14} className="mr-1" />
                            Edit Reason
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-200 hover:bg-red-50"
                            onClick={() => {
                              setSelectedRejection(rejection);
                              setShowRevokeDialog(true);
                            }}
                          >
                            <RefreshCw size={14} className="mr-1" />
                            Revoke
                          </Button>
                        </>
                      )}
                      {!rejection.canEdit && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-gray-600 border-gray-200"
                          disabled
                        >
                          <Eye size={14} className="mr-1" />
                          View Only
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  {/* Rejection Details */}
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium mb-1">Rejection Reason</h4>
                        <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                          {rejection.rejectionReason}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Rejected on {formatDate(rejection.rejectionDate)}
                        </p>
                      </div>
                      
                      {rejection.adminFeedback && (
                        <div>
                          <h4 className="text-sm font-medium mb-1">Admin Feedback</h4>
                          <div className="flex items-start gap-2 bg-blue-50 p-3 rounded-md">
                            <MessageSquare size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
                            <div>
                              <p className="text-sm text-blue-700">{rejection.adminFeedback}</p>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* Rejection Flow Status */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">Rejection Flow Status</h4>
                        <div className="flex items-center">
                          <div className="flex items-center">
                            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 text-purple-700">
                              <CheckCircle size={14} />
                            </div>
                            <div className="ml-2">
                              <p className="text-xs font-medium">Rejected</p>
                            </div>
                          </div>
                          <div className="w-8 h-0.5 bg-gray-200 mx-1"></div>
                          <div className="flex items-center">
                            <div className={`flex items-center justify-center w-6 h-6 rounded-full ${
                              rejection.status === "awaiting_approval" 
                                ? "bg-yellow-100 text-yellow-700" 
                                : "bg-green-100 text-green-700"
                            }`}>
                              {rejection.status === "awaiting_approval" 
                                ? <Clock size={14} /> 
                                : <CheckCircle size={14} />
                              }
                            </div>
                            <div className="ml-2">
                              <p className="text-xs font-medium">Admin Review</p>
                            </div>
                          </div>
                          <div className="w-8 h-0.5 bg-gray-200 mx-1"></div>
                          <div className="flex items-center">
                            <div className={`flex items-center justify-center w-6 h-6 rounded-full ${
                              rejection.adminAction 
                                ? "bg-green-100 text-green-700" 
                                : "bg-gray-100 text-gray-400"
                            }`}>
                              {rejection.adminAction 
                                ? <CheckCircle size={14} /> 
                                : <Clock size={14} />
                              }
                            </div>
                            <div className="ml-2">
                              <p className="text-xs font-medium">
                                {rejection.adminAction === "reassigned" 
                                  ? "Reassigned" 
                                  : rejection.adminAction === "confirmed" 
                                    ? "Confirmed" 
                                    : rejection.adminAction === "expired" 
                                      ? "Expired" 
                                      : "Pending"
                                }
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <X className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900">No rejections found</h3>
            <p className="text-gray-500 mt-2">
              {searchTerm || statusFilter !== "all" 
                ? "Try adjusting your search or filter criteria." 
                : "You haven't rejected any session requests yet."}
            </p>
          </div>
        )}
      </div>

      {/* Edit Reason Dialog */}
      {selectedRejection && (
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Rejection Reason</DialogTitle>
              <DialogDescription>
                Update your reason for rejecting the session with {selectedRejection.student.name}.
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Rejection Reason
              </label>
              <Textarea
                value={editedReason}
                onChange={(e) => setEditedReason(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-sm text-gray-500 mt-2">
                Your updated reason will be reviewed by an admin.
              </p>
            </div>
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>Cancel</Button>
              <Button onClick={handleEditReason}>Update Reason</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Revoke Rejection Dialog */}
      {selectedRejection && (
        <Dialog open={showRevokeDialog} onOpenChange={setShowRevokeDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Revoke Rejection</DialogTitle>
              <DialogDescription>
                Are you sure you want to revoke your rejection for the session with {selectedRejection.student.name}?
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              <p className="text-sm text-gray-700">
                Revoking this rejection means you are willing to conduct this session as originally scheduled:
              </p>
              <ul className="mt-2 space-y-1 text-sm text-gray-600">
                <li>• Topic: {selectedRejection.topic}</li>
                <li>• Date: {formatDate(selectedRejection.date)}</li>
                <li>• Time: {selectedRejection.time}</li>
              </ul>
              <p className="text-sm text-gray-500 mt-4">
                This action cannot be undone once the admin approves your revocation.
              </p>
            </div>
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setShowRevokeDialog(false)}>Cancel</Button>
              <Button onClick={handleRevokeRejection}>Revoke Rejection</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </TutorPageLayout>
  );
};

export default RejectionRequests;

import React, { useState, useEffect, useRef } from 'react';

interface MathElement {
  id: number;
  symbol: string; // Can be a symbol, equation, or formula
  x: number;
  y: number;
  opacity: number;
  timestamp: number;
}

interface AnimatedFaviconProps {
  containerRef: React.RefObject<HTMLElement>;
}

const AnimatedFavicon: React.FC<AnimatedFaviconProps> = ({ containerRef }) => {
  const [logoPosition, setLogoPosition] = useState({ x: 0, y: 0 });
  const [mathElements, setMathElements] = useState<MathElement[]>([]);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const animationRef = useRef<number>();
  const lastSymbolTime = useRef<number>(0);
  const symbolIdCounter = useRef<number>(0);

  // Mathematical symbols and equations to drop
  const mathElementsArray = [
    // Basic symbols
    '∑', '∫', '∂', '∆', '∇', '∞', 'π', '√', '±', '≈', '≤', '≥', '∈', '∉', '∪', '∩', '⊂', '⊃', '∀', '∃',
    'α', 'β', 'γ', 'θ', 'λ', 'μ', 'σ', 'φ', 'ψ', 'ω',
    // Short equations and formulas
    'E=mc²', 'a²+b²=c²', 'F=ma', 'PV=nRT', 'x=(-b±√(b²-4ac))/2a', 'sin²θ+cos²θ=1',
    'e^(iπ)+1=0', 'A=πr²', 'V=4/3πr³', 'y=mx+b', 'f(x)=ax²+bx+c', 'lim(x→∞)',
    'd/dx', '∫₀^∞', 'Σᵢ₌₁ⁿ', '∇²φ=0', 'ℏω', 'kT', 'log₂(x)', 'sin(x)', 'cos(x)',
    'tan(x)', 'e^x', 'ln(x)', 'x!', 'nCr', 'P(A|B)', '|x|', '√x', 'x²', 'x³',
    'dx/dt', 'Δx', 'f\'(x)', 'f\'\'(x)', '∂f/∂x', '∮', '∯', '∰'
  ];

  // Update container dimensions
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerDimensions({ width: rect.width, height: rect.height });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [containerRef]);

  // Animation loop
  useEffect(() => {
    if (containerDimensions.width === 0 || containerDimensions.height === 0) return;

    const startTime = Date.now();
    const animationDuration = 25000; // 25 seconds for full loop (slower)
    const symbolDropInterval = 150; // Drop symbol every 150ms

    const animate = () => {
      const currentTime = Date.now();
      const elapsed = (currentTime - startTime) % animationDuration;
      const progress = elapsed / animationDuration;

      // Calculate logo position around the border with padding
      const padding = 60; // Distance from border
      const adjustedWidth = containerDimensions.width - 2 * padding;
      const adjustedHeight = containerDimensions.height - 2 * padding;
      const perimeter = 2 * (adjustedWidth + adjustedHeight);
      const currentDistance = progress * perimeter;

      let x, y;

      if (currentDistance < adjustedWidth) {
        // Top edge
        x = padding + currentDistance;
        y = padding;
      } else if (currentDistance < adjustedWidth + adjustedHeight) {
        // Right edge
        x = padding + adjustedWidth;
        y = padding + (currentDistance - adjustedWidth);
      } else if (currentDistance < 2 * adjustedWidth + adjustedHeight) {
        // Bottom edge
        x = padding + adjustedWidth - (currentDistance - adjustedWidth - adjustedHeight);
        y = padding + adjustedHeight;
      } else {
        // Left edge
        x = padding;
        y = padding + adjustedHeight - (currentDistance - 2 * adjustedWidth - adjustedHeight);
      }

      setLogoPosition({ x, y });

      // Drop mathematical symbols and equations
      if (currentTime - lastSymbolTime.current > symbolDropInterval) {
        const randomElement = mathElementsArray[Math.floor(Math.random() * mathElementsArray.length)];
        const newElement: MathElement = {
          id: symbolIdCounter.current++,
          symbol: randomElement,
          x: x + (Math.random() - 0.5) * 40, // Add some randomness around position
          y: y + (Math.random() - 0.5) * 40,
          opacity: 1,
          timestamp: currentTime
        };

        setMathElements(prev => [...prev, newElement]);
        lastSymbolTime.current = currentTime;
      }

      // Update element opacity and remove old ones
      setMathElements(prev =>
        prev
          .map(element => ({
            ...element,
            opacity: Math.max(0, 1 - (currentTime - element.timestamp) / 1500) // Fade over 1.5 seconds
          }))
          .filter(element => element.opacity > 0)
      );

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [containerDimensions]);

  if (containerDimensions.width === 0 || containerDimensions.height === 0) {
    return null;
  }

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {/* Mathematical Symbols and Equations */}
      {mathElements.map(element => {
        // Determine if it's a longer equation or short symbol
        const isEquation = element.symbol.length > 3;
        const fontSize = isEquation ? '14px' : '24px';
        const fontWeight = isEquation ? '600' : 'bold';

        return (
          <div
            key={element.id}
            className="absolute select-none z-10 whitespace-nowrap"
            style={{
              left: `${element.x}px`,
              top: `${element.y}px`,
              opacity: element.opacity,
              transform: `scale(${element.opacity}) rotate(${(1 - element.opacity) * 15}deg)`,
              transition: 'opacity 0.1s ease-out, transform 0.1s ease-out',
              color: `hsl(${220 + (1 - element.opacity) * 60}, 70%, ${50 + element.opacity * 20}%)`,
              textShadow: '0 1px 2px rgba(0,0,0,0.1)',
              fontSize: fontSize,
              fontWeight: fontWeight,
              fontFamily: isEquation ? 'monospace' : 'inherit',
              maxWidth: '200px',
              overflow: 'hidden',
            }}
          >
            {element.symbol}
          </div>
        );
      })}
    </div>
  );
};

export default AnimatedFavicon;

import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import InquiryForm from "@/components/InquiryForm";
import useScrollToTop from "@/hooks/useScrollToTop";
import { Mail, Phone, MapPin } from "lucide-react";

const Inquiry = () => {
  // Use the scroll to top hook
  useScrollToTop();

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-4 sm:py-6 md:py-8 lg:py-12">
        <div className="max-w-7xl mx-2 sm:mx-4 md:mx-9 px-2 sm:px-2 lg:px-0">
          {/* Flex container matching Feedback page structure */}
          <div className="flex flex-col lg:flex-row items-start gap-8 lg:gap-16">
            <div className="lg:max-w-lg">
              <p className="text-xl text-gray-500 mt-2 sm:mt-4 md:mt-2 lg:mt-0">
                Our team is ready to assist you with any inquiries about our
                tutoring services and reinforcement learning platform. Whether
                you have questions about courses, pricing, or technical
                support, we're here to help.
              </p>

              {/* Get in Touch Section */}
              <div className="mt-8 lg:mt-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h2>

                <div className="space-y-6">
                  {/* Email */}
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <Mail className="h-6 w-6 text-purple-600 mt-1" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">Email Us</h3>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-gray-500 hover:text-purple-600 transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  {/* Phone */}
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <Phone className="h-6 w-6 text-purple-600 mt-1" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">Call Us</h3>
                      <a
                        href="tel:+918690845205"
                        className="text-gray-500 hover:text-purple-600 transition-colors"
                      >
                        (+91) 8690845205
                      </a>
                    </div>
                  </div>

                  {/* Address */}
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <MapPin className="h-6 w-6 text-purple-600 mt-1" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-gray-900">Visit Us</h3>
                      <p className="text-gray-500">
                        D-213, 4th Floor, 244/6, Sangam Vihar,<br />
                        Wazirabad, Delhi-110084, India
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="w-full lg:flex-1">
              <InquiryForm />
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Inquiry;

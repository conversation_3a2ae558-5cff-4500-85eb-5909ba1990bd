import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { X, Menu, Home } from "lucide-react";
import { useSidebarStore } from "@/store/sidebarStore";
import HorizontalDivider from "./HorizontalDivider";

export interface SidebarItemType {
  icon: React.ReactNode;
  label: string;
  path?: string;
  subItems?: SidebarItemType[];
  badgeCount?: number;
}

interface SidebarProps {
  title: React.ReactNode;
  menuItems: SidebarItemType[];
  onLogout: () => void;
  logoutIcon: React.ReactNode;
  headerColor?: string;
  homeLink?: string;
  homeIcon?: React.ReactNode;
  expandIcon?: React.ReactNode;
  collapseIcon?: React.ReactNode;
}

const Sidebar: React.FC<SidebarProps> = ({
  title,
  menuItems,
  onLogout,
  logoutIcon,
  headerColor = "text-gray-800",
  homeLink = "/",
  homeIcon,
  expandIcon = <Menu size={16} />,
  collapseIcon = <X size={16} />,
}) => {
  const { isCollapsed, toggleCollapse } = useSidebarStore();
  const location = useLocation();

  return (
    <div
      className={cn(
        "bg-white h-screen shadow-md flex flex-col transition-all duration-300 ease-in-out sticky top-0",
        isCollapsed ? "w-16" : "w-60"
      )}
    >
      {/* Toggle button positioned absolutely */}
      <button
        onClick={toggleCollapse}
        className={cn(
          "absolute top-4 bg-white rounded-full shadow-md p-1.5 focus:outline-none hover:bg-gray-100 transition-all duration-300 z-10",
          isCollapsed ? "right-0 translate-x-1/2" : "right-4"
        )}
        aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        type="button"
      >
        {isCollapsed ? expandIcon : collapseIcon}
      </button>

      <div className="p-4 flex items-center">
        {isCollapsed ? (
          <Link
            to={homeLink}
            className="mx-auto hover:text-rfpurple-600 transition-colors"
            aria-label="Home"
          >
            <div className={headerColor}>
              {homeIcon}
            </div>
          </Link>
        ) : (
          <Link
            to={homeLink}
            className={cn("font-bold text-xl hover:text-rfpurple-600 transition-colors", headerColor)}
          >
            {title}
          </Link>
        )}
      </div>
      <HorizontalDivider />

      <div className="flex-1 overflow-y-auto py-4 scrollbar-thin">
        <nav className="px-2 space-y-1">
          {menuItems.map((item, index) => {
            const isActive = item.path ? location.pathname === item.path : false;
            const hasSubItems = item.subItems && item.subItems.length > 0;
            const { isCollapsed, toggleSection, isSectionExpanded } = useSidebarStore();
            const isExpanded = isSectionExpanded(index);

            // Check if any sub-item is active
            const isSubItemActive = hasSubItems && item.subItems?.some(
              subItem => location.pathname === subItem.path
            );

            return (
              <div key={index} className="mb-1">
                {/* Main menu item */}
                {item.path ? (
                  <Link
                    to={item.path}
                    className={cn(
                      "flex items-center px-3 py-2 rounded-md transition-colors",
                      isActive
                        ? "bg-rfpurple-50 text-rfpurple-600"
                        : "text-gray-600 hover:bg-gray-100",
                      isCollapsed ? "justify-center" : ""
                    )}
                  >
                    <div
                      className={isActive ? "text-rfpurple-600" : "text-gray-500"}
                    >
                      {item.icon}
                    </div>

                    <span
                      className={cn(
                        "ml-3 transition-opacity duration-200 flex items-center",
                        isCollapsed ? "opacity-0 absolute" : "opacity-100"
                      )}
                    >
                      {item.label}
                      {item.badgeCount && item.badgeCount > 0 && (
                        <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
                          {item.badgeCount}
                        </span>
                      )}
                    </span>
                  </Link>
                ) : (
                  <button
                    onClick={() => !isCollapsed && toggleSection(index)}
                    className={cn(
                      "flex items-center w-full px-3 py-2 rounded-md transition-colors",
                      (isExpanded || isSubItemActive)
                        ? "bg-rfpurple-50 text-rfpurple-600"
                        : "text-gray-600 hover:bg-gray-100",
                      isCollapsed ? "justify-center" : ""
                    )}
                  >
                    <div
                      className={(isExpanded || isSubItemActive) ? "text-rfpurple-600" : "text-gray-500"}
                    >
                      {item.icon}
                    </div>

                    {!isCollapsed && (
                      <>
                        <span className="ml-3 flex-1 text-left flex items-center">
                          {item.label}
                          {item.badgeCount && item.badgeCount > 0 && (
                            <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
                              {item.badgeCount}
                            </span>
                          )}
                        </span>
                        {hasSubItems && (
                          <svg
                            className={cn(
                              "w-4 h-4 transition-transform",
                              isExpanded ? "transform rotate-180" : ""
                            )}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        )}
                      </>
                    )}
                  </button>
                )}

                {/* Sub-items */}
                {hasSubItems && !isCollapsed && isExpanded && (
                  <div className="mt-1 ml-6 space-y-1">
                    {item.subItems?.map((subItem, subIndex) => {
                      const isSubActive = location.pathname === subItem.path;

                      return (
                        <Link
                          key={subIndex}
                          to={subItem.path}
                          className={cn(
                            "flex items-center px-3 py-2 rounded-md text-sm transition-colors",
                            isSubActive
                              ? "bg-rfpurple-50 text-rfpurple-600"
                              : "text-gray-600 hover:bg-gray-100"
                          )}
                        >
                          {subItem.icon && (
                            <div className={isSubActive ? "text-rfpurple-600" : "text-gray-500"}>
                              {subItem.icon}
                            </div>
                          )}
                          <span className={cn("ml-3 flex items-center", !subItem.icon && "ml-0")}>
                            {subItem.label}
                            {subItem.badgeCount && subItem.badgeCount > 0 && (
                              <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
                                {subItem.badgeCount}
                              </span>
                            )}
                          </span>
                        </Link>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </nav>
      </div>

      <HorizontalDivider />
      <div className="p-4">
        <button
          onClick={onLogout}
          className={cn(
            "flex items-center w-full px-3 py-2 text-gray-600 rounded-md hover:bg-gray-100 transition-colors",
            isCollapsed ? "justify-center" : ""
          )}
        >
          <div className="text-gray-500">{logoutIcon}</div>

          <span
            className={cn(
              "ml-3 transition-opacity duration-200",
              isCollapsed ? "opacity-0 absolute" : "opacity-100"
            )}
          >
            Logout
          </span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;











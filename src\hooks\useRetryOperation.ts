import { useEffect, useCallback, useRef } from "react";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface RetryConfig {
  maxAttempts?: number;
  retryInterval?: number;
  persistKey?: string; // Optional key for persisting state across reloads
}

interface RetryState {
  attempts: number;
  isRetrying: boolean;
  error: Error | null;
  success: boolean;
  completed: boolean; // Add a new flag to track completion
  isExecuting: boolean; // Add flag to track execution in progress
}

interface RetryStore extends RetryState {
  setState: (state: Partial<RetryState>) => void;
}

/**
 * A hook for retrying operations with configurable persistence
 */
export function useRetryOperation<T>(
  operation: () => Promise<T> | boolean | T,
  config: RetryConfig = {}
) {
  const { maxAttempts = 5, retryInterval = 500, persistKey } = config;
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const operationRef = useRef(operation);

  // Update the operation ref when it changes
  useEffect(() => {
    operationRef.current = operation;
  }, [operation]);

  // Create a Zustand store for this specific retry operation
  const useRetryStore = create<RetryStore>()(
    persist(
      (set) => ({
        attempts: 0,
        isRetrying: false,
        error: null,
        success: false,
        completed: false,
        isExecuting: false, // Add new state property
        setState: (newState) => set((state) => ({ ...state, ...newState })),
      }),
      {
        name: persistKey ? `retry_${persistKey}` : undefined,
        skipHydration: !persistKey,
      }
    )
  );

  // Get state and setState from the store
  const state = useRetryStore();
  const setState = state.setState;

  // Clean up any pending timeouts when component unmounts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Execute the operation with retries
  const execute = useCallback(async () => {
    // Get the latest state directly from the store
    const currentState = useRetryStore.getState();
    
    // If already completed successfully, don't retry
    if (currentState.completed) {
      console.log("Operation already completed successfully, skipping retry");
      return true;
    }

    // Use store state to prevent duplicate executions
    if (currentState.isExecuting) {
      console.log("Already executing, skipping duplicate execution");
      return false;
    }

    // Set executing state in the store
    setState({ isExecuting: true, isRetrying: true, error: null, success: false });

    try {
      // Use the ref to get the latest operation function
      const result = await operationRef.current();

      // If operation returns true or a value, consider it successful
      if (result === true || (result !== false && result !== undefined)) {
        setState({ 
          isRetrying: false,
          isExecuting: false,
          success: true,
          completed: true // Mark as completed on success
        });
        return result;
      }

      // Get the latest state again for the attempts check
      const updatedState = useRetryStore.getState();
      
      // If we haven't reached max attempts, schedule another retry
      if (updatedState.attempts < maxAttempts - 1) {
        setState({ attempts: updatedState.attempts + 1 });

        // Schedule next retry
        timeoutRef.current = setTimeout(() => {
          timeoutRef.current = null;
          // Reset executing state before next attempt
          setState({ isExecuting: false });
          execute();
        }, retryInterval);
        return false;
      } else {
        // Max attempts reached
        setState({
          isRetrying: false,
          isExecuting: false,
          completed: true, // Mark as completed even on failure
          error: new Error(`Operation failed after ${maxAttempts} attempts`),
        });
        return false;
      }
    } catch (error) {
      setState({
        isRetrying: false,
        isExecuting: false,
        completed: true, // Mark as completed on error
        error: error instanceof Error ? error : new Error(String(error)),
      });
      return false;
    }
  }, [maxAttempts, retryInterval, setState]);

  // Reset the retry state
  const reset = useCallback(() => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    // Reset store state
    setState({ 
      attempts: 0, 
      isRetrying: false,
      isExecuting: false,
      error: null, 
      success: false,
      completed: false // Reset completed flag
    });
    
    if (persistKey) {
      try {
        sessionStorage.removeItem(`retry_${persistKey}`);
      } catch (e) {
        console.error("Failed to clear session storage:", e);
      }
    }
  }, [persistKey, setState]);

  return {
    execute,
    reset,
    ...state,
  };
}



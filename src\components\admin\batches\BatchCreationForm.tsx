// src/components/admin/batches/BatchCreationForm.tsx
import React, { useEffect } from "react";
import { useBillingStore } from "@/store/billingStore";
import { useTutorStore } from "@/store/tutorStore";

import { useBatchCreationStore } from "@/store/batchCreationStore";
import { Button } from "@/components/ui/Button";
import { Label } from "@/components/ui/Label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/Select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/Card";
import { AlertCircle, CheckCircle, Loader2, Users } from "lucide-react";
import { format } from "date-fns";

interface BatchCreationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

const BatchCreationForm: React.FC<BatchCreationFormProps> = ({ onSuccess, onCancel }) => {
  // Get stores
  const { fetchProducts } = useBillingStore();
  const { fetchTutors, tutors } = useTutorStore();

  // Get batch creation store
  const {
    selectedStudent,
    selectedTutor,
    selectedSubscription,
    enrolledStudents,
    selectedStudentData,
    isLoadingStudents,
    isLoading,
    error,
    success,
    setSelectedTutor,
    setSelectedSubscription,
    fetchEnrolledStudents,
    handleStudentSelection,
    handleSubmit
  } = useBatchCreationStore();

  // Fetch data on mount
  useEffect(() => {
    console.log("BatchCreationForm: Fetching data...");
    fetchEnrolledStudents();
    fetchTutors();
    fetchProducts();

    // Reset form when component unmounts
    return () => {
      useBatchCreationStore.getState().resetForm();
    };
  }, [fetchEnrolledStudents, fetchTutors, fetchProducts]);

  // Debug: Log enrolled students data when it changes
  useEffect(() => {
    console.log("BatchCreationForm: Enrolled students data updated:", enrolledStudents);
  }, [enrolledStudents]);

  // Handle successful submission
  useEffect(() => {
    if (success) {
      // Wait a moment before closing the form
      const timer = setTimeout(() => {
        onSuccess();
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [success, onSuccess]);

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy");
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create New Batch</CardTitle>
        <CardDescription>
          Assign a student to a tutor <br/>
          Provide assistance in curriculum configuration if needed
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {/* Loading State for Students */}
          {isLoadingStudents && (
            <div className="flex items-center justify-center p-6 text-gray-600">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading enrolled students for batch creation...</span>
            </div>
          )}

          {/* Conditional Messages */}
          {!isLoadingStudents && (
            <div className="p-3 bg-blue-50 rounded-md text-sm text-blue-800 flex items-center">
              <Users className="h-4 w-4 mr-2" />
              {enrolledStudents.length === 0 ? (
                "No enrolled students found for Batch creation"
              ) : (
                `Total ${enrolledStudents.length} enrolled student${enrolledStudents.length !== 1 ? 's' : ''} found`
              )}
            </div>
          )}

          {/* Student Selection */}
          {!isLoadingStudents && enrolledStudents.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="student">Student <span className="text-red-500">*</span></Label>
              <Select
                value={selectedStudent}
                onValueChange={handleStudentSelection}
              >
                <SelectTrigger id="student">
                  <SelectValue placeholder="Select a student" />
                </SelectTrigger>
                <SelectContent>
                  {enrolledStudents.map(student => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.first_name} {student.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Subscription Selection */}
          {selectedStudent && selectedStudentData && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="subscription">Subscription <span className="text-red-500">*</span></Label>
                {selectedStudentData.subscriptions.length === 0 ? (
                  <div className="p-3 bg-yellow-50 rounded-md text-sm text-yellow-800">
                    <AlertCircle className="h-4 w-4 inline-block mr-2" />
                    This student has no active subscriptions. They need to purchase a product first.
                  </div>
                ) : (
                  <Select
                    value={selectedSubscription}
                    onValueChange={setSelectedSubscription}
                  >
                    <SelectTrigger id="subscription">
                      <SelectValue placeholder="Select a subscription" />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedStudentData.subscriptions.map(subscription => (
                        <SelectItem key={subscription.id} value={subscription.id}>
                          {subscription.product_name} (Expires: {formatDate(subscription.current_period_end)})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>


            </div>
          )}



          {/* Tutor Selection */}
          {!isLoadingStudents && enrolledStudents.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="tutor">Assign Default Tutor</Label>
              <Select
                value={selectedTutor}
                onValueChange={setSelectedTutor}
              >
                <SelectTrigger id="tutor">
                  <SelectValue placeholder="Select a tutor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No default tutor</SelectItem>
                  {tutors.map(tutor => (
                    <SelectItem key={tutor.id} value={tutor.id}>
                      {tutor.first_name} {tutor.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-50 rounded-md text-sm text-red-800">
              <AlertCircle className="h-4 w-4 inline-block mr-2" />
              {error}
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="p-3 bg-green-50 rounded-md text-sm text-green-800">
              <CheckCircle className="h-4 w-4 inline-block mr-2" />
              Batch created successfully!
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading || success}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              isLoading ||
              success ||
              isLoadingStudents ||
              enrolledStudents.length === 0 ||
              !selectedStudent ||
              !selectedSubscription
            }
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Creating...
              </>
            ) : (
              "Create Batch"
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};

export default BatchCreationForm;
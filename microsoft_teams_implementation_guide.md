# Microsoft Teams Backend Integration Implementation Guide

## 🎯 Overview

This guide provides step-by-step instructions for implementing Microsoft Teams integration using **Supabase Edge Functions** for secure, server-side authentication and meeting management. This backend approach provides better security and user experience compared to client-side implementations.

## 🏗️ Architecture

```
Frontend (React) → Supabase Edge Functions → Microsoft Graph API
                ↓
            Database (user_integrations table)
```

### Key Benefits
- ✅ **Secure**: Tokens stored server-side, never exposed to frontend
- ✅ **Better UX**: No popup windows or CORS issues  
- ✅ **Production Ready**: Proper token refresh and error handling
- ✅ **Maintainable**: Clean separation of concerns

## 📋 Prerequisites

Before starting the implementation, ensure you have:

1. **Azure Account**: Access to Azure portal with admin privileges
2. **Microsoft 365 Subscription**: Required for Teams functionality
3. **Supabase Project**: Your existing Supabase setup with CLI access
4. **Development Environment**: Node.js, npm, and your tutoring platform codebase

## Part 1: Azure App Registration Setup

### 1. Access Azure Portal
1. Navigate to [Azure Portal](https://portal.azure.com)
2. Sign in with your Azure account
3. Ensure you're in the correct tenant/directory

### 2. Navigate to App Registrations
1. In the Azure portal search bar, type: `App registrations`
2. Click on **App registrations** from the results
   - **Note:** The service is now under "Microsoft Entra ID" (formerly Azure Active Directory)
   - If you don't see it directly, you can also search for `Microsoft Entra ID` or `Azure Active Directory`
3. Click the **+ New registration** button

### 3. Configure Basic App Information
Fill out the registration form:

**Name:** `rfLearn Teams`

**Supported account types:** Select one of:
- ✅ **Accounts in this organizational directory only** (Single tenant) - *Recommended for most cases*
- ⚠️ **Accounts in any organizational directory** (Multi-tenant) - *Only if you need multi-tenant support*

**Redirect URI:**
- **Type:** Web
- **URL:** `https://your-supabase-project.supabase.co/functions/v1/teams-auth/callback`
- **Note:** Replace `your-supabase-project` with your actual Supabase project reference

Click **Register** to create the app.

### 4. Note Down Important IDs
After registration, you'll see the **Overview** page. Copy these values:

**Application (client) ID:** `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
→ This is your `AZURE_CLIENT_ID`

**Directory (tenant) ID:** `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
→ This is your `AZURE_TENANT_ID`

### 5. Create Client Secret
1. In the left sidebar, click **Certificates & secrets**
2. Click **+ New client secret**
3. **Description:** `rfLearn Teams Integration Secret`
4. **Expires:** Select `24 months` (recommended)
5. Click **Add**
6. **⚠️ IMPORTANT:** Copy the **Value** immediately (you can only see it once!)
   → This is your `AZURE_CLIENT_SECRET` (backend only)

### 6. Configure API Permissions
1. In the left sidebar, click **API permissions**
2. Click **+ Add a permission**
3. Select **Microsoft Graph**
4. Select **Application permissions**
5. Search for and add these permissions:
   - ✅ `OnlineMeetings.ReadWrite.All`
   - ✅ `User.Read.All`
   - ✅ `Calendars.ReadWrite.All` (optional, for calendar integration)
6. Click **Add permissions**
7. **⚠️ CRITICAL:** Click **Grant admin consent for [Your Organization]**
8. Confirm by clicking **Yes**

### 7. Verify Configuration
Go back to **Overview** and verify:
- ✅ Application ID is visible
- ✅ Directory ID is visible
- ✅ Redirect URI is configured correctly
- ✅ API permissions are granted with admin consent

## Part 2: Backend Implementation

### 1. Deploy Supabase Edge Functions

The backend implementation consists of two Edge Functions that are already created in your project:

- `supabase/functions/teams-auth/index.ts` - Handles OAuth flow
- `supabase/functions/teams-meetings/index.ts` - Creates meetings

**Deploy using the provided script:**

```bash
# Windows (PowerShell)
./scripts/deploy-teams-backend.ps1

# Linux/Mac
./scripts/deploy-teams-backend.sh
```

**Or deploy manually:**

```bash
# Deploy functions
supabase functions deploy teams-auth
supabase functions deploy teams-meetings

# Run database migration
supabase db push
```

### 2. Set Environment Variables

**No frontend environment variables needed!** All sensitive data is now handled server-side.

Set these in Supabase Edge Functions:

```bash
# Set in Supabase (keep secret)
supabase secrets set AZURE_CLIENT_ID=your_application_client_id_from_step_4
supabase secrets set AZURE_CLIENT_SECRET=your_client_secret_from_step_5
supabase secrets set AZURE_TENANT_ID=your_directory_tenant_id_from_step_4
supabase secrets set FRONTEND_URL=http://localhost:8080
```

### 3. Database Schema

The migration creates a `user_integrations` table to store OAuth tokens securely:

```sql
CREATE TABLE user_integrations (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    provider TEXT CHECK (provider IN ('microsoft_teams', 'google_meet', 'zoom')),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMPTZ NOT NULL,
    scope TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, provider)
);
```

## Part 3: Testing Your Setup

### 1. Deploy and Configure

1. **Deploy the backend functions:**
   ```bash
   ./scripts/deploy-teams-backend.ps1
   ```

2. **Update Azure redirect URI** to match your Supabase function URL

3. **Set environment variables** in Supabase

### 2. Test Authentication

1. **Start your development server:**
   ```bash
   npm run dev
   ```

2. **Test authentication:**
   - Navigate to any page with Teams integration (e.g., session management)
   - Click "Connect to Teams"
   - You should be redirected to Microsoft login
   - After successful login, you should be redirected back with `?teams_success=true`

### 3. Test Meeting Creation

1. **Try creating a test meeting** from a session page
2. **Verify that a Teams meeting URL is generated and stored**
3. **Check the database** to ensure tokens are stored in `user_integrations`

## 🔄 Authentication Flow

### Step 1: Initiate Authentication
```typescript
// User clicks "Connect to Teams"
const success = await teamsService.authenticate(currentUrl);
// User is redirected to Microsoft OAuth
```

### Step 2: OAuth Callback
```
Microsoft → Supabase Edge Function → Exchange code for tokens → Store in database → Redirect back to frontend
```

### Step 3: Success Handling
```typescript
// Frontend detects success from URL parameters
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('teams_success') === 'true') {
    setIsAuthenticated(true);
    // Show success message
  }
}, []);
```

## 💻 Usage Examples

### Check Authentication Status
```typescript
const { isAuthenticated } = useTeamsIntegration();
```

### Authenticate User
```typescript
const { authenticate } = useTeamsIntegration();
await authenticate(); // Redirects to Microsoft, then back
```

### Create Meeting
```typescript
const { createAndStoreMeeting } = useTeamsIntegration();

const success = await createAndStoreMeeting(sessionId, {
  subject: 'Math Tutoring Session',
  startTime: '2024-12-08T10:00:00Z',
  endTime: '2024-12-08T11:00:00Z',
  participants: ['<EMAIL>', '<EMAIL>'],
});
```

## 🚨 Common Issues & Solutions

### Issue: "Redirect URI mismatch"
**Solution:** 
- Ensure Azure redirect URI exactly matches your Supabase function URL
- Format: `https://your-project.supabase.co/functions/v1/teams-auth/callback`

### Issue: "Permission denied"
**Solution:**
- Verify API permissions are granted with admin consent
- Check that all required permissions are added

### Issue: "Function deployment fails"
**Solution:**
- Ensure Supabase CLI is installed and logged in
- Check that you're in the project root directory
- Verify `supabase/config.toml` exists

### Issue: "Authentication fails silently"
**Solution:**
- Check Supabase function logs: `supabase functions logs teams-auth`
- Verify environment variables are set correctly
- Ensure user has proper Supabase authentication

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Authentication redirects to Microsoft and back successfully
- ✅ URL shows `?teams_success=true` after authentication
- ✅ Meeting creation generates valid Teams URLs
- ✅ Tokens are stored in `user_integrations` table
- ✅ No CORS or popup-related errors

## 📚 Additional Resources

- [TEAMS_BACKEND_IMPLEMENTATION.md](./TEAMS_BACKEND_IMPLEMENTATION.md) - Detailed technical documentation
- [AZURE_APP_REGISTRATION_WALKTHROUGH.md](./AZURE_APP_REGISTRATION_WALKTHROUGH.md) - Step-by-step Azure setup
- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)

---

**🎯 Next:** Once this setup is complete, your Teams integration will be fully functional with secure, server-side token management!

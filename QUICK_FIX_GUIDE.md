# Quick Fix Guide for Profile Data Issue

## The Problem
You're getting this error:
```
ERROR: 42601: syntax error at or near "FILTER"
```

This is because your PostgreSQL version doesn't support the `FILTER` clause syntax.

## Solution Options (Try in Order)

### Option 1: Emergency Fix (Recommended First)
```sql
\i emergency_profile_fix.sql
```

**What it does:**
- ✅ Creates minimal functions without complex syntax
- ✅ Bypasses subscription logic temporarily
- ✅ Gets basic profile data working immediately
- ✅ Compatible with all PostgreSQL versions

**Expected result:** Profile name should display correctly

### Option 2: Simple Fix (If Option 1 Works)
```sql
\i fix_student_profile_function_simple.sql
```

**What it does:**
- ✅ Recreates functions with subqueries instead of FILTER
- ✅ Includes subscription logic
- ✅ More complete data but more complex

### Option 3: Original Fix (If Your PostgreSQL is 9.4+)
```sql
\i fix_student_profile_function.sql
```

**What it does:**
- ✅ Full-featured functions with all data
- ❌ Requires PostgreSQL 9.4+ for FILTER syntax

## Quick Test

After running any fix, test it:

```sql
-- Test if the function works
SELECT first_name, last_name, email FROM get_basic_student_profile(auth.uid());

-- Should return: Yusuf | Ali | <EMAIL>
```

## Expected Results

### ✅ Success Indicators
- No more SQL syntax errors
- Console shows: "Profile data updated successfully"
- Dashboard displays: "Welcome, Yusuf Ali!"
- User menu shows correct name

### 🔍 Browser Console Should Show
```
ProfileStore: Starting updateProfile for userId: 715d2b84-cc4a-443b-bee1-74e80725b21d
ProfileStore: Profile query result: { data: [...], error: null }
ProfileStore: Profile data updated successfully: {
  firstName: "Yusuf",
  lastName: "Ali",
  email: "<EMAIL>"
}
```

## If Still Having Issues

### Issue: Functions don't exist
```sql
-- Check if functions were created
SELECT routine_name FROM information_schema.routines 
WHERE routine_name LIKE '%student%profile%';
```

### Issue: Still getting errors
1. **Clear browser cache** completely
2. **Restart your development server**
3. **Check PostgreSQL version:**
   ```sql
   SELECT version();
   ```

### Issue: Profile data still not loading
1. **Check if profile exists:**
   ```sql
   SELECT * FROM profiles WHERE id = auth.uid();
   ```
2. **If no profile, create one:**
   ```sql
   \i fix_profile_data_loading.sql
   ```

## Troubleshooting by PostgreSQL Version

### PostgreSQL 9.3 or older
- ✅ Use `emergency_profile_fix.sql`
- ❌ Avoid FILTER syntax entirely

### PostgreSQL 9.4 - 12
- ✅ Use `fix_student_profile_function_simple.sql`
- ⚠️ FILTER syntax may work but use subqueries to be safe

### PostgreSQL 13+
- ✅ Any of the scripts should work
- ✅ Full FILTER syntax support

## Quick Recovery Steps

1. **Run emergency fix:**
   ```sql
   \i emergency_profile_fix.sql
   ```

2. **Clear browser cache:**
   ```
   Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
   ```

3. **Check result:**
   - Dashboard should show "Welcome, Yusuf Ali!"
   - No more console errors

4. **If working, optionally upgrade:**
   ```sql
   \i fix_student_profile_function_simple.sql
   ```

## Success Confirmation

You'll know it worked when:
- ✅ No SQL syntax errors in console
- ✅ Dashboard shows your actual name
- ✅ Profile menu displays correctly
- ✅ No more "User" fallback text

The emergency fix prioritizes getting your name to display correctly over complex subscription features. Once that's working, you can optionally upgrade to more complete functions.

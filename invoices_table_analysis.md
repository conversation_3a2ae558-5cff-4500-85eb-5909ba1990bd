# Invoices Table Analysis and Migration

## Current vs Enhanced Structure

### Your Existing Table
```sql
CREATE TABLE invoices (
  id UUID PRIMARY KEY,
  student_id UUID NOT NULL,           -- ✓ Keep
  amount NUMERIC(10,2) NOT NULL,      -- ✓ Keep  
  status TEXT NOT NULL,               -- ✓ Enhance
  payment_method TEXT,                -- ✓ Keep
  payment_id TEXT,                    -- ✓ Keep
  created_at TIMESTAMP DEFAULT now(), -- ✓ Keep
  updated_at TIMESTAMP DEFAULT now(), -- ✓ Keep
  actual_amount NUMERIC(10,2),        -- ✓ Keep
  workflow_id UUID                    -- ✓ Keep (excellent for workflow integration)
);
```

### Enhanced Structure (After Migration)
```sql
CREATE TABLE invoices (
  -- Existing columns (preserved)
  id UUID PRIMARY KEY,
  student_id UUID NOT NULL,
  amount NUMERIC(10,2) NOT NULL,
  actual_amount NUMERIC(10,2),
  status TEXT NOT NULL,               -- Enhanced with new statuses
  payment_method TEXT,
  payment_id TEXT,
  workflow_id UUID,                   -- Links to subscription workflow
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now(),
  
  -- New columns for enhanced functionality
  subscription_id UUID,               -- Links to subscriptions table
  stripe_invoice_id TEXT UNIQUE,      -- Stripe integration
  stripe_customer_id TEXT,            -- Stripe customer reference
  stripe_subscription_id TEXT,        -- Stripe subscription reference
  invoice_number TEXT,                -- Human-readable invoice numbers
  amount_due NUMERIC(10,2),          -- Amount due (calculated from amount)
  amount_paid NUMERIC(10,2),         -- Amount paid (0 or amount)
  amount_remaining NUMERIC(10,2),    -- Remaining balance
  currency TEXT DEFAULT 'usd',       -- Currency support
  due_date TIMESTAMP,                 -- Payment due date
  paid_at TIMESTAMP,                  -- When payment was completed
  invoice_pdf_url TEXT,              -- PDF download link
  hosted_invoice_url TEXT,           -- Stripe hosted invoice page
  receipt_number TEXT,               -- Receipt reference
  stripe_created_at TIMESTAMP        -- Stripe creation timestamp
);
```

## Key Enhancements

### 1. **Preserved Your Design**
- **workflow_id**: Your excellent design linking invoices to workflows is preserved
- **All existing columns**: No data loss, all current functionality maintained
- **Existing statuses**: `pending`, `paid`, `failed`, `refunded` still work

### 2. **Added Stripe Integration**
- **stripe_invoice_id**: Links to Stripe invoice objects
- **stripe_customer_id**: Links to Stripe customer
- **stripe_subscription_id**: Links to Stripe subscription
- **Enhanced status**: Added `draft`, `open`, `uncollectible`, `void` for Stripe compatibility

### 3. **Better Invoice Management**
- **invoice_number**: Human-readable numbers (INV-000001, INV-000002, etc.)
- **amount_due/paid/remaining**: Clear payment tracking
- **due_date/paid_at**: Better date management
- **PDF and hosted URLs**: Direct links to invoice documents

### 4. **Dual Linking Strategy**
```sql
-- Your workflow-based approach (preserved)
workflow_id → subscription_workflows → subscriptions

-- Direct subscription linking (added)
subscription_id → subscriptions

-- Both approaches supported for flexibility
```

## Migration Benefits

### 1. **Backward Compatibility**
- All existing queries continue to work
- No breaking changes to current functionality
- Existing data preserved and enhanced

### 2. **Forward Compatibility**
- Ready for Stripe integration
- Supports advanced billing features
- Extensible for future payment providers

### 3. **Data Integrity**
- Automatic calculation of amount_due, amount_paid, amount_remaining
- Automatic paid_at timestamp when status changes to 'paid'
- Invoice number generation for existing records

## Migration Process

### 1. **Safe Migration**
```sql
-- Adds columns without affecting existing data
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS subscription_id UUID;
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS stripe_invoice_id TEXT;
-- ... other columns

-- Populates new columns from existing data
UPDATE invoices SET 
  amount_due = amount,
  amount_paid = CASE WHEN status = 'paid' THEN amount ELSE 0 END,
  amount_remaining = CASE WHEN status = 'paid' THEN 0 ELSE amount END;
```

### 2. **Automatic Enhancements**
- **Invoice numbers**: Generated for all existing invoices
- **Subscription linking**: Automatic linking via workflow_id where possible
- **Payment tracking**: Calculated from existing status and amount data

### 3. **Helper Functions**
```sql
-- Generate sequential invoice numbers
SELECT generate_invoice_number(); -- Returns 'INV-000001'

-- Link invoice to subscription via workflow
SELECT link_invoice_to_subscription('invoice-uuid');

-- Update missing invoice numbers
SELECT update_missing_invoice_numbers();
```

## Usage Examples

### 1. **Create Invoice (Enhanced)**
```typescript
const invoice = await supabase
  .from('invoices')
  .insert({
    student_id: 'student-123',
    workflow_id: 'workflow-456',
    amount: 99.99,
    status: 'pending',
    payment_method: 'stripe',
    invoice_number: 'INV-000123',
    amount_due: 99.99,
    amount_remaining: 99.99,
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  });
```

### 2. **Process Payment**
```typescript
// When payment is completed
await supabase
  .from('invoices')
  .update({
    status: 'paid',
    stripe_invoice_id: 'in_stripe_123',
    payment_id: 'pi_stripe_456'
    // paid_at, amount_paid, amount_remaining updated automatically by trigger
  })
  .eq('id', invoiceId);
```

### 3. **Query Enhanced Data**
```sql
-- Get invoice with full payment details
SELECT 
  i.*,
  sw.product_type,
  s.current_period_start,
  s.current_period_end
FROM invoices i
LEFT JOIN subscription_workflows sw ON i.workflow_id = sw.id
LEFT JOIN subscriptions s ON i.subscription_id = s.id
WHERE i.student_id = 'student-123';
```

## Advantages of Your Current Design

### 1. **Workflow Integration**
Your `workflow_id` approach is excellent because:
- **Direct connection** to the subscription process
- **Clear audit trail** from workflow to invoice
- **Supports complex billing** scenarios

### 2. **Simplicity**
- **Clean, focused structure** for core billing needs
- **Easy to understand** and maintain
- **Efficient queries** for common operations

### 3. **Flexibility**
- **actual_amount** field allows for discounts/adjustments
- **Generic payment_method** supports multiple providers
- **Extensible** design that grows with your needs

## Summary

The migration enhances your existing invoices table while:

✅ **Preserving** all existing functionality and data  
✅ **Adding** Stripe integration capabilities  
✅ **Maintaining** your excellent workflow-based design  
✅ **Providing** automatic data migration and cleanup  
✅ **Supporting** both simple and complex billing scenarios  

Your current design is solid and the enhancements make it production-ready for advanced billing features while keeping the simplicity you already have.

import { create } from "zustand";

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  duration_days: number;
  type: 'booster' | 'custom' | 'preparation';
  features: Record<string, any>;
  is_active: boolean;
}

export interface Invoice {
  id: string;
  student_id: string;
  amount: number;
  status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: string;
  payment_id: string;
  created_at: Date;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  id: string;
  invoice_id: string;
  product_id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  subtotal: number;
}

export interface Subscription {
  id: string;
  student_id: string;
  product_id: string;
  product_name: string;
  invoice_id: string;
  start_date: Date;
  end_date: Date;
  status: 'active' | 'expired' | 'cancelled';
  days_remaining: number;
}

interface BillingStore {
  // State
  products: Product[];
  invoices: Invoice[];
  subscriptions: Subscription[];
  activeSubscriptions: Subscription[];
  isLoading: boolean;
  error: string | null;
  purchaseInProgress: Set<string>; // Track products being purchased

  // Actions
  fetchProducts: () => Promise<void>;
  fetchInvoices: (studentId: string) => Promise<void>;
  fetchSubscriptions: (studentId: string) => Promise<void>;
  fetchActiveSubscriptions: (studentId: string) => Promise<void>;
  purchaseProduct: (studentId: string, productId: string, paymentMethod: string) => Promise<boolean>;
  getActiveSubscriptions: () => Subscription[];
  getInvoiceDetails: (invoiceId: string) => Promise<Invoice | null>;
  cancelSubscription: (subscriptionId: string) => Promise<boolean>;
  isEnrolled: (studentId: string) => boolean;
}

export const useBillingStore = create<BillingStore>((set, get) => ({
  products: [],
  invoices: [],
  subscriptions: [],
  activeSubscriptions: [],
  isLoading: false,
  error: null,
  purchaseInProgress: new Set(),

  fetchProducts: async () => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (error) throw error;

      set({ products: data, isLoading: false });
    } catch (error) {
      console.error("Error fetching products:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load products",
        isLoading: false
      });
    }
  },

  fetchInvoices: async (studentId) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Fetch invoices with their items
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('invoices')
        .select('*')
        .eq('student_id', studentId)
        .order('created_at', { ascending: false });

      if (invoicesError) throw invoicesError;

      // For each invoice, fetch its items
      const invoicesWithItems = await Promise.all(
        invoicesData.map(async (invoice) => {
          const { data: itemsData, error: itemsError } = await supabase
            .from('invoice_items')
            .select(`
              *,
              products:product_id (name)
            `)
            .eq('invoice_id', invoice.id);

          if (itemsError) throw itemsError;

          // Format items with product name
          const items = itemsData.map(item => ({
            ...item,
            product_name: item.products.name
          }));

          return {
            ...invoice,
            created_at: new Date(invoice.created_at),
            items
          };
        })
      );

      set({ invoices: invoicesWithItems, isLoading: false });
    } catch (error) {
      console.error("Error fetching invoices:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load invoices",
        isLoading: false
      });
    }
  },

  fetchSubscriptions: async (studentId) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      const { data, error } = await supabase
        .from('subscriptions')
        .select(`
          *,
          products:product_id (name)
        `)
        .eq('student_id', studentId)
        .order('current_period_end', { ascending: false });

      if (error) throw error;

      // Calculate days remaining for each subscription
      const now = new Date();
      const subscriptionsWithDaysRemaining = data.map(sub => {
        const endDate = new Date(sub.current_period_end);
        const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

        return {
          ...sub,
          product_name: sub.products.name,
          start_date: new Date(sub.current_period_start),
          end_date: new Date(sub.current_period_end),
          days_remaining: daysRemaining
        };
      });

      set({ subscriptions: subscriptionsWithDaysRemaining, isLoading: false });
    } catch (error) {
      console.error("Error fetching subscriptions:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load subscriptions",
        isLoading: false
      });
    }
  },

  purchaseProduct: async (studentId, productId, paymentMethod) => {
    // Check if this product is already being purchased
    const currentState = get();
    if (currentState.purchaseInProgress.has(productId)) {
      console.log(`Purchase already in progress for product ${productId}`);
      return false;
    }

    // Add to purchase tracking
    set(state => ({
      ...state,
      isLoading: true,
      error: null,
      purchaseInProgress: new Set([...state.purchaseInProgress, productId])
    }));

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // 1. Check if student already has an active subscription for this product
      const { data: existingSubscription, error: existingError } = await supabase
        .from('subscriptions')
        .select('id, status, current_period_end')
        .eq('student_id', studentId)
        .eq('product_id', productId)
        .eq('status', 'active')
        .single();

      // If there's an active subscription, prevent duplicate purchase
      if (existingSubscription && !existingError) {
        const endDate = new Date(existingSubscription.current_period_end);
        const now = new Date();
        const daysRemaining = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        if (daysRemaining > 0) {
          throw new Error(`You already have an active subscription for this product with ${daysRemaining} days remaining.`);
        }
      }

      // 2. Get product details
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('id', productId)
        .single();

      if (productError) throw productError;

      // 2. Create invoice
      const { data: invoiceData, error: invoiceError } = await supabase
        .from('invoices')
        .insert({
          student_id: studentId,
          amount: productData.price,
          status: 'paid', // Assuming immediate payment for simplicity
          payment_method: paymentMethod,
          payment_id: `payment_${Date.now()}` // In a real app, this would come from payment processor
        })
        .select()
        .single();

      if (invoiceError) throw invoiceError;

      // 3. Create invoice item
      const { error: itemError } = await supabase
        .from('invoice_items')
        .insert({
          invoice_id: invoiceData.id,
          product_id: productId,
          quantity: 1,
          unit_price: productData.price,
          subtotal: productData.price
        });

      if (itemError) throw itemError;

      // 4. Create subscription
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + productData.duration_days);

      const { error: subscriptionError } = await supabase
        .from('subscriptions')
        .insert({
          student_id: studentId,
          product_id: productId,
          invoice_id: invoiceData.id,
          current_period_start: startDate.toISOString(),
          current_period_end: endDate.toISOString(),
          status: 'active'
        })
        .select()
        .single();

      if (subscriptionError) throw subscriptionError;

      // 5. Create notification using the notification service
      const { NotificationService } = await import('@/services/notificationService');
      await NotificationService.createFromTemplate(
        studentId,
        NotificationService.templates.paymentSuccess(productData.name)
      );

      // 6. Refresh data
      await get().fetchInvoices(studentId);
      await get().fetchSubscriptions(studentId);

      // Remove from purchase tracking and reset loading
      set(state => {
        const newPurchaseInProgress = new Set(state.purchaseInProgress);
        newPurchaseInProgress.delete(productId);
        return {
          ...state,
          isLoading: false,
          purchaseInProgress: newPurchaseInProgress
        };
      });
      return true;
    } catch (error) {
      console.error("Error purchasing product:", error);
      // Remove from purchase tracking and set error
      set(state => {
        const newPurchaseInProgress = new Set(state.purchaseInProgress);
        newPurchaseInProgress.delete(productId);
        return {
          ...state,
          error: error instanceof Error ? error.message : "Failed to complete purchase",
          isLoading: false,
          purchaseInProgress: newPurchaseInProgress
        };
      });
      return false;
    }
  },

  fetchActiveSubscriptions: async (studentId: string) => {
    // Make sure subscriptions are loaded first
    if (get().subscriptions.length === 0) {
      await get().fetchSubscriptions(studentId);
    }

    // Filter active subscriptions and update state
    const activeSubscriptions = get().subscriptions.filter(sub =>
      sub.status === 'active' && sub.days_remaining > 0
    );

    set({ activeSubscriptions });
  },

  getActiveSubscriptions: () => {
    // Simply return the active subscriptions from state
    return get().activeSubscriptions;
  },

  getInvoiceDetails: async (invoiceId) => {
    const { supabase } = await import("@/lib/supabaseClient");

    try {
      // Get invoice
      const { data: invoice, error: invoiceError } = await supabase
        .from('invoices')
        .select('*')
        .eq('id', invoiceId)
        .single();

      if (invoiceError) throw invoiceError;

      // Get invoice items
      const { data: items, error: itemsError } = await supabase
        .from('invoice_items')
        .select(`
          *,
          products:product_id (name)
        `)
        .eq('invoice_id', invoiceId);

      if (itemsError) throw itemsError;

      // Format items with product name
      const formattedItems = items.map(item => ({
        ...item,
        product_name: item.products.name
      }));

      return {
        ...invoice,
        created_at: new Date(invoice.created_at),
        items: formattedItems
      };
    } catch (error) {
      console.error("Error fetching invoice details:", error);
      return null;
    }
  },

  cancelSubscription: async (subscriptionId) => {
    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Update subscription status to cancelled
      const { error } = await supabase
        .from('subscriptions')
        .update({ status: 'cancelled' })
        .eq('id', subscriptionId);

      if (error) throw error;

      // Refresh subscriptions
      const subscription = get().subscriptions.find(sub => sub.id === subscriptionId);
      if (subscription) {
        await get().fetchSubscriptions(subscription.student_id);
      }

      set({ isLoading: false });
      return true;
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to cancel subscription",
        isLoading: false
      });
      return false;
    }
  },

  isEnrolled: (studentId) => {
    // Check if the student has any active subscriptions
    // First ensure we have the latest active subscriptions
    const activeSubscriptions = get().activeSubscriptions;

    return activeSubscriptions.some(sub => sub.student_id === studentId);
  }
}));

// src/pages/admin/batches/ViewBatches.tsx
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import AdminPageLayout from "@/components/layouts/AdminPageLayout";
import { useAdminBatchStore } from "@/store/adminBatchStore";
import { useBatchManagementUIStore } from "@/store/batchManagementUIStore";
import BatchList from "@/components/admin/batches/BatchList";
import { useProfileData } from "@/hooks/useProfileData";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { ArrowLeft, Search, AlertCircle } from "lucide-react";
import LoadingSpinner from "@/components/LoadingSpinner";

const ViewBatches: React.FC = () => {
  const navigate = useNavigate();
  const profileData = useProfileData();
  
  const {
    batches,
    isLoading,
    error,
    fetchBatches
  } = useAdminBatchStore();
  
  const {
    searchTerm,
    setSearchTerm,
    setSelectedBatchId,
    setIsDeleteDialogOpen,
    setIsAssignTutorDialogOpen
  } = useBatchManagementUIStore();

  // Fetch data on mount
  useEffect(() => {
    fetchBatches();
  }, [fetchBatches]);

  // Filter batches based on search term
  const filteredBatches = batches.filter(batch => {
    const searchString = searchTerm.toLowerCase();
    return (
      batch.student_name?.toLowerCase().includes(searchString) ||
      batch.subject_name?.toLowerCase().includes(searchString) ||
      batch.tutor_name?.toLowerCase().includes(searchString) ||
      batch.product_name?.toLowerCase().includes(searchString)
    );
  });

  return (
    <AdminPageLayout
      title="View Batches"
      description="Browse and search through all batches"
      profileData={profileData}
      actions={
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate("/admin/batches")}
          className="flex items-center gap-1"
        >
          <ArrowLeft size={16} />
          Back to Batch Management
        </Button>
      }
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading batches...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => fetchBatches()}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Search */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder="Search batches..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Batch List */}
          <BatchList
            batches={filteredBatches}
            onEdit={(batchId) => navigate(`/admin/batches/edit/${batchId}`)}
            onDelete={(batchId) => {
              setSelectedBatchId(batchId);
              navigate(`/admin/batches/delete/${batchId}`);
            }}
            onAssignTutor={(batchId) => {
              setSelectedBatchId(batchId);
              setIsAssignTutorDialogOpen(true);
            }}
          />
        </div>
      )}
    </AdminPageLayout>
  );
};

export default ViewBatches;

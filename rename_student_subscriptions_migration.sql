-- Migration Script: Rename student_subscriptions to subscriptions
-- This script safely renames the student_subscriptions table to subscriptions
-- and updates all related foreign key references and constraints

-- =====================================================
-- 1. RENAME TABLE AND UPDATE STRUCTURE
-- =====================================================

DO $$
BEGIN
    -- Check if student_subscriptions table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'student_subscriptions') THEN
        
        RAISE NOTICE 'Found student_subscriptions table, proceeding with migration...';
        
        -- If subscriptions table already exists, we need to handle this carefully
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions') THEN
            RAISE NOTICE 'subscriptions table already exists, backing up existing data...';
            
            -- Create backup table
            CREATE TABLE subscriptions_backup AS SELECT * FROM subscriptions;
            DROP TABLE subscriptions CASCADE;
            
            RAISE NOTICE 'Existing subscriptions table backed up and dropped.';
        END IF;
        
        -- Rename the table
        ALTER TABLE student_subscriptions RENAME TO subscriptions;
        RAISE NOTICE 'Renamed student_subscriptions to subscriptions.';
        
        -- Add new columns for enhanced subscription management
        ALTER TABLE subscriptions 
        ADD COLUMN IF NOT EXISTS workflow_id UUID,
        ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT,
        ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
        ADD COLUMN IF NOT EXISTS stripe_price_id TEXT,
        ADD COLUMN IF NOT EXISTS trial_start TIMESTAMP,
        ADD COLUMN IF NOT EXISTS trial_end TIMESTAMP,
        ADD COLUMN IF NOT EXISTS canceled_at TIMESTAMP,
        ADD COLUMN IF NOT EXISTS ended_at TIMESTAMP,
        ADD COLUMN IF NOT EXISTS amount DECIMAL(10,2),
        ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'usd',
        ADD COLUMN IF NOT EXISTS interval_type TEXT,
        ADD COLUMN IF NOT EXISTS access_granted_at TIMESTAMP,
        ADD COLUMN IF NOT EXISTS access_expires_at TIMESTAMP;
        
        RAISE NOTICE 'Added new columns for enhanced subscription management.';
        
        -- Rename columns to match new schema
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscriptions' AND column_name = 'start_date') THEN
            ALTER TABLE subscriptions RENAME COLUMN start_date TO current_period_start;
            RAISE NOTICE 'Renamed start_date to current_period_start.';
        END IF;
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscriptions' AND column_name = 'end_date') THEN
            ALTER TABLE subscriptions RENAME COLUMN end_date TO current_period_end;
            RAISE NOTICE 'Renamed end_date to current_period_end.';
        END IF;
        
        -- Update status check constraint to include new values
        ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS student_subscriptions_status_check;
        ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_status_check;
        ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_status_check 
        CHECK (status IN ('active', 'expired', 'cancelled', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid'));
        
        RAISE NOTICE 'Updated status check constraint.';
        
        -- Add foreign key constraint for workflow_id if subscription_workflows table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_workflows') THEN
            ALTER TABLE subscriptions 
            ADD CONSTRAINT fk_subscriptions_workflow_id 
            FOREIGN KEY (workflow_id) REFERENCES subscription_workflows(id) ON DELETE SET NULL;
            
            RAISE NOTICE 'Added foreign key constraint for workflow_id.';
        END IF;
        
        -- Add unique constraint for stripe_subscription_id
        ALTER TABLE subscriptions 
        ADD CONSTRAINT unique_stripe_subscription_id UNIQUE (stripe_subscription_id);
        
        RAISE NOTICE 'Added unique constraint for stripe_subscription_id.';
        
        -- Add check constraint for interval_type
        ALTER TABLE subscriptions 
        ADD CONSTRAINT check_interval_type 
        CHECK (interval_type IN ('one_time', 'monthly', 'yearly') OR interval_type IS NULL);
        
        RAISE NOTICE 'Added check constraint for interval_type.';
        
    ELSE
        RAISE NOTICE 'student_subscriptions table not found, creating new subscriptions table...';
        
        -- Create new subscriptions table if student_subscriptions doesn't exist
        CREATE TABLE IF NOT EXISTS subscriptions (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
            product_id UUID REFERENCES products(id) ON DELETE RESTRICT,
            invoice_id UUID REFERENCES invoices(id),
            workflow_id UUID,
            
            -- Stripe identifiers
            stripe_subscription_id TEXT UNIQUE,
            stripe_customer_id TEXT,
            stripe_price_id TEXT,
            
            -- Subscription details
            status TEXT NOT NULL CHECK (status IN ('active', 'expired', 'cancelled', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid')),
            current_period_start TIMESTAMP,
            current_period_end TIMESTAMP,
            trial_start TIMESTAMP,
            trial_end TIMESTAMP,
            canceled_at TIMESTAMP,
            ended_at TIMESTAMP,
            
            -- Billing details
            amount DECIMAL(10,2),
            currency TEXT DEFAULT 'usd',
            interval_type TEXT CHECK (interval_type IN ('one_time', 'monthly', 'yearly')),
            
            -- Access control
            access_granted_at TIMESTAMP,
            access_expires_at TIMESTAMP,
            
            -- Timestamps
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
        );
        
        -- Add foreign key constraint for workflow_id if subscription_workflows table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_workflows') THEN
            ALTER TABLE subscriptions 
            ADD CONSTRAINT fk_subscriptions_workflow_id 
            FOREIGN KEY (workflow_id) REFERENCES subscription_workflows(id) ON DELETE SET NULL;
        END IF;
        
        RAISE NOTICE 'Created new subscriptions table.';
    END IF;
END $$;

-- =====================================================
-- 2. UPDATE INDEXES
-- =====================================================

-- Drop old indexes if they exist
DROP INDEX IF EXISTS idx_student_subscriptions_student_id;
DROP INDEX IF EXISTS idx_student_subscriptions_product_id;
DROP INDEX IF EXISTS idx_student_subscriptions_status;

-- Create new indexes for subscriptions table
CREATE INDEX IF NOT EXISTS idx_subscriptions_student_id ON subscriptions(student_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_product_id ON subscriptions(product_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_workflow_id ON subscriptions(workflow_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_current_period ON subscriptions(current_period_start, current_period_end);

-- =====================================================
-- 3. UPDATE FOREIGN KEY REFERENCES
-- =====================================================

-- Update subscription_curriculum table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_curriculum') THEN
        -- Drop the old foreign key constraint
        ALTER TABLE subscription_curriculum DROP CONSTRAINT IF EXISTS subscription_curriculum_subscription_id_fkey;
        
        -- Add the new foreign key constraint
        ALTER TABLE subscription_curriculum 
        ADD CONSTRAINT subscription_curriculum_subscription_id_fkey 
        FOREIGN KEY (subscription_id) REFERENCES subscriptions(id);
        
        RAISE NOTICE 'Updated foreign key reference in subscription_curriculum table.';
    END IF;
END $$;

-- =====================================================
-- 4. ADD TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Create or replace the update timestamp function if it doesn't exist
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger for subscriptions table
DROP TRIGGER IF EXISTS update_subscriptions_timestamp ON subscriptions;
CREATE TRIGGER update_subscriptions_timestamp 
BEFORE UPDATE ON subscriptions 
FOR EACH ROW EXECUTE FUNCTION update_timestamp();

-- =====================================================
-- 5. VERIFICATION
-- =====================================================

DO $$
BEGIN
    -- Verify the migration was successful
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions') THEN
        RAISE NOTICE 'Migration completed successfully. subscriptions table exists.';
        
        -- Check if old table still exists (it shouldn't)
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'student_subscriptions') THEN
            RAISE WARNING 'student_subscriptions table still exists. Manual cleanup may be required.';
        ELSE
            RAISE NOTICE 'student_subscriptions table successfully removed.';
        END IF;
    ELSE
        RAISE ERROR 'Migration failed. subscriptions table does not exist.';
    END IF;
END $$;

RAISE NOTICE 'Migration script completed. Please verify your application works correctly with the new table structure.';

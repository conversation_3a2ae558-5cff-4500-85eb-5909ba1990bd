-- Manual test to trigger the onboarding completion process

-- First, let's see what candidate_student records exist
SELECT 
    id,
    first_name,
    last_name,
    email,
    onboarding_completed,
    education_level,
    subjects_of_interest,
    learning_goals,
    date_of_birth
FROM candidate_student 
ORDER BY created_at DESC
LIMIT 5;

-- Test 1: Try to manually update a candidate_student record to trigger the function
-- (Replace the UUID with an actual ID from your candidate_student table)
/*
UPDATE candidate_student 
SET onboarding_completed = true,
    updated_at = NOW()
WHERE id = 'YOUR_CANDIDATE_ID_HERE'
AND onboarding_completed = false;
*/

-- Test 2: Check if the trigger fired by looking for new records
-- Run this after the UPDATE above
/*
SELECT 'Profiles created:' as check_type, COUNT(*) as count
FROM profiles 
WHERE id IN (SELECT id FROM candidate_student WHERE onboarding_completed = true)

UNION ALL

SELECT 'Students created:' as check_type, COUNT(*) as count
FROM students 
WHERE id IN (SELECT id FROM candidate_student WHERE onboarding_completed = true)

UNION ALL

SELECT 'Logs created:' as check_type, COUNT(*) as count
FROM logs 
WHERE context->>'source' LIKE '%handle_student_candidate_completion%';
*/

-- Test 3: Create a test function to manually call the trigger logic
CREATE OR REPLACE FUNCTION test_trigger_logic(test_candidate_id UUID)
RETURNS TEXT AS $$
DECLARE
    candidate_record candidate_student%ROWTYPE;
    error_message TEXT;
    result_message TEXT := '';
BEGIN
    -- Get the candidate record
    SELECT * INTO candidate_record 
    FROM candidate_student 
    WHERE id = test_candidate_id;
    
    IF candidate_record.id IS NULL THEN
        RETURN 'No candidate found with ID: ' || test_candidate_id;
    END IF;
    
    result_message := 'Testing with candidate: ' || candidate_record.first_name || ' ' || candidate_record.last_name || E'\n';
    
    -- Test profiles insert
    BEGIN
        INSERT INTO public.profiles (
            id, 
            first_name, 
            last_name, 
            user_type,
            email,
            created_at,
            updated_at
        )
        VALUES (
            candidate_record.id, 
            candidate_record.first_name, 
            candidate_record.last_name, 
            'student',
            candidate_record.email,
            NOW(),
            NOW()
        )
        ON CONFLICT (id) 
        DO UPDATE SET
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            user_type = 'student',
            email = EXCLUDED.email,
            updated_at = NOW();
            
        result_message := result_message || 'Profiles insert: SUCCESS' || E'\n';
    EXCEPTION WHEN OTHERS THEN
        result_message := result_message || 'Profiles insert ERROR: ' || SQLERRM || E'\n';
    END;
    
    -- Test students insert
    BEGIN
        INSERT INTO public.students (
            id,
            date_of_birth,
            education_level,
            subjects_of_interest,
            learning_goals,
            created_at,
            updated_at
        )
        VALUES (
            candidate_record.id,
            candidate_record.date_of_birth,
            candidate_record.education_level,
            candidate_record.subjects_of_interest,
            candidate_record.learning_goals,
            NOW(),
            NOW()
        )
        ON CONFLICT (id)
        DO UPDATE SET
            date_of_birth = EXCLUDED.date_of_birth,
            education_level = EXCLUDED.education_level,
            subjects_of_interest = EXCLUDED.subjects_of_interest,
            learning_goals = EXCLUDED.learning_goals,
            updated_at = NOW();
            
        result_message := result_message || 'Students insert: SUCCESS' || E'\n';
    EXCEPTION WHEN OTHERS THEN
        result_message := result_message || 'Students insert ERROR: ' || SQLERRM || E'\n';
    END;
    
    RETURN result_message;
END;
$$ LANGUAGE plpgsql;

-- Usage example (replace with actual candidate ID):
-- SELECT test_trigger_logic('your-candidate-id-here');

import React, { useState } from "react";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/Dialog";
import { Textarea } from "@/components/ui/TextArea";
import { useToast } from "@/components/ui/UseToast";
import { useSessionRequestStore, SessionRequest } from "@/store/sessionRequestStore";
import { Calendar, Clock, AlertCircle, CheckCircle, X, MessageCircle, User, BookOpen } from "lucide-react";

interface SessionRequestCardProps {
  request: SessionRequest;
  onAccept?: (requestId: string) => void;
  onReject?: (requestId: string, reason: string) => void;
}

const SessionRequestCard: React.FC<SessionRequestCardProps> = ({
  request,
  onAccept,
  onReject
}) => {
  const { toast } = useToast();
  const [isRejecting, setIsRejecting] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  const { acceptSessionRequest, rejectSessionRequest } = useSessionRequestStore();
  
  // Handle accept button click
  const handleAccept = async () => {
    setIsLoading(true);
    
    try {
      await acceptSessionRequest(request.id);
      
      toast({
        title: "Request accepted",
        description: "The session request has been accepted successfully.",
      });
      
      if (onAccept) {
        onAccept(request.id);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to accept the session request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle reject button click
  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      toast({
        title: "Reason required",
        description: "Please provide a reason for rejecting the request.",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      await rejectSessionRequest(request.id, rejectionReason);
      
      toast({
        title: "Request rejected",
        description: "The session request has been rejected successfully.",
      });
      
      setIsRejecting(false);
      setRejectionReason("");
      
      if (onReject) {
        onReject(request.id, rejectionReason);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject the session request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Get urgency badge
  const getUrgencyBadge = () => {
    switch (request.urgency) {
      case "high":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Within 24 hrs
          </Badge>
        );
      case "medium":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Within 48 hrs
          </Badge>
        );
      case "low":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            No rush
          </Badge>
        );
      default:
        return null;
    }
  };
  
  return (
    <Card className="mb-4">
      <CardContent className="pt-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Student info */}
          <div className="md:w-1/4">
            <div className="flex items-center mb-2">
              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                <User className="h-5 w-5 text-gray-500" />
              </div>
              <div>
                <div className="font-medium">Student</div>
                <div className="text-sm text-gray-500">ID: {request.studentId}</div>
              </div>
            </div>
          </div>
          
          {/* Session details */}
          <div className="md:w-2/4">
            <div className="flex flex-wrap gap-2 mb-2">
              {getUrgencyBadge()}
              {request.autoAccepted && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Auto-Accept
                </Badge>
              )}
              {request.hasConflict && (
                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" /> Conflict
                </Badge>
              )}
            </div>
            
            <h3 className="font-medium flex items-center">
              <BookOpen className="h-4 w-4 mr-1" />
              Topic ID: {request.topicId}
              {request.subtopicId && ` / Subtopic ID: ${request.subtopicId}`}
            </h3>
            
            <div className="flex items-center text-sm text-gray-600 mt-1">
              <Calendar className="h-4 w-4 mr-1" />
              {request.requestedDate} at {request.requestedTime} • {request.durationMin} minutes
            </div>
            
            {request.notes && (
              <div className="mt-2 text-sm">
                <div className="font-medium">Notes:</div>
                <div className="text-gray-600">{request.notes}</div>
              </div>
            )}
            
            {request.hasConflict && (
              <div className="mt-2 bg-red-50 p-2 rounded-md text-sm text-red-700 flex items-start">
                <AlertCircle className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
                <div>
                  <span className="font-medium">Conflicts with:</span> {request.conflictDetails}
                </div>
              </div>
            )}
          </div>
          
          {/* Actions */}
          <div className="md:w-1/4 flex md:flex-col md:items-end gap-2">
            <Button
              variant="default"
              className="bg-green-600 hover:bg-green-700 text-white"
              onClick={handleAccept}
              disabled={isLoading}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Accept
            </Button>
            
            <Dialog open={isRejecting} onOpenChange={setIsRejecting}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="border-red-200 text-red-700 hover:bg-red-50"
                  disabled={isLoading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Reject
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Reject Session Request</DialogTitle>
                  <DialogDescription>
                    Please provide a reason for rejecting this session request.
                    This will be shared with the student.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="py-4">
                  <Textarea
                    placeholder="Enter reason for rejection"
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
                
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsRejecting(false)}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleReject}
                    disabled={isLoading || !rejectionReason.trim()}
                  >
                    {isLoading ? "Rejecting..." : "Confirm Rejection"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SessionRequestCard;

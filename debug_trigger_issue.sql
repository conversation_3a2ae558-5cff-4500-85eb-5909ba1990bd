-- Debug script to check trigger and function status

-- 1. Check if the trigger exists
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement,
    action_condition
FROM information_schema.triggers 
WHERE trigger_name = 'on_student_candidate_completed';

-- 2. Check if the function exists
SELECT 
    routine_name,
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name = 'handle_student_candidate_completion';

-- 3. Check the current students table schema
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'students' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. Check candidate_student table schema
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'candidate_student' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 5. Check if there are any candidate_student records with onboarding_completed = true
SELECT 
    id,
    first_name,
    last_name,
    email,
    onboarding_completed,
    created_at,
    updated_at
FROM candidate_student 
WHERE onboarding_completed = true
ORDER BY updated_at DESC
LIMIT 5;

-- 6. Check if there are corresponding records in profiles table
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.user_type,
    p.email
FROM profiles p
WHERE p.id IN (
    SELECT id FROM candidate_student WHERE onboarding_completed = true
)
LIMIT 5;

-- 7. Check if there are corresponding records in students table
SELECT 
    s.id,
    s.education_level,
    s.subjects_of_interest,
    s.learning_goals,
    s.date_of_birth
FROM students s
WHERE s.id IN (
    SELECT id FROM candidate_student WHERE onboarding_completed = true
)
LIMIT 5;

-- 8. Check all logs (not just trigger-related)
SELECT 
    created_at,
    level,
    message,
    user_id,
    context
FROM logs 
ORDER BY created_at DESC 
LIMIT 10;

-- 9. Test if we can manually call the trigger function
-- (This will help us see if the function itself works)
DO $$
DECLARE
    test_record candidate_student%ROWTYPE;
    result candidate_student%ROWTYPE;
BEGIN
    -- Get a test record
    SELECT * INTO test_record 
    FROM candidate_student 
    WHERE onboarding_completed = true 
    LIMIT 1;
    
    IF test_record.id IS NOT NULL THEN
        RAISE NOTICE 'Testing trigger function with record ID: %', test_record.id;
        
        -- Manually call the trigger function
        SELECT * INTO result FROM handle_student_candidate_completion() 
        WHERE NEW = test_record;
        
        RAISE NOTICE 'Trigger function executed successfully';
    ELSE
        RAISE NOTICE 'No test record found with onboarding_completed = true';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error testing trigger function: %', SQLERRM;
END $$;

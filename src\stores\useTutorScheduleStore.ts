import { create } from 'zustand';

// Types for tutor schedule management
export interface Student {
  id: string;
  name: string;
  email: string;
  profilePictureUrl?: string;
  batchId: string;
  batchName: string;
  packageType: string;
  packageName: string;
  status: string;
  totalSessions?: number;
  remainingSessions?: number;
}

interface TutorScheduleState {
  // State
  selectedStudent: string | null;
  students: Student[];
  pendingRequests: number;
  isLoading: boolean;
  error: string | null;

  // Actions
  setSelectedStudent: (studentId: string | null) => void;
  setStudents: (students: Student[]) => void;
  setPendingRequests: (count: number) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  fetchStudents: (tutorId: string) => Promise<void>;
  fetchPendingRequestsCount: (tutorId: string) => Promise<void>;
  resetState: () => void;
}

/**
 * Zustand store for managing tutor schedule page state
 * Handles student selection, pending requests count, and related UI state
 */
export const useTutorScheduleStore = create<TutorScheduleState>((set, get) => ({
  // Initial state
  selectedStudent: null,
  students: [],
  pendingRequests: 0,
  isLoading: false,
  error: null,

  // Actions
  setSelectedStudent: (studentId: string | null) => {
    set({ selectedStudent: studentId });
  },

  setStudents: (students: Student[]) => {
    set({ students });
  },

  setPendingRequests: (count: number) => {
    set({ pendingRequests: count });
  },

  setIsLoading: (isLoading: boolean) => {
    set({ isLoading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  fetchStudents: async (tutorId: string) => {
    if (!tutorId) {
      set({ error: 'Tutor ID is required' });
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Initialize arrays to store results
      let batchStudents: any[] = [];
      let topicStudents: any[] = [];
      let subtopicStudents: any[] = [];

      // Query to get students assigned to this tutor through default batch assignment
      try {
        const { data, error: batchError } = await supabase
          .from('batches')
          .select(`
            id,
            name,
            package_type,
            package_name,
            status,
            total_sessions,
            remaining_sessions,
            student:student_id (
              id,
              first_name,
              last_name,
              email,
              profile_picture_url
            )
          `)
          .eq('default_tutor_id', tutorId)
          .eq('status', 'active')
          .order('created_at', { ascending: false });

        if (batchError) {
          console.error('Error fetching batch students:', batchError);
          // If it's a table not found error, continue with empty array
          if (batchError.message.includes('relation') && batchError.message.includes('does not exist')) {
            console.warn('Batches table does not exist, skipping batch students query');
          } else {
            throw batchError;
          }
        } else {
          batchStudents = data || [];
        }
      } catch (error) {
        console.warn('Failed to fetch batch students, continuing with empty array:', error);
        batchStudents = [];
      }

      // Query to get students assigned through custom topic assignments
      try {
        const { data, error: topicError } = await supabase
          .from('batch_topics')
          .select(`
            batch:batch_id (
              id,
              name,
              package_type,
              package_name,
              status,
              total_sessions,
              remaining_sessions,
              student:student_id (
                id,
                first_name,
                last_name,
                email,
                profile_picture_url
              )
            )
          `)
          .eq('custom_tutor_id', tutorId)
          .eq('batch.status', 'active');

        if (topicError) {
          console.error('Error fetching topic students:', topicError);
          if (topicError.message.includes('relation') && topicError.message.includes('does not exist')) {
            console.warn('Batch_topics table does not exist, skipping topic students query');
          } else {
            throw topicError;
          }
        } else {
          topicStudents = data || [];
        }
      } catch (error) {
        console.warn('Failed to fetch topic students, continuing with empty array:', error);
        topicStudents = [];
      }

      // Query to get students assigned through custom subtopic assignments
      try {
        const { data, error: subtopicError } = await supabase
          .from('batch_subtopics')
          .select(`
            batch_topic:batch_topic_id (
              batch:batch_id (
                id,
                name,
                package_type,
                package_name,
                status,
                total_sessions,
                remaining_sessions,
                student:student_id (
                  id,
                  first_name,
                  last_name,
                  email,
                  profile_picture_url
                )
              )
            )
          `)
          .eq('custom_tutor_id', tutorId)
          .eq('batch_topic.batch.status', 'active');

        if (subtopicError) {
          console.error('Error fetching subtopic students:', subtopicError);
          if (subtopicError.message.includes('relation') && subtopicError.message.includes('does not exist')) {
            console.warn('Batch_subtopics table does not exist, skipping subtopic students query');
          } else {
            throw subtopicError;
          }
        } else {
          subtopicStudents = data || [];
        }
      } catch (error) {
        console.warn('Failed to fetch subtopic students, continuing with empty array:', error);
        subtopicStudents = [];
      }

      // Combine all batch data from different sources
      const allBatches: any[] = [];

      // Add batches from default tutor assignment
      if (batchStudents && Array.isArray(batchStudents)) {
        allBatches.push(...batchStudents);
      }

      // Add batches from topic assignments
      if (topicStudents && Array.isArray(topicStudents)) {
        topicStudents.forEach(item => {
          if (item && item.batch) {
            allBatches.push(item.batch);
          }
        });
      }

      // Add batches from subtopic assignments
      if (subtopicStudents && Array.isArray(subtopicStudents)) {
        subtopicStudents.forEach(item => {
          if (item && item.batch_topic && item.batch_topic.batch) {
            allBatches.push(item.batch_topic.batch);
          }
        });
      }

      // If no batches found, return empty array (this is not an error)
      if (allBatches.length === 0) {
        console.log('No students assigned to tutor:', tutorId);
        set({
          students: [],
          isLoading: false,
          error: null // Clear any previous errors
        });
        return;
      }

      // Transform the data to match our Student interface
      const formattedStudents: Student[] = allBatches
        .filter(batch => batch && batch.student) // Ensure batch and student data exists
        .map(batch => ({
          id: batch.student.id,
          name: `${batch.student.first_name || ''} ${batch.student.last_name || ''}`.trim(),
          email: batch.student.email || '',
          profilePictureUrl: batch.student.profile_picture_url,
          batchId: batch.id,
          batchName: batch.name || '',
          packageType: batch.package_type || '',
          packageName: batch.package_name || '',
          status: batch.status || 'active',
          totalSessions: batch.total_sessions || 0,
          remainingSessions: batch.remaining_sessions || 0,
        }));

      // Remove duplicates (same student might have multiple batches)
      const uniqueStudents = formattedStudents.reduce((acc, student) => {
        const existingStudent = acc.find(s => s.id === student.id);
        if (!existingStudent) {
          acc.push(student);
        }
        return acc;
      }, [] as Student[]);

      console.log(`Found ${uniqueStudents.length} unique students for tutor:`, tutorId);

      set({
        students: uniqueStudents,
        isLoading: false,
        error: null // Clear any previous errors
      });
    } catch (error) {
      console.error('Error fetching students:', error);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Failed to fetch students';

      if (error instanceof Error) {
        // Check for common database/permission errors
        if (error.message.includes('relation') && error.message.includes('does not exist')) {
          errorMessage = 'Database tables not found. Please contact support.';
        } else if (error.message.includes('permission denied')) {
          errorMessage = 'Permission denied. Please contact support.';
        } else if (error.message.includes('connection')) {
          errorMessage = 'Database connection error. Please try again.';
        } else {
          errorMessage = error.message;
        }
      }

      set({
        error: errorMessage,
        isLoading: false,
        students: [] // Ensure students array is empty on error
      });
    }
  },

  fetchPendingRequestsCount: async (tutorId: string) => {
    if (!tutorId) {
      return;
    }

    try {
      const { supabase } = await import("@/lib/supabaseClient");

      // Get count of pending session requests for this tutor
      const { count, error } = await supabase
        .from('session_requests')
        .select('*', { count: 'exact', head: true })
        .eq('tutor_id', tutorId)
        .eq('status', 'pending');

      if (error) throw error;

      set({ pendingRequests: count || 0 });
    } catch (error) {
      console.error('Error fetching pending requests count:', error);
      // Don't set error for this as it's not critical
      set({ pendingRequests: 0 });
    }
  },

  resetState: () => {
    set({
      selectedStudent: null,
      students: [],
      pendingRequests: 0,
      isLoading: false,
      error: null
    });
  },

  // Future methods can be added here for:
  // - fetchStudentDetails: async (studentId: string) => { ... },
  // - updateStudentAssignment: async (studentId: string, assignment: any) => { ... },
  // - removeStudentAssignment: async (studentId: string) => { ... },
}));

import { create } from 'zustand';
import { UserStatus, IsOnboarded } from "@/constants/auth";

interface UserStatusState {
  email: string;
  userStatus: UserStatus;
  userType: string | null;
  isOnboarded: IsOnboarded;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setEmail: (email: string) => void;
  setUserStatus: (status: UserStatus) => void;
  setUserType: (type: string | null) => void;
  setIsOnboarded: (status: IsOnboarded) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

export const useUserStatusStore = create<UserStatusState>((set) => ({
  email: '',
  userStatus: null,
  userType: null,
  isOnboarded: null,
  isLoading: false,
  error: null,
  
  setEmail: (email) => set({ email }),
  setUserStatus: (status) => set({ userStatus: status }),
  setUserType: (type) => set({ userType: type }),
  setIsOnboarded: (status) => set({ isOnboarded: status }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  reset: () => set({
    email: '',
    userStatus: null,
    userType: null,
    isOnboarded: null,
    isLoading: false,
    error: null
  })
}));
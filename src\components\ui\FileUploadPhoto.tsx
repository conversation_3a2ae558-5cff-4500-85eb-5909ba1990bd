import React, { useState } from "react";
import FileUpload, { FileUploadProps } from "./FileUpload";
import PhotoCropModal from "./PhotoCropModal";

type FileUploadPhotoProps = Omit<
  FileUploadProps,
  "accept" | "maxSize" | "buttonText"
> & {
  buttonText?: string;
  enableCropAndRotate?: boolean;
  onCroppedFileSelect?: (file: File) => void;
};

const FileUploadPhoto: React.FC<FileUploadPhotoProps> = ({
  buttonText = "Upload Photo",
  enableCropAndRotate = false,
  onCroppedFileSelect,
  onFileSelect,
  ...props
}) => {
  const [showCropModal, setShowCropModal] = useState(false);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);

  const handleFileSelect = (file: File) => {
    if (enableCropAndRotate && !file.error) {
      // Show crop modal for valid image files - don't call onFileSelect yet
      setSelectedImageFile(file);
      setShowCropModal(true);
    } else {
      // Use original behavior for non-crop mode or error files
      onFileSelect(file);
    }
  };

  const handleCroppedFile = (croppedFile: File) => {
    setShowCropModal(false);
    setSelectedImageFile(null);

    if (onCroppedFileSelect) {
      onCroppedFileSelect(croppedFile);
    } else {
      onFileSelect(croppedFile);
    }
  };

  const handleCropCancel = () => {
    setShowCropModal(false);
    setSelectedImageFile(null);
  };

  return (
    <>
      <FileUpload
        accept=".jpg,.jpeg,.png,image/jpeg,image/png"
        maxSize={5 * 1024 * 1024} // 5MB
        buttonText={buttonText}
        dragDropText="Drag and drop your photo here, or click to select"
        onFileSelect={handleFileSelect}
        {...props}
      />

      {enableCropAndRotate && (
        <PhotoCropModal
          isOpen={showCropModal}
          onClose={handleCropCancel}
          imageFile={selectedImageFile}
          onCroppedFile={handleCroppedFile}
        />
      )}
    </>
  );
};

export default FileUploadPhoto;

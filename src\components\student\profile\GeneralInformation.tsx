import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Edit, Plus } from "lucide-react";

interface GeneralInformationProps {
  profileData: {
    skills: string[];
    jobPreference: string;
  };
}

const GeneralInformation: React.FC<GeneralInformationProps> = ({ profileData }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl">General information</CardTitle>
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">Skills:</h3>
            <div className="flex flex-wrap gap-2">
              {profileData.skills.map((skill, index) => (
                <Badge key={index} variant="secondary" className="bg-gray-100 hover:bg-gray-200 text-gray-800">
                  {skill}
                </Badge>
              ))}
              <Badge variant="outline" className="border-dashed cursor-pointer">
                <Plus className="h-3 w-3 mr-1" />
                Add skill
              </Badge>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-2">Job preference:</h3>
            <p className="text-gray-700">{profileData.jobPreference}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GeneralInformation;

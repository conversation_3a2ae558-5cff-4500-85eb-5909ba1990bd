import React, { useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import LoadingSpinner from "@/components/LoadingSpinner";
import EditSectionModal, { FormInput } from "@/components/student/profile/EditSectionModal";
import { Button } from "@/components/ui/Button";
import FileUploadPhoto from "@/components/ui/FileUploadPhoto";
import { X } from "lucide-react";
import { create } from "zustand";
import { useProfileStore } from "@/store/profileStore";
import { generateProfilePicturePath, getBucketForUserType, generateUniqueFileName } from "@/utils/storageUtils";

// Import section components
import ProfileCardSection from "@/components/student/profile/ProfileCardSection";
import ProfileCompletenessSection from "@/components/student/profile/ProfileCompletenessSection";
import GeneralInformationSection from "@/components/student/profile/GeneralInformationSection";
import EducationSection from "@/components/student/profile/EducationSection";
import AchievementsSection from "@/components/student/profile/AchievementsSection";
import PhotosSection from "@/components/student/profile/PhotosSection";
import HobbiesInterestsSection from "@/components/student/profile/HobbiesInterestsSection";
import StudyPreferencesSection from "@/components/student/profile/StudyPreferencesSection";

// Define the student profile data interface with onboarding fields
export interface StudentExtendedProfileData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  userType: string;
  createdAt: Date | null;
  updatedAt: Date | null;
  profilePictureUrl: string | null;

  // Onboarding fields
  education_level: string; // grade
  subjects_of_interest: string[];
  learning_goals: string[];
  // Fields filled post onboarding
  study_preferences: {
    preferred_time: string;
    preferred_days: string[];
    learning_style: string;
    communication_preference: string;
  };
  academic_history: {
    school_name: string;
    achievements: string[];
    favorite_subjects: string[];
  };

  // Additional fields
  hobbies: string[];
  interests: string[];
  location: string;
}

// Define section types for edit modals
type SectionType = 'general' | 'education' | 'achievements' | 'photos' | 'hobbies' | 'study';

// Create a store for the extended profile
interface StudentExtendedProfileStore {
  profileData: StudentExtendedProfileData;
  editData: Partial<StudentExtendedProfileData>;
  pendingChanges: Partial<StudentExtendedProfileData>;
  hasPendingChanges: boolean;
  isLoading: boolean;
  error: string | null;
  isEditing: boolean;
  photoUploadOpen: boolean;
  uploadType: 'profile' | 'gallery';
  selectedFile: File | null;
  uploadStatus: 'idle' | 'uploading' | 'success' | 'error';
  activeSectionEdit: SectionType | null;
  modifiedSections: Set<SectionType>;

  // Actions
  fetchProfile: (userId: string, authProfileData?: any) => Promise<void>;
  updateProfile: (data: Partial<StudentExtendedProfileData>) => Promise<void>;
  setIsEditing: (isEditing: boolean) => void;
  setEditData: (data: Partial<StudentExtendedProfileData>) => void;
  updateEditData: (field: string, value: any) => void;
  updateNestedEditData: (parent: string, field: string, value: any) => void;
  resetEditData: () => void;
  setPhotoUploadOpen: (isOpen: boolean) => void;
  setUploadType: (type: 'profile' | 'gallery') => void;
  setSelectedFile: (file: File | null) => void;
  setUploadStatus: (status: 'idle' | 'uploading' | 'success' | 'error') => void;
  handleOpenPhotoUpload: (type: 'profile' | 'gallery') => void;
  setActiveSectionEdit: (section: SectionType | null) => void;

  // New actions for pending changes
  addPendingChanges: (section: SectionType, changes: Partial<StudentExtendedProfileData>) => void;
  saveAllChanges: () => Promise<void>;
  discardAllChanges: () => void;
  getDisplayData: () => StudentExtendedProfileData;
  isSectionModified: (section: SectionType) => boolean;
}

// Create the store
const useStudentExtendedProfileStore = create<StudentExtendedProfileStore>((set) => ({
  profileData: {
    id: "",
    firstName: "",
    lastName: "",
    email: "",
    userType: "student",
    createdAt: null,
    updatedAt: null,
    profilePictureUrl: null,

    education_level: "", //grade
    subjects_of_interest: [],
    learning_goals: [],
    study_preferences: {
      preferred_time: "",
      preferred_days: [],
      learning_style: "visual",
      communication_preference: "video"
    },
    academic_history: {
      school_name: "",
      achievements: [],
      favorite_subjects: []
    },

    hobbies: [],
    interests: [],
    location: ""
  },
  editData: {},
  pendingChanges: {},
  hasPendingChanges: false,
  modifiedSections: new Set<SectionType>(),
  isLoading: true,
  error: null,
  isEditing: false,
  photoUploadOpen: false,
  uploadType: 'profile',
  selectedFile: null,
  uploadStatus: 'idle',
  activeSectionEdit: null,

  fetchProfile: async (userId, authProfileData = null) => {
    set({ isLoading: true, error: null });

    try {
      // Define empty student data defaults
      const emptyStudentData = {
        education_level: "",
        subjects_of_interest: [],
        learning_goals: [],
        study_preferences: {
          preferred_time: "",
          preferred_days: [],
          learning_style: "",
          communication_preference: ""
        },
        academic_history: {
          school_name: "",
          grade: "",
          achievements: [],
          favorite_subjects: []
        },
        hobbies: [],
        interests: [],
        location: ""
      };

      // If we don't have authProfileData, we can't proceed
      if (!authProfileData) {
        console.log("No profile data available, using empty defaults");
        set({
          profileData: {
            id: userId,
            firstName: "",
            lastName: "",
            email: "",
            userType: "student",
            profilePictureUrl: null,
            createdAt: null,
            updatedAt: null,
            ...emptyStudentData
          },
          isLoading: false,
          // Don't automatically open edit mode - let user choose when to edit
          isEditing: false
        });
        return;
      }

      // Extract all profile data from passed authProfileData
      // This data already includes student-specific fields from AuthContext
      const profileData = {
        id: userId,
        firstName: authProfileData.firstName || "",
        lastName: authProfileData.lastName || "",
        email: authProfileData.email || "",
        userType: "student",
        profilePictureUrl: authProfileData.profilePictureUrl || null,
        createdAt: authProfileData.createdAt || null,
        updatedAt: authProfileData.updatedAt || null,
        // Student-specific fields - use camelCase from AuthContext
        education_level: authProfileData.educationLevel || "",
        subjects_of_interest: authProfileData.subjectsOfInterest || [],
        learning_goals: authProfileData.learningGoals || [],
        study_preferences: authProfileData.studyPreferences || {
          preferred_time: "",
          preferred_days: [],
          learning_style: "",
          communication_preference: ""
        },
        academic_history: authProfileData.academicHistory || {
          school_name: "",
          grade: "",
          achievements: [],
          favorite_subjects: []
        },
        hobbies: authProfileData.hobbies || [],
        interests: authProfileData.interests || [],
        location: authProfileData.location || ""
      };

      // Set the profile data in the store
      set({
        profileData,
        isLoading: false,
        // Don't automatically open edit mode - let user choose when to edit
        isEditing: false
      });
    } catch (error) {
      console.error("Error processing profile data:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load profile",
        isLoading: false
      });
    }
  },

  updateProfile: async (data) => {
    set({ isLoading: true, error: null });

    try {
      // Import supabase client dynamically to avoid circular dependencies
      const { supabase } = await import("@/lib/supabaseClient");

      // Update the profiles table if profile picture URL is provided
      if (data.profilePictureUrl !== undefined) {
        const { error: profileError } = await supabase
          .from("profiles")
          .update({
            profile_picture_url: data.profilePictureUrl,
            updated_at: new Date().toISOString()
          })
          .eq("id", data.id);

        if (profileError) {
          throw profileError;
        }
      }

      // Extract student-specific data to save to the students table
      const studentData = {
        education_level: data.education_level,
        subjects_of_interest: data.subjects_of_interest,
        learning_goals: data.learning_goals,
        study_preferences: data.study_preferences,
        academic_history: data.academic_history,
        hobbies: data.hobbies,
        interests: data.interests,
        location: data.location
      };

      // Save the student data to Supabase (only if there's student-specific data)
      const hasStudentData = Object.values(studentData).some(value =>
        value !== undefined && value !== null &&
        (Array.isArray(value) ? value.length > 0 : value !== "")
      );

      if (hasStudentData) {
        const { error: updateError } = await supabase
          .from("students")
          .upsert({
            id: data.id,
            ...studentData
          });

        if (updateError) {
          throw updateError;
        }
      }

      // Also update the global profile store to keep data in sync
      const profileStoreUpdate: any = {
        education_level: data.education_level,
        subjects_of_interest: data.subjects_of_interest,
        learning_goals: data.learning_goals,
        study_preferences: data.study_preferences,
        academic_history: data.academic_history,
        hobbies: data.hobbies,
        interests: data.interests,
        location: data.location
      };

      // Include profile picture URL if it was updated
      if (data.profilePictureUrl !== undefined) {
        profileStoreUpdate.profilePictureUrl = data.profilePictureUrl;
      }

      useProfileStore.getState().setProfileData(profileStoreUpdate);

      // Update the profile data in the local store
      set(state => ({
        profileData: { ...state.profileData, ...data },
        isLoading: false,
        isEditing: false
      }));
    } catch (error) {
      console.error("Error updating student profile:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to update profile",
        isLoading: false
      });
    }
  },

  setIsEditing: (isEditing) => set({ isEditing }),

  setEditData: (data) => set({ editData: data }),

  updateEditData: (field, value) => set(state => ({
    editData: { ...state.editData, [field]: value }
  })),

  updateNestedEditData: (parent, field, value) => set(state => {
    // Create a deep copy of the nested object
    const parentObj = state.editData[parent as keyof typeof state.editData];
    const updatedParentObj = parentObj ? { ...parentObj as object } : {};

    // Set the new value
    return {
      editData: {
        ...state.editData,
        [parent]: {
          ...updatedParentObj,
          [field]: value
        }
      }
    };
  }),

  resetEditData: () => set({ editData: {} }),

  setPhotoUploadOpen: (isOpen) => set({ photoUploadOpen: isOpen }),

  setUploadType: (type) => set({ uploadType: type }),

  setSelectedFile: (file) => set({ selectedFile: file }),

  setUploadStatus: (status) => set({ uploadStatus: status }),

  handleOpenPhotoUpload: (type) => {
    set({
      uploadType: type,
      photoUploadOpen: true,
      selectedFile: null,
      uploadStatus: 'idle'
    });
  },

  setActiveSectionEdit: (section) => set({ activeSectionEdit: section }),

  // New methods for pending changes
  addPendingChanges: (section, changes) => set(state => {
    // Use a type assertion to handle the complex nested structure
    const newPendingChanges = { ...state.pendingChanges } as any;

    // Deep merge the changes into pending changes
    const deepMerge = (target: any, source: any) => {
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          // If the key doesn't exist in target or is not an object, create it
          if (!target[key] || typeof target[key] !== 'object') {
            target[key] = {};
          }
          deepMerge(target[key], source[key]);
        } else {
          // For primitives and arrays, just replace the value
          target[key] = source[key];
        }
      });
      return target;
    };

    // Apply deep merge
    deepMerge(newPendingChanges, changes);

    // Add the section to the modified sections set
    const newModifiedSections = new Set(state.modifiedSections);
    newModifiedSections.add(section);

    return {
      pendingChanges: newPendingChanges as Partial<StudentExtendedProfileData>,
      hasPendingChanges: true,
      modifiedSections: newModifiedSections
    };
  }),

  saveAllChanges: async () => {
    const state = useStudentExtendedProfileStore.getState();

    if (!state.hasPendingChanges) return;

    try {
      // Combine profile data with pending changes
      const updatedData = {
        ...state.profileData,
        ...state.pendingChanges
      };

      // Call the existing updateProfile method to save to the database
      await state.updateProfile(updatedData);

      // After successful save, update the profileData with the saved changes
      // and clear pending changes
      set({
        profileData: updatedData,
        pendingChanges: {},
        hasPendingChanges: false,
        modifiedSections: new Set<SectionType>()
      });
    } catch (error) {
      // If save fails, don't clear pending changes
      console.error('Error saving changes:', error);
      throw error;
    }
  },

  discardAllChanges: () => set({
    pendingChanges: {},
    hasPendingChanges: false,
    modifiedSections: new Set<SectionType>()
  }),

  getDisplayData: () => {
    const state = useStudentExtendedProfileStore.getState();
    // Combine profile data with pending changes to get the current display data
    return {
      ...state.profileData,
      ...state.pendingChanges
    } as StudentExtendedProfileData;
  },

  isSectionModified: (section) => {
    const state = useStudentExtendedProfileStore.getState();
    return state.modifiedSections.has(section);
  }
}));

const StudentProfile: React.FC = () => {
  const { user, profileData: authProfileData, refreshUserData } = useAuth();
  const { isLoading, error } = useProfileStore();

  // Use the student-specific fields directly from the AuthContext's profileData
  const {
    profileData: storeProfileData,
    pendingChanges,
    isLoading: isSaving,
    setIsEditing,
    editData,
    isEditing,
    setEditData,
    updateEditData,
    resetEditData,
    updateProfile,
    fetchProfile,
    photoUploadOpen,
    uploadType,
    selectedFile,
    uploadStatus,
    setPhotoUploadOpen,
    setSelectedFile,
    setUploadStatus,
    handleOpenPhotoUpload,
    activeSectionEdit,
    setActiveSectionEdit,
    // New methods for pending changes
    addPendingChanges,
    saveAllChanges,
    discardAllChanges,
    getDisplayData,
    isSectionModified,
    hasPendingChanges,
    modifiedSections
  } = useStudentExtendedProfileStore();

  // Initialize profile data from AuthContext when component mounts or user changes
  useEffect(() => {
    if (user?.id && authProfileData) {
      console.log('Initializing Student Profile with AuthContext data:', authProfileData);
      fetchProfile(user.id, authProfileData);
    }
  }, [user?.id, authProfileData, fetchProfile]);

  // Get the display data (combination of profile data and pending changes)
  const profileData = getDisplayData();

  // Debug: Log the profile data being used by components
  console.log('Student Profile - profileData being used by components:', profileData);
  console.log('Student Profile - AuthContext profilePictureUrl:', authProfileData.profilePictureUrl);
  console.log('Student Profile - Local store profilePictureUrl:', profileData.profilePictureUrl);

  // Handle file selection (for initial file selection, before cropping)
  const handleFileSelect = (file: File) => {
    // Don't set selectedFile yet - this just opens the crop modal
    console.log('Initial file selected for cropping:', file.name);
  };

  // Handle cropped file selection (this is the final processed file, ready for upload)
  const handleCroppedFileSelect = (file: File) => {
    // Set the cropped file - this makes it ready for upload when user clicks OK
    setSelectedFile(file);
    console.log('Cropped file ready for upload:', file.name);
  };

  // Handle file removal
  const handleFileRemove = () => {
    setSelectedFile(null);
  };

  // Handle photo upload (triggered by OK button after cropping)
  const handleUploadPhoto = async () => {
    if (!selectedFile) {
      console.log('No file selected for upload');
      return;
    }

    console.log('Starting upload process for:', selectedFile.name);

    try {
      setUploadStatus('uploading');

      // Import supabase client dynamically to avoid circular dependencies
      const { supabase } = await import("@/lib/supabaseClient");

      // Generate a unique file name
      const fileName = generateUniqueFileName(selectedFile.name);

      // Create the file path based on upload type and user type
      const filePath = uploadType === 'profile'
        ? generateProfilePicturePath('student', user?.id, fileName)
        : `gallery/${user?.id}/${fileName}`;

      // Get the appropriate bucket for the user type
      const bucket = getBucketForUserType('student');

      // Upload file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, selectedFile);

      if (uploadError) {
        throw uploadError;
      }

      // Get the public URL
      const { data } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      // Update profile with new photo URL if it's a profile photo
      if (uploadType === 'profile') {
        // Store the old profile picture URL before updating the profile
        const oldProfilePictureUrl = profileData.profilePictureUrl;

        // Update the profile with the new URL
        await updateProfile({
          ...profileData,
          profilePictureUrl: data.publicUrl
        });

        // Refresh the AuthContext to sync the new profile picture URL
        console.log('Refreshing AuthContext after profile picture upload...');
        await refreshUserData();
        console.log('AuthContext refreshed successfully');

        // After successful upload and profile update, delete the old profile picture
        if (oldProfilePictureUrl) {
          try {
            // Extract the path from the old URL using utility function
            const { parseStorageUrl } = await import("@/utils/storageUtils");
            const parsedUrl = parseStorageUrl(oldProfilePictureUrl);

            if (parsedUrl) {
              // Delete the old file
              const { error: deleteError } = await supabase.storage
                .from(parsedUrl.bucket)
                .remove([parsedUrl.filePath]);

              if (deleteError) {
                console.warn('Error deleting old profile picture:', deleteError);
                // Continue even if delete fails as the new upload was successful
              }
            }
          } catch (deleteError) {
            console.warn('Error processing old profile picture URL:', deleteError);
            // Continue even if URL parsing or delete fails as the new upload was successful
          }
        }
      }

      setUploadStatus('success');

      // Close the modal after a short delay
      setTimeout(() => {
        setPhotoUploadOpen(false);
      }, 1500);

    } catch (error) {
      console.error('Error uploading photo:', error);
      setUploadStatus('error');

      // Show user-friendly error message
      if (error?.message?.includes('Unauthorized') || error?.message?.includes('row-level security')) {
        console.error('Storage permission error. Please run the storage setup SQL script.');
        alert('Upload failed: Storage permissions not configured. Please contact support or check the setup_storage_buckets.sql file.');
      } else {
        alert(`Upload failed: ${error?.message || 'Unknown error occurred'}`);
      }
    }
  };

  // Initialize edit data when entering edit mode
  useEffect(() => {
    if (isEditing) {
      setEditData({ ...storeProfileData, ...pendingChanges });
    }
  }, [isEditing, storeProfileData, pendingChanges, setEditData]);

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    updateEditData(field, value);
  };

  // Handle save
  const handleSave = () => {
    updateProfile(editData);
  };

  // Handle cancel
  const handleCancel = () => {
    setIsEditing(false);
    resetEditData();
  };

  // Map education level to display text
  const getEducationLevelText = (level: string) => {
    const levels: Record<string, string> = {
      primary: "Primary School",
      secondary: "Secondary School",
      high_school: "High School",
      undergraduate: "Undergraduate",
      postgraduate: "Postgraduate",
      professional: "Professional"
    };
    return levels[level] || level;
  };

  // Map learning goals to display text
  const getLearningGoalText = (goal: string) => {
    const goals: Record<string, string> = {
      improve_grades: "Improve my grades",
      exam_prep: "Prepare for exams",
      learn_new_skills: "Learn new skills",
      career_advancement: "Advance my career",
      personal_interest: "Personal interest",
      homework_help: "Get help with homework"
    };
    return goals[goal] || goal;
  };

  if (isLoading) {
    return (
      <StudentPageLayout
        title="My Profile"
        description="View and manage your profile information"
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading your profile...</p>
        </div>
      </StudentPageLayout>
    );
  }

  // Create a simple retry function that just reloads the page
  const handleRetry = () => {
    // Simply reload the page to get a fresh state
    window.location.reload();
  };

  if (error) {
    return (
      <StudentPageLayout
        title="My Profile"
        description="View and manage your profile information"
      >
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <p className="text-red-600 font-semibold">Error: {error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={handleRetry}
          >
            Try Again
          </Button>
        </div>
      </StudentPageLayout>
    );
  }

  // Handle save all changes
  const handleSaveAllChanges = async () => {
    try {
      await saveAllChanges();
      // Success feedback could be added here if needed
      console.log('All changes saved successfully');
    } catch (error) {
      // Error handling - the error is already logged in saveAllChanges
      console.error('Failed to save changes:', error);
      // The error state is already set in the updateProfile method
    }
  };

  // Handle discard all changes
  const handleDiscardAllChanges = () => {
    discardAllChanges();
  };

  return (
    <StudentPageLayout
      title="My Profile"
      description="View and manage your profile information"
    >
      {/* Two-column layout */}
      <div className="flex flex-col md:flex-row gap-6 relative">
        {/* Left sidebar */}
        <div className="md:w-1/3 space-y-6">
          {/* Profile Photo and Basic Info */}
          <ProfileCardSection
            profileData={profileData}
            handleOpenPhotoUpload={handleOpenPhotoUpload}
            setIsEditing={setIsEditing}
          />

          {/* Profile Completeness */}
          <ProfileCompletenessSection profileData={profileData} />
        </div>

        {/* Main content area */}
        <div className="md:w-2/3 space-y-6">
          {/* General Information Section */}
          <GeneralInformationSection
            profileData={profileData}
            editData={editData}
            activeSectionEdit={activeSectionEdit}
            setActiveSectionEdit={setActiveSectionEdit}
            updateEditData={updateEditData}
            updateProfile={(data) => addPendingChanges('general', data)}
            getLearningGoalText={getLearningGoalText}
            isModified={isSectionModified('general')}
          />

          {/* Education Section */}
          <EducationSection
            profileData={profileData}
            editData={editData}
            activeSectionEdit={activeSectionEdit}
            setActiveSectionEdit={setActiveSectionEdit}
            updateEditData={updateEditData}
            updateProfile={(data) => addPendingChanges('education', data)}
            getEducationLevelText={getEducationLevelText}
          />

          {/* Achievements Section */}
          <AchievementsSection
            profileData={profileData}
            editData={editData}
            activeSectionEdit={activeSectionEdit}
            setActiveSectionEdit={setActiveSectionEdit}
            updateEditData={updateEditData}
            updateProfile={(data) => addPendingChanges('achievements', data)}
          />

          {/* Photos & Video Section */}
          <PhotosSection
            profileData={profileData}
            activeSectionEdit={activeSectionEdit}
            setActiveSectionEdit={setActiveSectionEdit}
            handleOpenPhotoUpload={handleOpenPhotoUpload}
          />

          {/* Hobbies & Interests Section */}
          <HobbiesInterestsSection
            profileData={profileData}
            editData={editData}
            activeSectionEdit={activeSectionEdit}
            setActiveSectionEdit={setActiveSectionEdit}
            updateEditData={updateEditData}
            updateProfile={(data) => addPendingChanges('hobbies', data)}
          />

          {/* Study Preferences Section */}
          <StudyPreferencesSection
            profileData={profileData}
            editData={editData}
            activeSectionEdit={activeSectionEdit}
            setActiveSectionEdit={setActiveSectionEdit}
            updateEditData={updateEditData}
            updateProfile={(data) => addPendingChanges('study', data)}
          />
        </div>

        {/* Floating Save Changes Button */}
        {hasPendingChanges && (
          <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-2">
            <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
              <div className="text-sm font-medium mb-2">
                {modifiedSections.size} section{modifiedSections.size !== 1 ? 's' : ''} modified
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDiscardAllChanges}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  Discard
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveAllChanges}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    'Save All Changes'
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Edit Profile Modal */}
      <EditSectionModal
        isOpen={isEditing}
        onClose={handleCancel}
        title="Edit Profile"
        onSubmit={handleSave}
        layout="default"
        footerContent={
          <div className="flex flex-col-reverse sm:flex-row w-full gap-2">
            <Button variant="outline" onClick={handleCancel} className="w-full sm:w-auto">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} className="w-full sm:w-auto">
              OK
            </Button>
          </div>
        }
      >
        <FormInput
          label="First Name"
          id="firstName"
          value={editData.firstName || ""}
          onChange={(e) => handleInputChange("firstName", e.target.value)}
          helpText="Your first name as it will appear on your profile"
        />

        <FormInput
          label="Last Name"
          id="lastName"
          value={editData.lastName || ""}
          onChange={(e) => handleInputChange("lastName", e.target.value)}
          helpText="Your last name as it will appear on your profile"
        />

        <FormInput
          label="Location"
          id="location"
          value={editData.location || ""}
          onChange={(e) => handleInputChange("location", e.target.value)}
          placeholder="City, Country"
          helpText="Your current city and country of residence"
        />

        {/* More fields can be added here */}
      </EditSectionModal>

      {/* Photo Upload Modal */}
      <EditSectionModal
        isOpen={photoUploadOpen}
        onClose={() => setPhotoUploadOpen(false)}
        title={uploadType === 'profile' ? 'Upload Profile Photo' : 'Add Photo to Gallery'}
        onSubmit={handleUploadPhoto}
        maxWidth="sm:max-w-md"
        isSaveDisabled={!selectedFile || uploadStatus === 'uploading'}
        saveButtonText={uploadStatus === 'uploading' ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Uploading...
          </>
        ) : 'OK'}
        cancelButtonText="Cancel"
      >
        <FileUploadPhoto
          onFileSelect={handleFileSelect}
          onFileRemove={handleFileRemove}
          buttonText={uploadType === 'profile' ? 'Select Profile Photo' : 'Select Photo'}
          status={uploadStatus}
          fileName={selectedFile?.name} // Only shows filename after cropping is complete
          enableCropAndRotate={true}
          onCroppedFileSelect={handleCroppedFileSelect}
        />
      </EditSectionModal>
    </StudentPageLayout>
  );
};

export default StudentProfile;

import { create } from 'zustand';

// Types for sessions
export interface Session {
  id: string;
  batchId: string;
  topicId: string;
  subtopicId?: string;
  tutorId: string;
  studentId: string;
  scheduledAt: string; // ISO date string
  durationMin: number;
  status: 'scheduled' | 'completed' | 'cancelled';
  mode: 'video' | 'audio' | 'quiz' | 'hybrid';
  createdBy: 'admin' | 'tutor' | 'student';
  createdAt: string;
}

// Interface for the store state
interface SessionState {
  // State
  sessions: Session[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  createSession: (sessionData: Omit<Session, 'id' | 'status' | 'createdAt'>) => Promise<Session>;
  updateSessionStatus: (sessionId: string, status: Session['status']) => Promise<void>;
  fetchSessions: (filters?: { tutorId?: string; studentId?: string; status?: Session['status']; startDate?: string; endDate?: string }) => Promise<void>;
  getSessionsForDateRange: (startDate: string, endDate: string, tutorId?: string, studentId?: string) => Session[];
  checkAvailability: (date: string, startTime: string, endTime: string, tutorId?: string, studentId?: string) => Promise<{ isAvailable: boolean; conflictingSession?: Session }>;
}

// Sample data for development
const sampleSessions: Session[] = [
  {
    id: "session-001",
    batchId: "batch-001",
    topicId: "topic-001",
    subtopicId: "subtopic-001",
    tutorId: "tutor-001",
    studentId: "student-001",
    scheduledAt: "2023-07-15T14:00:00Z",
    durationMin: 60,
    status: "scheduled",
    mode: "video",
    createdBy: "student",
    createdAt: "2023-07-10T10:30:00Z",
  },
  {
    id: "session-002",
    batchId: "batch-001",
    topicId: "topic-002",
    subtopicId: "subtopic-002",
    tutorId: "tutor-001",
    studentId: "student-002",
    scheduledAt: "2023-07-16T10:00:00Z",
    durationMin: 45,
    status: "scheduled",
    mode: "video",
    createdBy: "tutor",
    createdAt: "2023-07-11T09:15:00Z",
  },
];

// Create the store
export const useSessionStore = create<SessionState>((set, get) => ({
  // Initial state
  sessions: sampleSessions,
  isLoading: false,
  error: null,

  // Actions
  createSession: async (sessionData) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create new session with generated ID
      const newSession: Session = {
        ...sessionData,
        id: `session-${Date.now()}`,
        status: 'scheduled',
        createdAt: new Date().toISOString(),
      };
      
      // Update state with new session
      set(state => ({
        sessions: [...state.sessions, newSession],
        isLoading: false,
      }));
      
      return newSession;
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  updateSessionStatus: async (sessionId, status) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update session status
      set(state => ({
        sessions: state.sessions.map(session => 
          session.id === sessionId 
            ? { ...session, status } 
            : session
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  fetchSessions: async (filters) => {
    set({ isLoading: true, error: null });
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Filter sessions based on provided filters
      let filteredSessions = [...sampleSessions];
      
      if (filters) {
        if (filters.tutorId) {
          filteredSessions = filteredSessions.filter(session => session.tutorId === filters.tutorId);
        }
        if (filters.studentId) {
          filteredSessions = filteredSessions.filter(session => session.studentId === filters.studentId);
        }
        if (filters.status) {
          filteredSessions = filteredSessions.filter(session => session.status === filters.status);
        }
        if (filters.startDate && filters.endDate) {
          filteredSessions = filteredSessions.filter(session => {
            const sessionDate = new Date(session.scheduledAt);
            const startDate = new Date(filters.startDate!);
            const endDate = new Date(filters.endDate!);
            return sessionDate >= startDate && sessionDate <= endDate;
          });
        }
      }
      
      set({ sessions: filteredSessions, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An unknown error occurred' 
      });
      throw error;
    }
  },
  
  getSessionsForDateRange: (startDate, endDate, tutorId, studentId) => {
    const sessions = get().sessions;
    
    return sessions.filter(session => {
      const sessionDate = new Date(session.scheduledAt);
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      const dateInRange = sessionDate >= start && sessionDate <= end;
      const matchesTutor = tutorId ? session.tutorId === tutorId : true;
      const matchesStudent = studentId ? session.studentId === studentId : true;
      
      return dateInRange && matchesTutor && matchesStudent;
    });
  },
  
  checkAvailability: async (date, startTime, endTime, tutorId, studentId) => {
    // Convert date and times to a comparable format
    const startDateTime = new Date(`${date}T${startTime}`);
    const endDateTime = new Date(`${date}T${endTime}`);
    
    // Get sessions for the specified date
    const sessions = get().sessions.filter(session => {
      const sessionDate = new Date(session.scheduledAt).toISOString().split('T')[0];
      return sessionDate === date;
    });
    
    // Filter by tutor or student if provided
    const filteredSessions = sessions.filter(session => {
      const matchesTutor = tutorId ? session.tutorId === tutorId : true;
      const matchesStudent = studentId ? session.studentId === studentId : true;
      return matchesTutor && matchesStudent;
    });
    
    // Check for conflicts
    const conflictingSession = filteredSessions.find(session => {
      const sessionStart = new Date(session.scheduledAt);
      const sessionEnd = new Date(sessionStart.getTime() + session.durationMin * 60000);
      
      // Check if there's an overlap
      return (
        (startDateTime < sessionEnd && endDateTime > sessionStart) ||
        (sessionStart < endDateTime && sessionEnd > startDateTime)
      );
    });
    
    if (conflictingSession) {
      return {
        isAvailable: false,
        conflictingSession
      };
    }
    
    return { isAvailable: true };
  }
}));

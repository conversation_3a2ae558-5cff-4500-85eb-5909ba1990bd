import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import AdminSidebar from "@/components/admin/Sidebar";
import UserProfileMenu from "@/components/UserProfileMenu";
import { useProfileData } from "@/hooks/useProfileData";
import { Users, Search, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";

const AddUsersToSession = () => {
  const navigate = useNavigate();
  const profileData = useProfileData();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUsers, setSelectedUsers] = useState([]);

  // Mock session data
  const session = {
    id: "123",
    subject: "Mathematics",
    date: "2023-12-15",
    time: "14:00",
    tutor: "<PERSON>",
  };

  // Mock users data
  const users = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Student",
    },
    {
      id: "2",
      name: "Bob Williams",
      email: "<EMAIL>",
      role: "Student",
    },
    {
      id: "3",
      name: "Carol <PERSON>",
      email: "<EMAIL>",
      role: "Student",
    },
    {
      id: "4",
      name: "Dave <PERSON>",
      email: "<EMAIL>",
      role: "Student",
    },
  ];

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleUserSelection = (userId) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  const handleSubmit = () => {
    console.log("Adding users to session:", selectedUsers);
    navigate("/admin/sessions");
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Add Users to Session</h1>
            <UserProfileMenu
              isAdmin={true}
              isAdminPage={true}
            />
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Session Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Subject</p>
                  <p>{session.subject}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Tutor</p>
                  <p>{session.tutor}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Date</p>
                  <p>{session.date}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Time</p>
                  <p>{session.time}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5 text-rfpurple-600" />
                Select Users to Add
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative mb-4">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  size={18}
                />
                <Input
                  placeholder="Search users by name or email"
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">Select</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => toggleUserSelection(user.id)}
                          className="h-4 w-4 text-rfpurple-600 focus:ring-rfpurple-500 border-gray-300 rounded"
                        />
                      </TableCell>
                      <TableCell>{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.role}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => navigate("/admin/sessions")}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                className="button-gradient"
                disabled={selectedUsers.length === 0}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Selected Users
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AddUsersToSession;

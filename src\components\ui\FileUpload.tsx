import React, { useRef, useState } from "react";
import { Upload, Loader2, CheckCircle2, AlertCircle, X } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove?: () => void;
  accept?: string;
  maxSize?: number; // in bytes
  status?: "idle" | "uploading" | "success" | "error";
  errorMessage?: string;
  fileName?: string;
  buttonText?: string;
  dragDropText?: string;
  successText?: string;
  uploadingText?: string;
  className?: string;
  buttonClassName?: string;
  dropzoneClassName?: string;
  required?: boolean;
}

const handleFileRemove = (e: React.MouseEvent, onFileRemove?: () => void) => {
  e.preventDefault();
  e.stopPropagation();
  if (onFileRemove) {
    onFileRemove();
  }
};

const FileUpload = React.forwardRef<HTMLDivElement, FileUploadProps>(({
  onFileSelect,
  onFileRemove,
  accept = "*/*",
  maxSize = 5 * 1024 * 1024, // 5MB default
  status = "idle",
  errorMessage = "Upload failed",
  fileName,
  buttonText = "Upload File",
  dragDropText = "Drag and drop your file here, or click to select",
  successText = "Successfully Uploaded",
  uploadingText = "Uploading...",
  className,
  buttonClassName,
  dropzoneClassName,
  required = false,
}, ref) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);

  const handleFileChange = (file: File | null) => {
    if (!file) return;

    // Check file size
    if (file.size > maxSize) {
      console.error(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
      // Create an error file
      const errorFile = new File([], file.name, { type: file.type });
      Object.defineProperty(errorFile, 'error', {
        value: true,
        writable: false
      });
      Object.defineProperty(errorFile, 'errorMessage', {
        value: `File size exceeds ${maxSize / (1024 * 1024)}MB limit`,
        writable: false
      });
      onFileSelect(errorFile);
      return;
    }

    // Check file type if accept is specified
    if (accept !== "*/*") {
      const acceptedTypes = accept.split(",").map(type => type.trim());
      const fileType = file.type;
      const fileExtension = `.${file.name.split(".").pop()?.toLowerCase()}`;

      const isAccepted = acceptedTypes.some(type => {
        if (type.startsWith(".")) {
          // Extension check
          return fileExtension === type.toLowerCase();
        } else if (type.includes("*")) {
          // Wildcard MIME type check (e.g., "image/*")
          return fileType.startsWith(type.split("*")[0]);
        } else {
          // Exact MIME type check
          return fileType === type;
        }
      });

      if (!isAccepted) {
        console.error(`File type not supported. Accepted formats: ${accept}`);
        const errorFile = new File([], file.name, { type: file.type });
        Object.defineProperty(errorFile, 'error', {
          value: true,
          writable: false
        });
        Object.defineProperty(errorFile, 'errorMessage', {
          value: `File type not supported. Accepted formats: PDF, DOC, DOCX`,
          writable: false
        });
        onFileSelect(errorFile);
        return;
      }
    }

    onFileSelect(file);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      try {
        handleFileChange(e.dataTransfer.files[0]);
      } catch (error) {
        console.error("Error handling dropped file:", error);
      }
    }
  };

  const renderButton = () => {
    switch (status) {
      case "uploading":
        return (
          <Button
            type="button"
            className={cn("w-full", buttonClassName)}
            disabled
          >
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {uploadingText}
          </Button>
        );
      case "success":
        return (
          <Button
            type="button"
            className={cn("w-full bg-green-600 hover:bg-green-700", buttonClassName)}
            onClick={() => fileInputRef.current?.click()}
          >
            <CheckCircle2 className="mr-2 h-4 w-4" />
            {successText}
          </Button>
        );
      case "error":
        return (
          <Button
            type="button"
            className={cn("w-full bg-red-600 hover:bg-red-700", buttonClassName)}
            onClick={() => fileInputRef.current?.click()}
          >
            <AlertCircle className="mr-2 h-4 w-4" />
            {errorMessage}
          </Button>
        );
      default:
        return (
          <Button
            type="button"
            className={cn("w-full", buttonClassName)}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="mr-2 h-4 w-4" />
            {buttonText}
          </Button>
        );
    }
  };

  return (
    <div ref={ref} className={cn("space-y-4", className)}>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept={accept}
        onChange={(e) => {
          try {
            handleFileChange(e.target.files?.[0] || null);
          } catch (error) {
            console.error("Error handling file selection:", error);
          }
        }}
      />

      <div
        className={cn(
          "border-2 border-dashed rounded-md p-6 text-center",
          dragActive ? "border-purple-500 bg-purple-50" : "border-gray-300",
          dropzoneClassName
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {renderButton()}

        <p className="mt-2 text-sm text-gray-500">
          {dragDropText}
        </p>
        <p className="text-xs text-gray-400 mt-1">
          Maximum file size: {maxSize / (1024 * 1024)}MB
        </p>
      </div>

      {fileName && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm text-gray-500 flex items-center justify-between"
        >
          <span>Selected file: {fileName}</span>
          {onFileRemove && (
            <button
              onClick={(e) => handleFileRemove(e, onFileRemove)}
              className="text-red-500 hover:text-red-700 ml-2"
              aria-label="Remove file"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </motion.div>
      )}
    </div>
  );
});

FileUpload.displayName = "FileUpload";

export default FileUpload;







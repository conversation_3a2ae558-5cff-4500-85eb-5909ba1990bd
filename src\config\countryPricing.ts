export interface CountryConfig {
  code: string;
  name: string;
  flag: string; // Country flag CDN URL
  currency: {
    code: string;
    symbol: string;
    name: string;
  };
  timezones: string[];
  exchangeRate: number; // Rate to convert from USD base price
  priceMultiplier?: number; // Additional multiplier for market-specific pricing
}

export interface PricingPlan {
  id: string;
  name: string;
  type: string;
  description: string;
  basePriceUSD: number; // Base price in USD
  originalPriceUSD?: number;
  duration: string;
  popular?: boolean;
  icon: string;
  color: string;
  features: string[];
  sessions: string;
  priceType: 'per_session' | 'fixed';
  note?: string;
}

// Country configurations with timezone mappings
export const COUNTRY_CONFIGS: Record<string, CountryConfig> = {
  US: {
    code: 'US',
    name: 'United States',
    flag: 'https://flagcdn.com/w40/us.png',
    currency: {
      code: 'USD',
      symbol: '$',
      name: 'US Dollar'
    },
    timezones: [
      'America/New_York',
      'America/Detroit',
      'America/Kentucky/Louisville',
      'America/Kentucky/Monticello',
      'America/Indiana/Indianapolis',
      'America/Indiana/Vincennes',
      'America/Indiana/Winamac',
      'America/Indiana/Marengo',
      'America/Indiana/Petersburg',
      'America/Indiana/Vevay',
      'America/Chicago',
      'America/Indiana/Tell_City',
      'America/Indiana/Knox',
      'America/Menominee',
      'America/North_Dakota/Center',
      'America/North_Dakota/New_Salem',
      'America/North_Dakota/Beulah',
      'America/Denver',
      'America/Boise',
      'America/Phoenix',
      'America/Los_Angeles',
      'America/Anchorage',
      'America/Juneau',
      'America/Sitka',
      'America/Metlakatla',
      'America/Yakutat',
      'America/Nome',
      'America/Adak',
      'Pacific/Honolulu',
      'America/Puerto_Rico',
      'America/St_Thomas',
      'Pacific/Guam',
      'Pacific/Saipan',
      'Pacific/Pago_Pago',
      'Pacific/Wake',
      'Pacific/Midway'
    ],
    exchangeRate: 1.0, // Base currency
    priceMultiplier: 1.0
  },
  IN: {
    code: 'IN',
    name: 'India',
    flag: 'https://flagcdn.com/w40/in.png',
    currency: {
      code: 'INR',
      symbol: '₹',
      name: 'Indian Rupee'
    },
    timezones: [
      'Asia/Kolkata'
    ],
    exchangeRate: 83.0, // Approximate USD to INR rate
    priceMultiplier: 0.8 // 20% discount for Indian market
  }
};

// Default fallback country (US)
export const DEFAULT_COUNTRY = 'US';

// Base pricing plans in USD
export const BASE_PRICING_PLANS: PricingPlan[] = [
  {
    id: 'booster',
    name: 'High Dosage Tutoring',
    type: 'booster',
    description: 'Comprehensive subject coverage with all topics included',
    basePriceUSD: 21.5,
    originalPriceUSD: 30,
    duration: '90 days',
    popular: true,
    icon: 'Zap',
    color: 'from-green-400 to-green-600',
    features: [
      'Complete subject coverage',
      'Stay on track with school work',
      'Structured learning path',
      'Expert tutor assignment',
      'Progress tracking',
      'Interactive sessions',
      'Study materials included',
      'Priority support'
    ],
    sessions: '20-25 sessions',
    priceType: 'per_session'
  },
  {
    id: 'custom',
    name: 'Personalized Plan',
    type: 'custom',
    description: 'Build your own learning path by selecting specific topics',
    basePriceUSD: 19.5,
    duration: 'Flexible',
    popular: false,
    icon: 'Target',
    color: 'from-blue-400 to-blue-600',
    features: [
      'Choose specific topics',
      'Select subtopics',
      'Flexible curriculum',
      'Pay for what you need',
      'Personalized tutor matching',
      'You have flexibility to choose exactly what your child needs help with',
      'Adaptive scheduling',
      'Progress monitoring'
    ],
    sessions: 'Variable',
    priceType: 'per_session',
    note: 'Final price calculated based on selected topics'
  },
  {
    id: 'preparation',
    name: 'Test Booster',
    type: 'preparation',
    description: 'Focused preparation for specific exams and assessments',
    basePriceUSD: 25.0,
    duration: 'Flexible',
    popular: false,
    icon: 'Award',
    color: 'from-purple-400 to-purple-600',
    features: [
      'Exam-focused curriculum',
      'The Maryland Comprehensive Assessment Program (MCAP)',
      'The Independent School Entrance Examination (ISEE)',
      'The Scholastic Assessment Test (SAT)',
      'Practice tests included',
      'Targeted weak area focus',
      'Test-taking strategies',
      'Mock exam sessions'
    ],
    sessions: 'Variable',
    priceType: 'per_session',
    note: 'Final price calculated based on preparation needs'
  }
];

/**
 * Get country by timezone
 */
export const getCountryByTimezone = (timezone: string): string => {
  for (const [countryCode, config] of Object.entries(COUNTRY_CONFIGS)) {
    if (config.timezones.includes(timezone)) {
      return countryCode;
    }
  }
  return DEFAULT_COUNTRY;
};

/**
 * Get country configuration
 */
export const getCountryConfig = (countryCode: string): CountryConfig => {
  return COUNTRY_CONFIGS[countryCode] || COUNTRY_CONFIGS[DEFAULT_COUNTRY];
};

/**
 * Calculate localized price
 */
export const calculateLocalizedPrice = (
  basePriceUSD: number,
  countryCode: string
): number => {
  const config = getCountryConfig(countryCode);
  const localPrice = basePriceUSD * config.exchangeRate * (config.priceMultiplier || 1.0);
  
  // Round to appropriate decimal places based on currency
  if (config.currency.code === 'INR') {
    return Math.round(localPrice); // Round to nearest rupee
  }
  return Math.round(localPrice * 100) / 100; // Round to 2 decimal places for USD
};

/**
 * Format price with currency symbol
 */
export const formatPrice = (price: number, countryCode: string): string => {
  const config = getCountryConfig(countryCode);

  if (config.currency.code === 'INR') {
    // Indian number formatting with commas
    return `${config.currency.symbol}${price.toLocaleString('en-IN')}`;
  }

  // US dollar formatting
  return `${config.currency.symbol}${price.toFixed(2)}`;
};

/**
 * Check if a country offers a discount
 */
export const hasDiscount = (countryCode: string): boolean => {
  const config = getCountryConfig(countryCode);
  return (config.priceMultiplier || 1.0) < 1.0;
};

/**
 * Calculate discount percentage
 */
export const getDiscountPercentage = (countryCode: string): number => {
  const config = getCountryConfig(countryCode);
  const multiplier = config.priceMultiplier || 1.0;
  if (multiplier >= 1.0) return 0;
  return Math.round((1 - multiplier) * 100);
};

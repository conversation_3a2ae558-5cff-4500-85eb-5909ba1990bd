import { useEffect } from "react";

/**
 * Hook that scrolls the window to the top when a component mounts
 * Cross-browser compatible with both modern browsers and IE
 * @param dependencies - Optional array of dependencies to trigger scroll on changes
 */
const useScrollToTop = (dependencies: any[] = []) => {
  useEffect(() => {
    const scrollToTop = () => {
      // Try multiple scroll methods for cross-browser compatibility
      if ("scrollTo" in window) {
        // Modern browsers including Chrome
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: "auto", // Use "auto" instead of "smooth" for immediate scrolling
        });
      } else {
        // Fallback for older browsers like IE
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0; // For Safari
      }
    };

    // Immediate scroll
    scrollToTop();

    // Additional scroll after a short delay to handle any layout shifts
    const timeoutId = setTimeout(scrollToTop, 50);

    return () => clearTimeout(timeoutId);
  }, dependencies);
};

export default useScrollToTop;

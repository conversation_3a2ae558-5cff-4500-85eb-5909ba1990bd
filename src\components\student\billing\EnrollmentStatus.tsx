// src/components/student/billing/EnrollmentStatus.tsx
import React from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowRight, RefreshCw } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { ROUTES } from "@/routes/RouteConfig";

interface EnrollmentStatusProps {
  onViewProducts?: () => void; // Made optional since we'll use navigation instead
}

const EnrollmentStatus: React.FC<EnrollmentStatusProps> = () => {
  const navigate = useNavigate();
  const { isEnrolled, refreshEnrollmentStatus, user, userType } = useAuth();

  // Debug logging
  React.useEffect(() => {
    console.log('EnrollmentStatus component - Current state:', {
      isEnrolled,
      userId: user?.id,
      userType
    });
  }, [isEnrolled, user?.id, userType]);

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-blue-800">
              {isEnrolled ? "You're Enrolled!" : "Get Started with RF Learn"}
            </CardTitle>
            <CardDescription>
              {isEnrolled
                ? "You're currently enrolled in our learning programs."
                : "Explore our learning products and start your educational journey."}
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshEnrollmentStatus}
            className="text-blue-600 hover:text-blue-800"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isEnrolled ? (
          <div className="flex items-start space-x-3">
            <div className="bg-blue-100 p-2 rounded-full">
              <AlertCircle className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-blue-800">Active Enrollment</h4>
              <p className="text-sm text-blue-700 mt-1">
                You have active subscriptions. Check your dashboard to see your learning progress.
              </p>
            </div>
          </div>
        ) : (
          <div className="flex items-start space-x-3">
            <div className="bg-blue-100 p-2 rounded-full">
              <AlertCircle className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-blue-800">Not Enrolled</h4>
              <p className="text-sm text-blue-700 mt-1">
                You're not currently enrolled in any of our learning programs. Browse our products to get started and start the subscription process.
              </p>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        {isEnrolled ? (
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700"
            onClick={() => navigate('/student/dashboard')}
          >
            Go to Dashboard
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        ) : (
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700"
            onClick={() => navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION.path)}
          >
            View Learning Products
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default EnrollmentStatus;
import { useState, useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Star, Search, Clock, Calendar, MapPin, Filter } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import { Link } from "react-router-dom";
import TutorProfileModal from "@/components/tutor/ProfileModal";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import useScrollToTop from "@/hooks/useScrollToTop";

// Sample tutor data
export const tutors = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    photo:
      "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    rating: 4.9,
    reviewCount: 127,
    specialties: [
      "Reinforcement Learning",
      "Machine Learning",
      "AI Foundations",
    ],
    rate: 75,
    location: "Remote / San Francisco",
    experience: "12 years",
    availability: ["Mon", "Wed", "Thu", "Fri"],
    education: "Ph.D. in Computer Science, Stanford University",
    bio: "Expert in machine learning with a focus on reinforcement learning applications. Previously worked at Google AI and taught at Stanford.",
    achievements: [
      "Published 15+ research papers in top AI conferences",
      "Developed ML algorithms used by 3 Fortune 500 companies",
      "Google AI Research Award 2021",
      "Stanford Excellence in Teaching Award",
    ],
    professionalExperience: [
      "Senior Research Scientist at Google AI (2015-2022)",
      "Assistant Professor at Stanford University (2012-2015)",
      "AI Research Intern at DeepMind (2010-2012)",
    ],
    languages: ["English", "Mandarin", "French"],
    // Add reviews array for featured review
    reviews: [
      {
        id: "r1",
        studentName: "Alex S.",
        rating: 5,
        comment: "Dr. Johnson is an exceptional tutor! Her explanations of complex reinforcement learning concepts made them easy to understand. I've made significant progress in my research project thanks to her guidance.",
        date: "2023-11-15",
        tutorResponse: "Thank you for your kind words, Alex! It's been a pleasure working with such a dedicated student."
      },
      {
        id: "r2",
        studentName: "Maria L.",
        rating: 5,
        comment: "Very patient and knowledgeable. Highly recommend for anyone struggling with machine learning concepts.",
        date: "2023-10-22"
      }
    ],
  },
  {
    id: 2,
    name: "Prof. Michael Chen",
    photo:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    rating: 4.8,
    reviewCount: 94,
    specialties: ["Mathematical Foundations", "Deep Learning", "Data Science"],
    rate: 85,
    location: "Remote / New York",
    experience: "15 years",
    availability: ["Tue", "Wed", "Sat", "Sun"],
    education: "Ph.D. in Applied Mathematics, MIT",
    bio: "Mathematics professor specializing in the theoretical foundations of AI and machine learning. Author of three textbooks on applied math for AI.",
    reviews: [
      {
        id: "r3",
        studentName: "James T.",
        rating: 5,
        comment: "Prof. Chen's deep understanding of mathematical foundations has been invaluable for my research. His teaching approach makes complex concepts accessible.",
        date: "2023-12-05",
        tutorResponse: "I appreciate your feedback, James. It's rewarding to see your progress in applying these concepts."
      }
    ],
  },
  {
    id: 3,
    name: "Dr. Robert Kim",
    photo:
      "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    rating: 4.7,
    reviewCount: 68,
    specialties: ["Robotics", "Computer Vision", "Reinforcement Learning"],
    rate: 70,
    location: "Remote / Boston",
    experience: "9 years",
    availability: ["Mon", "Tue", "Thu", "Sat"],
    education: "Ph.D. in Robotics, Carnegie Mellon University",
    bio: "Robotics researcher specializing in computer vision and RL for autonomous systems. Previously led research teams at Boston Dynamics.",
    achievements: [
      "Patent holder for 3 robotics vision systems",
      "DARPA Robotics Challenge Finalist",
      "IEEE Robotics Pioneer Award 2020",
    ],
    professionalExperience: [
      "Lead Robotics Engineer at Boston Dynamics (2018-Present)",
      "Research Scientist at MIT Robotics Lab (2015-2018)",
      "Robotics Developer at iRobot (2013-2015)",
    ],
    languages: ["English", "Korean", "Japanese"],
    reviews: [
      {
        id: "r4",
        studentName: "Sophia L.",
        rating: 4,
        comment: "Dr. Kim's expertise in robotics and computer vision is impressive. His practical examples from industry really helped me understand the real-world applications.",
        date: "2023-10-18"
      }
    ],
  },
  {
    id: 4,
    name: "Dr. Emily Rodriguez",
    photo:
      "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    rating: 4.9,
    reviewCount: 112,
    specialties: ["NLP", "Conversational AI", "Machine Learning"],
    rate: 80,
    location: "Remote / Austin",
    experience: "11 years",
    availability: ["Mon", "Wed", "Fri", "Sun"],
    education: "Ph.D. in Computational Linguistics, UC Berkeley",
    bio: "Expert in natural language processing and conversational AI. Former lead researcher at OpenAI and contributor to several open-source NLP libraries.",
    reviews: [
      {
        id: "r5",
        studentName: "David M.",
        rating: 5,
        comment: "Dr. Rodriguez is an outstanding NLP tutor. Her deep knowledge of the field and clear explanations have been instrumental in my thesis work.",
        date: "2023-11-30",
        tutorResponse: "Thank you, David! It's been wonderful to see your progress in applying NLP techniques to your research."
      }
    ],
  },
  {
    id: 5,
    name: "Prof. James Wilson",
    photo:
      "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    rating: 4.6,
    reviewCount: 88,
    specialties: [
      "Deep Reinforcement Learning",
      "Neural Networks",
      "AI Ethics",
    ],
    rate: 90,
    location: "Remote / London",
    experience: "14 years",
    availability: ["Tue", "Thu", "Fri", "Sat"],
    education: "Ph.D. in Computer Science, University of Cambridge",
    bio: "Professor specializing in deep reinforcement learning and AI ethics. Conducts research on responsible AI development and deployment.",
    reviews: [
      {
        id: "r6",
        studentName: "Emma W.",
        rating: 5,
        comment: "Prof. Wilson's insights on AI ethics have completely transformed my approach to my research. His ability to connect technical concepts with ethical considerations is unmatched.",
        date: "2023-09-25",
        tutorResponse: "Thank you for your kind words, Emma. I'm glad our discussions have been valuable for your research direction."
      }
    ],
  },
  {
    id: 6,
    name: "Dr. Lisa Park",
    photo:
      "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-1.2.1&auto=format&fit=crop&w=256&q=80",
    rating: 4.8,
    reviewCount: 75,
    specialties: [
      "Game Development AI",
      "Reinforcement Learning",
      "Programming",
    ],
    rate: 65,
    location: "Remote / Seattle",
    experience: "8 years",
    availability: ["Mon", "Wed", "Thu", "Sun"],
    education: "Ph.D. in Computer Science, University of Washington",
    bio: "AI researcher focusing on game development and simulation. Previously worked at Valve and EA developing AI systems for popular game titles.",
    reviews: [
      {
        id: "r7",
        studentName: "Ryan K.",
        rating: 5,
        comment: "Dr. Park's experience in game development AI is incredible. She helped me implement a reinforcement learning algorithm for my game project that works perfectly.",
        date: "2023-12-10",
        tutorResponse: "Thanks Ryan! Your project was fascinating, and I'm glad I could help bring your ideas to life."
      }
    ],
  },
];

const TutorSearch = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [specialty, setSpecialty] = useState("all");
  const [availability, setAvailability] = useState("any");
  const [priceRange, setPriceRange] = useState("any");
  const [selectedTutor, setSelectedTutor] = useState(null);
  const [showProfileModal, setShowProfileModal] = useState(false);

  useScrollToTop();

  // Handle opening the tutor profile modal
  const handleOpenTutorProfile = (tutor) => {
    setSelectedTutor(tutor);
    setShowProfileModal(true);
    console.log("Opening modal for tutor:", tutor.name);
  };

  // Filter tutors based on search and filter criteria
  const filteredTutors = tutors.filter((tutor) => {
    // Search by name or bio
    const matchesSearch =
      tutor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tutor.bio.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tutor.specialties.some((s) =>
        s.toLowerCase().includes(searchTerm.toLowerCase())
      );

    // Filter by specialty
    const matchesSpecialty =
      specialty === "all" || tutor.specialties.some((s) => s === specialty);

    // Filter by availability
    const matchesAvailability =
      availability === "any" || tutor.availability.includes(availability);

    // Filter by price range
    let matchesPrice = true;
    if (priceRange === "under-70") {
      matchesPrice = tutor.rate < 70;
    } else if (priceRange === "70-85") {
      matchesPrice = tutor.rate >= 70 && tutor.rate <= 85;
    } else if (priceRange === "above-85") {
      matchesPrice = tutor.rate > 85;
    }

    return (
      matchesSearch && matchesSpecialty && matchesAvailability && matchesPrice
    );
  });

  // Get unique specialties for filter dropdown
  const uniqueSpecialties = Array.from(
    new Set(tutors.flatMap((tutor) => tutor.specialties))
  );

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Find Your Perfect Tutor
            </h1>
            <p className="mt-4 text-xl text-gray-500 max-w-2xl mx-auto">
              Connect with expert tutors specializing in reinforcement learning
              and AI technologies.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search input */}
              <div className="md:col-span-2">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    type="text"
                    placeholder="Search by name, specialty or keyword..."
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* Specialty filter */}
              <div>
                <Select value={specialty} onValueChange={setSpecialty}>
                  <SelectTrigger>
                    <SelectValue placeholder="Specialty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Specialties</SelectItem>
                    {uniqueSpecialties.map((specialty) => (
                      <SelectItem key={specialty} value={specialty}>
                        {specialty}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Price range filter */}
              <div>
                <Select value={priceRange} onValueChange={setPriceRange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Price Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">Any Price</SelectItem>
                    <SelectItem value="under-70">Under $70/hr</SelectItem>
                    <SelectItem value="70-85">$70-$85/hr</SelectItem>
                    <SelectItem value="above-85">Above $85/hr</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Availability filter */}
              <div>
                <Select value={availability} onValueChange={setAvailability}>
                  <SelectTrigger>
                    <SelectValue placeholder="Availability" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">Any Day</SelectItem>
                    <SelectItem value="Mon">Monday</SelectItem>
                    <SelectItem value="Tue">Tuesday</SelectItem>
                    <SelectItem value="Wed">Wednesday</SelectItem>
                    <SelectItem value="Thu">Thursday</SelectItem>
                    <SelectItem value="Fri">Friday</SelectItem>
                    <SelectItem value="Sat">Saturday</SelectItem>
                    <SelectItem value="Sun">Sunday</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="md:col-span-3 flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("");
                    setSpecialty("all");
                    setAvailability("any");
                    setPriceRange("any");
                  }}
                  className="mr-2"
                >
                  Reset Filters
                </Button>
                <Button className="button-gradient text-white">
                  <Filter className="mr-2 h-4 w-4" />
                  Apply Filters
                </Button>
              </div>
            </div>
          </div>

          <Tabs defaultValue="grid" className="mb-8">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {filteredTutors.length} tutors found
              </div>
              <TabsList>
                <TabsTrigger value="grid">Grid View</TabsTrigger>
                <TabsTrigger value="list">List View</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="grid" className="mt-6">
              {filteredTutors.length === 0 ? (
                <div className="text-center py-12 bg-white rounded-lg shadow">
                  <h3 className="text-lg font-medium text-gray-900">
                    No tutors found
                  </h3>
                  <p className="mt-2 text-gray-500">
                    Try adjusting your search filters to find available tutors.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredTutors.map((tutor) => (
                    <Card
                      key={tutor.id}
                      className="overflow-hidden hover:shadow-lg transition-shadow duration-300"
                    >
                      <div className="flex flex-col h-full">
                        <div className="flex items-center p-6 pb-4">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <img
                                  src={tutor.photo}
                                  alt={tutor.name}
                                  className="w-16 h-16 rounded-full object-cover mr-4 cursor-pointer hover:ring-2 hover:ring-rfpurple-300"
                                  onClick={() => handleOpenTutorProfile(tutor)}
                                />
                              </TooltipTrigger>
                              <TooltipContent>View full profile</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <div>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <CardTitle
                                    className="text-lg cursor-pointer hover:text-rfpurple-600 transition-colors"
                                    onClick={() =>
                                      handleOpenTutorProfile(tutor)
                                    }
                                  >
                                    {tutor.name}
                                  </CardTitle>
                                </TooltipTrigger>
                                <TooltipContent>
                                  View full profile
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <div className="flex items-center mt-1">
                              <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                              <span className="ml-1 text-sm font-medium">
                                {tutor.rating}
                              </span>
                              <span className="ml-1 text-sm text-gray-500">
                                ({Array.isArray(tutor.reviews) ? tutor.reviews.length : tutor.reviewCount} reviews)
                              </span>
                            </div>
                          </div>
                        </div>

                        <CardContent className="flex-grow">
                          <div className="flex flex-wrap gap-2 mb-4">
                            {tutor.specialties.map((specialty) => (
                              <Badge
                                key={specialty}
                                variant="secondary"
                                className="bg-gray-100 text-gray-700"
                              >
                                {specialty}
                              </Badge>
                            ))}
                          </div>

                          <div className="space-y-2 mb-4">
                            <div className="flex items-center text-sm">
                              <Clock className="h-4 w-4 mr-2 text-gray-500" />
                              <span className="text-gray-700">
                                {tutor.experience} experience
                              </span>
                            </div>
                            <div className="flex items-center text-sm">
                              <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                              <span className="text-gray-700">
                                {tutor.location}
                              </span>
                            </div>
                            <div className="flex items-center text-sm">
                              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                              <span className="text-gray-700">
                                Available: {tutor.availability.join(", ")}
                              </span>
                            </div>
                          </div>

                          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                            {tutor.bio}
                          </p>

                          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                            <div className="text-lg font-bold text-rfpurple-600">
                              ${tutor.rate}/hr
                            </div>
                            <Button
                              className="button-gradient text-white"
                              asChild
                            >
                              <Link to={`/book-session/${tutor.id}`}>
                                Book Session
                              </Link>
                            </Button>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="list" className="mt-6">
              {filteredTutors.length === 0 ? (
                <div className="text-center py-12 bg-white rounded-lg shadow">
                  <h3 className="text-lg font-medium text-gray-900">
                    No tutors found
                  </h3>
                  <p className="mt-2 text-gray-500">
                    Try adjusting your search filters to find available tutors.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredTutors.map((tutor) => (
                    <Card
                      key={tutor.id}
                      className="overflow-hidden hover:shadow-md transition-shadow duration-300"
                    >
                      <div className="p-6">
                        <div className="flex flex-col md:flex-row md:items-center">
                          <div className="flex items-center mb-4 md:mb-0 md:mr-6">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <img
                                    src={tutor.photo}
                                    alt={tutor.name}
                                    className="w-16 h-16 rounded-full object-cover mr-4 cursor-pointer hover:ring-2 hover:ring-rfpurple-300"
                                    onClick={() =>
                                      handleOpenTutorProfile(tutor)
                                    }
                                  />
                                </TooltipTrigger>
                                <TooltipContent>
                                  View full profile
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <div>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <h3
                                      className="text-lg font-semibold cursor-pointer hover:text-rfpurple-600 transition-colors"
                                      onClick={() =>
                                        handleOpenTutorProfile(tutor)
                                      }
                                    >
                                      {tutor.name}
                                    </h3>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    View full profile
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              <div className="flex items-center mt-1">
                                <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                                <span className="ml-1 text-sm font-medium">
                                  {tutor.rating}
                                </span>
                                <span className="ml-1 text-sm text-gray-500">
                                  ({Array.isArray(tutor.reviews) ? tutor.reviews.length : tutor.reviewCount} reviews)
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="flex-grow space-y-2 mb-4 md:mb-0">
                            <div className="flex flex-wrap gap-2 mb-2">
                              {tutor.specialties.map((specialty) => (
                                <Badge
                                  key={specialty}
                                  variant="secondary"
                                  className="bg-gray-100 text-gray-700"
                                >
                                  {specialty}
                                </Badge>
                              ))}
                            </div>
                            <p className="text-sm text-gray-600 line-clamp-2">
                              {tutor.bio}
                            </p>
                            <div className="flex flex-wrap gap-4 text-sm">
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-1 text-gray-500" />
                                <span>{tutor.experience}</span>
                              </div>
                              <div className="flex items-center">
                                <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                                <span>{tutor.location}</span>
                              </div>
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                                <span>
                                  Available: {tutor.availability.join(", ")}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center justify-between md:justify-end md:ml-4">
                            <div className="text-lg font-bold text-rfpurple-600 mr-4">
                              ${tutor.rate}/hr
                            </div>
                            <Button
                              className="button-gradient text-white"
                              asChild
                            >
                              <Link to={`/book-session/${tutor.id}`}>
                                Book Session
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {/* Add the TutorProfileModal component */}
      {selectedTutor && (
        <TutorProfileModal
          tutor={selectedTutor}
          isOpen={showProfileModal}
          onClose={() => setShowProfileModal(false)}
        />
      )}

      <Footer />
    </div>
  );
};

export default TutorSearch;

import React from "react";
import {
  LayoutDashboard,
  BookOpen,
  Users,
  UserCog,
  MessageSquare,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  Home,
  Layers,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import Sidebar, { SidebarItemType } from "@/components/ui/Sidebar";

const AdminSidebar: React.FC = () => {
  const { signOut } = useAuth();

  const menuItems: SidebarItemType[] = [
    {
      icon: <LayoutDashboard size={18} />,
      label: "Dashboard",
      path: "/admin-dashboard",
    },
    {
      icon: <BookOpen size={18} />,
      label: "Session Management",
      path: "/admin/sessions",
    },
    {
      icon: <UserCog size={18} />,
      label: "Tutor Management",
      path: "/admin/tutors",
    },
    {
      icon: <Users size={18} />,
      label: "User Management",
      path: "/admin/users",
    },
    {
      icon: <Layers size={18} />,
      label: "Batch Management",
      path: "/admin/batches",
    },
    {
      icon: <MessageSquare size={18} />,
      label: "Inquiries Management",
      path: "/admin/inquiries",
    },
    {
      icon: <Settings size={18} />,
      label: "Update System Information",
      path: "/admin/system",
    },
    {
      icon: <User size={18} />,
      label: "Update Account Details",
      path: "/admin/account",
    },
  ];

  const handleLogout = async () => {
    await signOut();
  };

  return (
    <Sidebar
      title="Admin Portal"
      menuItems={menuItems}
      onLogout={handleLogout}
      logoutIcon={<LogOut size={18} />}
      headerColor="text-rfpurple-800"
      homeLink="/admin-dashboard"
      homeIcon={<Home size={20} />}
      expandIcon={<Menu size={16} />}
      collapseIcon={<X size={16} />}
    />
  );
};

export default AdminSidebar;

import { create } from 'zustand';

// Types for social login providers
type SocialProvider = "google" | "azure" | "facebook" | "linkedin_oidc";

// Zustand store for social login loading states
interface SocialLoginState {
  loadingStates: Record<string, boolean>;
  setLoading: (provider: SocialProvider, isLoading: boolean) => void;
  clearAllLoading: () => void;
  isProviderLoading: (provider: SocialProvider) => boolean;
  hasAnyLoading: () => boolean;
}

export const useSocialLoginStore = create<SocialLoginState>((set, get) => ({
  loadingStates: {},
  
  setLoading: (provider: SocialProvider, isLoading: boolean) => {
    console.log(`🔄 Setting ${provider} loading state to:`, isLoading);
    set((state) => ({
      loadingStates: {
        ...state.loadingStates,
        [provider]: isLoading,
      },
    }));
  },
  
  clearAllLoading: () => {
    console.log('🧹 Clearing all social login loading states');
    set({ loadingStates: {} });
  },
  
  isProviderLoading: (provider: SocialProvider) => {
    return get().loadingStates[provider] || false;
  },
  
  hasAnyLoading: () => {
    const states = get().loadingStates;
    return Object.values(states).some(loading => loading === true);
  },
}));

// Helper hook for easier usage
export const useSocialLoginState = () => {
  const store = useSocialLoginStore();
  
  return {
    ...store,
    // Convenience methods
    setGoogleLoading: (isLoading: boolean) => store.setLoading('google', isLoading),
    setAzureLoading: (isLoading: boolean) => store.setLoading('azure', isLoading),
    setFacebookLoading: (isLoading: boolean) => store.setLoading('facebook', isLoading),
    setLinkedInLoading: (isLoading: boolean) => store.setLoading('linkedin_oidc', isLoading),
    
    // Check specific providers
    isGoogleLoading: store.isProviderLoading('google'),
    isAzureLoading: store.isProviderLoading('azure'),
    isFacebookLoading: store.isProviderLoading('facebook'),
    isLinkedInLoading: store.isProviderLoading('linkedin_oidc'),
  };
};

import React, { useState } from 'react';
import authService from '../services/authService';
import { handleComponentError } from '../services/errorHandler';

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [troubleshootingSteps, setTroubleshootingSteps] = useState<string[]>([]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError('Please enter your email address');
      return;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    setError('');
    setLoading(true);
    setTroubleshootingSteps([]);
    
    try {
      await authService.sendPasswordResetEmail(email);
      setSuccess(true);
    } catch (err: any) {
      console.error('Password reset error:', err);
      
      if (err.code === 'NETWORK_ERROR') {
        setError('Unable to connect to the server. Please check that:');
        setTroubleshootingSteps([
          'The backend server is running',
          'Your internet connection is working',
          'The API URL is correct in your .env file',
          'There are no CORS issues blocking the request'
        ]);
      } else {
        setError(err.message || 'An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="success-message">
        <h2>Password Reset Email Sent</h2>
        <p>If an account exists with the email you provided, you will receive password reset instructions.</p>
      </div>
    );
  }

  return (
    <div className="forgot-password-container">
      <h2>Forgot Password</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="email">Email Address</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={loading}
            placeholder="Enter your email"
            required
          />
        </div>
        
        {error && <div className="error-message">{error}</div>}
        
        {troubleshootingSteps.length > 0 && (
          <ul className="troubleshooting-list">
            {troubleshootingSteps.map((step, index) => (
              <li key={index}>{step}</li>
            ))}
          </ul>
        )}
        
        <button type="submit" disabled={loading} className="submit-button">
          {loading ? 'Sending...' : 'Reset Password'}
        </button>
      </form>
    </div>
  );
};

export default ForgotPassword;




import React from "react";
import AdminSidebar from "@/components/admin/Sidebar";
import { Link } from "react-router-dom";
import { UserPlus, Users, User, Edit, Trash2, CheckSquare } from "lucide-react";
import UserProfileMenu from "@/components/UserProfileMenu";
import { useProfileData } from "@/hooks/useProfileData";

const TutorManagement = () => {
  const profileData = useProfileData();

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Tutor Management</h1>
            <UserProfileMenu
              isAdmin={true}
              isAdminPage={true}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
            <Link
              to="/admin/tutors/create"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <UserPlus className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Create New Tutor</h3>
              <p className="text-sm text-gray-500">
                Add new tutors to the platform with their details and
                specializations.
              </p>
            </Link>

            <Link
              to="/admin/tutors/list"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Users className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">List All Tutors</h3>
              <p className="text-sm text-gray-500">
                View a complete list of all tutors currently registered in the
                system.
              </p>
            </Link>

            <Link
              to="/admin/tutors/view"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <User className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">View Tutor Details</h3>
              <p className="text-sm text-gray-500">
                Access detailed information about specific tutors and their
                performance.
              </p>
            </Link>

            <Link
              to="/admin/tutors/edit"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Edit className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">
                Edit/Update Tutor Details
              </h3>
              <p className="text-sm text-gray-500">
                Modify tutor information, credentials, and teaching areas.
              </p>
            </Link>

            <Link
              to="/admin/tutors/delete"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <Trash2 className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Delete Tutor</h3>
              <p className="text-sm text-gray-500">
                Remove tutors who are no longer active on the platform.
              </p>
            </Link>

            <Link
              to="/admin/tutors/approval-queue"
              className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition"
            >
              <div className="bg-rfpurple-100 p-3 rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                <CheckSquare className="text-rfpurple-600" size={20} />
              </div>
              <h3 className="font-semibold text-lg mb-2">Approval Queue</h3>
              <p className="text-sm text-gray-500">
                Review and respond to pending tutor applications.
              </p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TutorManagement;

# JSONB Curriculum Approach - How It Works

## Overview
Your existing `subscription_curriculum` table uses a **JSONB approach** to store curriculum selections, which is more efficient and flexible than normalized tables. This document explains how this approach enables batch creation and progress tracking.

## Current Table Structure

```sql
CREATE TABLE subscription_curriculum (
    id UUID PRIMARY KEY,
    workflow_id UUID NOT NULL,  -- Links to subscription_workflows
    selection_type TEXT NOT NULL, -- 'complete_subjects', 'selected_topics', 'selected_subtopics'
    selected_subjects JSONB DEFAULT '[]'::jsonb,  -- Array of subject IDs
    selected_topics JSONB DEFAULT '[]'::jsonb,    -- Array of topic IDs
    selected_subtopics JSONB DEFAULT '[]'::jsonb, -- Array of subtopic IDs
    estimated_sessions INTEGER,
    configured_by UUID NOT NULL,
    configured_by_role TEXT NOT NULL,
    configuration_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(workflow_id)
);
```

## How JSONB Selections Work

### 1. Complete Subjects (Booster Packages)
```sql
-- Student selects entire subjects
INSERT INTO subscription_curriculum VALUES (
    uuid_generate_v4(),
    'workflow-123',
    'complete_subjects',
    '["math-subject-id", "science-subject-id"]'::jsonb,  -- Complete subjects
    '[]'::jsonb,  -- No specific topics
    '[]'::jsonb,  -- No specific subtopics
    20,
    'student-456',
    'student',
    'Full Math and Science curriculum'
);
```

### 2. Selected Topics (Custom Packages)
```sql
-- Student selects specific topics from subjects
INSERT INTO subscription_curriculum VALUES (
    uuid_generate_v4(),
    'workflow-456',
    'selected_topics',
    '[]'::jsonb,  -- No complete subjects
    '["algebra-topic-id", "geometry-topic-id", "statistics-topic-id"]'::jsonb,  -- Specific topics
    '[]'::jsonb,  -- No specific subtopics (all subtopics of selected topics)
    15,
    'student-789',
    'student',
    'SAT Math preparation - selected topics only'
);
```

### 3. Selected Subtopics (Targeted Learning)
```sql
-- Student selects specific subtopics
INSERT INTO subscription_curriculum VALUES (
    uuid_generate_v4(),
    'workflow-789',
    'selected_subtopics',
    '[]'::jsonb,  -- No complete subjects
    '["algebra-topic-id"]'::jsonb,  -- Parent topics for context
    '["linear-equations-id", "quadratic-equations-id", "systems-equations-id"]'::jsonb,  -- Specific subtopics
    10,
    'student-101',
    'admin',
    'Remedial algebra - specific problem areas only'
);
```

## Batch Creation from JSONB Curriculum

### Function: Create Batch from Subscription Workflow

```typescript
async function createBatchFromWorkflow(workflowId: string) {
  // 1. Get curriculum configuration
  const { data: curriculum } = await supabase
    .from('subscription_curriculum')
    .select('*')
    .eq('workflow_id', workflowId)
    .single();

  if (!curriculum) {
    throw new Error('No curriculum configuration found');
  }

  // 2. Get workflow and subscription details
  const { data: workflow } = await supabase
    .from('subscription_workflows')
    .select(`
      *,
      subscriptions!inner(*)
    `)
    .eq('id', workflowId)
    .single();

  // 3. Create batch
  const { data: batch } = await supabase
    .from('batches')
    .insert({
      name: `${workflow.product_type} - ${new Date().getFullYear()}`,
      student_id: workflow.student_id,
      package_type: workflow.product_type,
      package_name: `${workflow.product_type} Package`,
      status: 'active',
      start_date: workflow.subscriptions.current_period_start,
      end_date: workflow.subscriptions.current_period_end,
      total_sessions: curriculum.estimated_sessions,
      remaining_sessions: curriculum.estimated_sessions
    })
    .select()
    .single();

  // 4. Add content to batch based on selection type
  await addContentToBatch(batch.id, curriculum);

  return batch;
}

async function addContentToBatch(batchId: string, curriculum: any) {
  switch (curriculum.selection_type) {
    case 'complete_subjects':
      await addCompleteSubjects(batchId, curriculum.selected_subjects);
      break;
    
    case 'selected_topics':
      await addSelectedTopics(batchId, curriculum.selected_topics);
      break;
    
    case 'selected_subtopics':
      await addSelectedSubtopics(batchId, curriculum.selected_topics, curriculum.selected_subtopics);
      break;
  }
}

// Add complete subjects to batch
async function addCompleteSubjects(batchId: string, subjectIds: string[]) {
  // Get all topics for selected subjects
  const { data: topics } = await supabase
    .from('topics')
    .select('id, subject_id')
    .in('subject_id', subjectIds);

  // Add all topics to batch
  for (const topic of topics) {
    const { data: batchTopic } = await supabase
      .from('batch_topics')
      .insert({
        batch_id: batchId,
        topic_id: topic.id,
        status: 'not_started'
      })
      .select()
      .single();

    // Add all subtopics for each topic
    const { data: subtopics } = await supabase
      .from('subtopics')
      .select('id')
      .eq('topic_id', topic.id);

    for (const subtopic of subtopics) {
      await supabase
        .from('batch_subtopics')
        .insert({
          batch_topic_id: batchTopic.id,
          subtopic_id: subtopic.id,
          status: 'not_started'
        });
    }
  }
}

// Add selected topics to batch
async function addSelectedTopics(batchId: string, topicIds: string[]) {
  for (const topicId of topicIds) {
    const { data: batchTopic } = await supabase
      .from('batch_topics')
      .insert({
        batch_id: batchId,
        topic_id: topicId,
        status: 'not_started'
      })
      .select()
      .single();

    // Add all subtopics for selected topics
    const { data: subtopics } = await supabase
      .from('subtopics')
      .select('id')
      .eq('topic_id', topicId);

    for (const subtopic of subtopics) {
      await supabase
        .from('batch_subtopics')
        .insert({
          batch_topic_id: batchTopic.id,
          subtopic_id: subtopic.id,
          status: 'not_started'
        });
    }
  }
}

// Add selected subtopics to batch
async function addSelectedSubtopics(batchId: string, topicIds: string[], subtopicIds: string[]) {
  for (const topicId of topicIds) {
    const { data: batchTopic } = await supabase
      .from('batch_topics')
      .insert({
        batch_id: batchId,
        topic_id: topicId,
        status: 'not_started'
      })
      .select()
      .single();

    // Add only selected subtopics
    const topicSubtopics = subtopicIds.filter(async (subtopicId) => {
      const { data } = await supabase
        .from('subtopics')
        .select('topic_id')
        .eq('id', subtopicId)
        .single();
      return data?.topic_id === topicId;
    });

    for (const subtopicId of topicSubtopics) {
      await supabase
        .from('batch_subtopics')
        .insert({
          batch_topic_id: batchTopic.id,
          subtopic_id: subtopicId,
          status: 'not_started'
        });
    }
  }
}
```

## Progress Tracking with JSONB Approach

### 1. Get Curriculum-Specific Progress
```sql
-- Get progress for a student's curriculum
WITH curriculum_config AS (
  SELECT 
    sc.selection_type,
    sc.selected_subjects,
    sc.selected_topics,
    sc.selected_subtopics
  FROM subscription_curriculum sc
  JOIN subscription_workflows sw ON sc.workflow_id = sw.id
  WHERE sw.student_id = 'student-123'
    AND sw.status = 'completed'
),
batch_progress AS (
  SELECT 
    bt.topic_id,
    bs.subtopic_id,
    bt.status as topic_status,
    bs.status as subtopic_status
  FROM batches b
  JOIN batch_topics bt ON b.id = bt.batch_id
  LEFT JOIN batch_subtopics bs ON bt.id = bs.batch_topic_id
  WHERE b.student_id = 'student-123'
    AND b.status = 'active'
)
SELECT 
  cc.selection_type,
  -- Calculate progress based on selection type
  CASE 
    WHEN cc.selection_type = 'complete_subjects' THEN
      -- Progress based on all topics in selected subjects
      (SELECT COUNT(*) FROM batch_progress WHERE topic_status = 'completed')::float / 
      (SELECT COUNT(*) FROM batch_progress)::float * 100
    WHEN cc.selection_type = 'selected_topics' THEN
      -- Progress based on selected topics only
      (SELECT COUNT(*) FROM batch_progress bp 
       WHERE bp.topic_id = ANY(SELECT jsonb_array_elements_text(cc.selected_topics)::uuid)
         AND bp.topic_status = 'completed')::float /
      jsonb_array_length(cc.selected_topics)::float * 100
    WHEN cc.selection_type = 'selected_subtopics' THEN
      -- Progress based on selected subtopics only
      (SELECT COUNT(*) FROM batch_progress bp 
       WHERE bp.subtopic_id = ANY(SELECT jsonb_array_elements_text(cc.selected_subtopics)::uuid)
         AND bp.subtopic_status = 'completed')::float /
      jsonb_array_length(cc.selected_subtopics)::float * 100
  END as progress_percentage
FROM curriculum_config cc;
```

### 2. Get Next Learning Item
```typescript
async function getNextLearningItem(studentId: string) {
  // Get curriculum configuration
  const { data: curriculum } = await supabase
    .from('subscription_curriculum')
    .select('*')
    .eq('workflow_id', (
      await supabase
        .from('subscription_workflows')
        .select('id')
        .eq('student_id', studentId)
        .eq('status', 'completed')
        .single()
    ).data.id)
    .single();

  let query = supabase
    .from('batch_subtopics')
    .select(`
      *,
      subtopics(name, description),
      batch_topics!inner(
        topic_id,
        topics(name)
      )
    `)
    .eq('status', 'not_started');

  // Filter based on curriculum selection
  if (curriculum.selection_type === 'selected_subtopics') {
    // Only show selected subtopics
    query = query.in('subtopic_id', curriculum.selected_subtopics);
  } else if (curriculum.selection_type === 'selected_topics') {
    // Only show subtopics from selected topics
    query = query.in('batch_topics.topic_id', curriculum.selected_topics);
  }
  // For complete_subjects, show all (no additional filter needed)

  const { data } = await query.limit(1);
  return data?.[0];
}
```

## Advantages of JSONB Approach

### 1. **Performance**
- Single query to get all curriculum data
- No complex joins across multiple tables
- Efficient JSONB indexing with GIN indexes

### 2. **Flexibility**
- Easy to add new selection criteria
- Simple to modify selections
- Compact storage

### 3. **Simplicity**
- Fewer tables to manage
- Simpler batch creation logic
- Easier to understand data flow

### 4. **Scalability**
- Better performance with large datasets
- Reduced database complexity
- Easier caching strategies

## Summary

The JSONB approach in your existing `subscription_curriculum` table is **superior** to normalized tables for this use case because:

1. **It stores curriculum selections efficiently** in JSON arrays
2. **It links directly to workflows** rather than subscriptions
3. **It enables flexible batch creation** based on selection type
4. **It supports accurate progress tracking** for subscribed content only
5. **It provides better performance** for curriculum-related queries

The normalized table approach I initially described would be overkill and less efficient for your needs. Your current JSONB approach is the right choice for flexible, performant curriculum management.

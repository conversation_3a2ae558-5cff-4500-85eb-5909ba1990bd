import React, { ReactNode } from "react";
import ProgressBar from "./ProgressBar";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";

interface OnboardingLayoutProps {
  children: ReactNode;
  currentStep: number;
  totalSteps: number;
  nextStep: () => void;
  prevStep: () => void;
  isNextDisabled?: boolean;
  nextButtonText?: string;
}

const OnboardingLayout: React.FC<OnboardingLayoutProps> = ({
  children,
  currentStep,
  totalSteps,
  nextStep,
  prevStep,
  isNextDisabled = false,
  nextButtonText = "Next",
}) => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-2xl bg-white rounded-lg shadow-md p-8">
        <ProgressBar currentStep={currentStep} totalSteps={totalSteps} />

        <div className="mb-8">{children}</div>

        <div className="flex justify-between">
          {currentStep > 1 ? (
            <Button variant="outline" onClick={prevStep}>
              Back
            </Button>
          ) : (
            <Button variant="outline" onClick={() => navigate("/")}>
              Cancel
            </Button>
          )}

          <Button onClick={nextStep} disabled={isNextDisabled}>
            {currentStep === totalSteps ? "Complete" : nextButtonText}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OnboardingLayout;

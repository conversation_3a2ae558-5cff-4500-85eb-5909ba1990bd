import { supabase } from '@/lib/supabaseClient';
import { config } from '@/config/environment';

export interface TeamsSessionData {
  subject: string;
  startTime: string;
  endTime: string;
  participants: string[];
  description?: string;
}

export interface TeamsMeetingResponse {
  meetingId: string;
  joinUrl: string;
  conferenceId?: string;
}

export interface MeetingMetadata {
  provider: 'microsoft_teams';
  meeting_id: string;
  join_url: string;
  conference_id?: string;
  organizer_id: string;
  settings: {
    allowAnonymousUsers: boolean;
    recordAutomatically: boolean;
    lobbyBypassSettings: string;
  };
}

class TeamsServiceBackend {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${config.supabase.url}/functions/v1`;
  }

  /**
   * Get authorization headers for Supabase function calls
   */
  private async getAuthHeaders(): Promise<HeadersInit> {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session?.access_token) {
      throw new Error('No active session found');
    }

    return {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Check if user is authenticated with Teams
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/teams-meetings`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      return data.isAuthenticated || false;
    } catch (error) {
      console.error('Failed to check Teams authentication:', error);
      return false;
    }
  }

  /**
   * Initiate Teams authentication flow
   */
  async authenticate(returnUrl?: string): Promise<boolean> {
    try {
      const headers = await this.getAuthHeaders();
      
      const url = new URL(`${this.baseUrl}/teams-auth`);
      url.searchParams.set('action', 'initiate');
      if (returnUrl) {
        url.searchParams.set('returnUrl', returnUrl);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error('Failed to initiate authentication');
      }

      const data = await response.json();
      
      if (data.authUrl) {
        // Redirect to Microsoft OAuth
        window.location.href = data.authUrl;
        return true;
      }

      return false;
    } catch (error) {
      console.error('Teams authentication failed:', error);
      return false;
    }
  }

  /**
   * Create a Teams meeting
   */
  async createMeeting(sessionData: TeamsSessionData): Promise<TeamsMeetingResponse> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/teams-meetings`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          sessionId: 'temp-session-id', // This will be overridden in createAndStoreMeeting
          sessionData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create Teams meeting');
      }

      const data = await response.json();
      
      return {
        meetingId: data.meetingId,
        joinUrl: data.joinUrl,
        conferenceId: data.conferenceId,
      };
    } catch (error) {
      console.error('Failed to create Teams meeting:', error);
      throw error;
    }
  }

  /**
   * Create a Teams meeting and store it in the database
   */
  async createAndStoreMeeting(sessionId: string, sessionData: TeamsSessionData): Promise<boolean> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/teams-meetings`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          sessionId,
          sessionData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create and store Teams meeting');
      }

      const data = await response.json();
      return data.success || false;
    } catch (error) {
      console.error('Failed to create and store Teams meeting:', error);
      throw error;
    }
  }

  /**
   * Sign out from Teams (remove stored tokens)
   */
  async signOut(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('No active user found');
      }

      const { error } = await supabase
        .from('user_integrations')
        .delete()
        .eq('user_id', user.id)
        .eq('provider', 'microsoft_teams');

      if (error) {
        throw error;
      }

      console.log('Successfully signed out from Teams');
    } catch (error) {
      console.error('Failed to sign out from Teams:', error);
      throw error;
    }
  }

  /**
   * Initialize the service (for compatibility with existing code)
   */
  async initialize(): Promise<boolean> {
    return await this.isAuthenticated();
  }
}

// Export singleton instance
export const teamsService = new TeamsServiceBackend();
export default teamsService;

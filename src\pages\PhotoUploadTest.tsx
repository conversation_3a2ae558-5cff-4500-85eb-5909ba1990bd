import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import FileUploadPhoto from "@/components/ui/FileUploadPhoto";
import { Button } from "@/components/ui/Button";

const PhotoUploadTest: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setUploadStatus("idle");

    // Create preview URL
    if (file && !file.error) {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    } else {
      setPreviewUrl(null);
    }
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    setUploadStatus("idle");
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setUploadStatus("uploading");

    // Simulate upload delay
    setTimeout(() => {
      setUploadStatus("success");
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Photo Upload with Crop & Rotate Test
          </h1>
          <p className="text-gray-600">
            Test the enhanced photo upload feature with preview, crop, and rotate functionality
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Photo</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FileUploadPhoto
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                buttonText="Select Photo"
                status={uploadStatus}
                fileName={selectedFile?.name}
                enableCropAndRotate={true}
              />

              {selectedFile && uploadStatus !== "uploading" && uploadStatus !== "success" && (
                <Button
                  onClick={handleUpload}
                  className="w-full"
                  disabled={!selectedFile}
                >
                  Upload Photo
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Preview Section */}
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
            </CardHeader>
            <CardContent>
              {previewUrl ? (
                <div className="space-y-4">
                  <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="text-sm text-gray-600">
                    <p><strong>File name:</strong> {selectedFile?.name}</p>
                    <p><strong>File size:</strong> {selectedFile ? (selectedFile.size / 1024 / 1024).toFixed(2) : 0} MB</p>
                    <p><strong>Status:</strong> {uploadStatus}</p>
                  </div>
                </div>
              ) : (
                <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500">No image selected</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Test</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2 text-gray-700">
              <li>Click "Select Photo" to choose an image file (JPG, PNG)</li>
              <li>The crop & rotate modal will open automatically</li>
              <li>Use the crop handles to adjust the crop area</li>
              <li>Click the "Rotate" button to rotate the image in 90-degree increments</li>
              <li>Click "Next" to apply the crop and rotation (modal closes)</li>
              <li>Click "OK" in the upload modal to start the actual upload</li>
              <li>The processed image will be uploaded and appear in the preview section</li>
            </ol>
          </CardContent>
        </Card>

        {/* Features */}
        <Card>
          <CardHeader>
            <CardTitle>Features Implemented</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">✅ Completed Features</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  <li>File upload with drag & drop</li>
                  <li>Image preview</li>
                  <li>Crop functionality with aspect ratio</li>
                  <li>90-degree rotation</li>
                  <li>Dark theme modal design</li>
                  <li>Error handling</li>
                  <li>File validation (size, type)</li>
                  <li>Processing states</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">🎨 Design Features</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  <li>Dark modal background</li>
                  <li>White crop selection border</li>
                  <li>Circular drag handles</li>
                  <li>Responsive layout</li>
                  <li>Smooth animations</li>
                  <li>Mobile-friendly controls</li>
                  <li>Accessibility support</li>
                  <li>Loading states</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PhotoUploadTest;

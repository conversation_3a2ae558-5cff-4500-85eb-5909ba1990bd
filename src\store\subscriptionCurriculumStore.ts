import { create } from "zustand";
import { supabase } from "@/lib/supabaseClient";
import { Subject, Topic, Subtopic } from "@/store/subjectStore";

// Types for subscription curriculum configuration
export interface SubscriptionCurriculumConfig {
  id: string;
  subscription_id: string;
  product_type: 'booster' | 'custom' | 'preparation';
  is_configured: boolean;
  configured_by?: string;
  configured_at?: string;
  subjects: SubscriptionSubject[];
  topics: SubscriptionTopic[];
  subtopics: SubscriptionSubtopic[];
}

export interface SubscriptionSubject {
  id: string;
  name: string;
  description?: string;
  include_all_topics: boolean;
}

export interface SubscriptionTopic {
  id: string;
  name: string;
  subject_id: string;
  include_all_subtopics: boolean;
}

export interface SubscriptionSubtopic {
  id: string;
  name: string;
  topic_id: string;
}

export interface CurriculumConfigurationData {
  product_type: 'booster' | 'custom' | 'preparation';
  subjects?: string[]; // Subject IDs for booster
  topics?: string[]; // Topic IDs for custom
  subtopics?: string[]; // Subtopic IDs for custom
}

interface SubscriptionCurriculumStore {
  // State
  configurations: Record<string, SubscriptionCurriculumConfig>; // keyed by subscription_id
  availableSubjects: Subject[];
  availableTopics: Topic[];
  availableSubtopics: Subtopic[];
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchCurriculumConfig: (subscriptionId: string) => Promise<SubscriptionCurriculumConfig | null>;
  fetchAvailableSubjects: () => Promise<void>;
  fetchAvailableTopics: (subjectId?: string) => Promise<void>;
  fetchAvailableSubtopics: (topicId?: string) => Promise<void>;
  configureCurriculum: (subscriptionId: string, config: CurriculumConfigurationData, userId: string) => Promise<boolean>;
  updateCurriculumConfig: (subscriptionId: string, config: CurriculumConfigurationData, userId: string) => Promise<boolean>;
  deleteCurriculumConfig: (subscriptionId: string) => Promise<boolean>;
  isSubscriptionConfigured: (subscriptionId: string) => boolean;
  getCurriculumConfig: (subscriptionId: string) => SubscriptionCurriculumConfig | null;
  resetState: () => void;
}

export const useSubscriptionCurriculumStore = create<SubscriptionCurriculumStore>((set, get) => ({
  // Initial state
  configurations: {},
  availableSubjects: [],
  availableTopics: [],
  availableSubtopics: [],
  isLoading: false,
  error: null,

  // Fetch curriculum configuration for a subscription
  fetchCurriculumConfig: async (subscriptionId: string) => {
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .rpc('get_subscription_curriculum_config', { p_subscription_id: subscriptionId });

      if (error) throw error;

      if (data && data.length > 0) {
        const config = data[0] as SubscriptionCurriculumConfig;
        set(state => ({
          configurations: {
            ...state.configurations,
            [subscriptionId]: config
          },
          isLoading: false
        }));
        return config;
      } else {
        // No configuration exists yet
        set({ isLoading: false });
        return null;
      }
    } catch (error) {
      console.error('Error fetching curriculum config:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch curriculum configuration',
        isLoading: false
      });
      return null;
    }
  },

  // Fetch available subjects
  fetchAvailableSubjects: async () => {
    try {
      const { data, error } = await supabase
        .from('subjects')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      set({ availableSubjects: data || [] });
    } catch (error) {
      console.error('Error fetching subjects:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to fetch subjects' });
    }
  },

  // Fetch available topics
  fetchAvailableTopics: async (subjectId?: string) => {
    try {
      let query = supabase
        .from('topics')
        .select('*')
        .order('display_order', { ascending: true });

      if (subjectId) {
        query = query.eq('subject_id', subjectId);
      }

      const { data, error } = await query;

      if (error) throw error;

      set({ availableTopics: data || [] });
    } catch (error) {
      console.error('Error fetching topics:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to fetch topics' });
    }
  },

  // Fetch available subtopics
  fetchAvailableSubtopics: async (topicId?: string) => {
    try {
      let query = supabase
        .from('subtopics')
        .select('*')
        .order('display_order', { ascending: true });

      if (topicId) {
        query = query.eq('topic_id', topicId);
      }

      const { data, error } = await query;

      if (error) throw error;

      set({ availableSubtopics: data || [] });
    } catch (error) {
      console.error('Error fetching subtopics:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to fetch subtopics' });
    }
  },

  // Configure curriculum for a subscription
  configureCurriculum: async (subscriptionId: string, config: CurriculumConfigurationData, userId: string) => {
    set({ isLoading: true, error: null });

    try {
      // Start a transaction
      const { data: curriculumData, error: curriculumError } = await supabase
        .from('subscription_curriculum')
        .insert({
          subscription_id: subscriptionId,
          product_type: config.product_type,
          is_configured: true,
          configured_by: userId,
          configured_at: new Date().toISOString()
        })
        .select()
        .single();

      if (curriculumError) throw curriculumError;

      const curriculumId = curriculumData.id;

      // Configure based on product type
      if (config.product_type === 'booster' && config.subjects) {
        // For booster: add complete subjects
        const subjectInserts = config.subjects.map(subjectId => ({
          subscription_curriculum_id: curriculumId,
          subject_id: subjectId,
          include_all_topics: true
        }));

        const { error: subjectsError } = await supabase
          .from('subscription_subjects')
          .insert(subjectInserts);

        if (subjectsError) throw subjectsError;

      } else if (config.product_type === 'custom') {
        // For custom: add selected topics and/or subtopics
        if (config.topics) {
          const topicInserts = config.topics.map(topicId => ({
            subscription_curriculum_id: curriculumId,
            topic_id: topicId,
            include_all_subtopics: !config.subtopics || config.subtopics.length === 0
          }));

          const { data: topicData, error: topicsError } = await supabase
            .from('subscription_topics')
            .insert(topicInserts)
            .select();

          if (topicsError) throw topicsError;

          // If specific subtopics are selected
          if (config.subtopics && config.subtopics.length > 0) {
            const subtopicInserts = config.subtopics.map(subtopicId => {
              // Find the corresponding topic for this subtopic
              const topicRecord = topicData.find(t => {
                // This would need to be matched properly based on the subtopic's topic_id
                // For now, we'll use the first topic record
                return true;
              });

              return {
                subscription_topic_id: topicRecord?.id,
                subtopic_id: subtopicId
              };
            });

            const { error: subtopicsError } = await supabase
              .from('subscription_subtopics')
              .insert(subtopicInserts);

            if (subtopicsError) throw subtopicsError;
          }
        }
      }

      // Refresh the configuration
      await get().fetchCurriculumConfig(subscriptionId);

      set({ isLoading: false });
      return true;

    } catch (error) {
      console.error('Error configuring curriculum:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to configure curriculum',
        isLoading: false
      });
      return false;
    }
  },

  // Update existing curriculum configuration
  updateCurriculumConfig: async (subscriptionId: string, config: CurriculumConfigurationData, userId: string) => {
    // For now, we'll delete and recreate the configuration
    const deleted = await get().deleteCurriculumConfig(subscriptionId);
    if (!deleted) return false;

    return await get().configureCurriculum(subscriptionId, config, userId);
  },

  // Delete curriculum configuration
  deleteCurriculumConfig: async (subscriptionId: string) => {
    try {
      const { error } = await supabase
        .from('subscription_curriculum')
        .delete()
        .eq('subscription_id', subscriptionId);

      if (error) throw error;

      // Remove from local state
      set(state => {
        const newConfigurations = { ...state.configurations };
        delete newConfigurations[subscriptionId];
        return { configurations: newConfigurations };
      });

      return true;
    } catch (error) {
      console.error('Error deleting curriculum config:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to delete curriculum configuration' });
      return false;
    }
  },

  // Check if subscription is configured
  isSubscriptionConfigured: (subscriptionId: string) => {
    const config = get().configurations[subscriptionId];
    return config?.is_configured || false;
  },

  // Get curriculum configuration
  getCurriculumConfig: (subscriptionId: string) => {
    return get().configurations[subscriptionId] || null;
  },

  // Reset state
  resetState: () => {
    set({
      configurations: {},
      availableSubjects: [],
      availableTopics: [],
      availableSubtopics: [],
      isLoading: false,
      error: null
    });
  }
}));

import React, { ReactNode } from 'react';
import StudentSidebar from '@/components/student/Sidebar';
import UserNavbar from '@/components/ui/UserNavbar';
import HorizontalDivider from '@/components/ui/HorizontalDivider';

interface StudentPageLayoutProps {
  children: ReactNode;
  title: string;
  description?: string | ReactNode;
  actions?: ReactNode;
}

/**
 * A consistent layout for all student pages that includes the sidebar, navbar,
 * and proper horizontal divider with no margins.
 */
const StudentPageLayout: React.FC<StudentPageLayoutProps> = ({
  children,
  title,
  description,
  actions
}) => {
  return (
    <div className="app-layout bg-gray-50">
      <div className="sidebar sticky top-0">
        <StudentSidebar />
      </div>
      <div className="content-area">
        <UserNavbar
          title={title}
          actions={actions}
          className="px-0 py-0 border-0"
        />

        <main className="flex-grow py-6 px-6 overflow-y-auto">
          <div className="max-w-7xl mx-auto">
            {description && (
              <div className="mt-2 text-lg text-gray-500 mb-6">
                {description}
              </div>
            )}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default StudentPageLayout;

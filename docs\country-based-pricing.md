# Country-Based Pricing System

## Overview

The country-based pricing system automatically detects user location through timezone and displays prices in the appropriate currency with market-specific adjustments. The system is highly flexible and allows easy addition of new countries.

## Features

- **Automatic timezone detection** using browser APIs
- **Country mapping** from timezone to country
- **Currency conversion** with exchange rates
- **Market-specific pricing** with multipliers
- **Localized formatting** for different currencies
- **Flexible configuration** for easy expansion

## Architecture

### Core Components

1. **Country Configuration** (`src/config/countryPricing.ts`)
   - Defines country-specific settings
   - Maps timezones to countries
   - Sets exchange rates and pricing multipliers

2. **Pricing Hooks** (`src/hooks/useCountryPricing.ts`)
   - `useCountryPricing()` - Auto-detects country from timezone
   - `useSpecificCountryPricing(countryCode)` - Gets pricing for specific country

3. **Timezone Integration** (`src/hooks/useTimezone.ts`)
   - Existing timezone detection functionality
   - Provides normalized timezone data

## Usage

### Basic Usage

```tsx
import { useCountryPricing } from '@/hooks/useCountryPricing';

const PricingComponent = () => {
  const { localizedPlans, countryConfig, isDetected } = useCountryPricing();
  
  return (
    <div>
      <h2>Pricing for {countryConfig.name}</h2>
      {localizedPlans.map(plan => (
        <div key={plan.id}>
          <h3>{plan.name}</h3>
          <p>{plan.formattedPrice}</p>
        </div>
      ))}
    </div>
  );
};
```

### Advanced Usage

```tsx
import { useSpecificCountryPricing } from '@/hooks/useCountryPricing';

const AdminPricingView = () => {
  const usPricing = useSpecificCountryPricing('US');
  const inPricing = useSpecificCountryPricing('IN');
  
  return (
    <div>
      <h2>US Pricing</h2>
      {/* Display US pricing */}
      
      <h2>India Pricing</h2>
      {/* Display India pricing */}
    </div>
  );
};
```

## Configuration

### Adding a New Country

1. **Add country configuration** in `src/config/countryPricing.ts`:

```typescript
export const COUNTRY_CONFIGS: Record<string, CountryConfig> = {
  // ... existing countries
  
  GB: {
    code: 'GB',
    name: 'United Kingdom',
    currency: {
      code: 'GBP',
      symbol: '£',
      name: 'British Pound'
    },
    timezones: [
      'Europe/London'
    ],
    exchangeRate: 0.79, // USD to GBP rate
    priceMultiplier: 1.1 // 10% premium for UK market
  }
};
```

2. **Update timezone mappings** if needed in `useTimezone.ts`

3. **Test the implementation** using the pricing test page at `/pricing-test`

### Configuration Options

- **exchangeRate**: Conversion rate from USD to local currency
- **priceMultiplier**: Market-specific pricing adjustment (1.0 = no change)
- **timezones**: Array of timezone identifiers for the country
- **currency**: Display information for the currency

## Current Supported Countries

### United States (US)
- **Currency**: USD ($)
- **Exchange Rate**: 1.0 (base currency)
- **Price Multiplier**: 1.0 (no adjustment)
- **Timezones**: All US timezones (35+ zones)

### India (IN)
- **Currency**: INR (₹)
- **Exchange Rate**: 83.0 (approximate)
- **Price Multiplier**: 0.8 (20% discount)
- **Timezones**: Asia/Kolkata

## Pricing Plans

The system uses base pricing plans defined in USD and converts them to local currencies:

1. **High Dosage Tutoring** - $21.50/session (was $30.00)
2. **Personalized Plan** - $19.50/session
3. **Test Booster** - $25.00/session

## Technical Details

### Timezone Detection Flow

1. Browser detects timezone using `Intl.DateTimeFormat().resolvedOptions().timeZone`
2. Timezone is normalized using alias mappings
3. Country is determined from timezone using predefined mappings
4. Pricing is calculated using country configuration

### Price Calculation

```typescript
const localPrice = basePriceUSD * exchangeRate * (priceMultiplier || 1.0);
```

### Currency Formatting

- **USD**: Standard US formatting with 2 decimal places
- **INR**: Indian number formatting with commas, rounded to nearest rupee

## Testing

### Test Pages

1. **Production Pricing**: `/pricing` - Shows actual pricing page with country detection
2. **Pricing Test**: `/pricing-test` - Interactive test page for different countries

### Manual Testing

1. Change browser timezone settings
2. Use the country selector in the test page
3. Verify pricing calculations and formatting

## Future Enhancements

### Planned Features

1. **Dynamic Exchange Rates**: Integration with currency APIs
2. **Regional Pricing**: Sub-country pricing (states, provinces)
3. **A/B Testing**: Different pricing strategies per market
4. **Purchasing Power Parity**: Automatic adjustments based on economic data

### Easy Extensions

1. **New Countries**: Add to `COUNTRY_CONFIGS`
2. **New Currencies**: Update formatting logic
3. **Complex Pricing**: Add business logic to price calculation
4. **Seasonal Pricing**: Time-based multipliers

## Best Practices

1. **Always test** new country configurations
2. **Keep exchange rates updated** regularly
3. **Consider local market conditions** when setting multipliers
4. **Validate timezone mappings** for accuracy
5. **Test currency formatting** with various amounts

## Troubleshooting

### Common Issues

1. **Timezone not detected**: Check browser compatibility
2. **Wrong country mapping**: Verify timezone-to-country mappings
3. **Incorrect formatting**: Check currency formatting logic
4. **Missing country**: Add to `COUNTRY_CONFIGS`

### Debug Information

The pricing test page (`/pricing-test`) provides detailed debug information including:
- Detected timezone
- Country mapping
- Exchange rates
- Price calculations
- Currency formatting

## API Reference

### useCountryPricing()

Returns:
- `countryCode`: Detected country code
- `countryConfig`: Country configuration object
- `localizedPlans`: Pricing plans with local currency
- `formatLocalPrice(usdPrice)`: Format any USD price to local currency
- `calculateLocalPrice(usdPrice)`: Calculate local price from USD
- `isDetected`: Whether timezone was successfully detected
- `detectedTimezone`: The detected timezone string

### useSpecificCountryPricing(countryCode)

Same as `useCountryPricing()` but for a specific country code.

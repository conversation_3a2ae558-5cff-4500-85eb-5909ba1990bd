-- SIMPLE FIX: Eliminate ALL recursion by using the most basic RLS policies possible
-- This addresses PostgREST queries causing infinite recursion

-- =====================================================
-- STEP 1: COMPLETELY DISABLE RLS TEMPORARILY
-- =====================================================

ALTER TABLE subscriptions DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 2: DROP ALL EXISTING POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Students can view their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Students can insert their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "System can update subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Admins can manage subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Service role can manage subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Admins can view all subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Students and admins can update subscriptions" ON subscriptions;

-- =====================================================
-- STEP 3: CREATE ULTRA-SIMPLE POLICIES (NO JOINS AT ALL)
-- =====================================================

-- Re-enable RLS
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Policy 1: Students can only see their own subscriptions
-- NO JOINS, NO SUBQUERIES - just direct comparison
CREATE POLICY "student_own_subscriptions" ON subscriptions
    FOR SELECT USING (
        student_id = auth.uid()
    );

-- Policy 2: Students can only insert their own subscriptions
CREATE POLICY "student_insert_own_subscriptions" ON subscriptions
    FOR INSERT WITH CHECK (
        student_id = auth.uid()
    );

-- Policy 3: Students can only update their own subscriptions
CREATE POLICY "student_update_own_subscriptions" ON subscriptions
    FOR UPDATE USING (
        student_id = auth.uid()
    );

-- Policy 4: Service role can do everything (for system operations)
CREATE POLICY "service_role_all_subscriptions" ON subscriptions
    FOR ALL USING (
        auth.role() = 'service_role'
    );

-- =====================================================
-- STEP 4: HANDLE ADMIN ACCESS SEPARATELY
-- =====================================================

-- Instead of using RLS for admin access, we'll handle it in the application layer
-- or create a separate admin function that uses SECURITY DEFINER

-- Create an admin function that bypasses RLS
CREATE OR REPLACE FUNCTION admin.get_all_subscriptions()
RETURNS TABLE(
    id uuid,
    student_id uuid,
    product_id uuid,
    status text,
    current_period_start timestamp with time zone,
    current_period_end timestamp with time zone,
    access_expires_at timestamp with time zone,
    created_at timestamp with time zone
)
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND user_type = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    -- Return all subscriptions for admins
    RETURN QUERY
    SELECT 
        s.id,
        s.student_id,
        s.product_id,
        s.status,
        s.current_period_start,
        s.current_period_end,
        s.access_expires_at,
        s.created_at
    FROM subscriptions s
    ORDER BY s.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION admin.get_all_subscriptions() TO authenticated;

-- =====================================================
-- STEP 5: CREATE SCHEMA FOR ADMIN FUNCTIONS IF NEEDED
-- =====================================================

CREATE SCHEMA IF NOT EXISTS admin;

-- =====================================================
-- STEP 6: VERIFICATION QUERIES
-- =====================================================

-- Check RLS status
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'subscriptions';

-- List current policies (should be very simple now)
SELECT 
    policyname,
    cmd as command,
    qual as condition
FROM pg_policies 
WHERE tablename = 'subscriptions'
ORDER BY policyname;

-- =====================================================
-- STEP 7: TEST THE SIMPLE POLICIES
-- =====================================================

-- Test 1: Basic subscriptions access (should work for students)
SELECT 'Testing basic subscriptions access...' as test_name;
-- Uncomment to test:
-- SELECT COUNT(*) FROM subscriptions;

-- Test 2: Test PostgREST-style query
SELECT 'Testing PostgREST-style query...' as test_name;
-- This simulates the query that was causing recursion:
-- SELECT s.*, p.name as product_name 
-- FROM subscriptions s 
-- LEFT JOIN products p ON p.id = s.product_id 
-- WHERE s.student_id = auth.uid()
-- ORDER BY s.current_period_end DESC;

-- =====================================================
-- STEP 8: ALTERNATIVE APPROACH - DISABLE RLS COMPLETELY
-- =====================================================

-- If the simple policies still cause issues, you can disable RLS entirely
-- and handle security in the application layer:

-- UNCOMMENT THESE LINES ONLY IF SIMPLE POLICIES STILL CAUSE RECURSION:
-- ALTER TABLE subscriptions DISABLE ROW LEVEL SECURITY;
-- 
-- -- Then handle security in your application by always filtering by student_id
-- -- Example: SELECT * FROM subscriptions WHERE student_id = current_user_id

-- =====================================================
-- STEP 9: SUMMARY AND RECOMMENDATIONS
-- =====================================================

/*
SUMMARY:

This script creates the simplest possible RLS policies to eliminate recursion:

1. NO JOINS in policy conditions
2. NO SUBQUERIES that reference other tables
3. Direct comparisons only (student_id = auth.uid())
4. Admin access handled via separate SECURITY DEFINER function

IF RECURSION STILL OCCURS:
- The issue might be with other tables (profiles, products)
- Consider disabling RLS entirely and handling security in application
- Check if there are triggers or other functions causing the recursion

TESTING:
1. Run this script
2. Test the PostgREST queries that were failing
3. Monitor logs for recursion errors
4. If still failing, disable RLS completely (uncomment Step 8)

APPLICATION CHANGES NEEDED:
- For admin users, use the admin.get_all_subscriptions() function
- For regular queries, the simple RLS policies should work
- Always ensure queries filter by student_id when appropriate
*/

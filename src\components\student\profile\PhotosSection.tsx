import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Edit, X } from "lucide-react";
import EditSectionModal from "@/components/student/profile/EditSectionModal";
import { StudentExtendedProfileData } from "@/pages/student/Profile";

interface PhotosSectionProps {
  profileData: StudentExtendedProfileData;
  activeSectionEdit: string | null;
  setActiveSectionEdit: (section: string | null) => void;
  handleOpenPhotoUpload: (type: 'profile' | 'gallery') => void;
}

const PhotosSection: React.FC<PhotosSectionProps> = ({
  profileData,
  activeSectionEdit,
  setActiveSectionEdit,
  handleOpenPhotoUpload
}) => {
  return (
    <>
      {/* Photos & Video Section */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Photos & Video</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setActiveSectionEdit('photos')}
          >
            <Edit className="h-3.5 w-3.5 mr-1" />
            Edit
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="aspect-square bg-gray-100 rounded-md relative group cursor-pointer" onClick={() => handleOpenPhotoUpload('gallery')}>
              <div className="absolute inset-0 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                  <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path>
                  <circle cx="12" cy="13" r="3"></circle>
                </svg>
              </div>
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="bg-white p-2 rounded-full shadow-md">
                  <Edit className="h-5 w-5 text-rfpurple-500" />
                </div>
              </div>
            </div>
            <div className="aspect-square bg-gray-100 rounded-md relative group cursor-pointer" onClick={() => handleOpenPhotoUpload('gallery')}>
              <div className="absolute inset-0 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                  <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path>
                  <circle cx="12" cy="13" r="3"></circle>
                </svg>
              </div>
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="bg-white p-2 rounded-full shadow-md">
                  <Edit className="h-5 w-5 text-rfpurple-500" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Photos Edit Modal */}
      <EditSectionModal
        isOpen={activeSectionEdit === 'photos'}
        onClose={() => setActiveSectionEdit(null)}
        title="Manage Photos"
        onSubmit={() => {
          // Save changes
          setActiveSectionEdit(null);
        }}
      >
        <div className="grid grid-cols-2 gap-4">
          <div className="aspect-square bg-gray-100 rounded-md relative group cursor-pointer flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
              <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path>
              <circle cx="12" cy="13" r="3"></circle>
            </svg>
            <div className="absolute top-2 right-2">
              <Button variant="destructive" size="icon" className="h-6 w-6 rounded-full">
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
          <div className="aspect-square bg-gray-100 rounded-md relative group cursor-pointer flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
              <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path>
              <circle cx="12" cy="13" r="3"></circle>
            </svg>
            <div className="absolute top-2 right-2">
              <Button variant="destructive" size="icon" className="h-6 w-6 rounded-full">
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        <Button className="w-full" onClick={() => handleOpenPhotoUpload('gallery')}>
          Select New Photo
        </Button>
      </EditSectionModal>
    </>
  );
};

export default PhotosSection;

# Timezone Notification System

## Overview

This system automatically manages timezone missing notifications for users who complete onboarding without setting their timezone. The notifications are persistent, cannot be deleted by users, and are automatically removed when the user sets their timezone.

## Features

### 🔄 Automatic Management
- **Auto-creation**: Notifications are automatically created when users complete onboarding without a timezone
- **Auto-removal**: Notifications are automatically removed when users set their timezone
- **Duplicate prevention**: System prevents creating multiple timezone notifications for the same user

### 🔒 System-Managed Notifications
- **Non-deletable**: Users cannot delete timezone notifications through the UI
- **Persistent**: Notifications remain until the underlying issue (missing timezone) is resolved
- **Clearly marked**: System-managed notifications are identified in the database

### 🎯 User Experience
- **Internal links**: Notifications include direct links to account preferences page
- **User-type aware**: Links are customized for students (`/student/account-preferences`) and tutors (`/tutor/account-preferences`)
- **Clear messaging**: Explains why timezone is important for session scheduling

## Database Schema

### New Fields in `notifications` Table
```sql
-- Added fields for system-managed notifications
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS is_system_managed BOOLEAN DEFAULT false;
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS notification_key TEXT;
```

### Notification Structure
```sql
{
  "id": "uuid",
  "user_id": "uuid",
  "title": "Set Your Timezone",
  "message": "Please set your timezone in account preferences...",
  "type": "system",
  "is_read": false,
  "is_system_managed": true,
  "notification_key": "timezone_missing",
  "created_at": "timestamp"
}
```

## Database Functions

### Core Functions
1. **`has_timezone_notification(user_id)`**: Checks if timezone notification exists
2. **`create_timezone_notification(user_id, user_type)`**: Creates timezone notification
3. **`remove_timezone_notification(user_id)`**: Removes timezone notification
4. **`handle_timezone_notification()`**: Trigger function for automatic management

### Utility Functions
1. **`create_missing_timezone_notifications()`**: Bulk create for existing users
2. **`cleanup_timezone_notifications()`**: Clean up orphaned notifications

## Triggers

### Profile Timezone Trigger
```sql
CREATE TRIGGER timezone_notification_trigger
    AFTER INSERT OR UPDATE OF timezone
    ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION handle_timezone_notification();
```

**Behavior:**
- **INSERT**: Creates notification if timezone is null/empty
- **UPDATE**: Removes notification when timezone is set, creates when timezone is cleared

## Security & Permissions

### RLS Policy Updates
```sql
-- Users cannot delete system-managed notifications
CREATE POLICY "Users can delete their own notifications" ON notifications
    FOR DELETE USING (
        auth.uid() = user_id 
        AND is_system_managed = false
    );
```

### Function Security
- All functions use `SECURITY DEFINER` for consistent permissions
- Functions validate input parameters and handle edge cases
- Proper error handling and logging

## Frontend Integration

### Updated Components
1. **NotificationDropdown.tsx**: Hides delete button for system-managed notifications
2. **Student/Tutor Notifications.tsx**: Prevents deletion of system-managed notifications
3. **Notification Store**: Updated interface to include new fields

### UI Behavior
- System-managed notifications show no delete button
- Clicking notification navigates to account preferences
- Clear visual indication that notification is system-managed

## Installation & Setup

### 1. Run Database Script
```bash
psql -d your_database -f timezone_notification_system.sql
```

### 2. Verify Installation
```sql
-- Check if functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_name LIKE '%timezone%';

-- Check if triggers exist
SELECT trigger_name FROM information_schema.triggers 
WHERE trigger_name = 'timezone_notification_trigger';
```

### 3. Test the System
```sql
-- Create a test user without timezone
INSERT INTO profiles (id, user_type, email, first_name, last_name)
VALUES ('test-user-id', 'student', '<EMAIL>', 'Test', 'User');

-- Check if notification was created
SELECT * FROM notifications WHERE user_id = 'test-user-id';

-- Set timezone and check if notification is removed
UPDATE profiles SET timezone = 'America/New_York' WHERE id = 'test-user-id';
SELECT * FROM notifications WHERE user_id = 'test-user-id';
```

## Monitoring & Maintenance

### Useful Queries

#### Check System-Managed Notifications
```sql
SELECT 
    p.first_name,
    p.last_name,
    p.user_type,
    p.timezone,
    n.title,
    n.created_at
FROM profiles p
JOIN notifications n ON p.id = n.user_id
WHERE n.is_system_managed = true
ORDER BY n.created_at DESC;
```

#### Find Users Without Timezone
```sql
SELECT id, first_name, last_name, user_type, timezone
FROM profiles 
WHERE (timezone IS NULL OR timezone = '')
AND user_type IN ('student', 'tutor');
```

#### Cleanup Orphaned Notifications
```sql
SELECT cleanup_timezone_notifications();
```

## Troubleshooting

### Common Issues

1. **Notifications not created**: Check if triggers are enabled
2. **Notifications not removed**: Verify timezone update triggers
3. **Duplicate notifications**: Run cleanup function

### Debug Queries
```sql
-- Check trigger status
SELECT * FROM pg_trigger WHERE tgname = 'timezone_notification_trigger';

-- Check function definitions
\df *timezone*

-- View recent notifications
SELECT * FROM notifications 
WHERE notification_key = 'timezone_missing' 
ORDER BY created_at DESC LIMIT 10;
```

## Future Enhancements

- [ ] Add notification preferences for users
- [ ] Implement notification scheduling
- [ ] Add email notifications for critical system notifications
- [ ] Create notification analytics dashboard
- [ ] Add support for multiple languages in notification messages

import { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Textarea } from "@/components/ui/TextArea";
import { Label } from "@/components/ui/Label";
import { useToast } from "@/hooks/use-toast";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";

const FeedbackForm = () => {
  const { toast } = useToast();
  const [rating, setRating] = useState("");
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      toast({
        title: "Feedback Submitted",
        description:
          "Thank you for your feedback! It helps us improve our services.",
      });
      setRating("");
      setFeedback("");
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <Card className="w-full max-w-lg lg:mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold">
          Share Your Feedback
        </CardTitle>
        <CardDescription>
          Your feedback helps us improve our platform and services. Let us know
          about your experience.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <Label>How would you rate your experience?</Label>
            <RadioGroup
              value={rating}
              onValueChange={setRating}
              className="flex space-x-4"
            >
              {[1, 2, 3, 4, 5].map((value) => (
                <div key={value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    id={`rating-${value}`}
                    value={value.toString()}
                  />
                  <Label htmlFor={`rating-${value}`} className="font-normal">
                    {value}
                  </Label>
                </div>
              ))}
            </RadioGroup>
            <div className="flex justify-between text-sm text-gray-500 px-1">
              <span>Poor</span>
              <span>Excellent</span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="feedback">Your Feedback</Label>
            <Textarea
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Please share your thoughts, suggestions, or concerns..."
              rows={5}
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full button-gradient text-white"
            disabled={isSubmitting || !rating}
          >
            {isSubmitting ? "Submitting..." : "Submit Feedback"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default FeedbackForm;

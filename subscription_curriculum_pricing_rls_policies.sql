-- RLS Policies for Subscription Curriculum and Pricing Tables
-- This file contains Row Level Security policies for subscription_curriculum and subscription_pricing tables

-- =====================================================
-- 1. SUBSCRIPTION_CURRICULUM TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on subscription_curriculum table
ALTER TABLE public.subscription_curriculum ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view their own curriculum" ON subscription_curriculum;
DROP POLICY IF EXISTS "Tutors can view curriculum for their students" ON subscription_curriculum;
DROP POLICY IF EXISTS "Admins can view all curriculum" ON subscription_curriculum;
DROP POLICY IF EXISTS "Students can create their own curriculum" ON subscription_curriculum;
DROP POLICY IF EXISTS "Admins can create curriculum" ON subscription_curriculum;
DROP POLICY IF EXISTS "Students can update their own curriculum" ON subscription_curriculum;
DROP POLICY IF EXISTS "Admins can update all curriculum" ON subscription_curriculum;
DROP POLICY IF EXISTS "Service role full access curriculum" ON subscription_curriculum;

-- Students can view curriculum for their own workflows
CREATE POLICY "Students can view their own curriculum"
ON public.subscription_curriculum
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_curriculum.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Tutors can view curriculum for students they have sessions with
CREATE POLICY "Tutors can view curriculum for their students"
ON public.subscription_curriculum
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_curriculum.workflow_id
    AND p.user_type = 'tutor'
    AND EXISTS (
      SELECT 1 FROM public.sessions s
      WHERE s.student_id = sw.student_id
      AND s.tutor_id = auth.uid()
    )
  )
);

-- Admins can view all curriculum
CREATE POLICY "Admins can view all curriculum"
ON public.subscription_curriculum
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can create curriculum for their own workflows
CREATE POLICY "Students can create their own curriculum"
ON public.subscription_curriculum
FOR INSERT
TO authenticated
WITH CHECK (
  configured_by = auth.uid()
  AND configured_by_role = 'student'
  AND EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_curriculum.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Admins can create curriculum for any workflow
CREATE POLICY "Admins can create curriculum"
ON public.subscription_curriculum
FOR INSERT
TO authenticated
WITH CHECK (
  configured_by = auth.uid()
  AND configured_by_role = 'admin'
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can update their own curriculum
CREATE POLICY "Students can update their own curriculum"
ON public.subscription_curriculum
FOR UPDATE
TO authenticated
USING (
  configured_by = auth.uid()
  AND configured_by_role = 'student'
  AND EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_curriculum.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
)
WITH CHECK (
  configured_by = auth.uid()
  AND configured_by_role = 'student'
  AND EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_curriculum.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Admins can update all curriculum
CREATE POLICY "Admins can update all curriculum"
ON public.subscription_curriculum
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access curriculum"
ON public.subscription_curriculum
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 2. SUBSCRIPTION_PRICING TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on subscription_pricing table
ALTER TABLE public.subscription_pricing ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view their own pricing" ON subscription_pricing;
DROP POLICY IF EXISTS "Admins can view all pricing" ON subscription_pricing;
DROP POLICY IF EXISTS "Students can create their own pricing" ON subscription_pricing;
DROP POLICY IF EXISTS "Admins can create pricing" ON subscription_pricing;
DROP POLICY IF EXISTS "Students can update their own pricing" ON subscription_pricing;
DROP POLICY IF EXISTS "Admins can update all pricing" ON subscription_pricing;
DROP POLICY IF EXISTS "Service role full access pricing" ON subscription_pricing;

-- Students can view pricing for their own workflows
CREATE POLICY "Students can view their own pricing"
ON public.subscription_pricing
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_pricing.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Admins can view all pricing
CREATE POLICY "Admins can view all pricing"
ON public.subscription_pricing
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can create pricing for their own workflows (system-generated)
CREATE POLICY "Students can create their own pricing"
ON public.subscription_pricing
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_pricing.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Admins can create pricing for any workflow
CREATE POLICY "Admins can create pricing"
ON public.subscription_pricing
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Students can update their own pricing (limited fields)
CREATE POLICY "Students can update their own pricing"
ON public.subscription_pricing
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_pricing.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.subscription_workflows sw
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE sw.id = subscription_pricing.workflow_id
    AND sw.student_id = auth.uid()
    AND p.user_type = 'student'
  )
);

-- Admins can update all pricing (including admin overrides)
CREATE POLICY "Admins can update all pricing"
ON public.subscription_pricing
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access pricing"
ON public.subscription_pricing
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 3. VERIFICATION FUNCTIONS
-- =====================================================

-- Function to verify RLS policies are enabled on subscription curriculum and pricing tables
CREATE OR REPLACE FUNCTION verify_subscription_curriculum_pricing_rls_policies()
RETURNS TABLE (
  table_name TEXT,
  rls_enabled BOOLEAN,
  policy_count INTEGER
) AS $$
BEGIN
  SET search_path TO public;

  RETURN QUERY
  SELECT
    t.tablename::TEXT,
    t.rowsecurity as rls_enabled,
    (
      SELECT COUNT(*)::INTEGER
      FROM pg_policies p
      WHERE p.tablename = t.tablename
    ) as policy_count
  FROM pg_tables t
  WHERE t.schemaname = 'public'
  AND t.tablename IN (
    'subscription_curriculum', 'subscription_pricing'
  )
  ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to list all subscription curriculum and pricing policies
CREATE OR REPLACE FUNCTION list_subscription_curriculum_pricing_policies()
RETURNS TABLE (
  table_name TEXT,
  policy_name TEXT,
  command TEXT,
  roles TEXT[],
  policy_condition TEXT
) AS $$
BEGIN
  SET search_path TO public;

  RETURN QUERY
  SELECT
    p.tablename::TEXT,
    p.policyname::TEXT,
    p.cmd::TEXT,
    p.roles,
    p.qual::TEXT
  FROM pg_policies p
  WHERE p.tablename IN (
    'subscription_curriculum', 'subscription_pricing'
  )
  ORDER BY p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. USAGE EXAMPLES AND VERIFICATION
-- =====================================================

-- Check RLS status for subscription curriculum and pricing tables
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE
        WHEN rowsecurity THEN 'RLS is ENABLED'
        ELSE 'RLS is DISABLED'
    END as status
FROM pg_tables
WHERE tablename IN ('subscription_curriculum', 'subscription_pricing')
ORDER BY tablename;

-- Count policies per table
SELECT
    tablename,
    COUNT(*) as policy_count
FROM pg_policies
WHERE tablename IN ('subscription_curriculum', 'subscription_pricing')
GROUP BY tablename
ORDER BY tablename;

-- =====================================================
-- 5. SECURITY NOTES AND BEST PRACTICES
-- =====================================================

/*
SECURITY CONSIDERATIONS:

1. PAYMENT DATA PROTECTION: Tutors are explicitly excluded from accessing
   subscription_pricing data, ensuring they cannot see student payment
   information as per user preferences.

2. WORKFLOW-BASED ACCESS: All access is controlled through the
   subscription_workflows table, ensuring users can only access data
   for workflows they are authorized to see.

3. ROLE-BASED CONFIGURATION: The configured_by and configured_by_role
   fields ensure that only the appropriate user types can create/modify
   curriculum configurations.

4. ADMIN OVERRIDE PROTECTION: Only admins can perform pricing overrides,
   protecting the integrity of the pricing system.

5. STUDENT OWNERSHIP: Students can only access and modify their own
   curriculum and pricing data, maintaining data privacy.

TESTING RECOMMENDATIONS:

1. Test with different user types (student, admin)
2. Verify tutors cannot access pricing data
3. Test curriculum access for tutors with student sessions
4. Verify admin override capabilities for pricing
5. Test workflow-based access control

PERFORMANCE CONSIDERATIONS:

1. Policies use EXISTS clauses for efficient lookups
2. Consider adding indexes on frequently queried columns:
   - subscription_curriculum(workflow_id, configured_by)
   - subscription_pricing(workflow_id, admin_override_by)
   - subscription_workflows(student_id, id)

MAINTENANCE:

1. Review policies when adding new user types
2. Update policies if subscription workflow changes
3. Monitor query performance and adjust indexes as needed
4. Regular security audits of policy effectiveness
5. Ensure pricing data remains protected from unauthorized access

IMPORTANT NOTES:

1. Tutors have NO ACCESS to subscription_pricing table - this protects
   student payment information as required.

2. Curriculum access for tutors is limited to students they actually
   teach (have sessions with).

3. Admin overrides in pricing are tracked and auditable.

4. All policies validate user_type from profiles table for security.
*/

-- Usage examples:
-- SELECT * FROM verify_subscription_curriculum_pricing_rls_policies();
-- SELECT * FROM list_subscription_curriculum_pricing_policies();

-- End of Subscription Curriculum and Pricing RLS Policies

import { create } from 'zustand';

export type NotificationFilterType = 'all' | 'billing' | 'academic' | 'system';
export type NotificationFilterStatus = 'all' | 'read' | 'unread';

interface NotificationPageState {
  // Filter state
  searchQuery: string;
  filterType: NotificationFilterType;
  filterStatus: NotificationFilterStatus;

  // Highlight state
  highlightedNotificationId: string | null;

  // Actions
  setSearchQuery: (query: string) => void;
  setFilterType: (type: NotificationFilterType) => void;
  setFilterStatus: (status: NotificationFilterStatus) => void;
  clearFilters: () => void;
  resetState: () => void;

  // Highlight actions
  setHighlightedNotificationId: (id: string | null) => void;
  clearHighlight: () => void;
}

interface NotificationDropdownState {
  // Dropdown state
  isOpen: boolean;

  // Actions
  setIsOpen: (isOpen: boolean) => void;
  toggleDropdown: () => void;
  closeDropdown: () => void;
}

const initialState = {
  searchQuery: '',
  filterType: 'all' as NotificationFilterType,
  filterStatus: 'all' as NotificationFilterStatus,
  highlightedNotificationId: null,
};

export const useNotificationPageStore = create<NotificationPageState>((set) => ({
  // Initial state
  ...initialState,

  // Actions
  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  setFilterType: (type: NotificationFilterType) => {
    set({ filterType: type });
  },

  setFilterStatus: (status: NotificationFilterStatus) => {
    set({ filterStatus: status });
  },

  clearFilters: () => {
    set({
      searchQuery: '',
      filterType: 'all',
      filterStatus: 'all',
    });
  },

  resetState: () => {
    set(initialState);
  },

  // Highlight actions
  setHighlightedNotificationId: (id: string | null) => {
    set({ highlightedNotificationId: id });
  },

  clearHighlight: () => {
    set({ highlightedNotificationId: null });
  },
}));

// Notification Dropdown Store
export const useNotificationDropdownStore = create<NotificationDropdownState>((set, get) => ({
  // Initial state
  isOpen: false,

  // Actions
  setIsOpen: (isOpen: boolean) => {
    set({ isOpen });
  },

  toggleDropdown: () => {
    set(state => ({ isOpen: !state.isOpen }));
  },

  closeDropdown: () => {
    set({ isOpen: false });
  },
}));

import React, { useEffect } from 'react';
import { Clock, MapPin, Save, Settings, RefreshCw, Bell, Calendar, Sun } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useTimezone, COMMON_TIMEZONES } from '@/hooks/useTimezone';
import { useAccountPreferencesStore } from '@/stores/useStudentAccountPreferencesStore';
import StudentPageLayout from '@/components/layouts/StudentPageLayout';

const AccountPreferences: React.FC = () => {
  const { user, profileData, refreshUserData } = useAuth();
  const { toast } = useToast();
  const [isInitialLoad, setIsInitialLoad] = React.useState(true);
  const {
    detectedTimezone,
    currentTimezone,
    getTimezoneDisplayInfo,
    formatTimeInTimezone,
    detectUserTimezone,
    normalizeTimezone
  } = useTimezone();

  const {
    selectedTimezone,
    hasChanges,
    isSaving,
    autoDetect,
    showMultipleTimezones,
    timezoneNotifications,
    lastUpdated,
    isRefreshing,
    updateTrigger,
    setSelectedTimezone,
    initializeTimezone,
    saveTimezoneSettings: saveTimezoneToStore,
    setAutoDetect,
    setShowMultipleTimezones,
    setTimezoneNotifications,
    setLastUpdated,
    setIsRefreshing,
    triggerUpdate,
    resetStore
  } = useAccountPreferencesStore();

  // Reset store on component mount to ensure clean state
  useEffect(() => {
    resetStore();
  }, [resetStore]);

  // Initialize selected timezone with proper priority handling
  useEffect(() => {
    // Don't initialize if we don't have profile data yet (unless it's explicitly null/empty)
    if (!profileData && isInitialLoad) {
      return;
    }

    // Always prioritize database value when available
    if (profileData?.timezone) {
      const normalizedProfileTimezone = normalizeTimezone(profileData.timezone);
      initializeTimezone(normalizedProfileTimezone);
      setIsInitialLoad(false);
    } else if (detectedTimezone && isInitialLoad) {
      // No saved timezone and this is the initial load - use detected timezone
      initializeTimezone(detectedTimezone);
      setIsInitialLoad(false);
    }
  }, [profileData?.timezone, detectedTimezone, initializeTimezone, normalizeTimezone, isInitialLoad]);

  // Handle auto-detect toggle - only apply when user explicitly enables it
  const handleAutoDetectToggle = (enabled: boolean) => {
    setAutoDetect(enabled);
    if (enabled && detectedTimezone && !isInitialLoad) {
      // Only auto-set if there's no saved timezone in database or user explicitly wants to override
      if (!profileData?.timezone) {
        setSelectedTimezone(detectedTimezone);
        setLastUpdated(new Date());
        toast({
          title: "Auto-detect Enabled",
          description: `Timezone automatically set to ${getTimezoneDisplayInfo(detectedTimezone).label}`,
        });
      } else {
        toast({
          title: "Auto-detect Enabled",
          description: "Auto-detect is now enabled. Your current timezone preference is preserved.",
        });
      }
    }
  };

  // Use detected timezone and save to database
  const handleUseDetectedTimezone = async () => {
    if (!detectedTimezone) return;

    if (!user?.id) {
      toast({
        title: "Error",
        description: "User not found. Please try logging in again.",
        variant: "destructive",
      });
      return;
    }

    // Update local state first
    setSelectedTimezone(detectedTimezone);
    setLastUpdated(new Date());

    // Save to database
    const result = await saveTimezoneToStore(user.id, detectedTimezone, refreshUserData);

    if (result.success) {
      toast({
        title: "Timezone Updated",
        description: `Timezone successfully changed to ${getTimezoneDisplayInfo(detectedTimezone).label}`,
      });
    } else {
      toast({
        title: "Error",
        description: result.error || "Failed to save timezone. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Refresh detected timezone
  const handleRefreshTimezone = async () => {
    setIsRefreshing(true);
    try {
      const newDetectedTimezone = detectUserTimezone();

      // Update the last updated time
      setLastUpdated(new Date());

      // If auto-detect is enabled and the timezone changed, update the selected timezone
      // But only if there's no saved timezone in database
      if (autoDetect && newDetectedTimezone !== detectedTimezone) {
        if (!profileData?.timezone) {
          setSelectedTimezone(newDetectedTimezone);
          toast({
            title: "Timezone Updated",
            description: `Detected timezone changed to ${getTimezoneDisplayInfo(newDetectedTimezone).label}`,
          });
        } else {
          triggerUpdate();
          toast({
            title: "Timezone Detected",
            description: `Detected timezone updated to ${getTimezoneDisplayInfo(newDetectedTimezone).label}. Your saved preference is preserved.`,
          });
        }
      } else if (newDetectedTimezone !== detectedTimezone) {
        triggerUpdate();
        toast({
          title: "Timezone Detected",
          description: `Detected timezone updated to ${getTimezoneDisplayInfo(newDetectedTimezone).label}`,
        });
      } else {
        toast({
          title: "No Change",
          description: "Your timezone is already up to date.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh timezone. Please try again.",
        variant: "destructive",
      });
    } finally {
      setTimeout(() => setIsRefreshing(false), 1000);
    }
  };

  // Save timezone settings
  const handleSaveTimezone = async () => {
    if (!user?.id) {
      toast({
        title: "Error",
        description: "User not found. Please try logging in again.",
        variant: "destructive",
      });
      return;
    }

    const result = await saveTimezoneToStore(user.id, selectedTimezone, refreshUserData);

    if (result.success) {
      toast({
        title: "Settings Saved",
        description: "Your timezone preference has been updated successfully.",
      });
    } else {
      toast({
        title: "Error",
        description: result.error || "Failed to save timezone settings. Please try again.",
        variant: "destructive",
      });
    }
  };

  const detectedTimezoneInfo = getTimezoneDisplayInfo(detectedTimezone);

  // Debug logging
  console.log('AccountPreferences Debug:', {
    profileTimezone: profileData?.timezone,
    selectedTimezone,
    detectedTimezone,
    isButtonDisabled: (profileData?.timezone && selectedTimezone === detectedTimezone) || isSaving
  });

  return (
    <StudentPageLayout
      title="Account Preferences"
      description="Manage your account settings and preferences"
    >
      <div className="space-y-6">

        {/* Timezone Settings Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-purple-600 mb-2">Timezone Settings</h2>
            <p className="text-gray-600">Manage your timezone preferences for accurate scheduling and notifications</p>
          </div>

          {/* Detected Timezone */}
          <div className="mb-8">
            <div className="bg-purple-50 rounded-lg p-6 border border-purple-100">
              <h3 className="text-lg font-semibold text-purple-600 mb-4">Detected Timezone</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-purple-600" />
                  <span className="text-gray-700">
                    Current Local Time: <span className="font-semibold text-purple-700">{formatTimeInTimezone(new Date(), detectedTimezone)}</span>
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-purple-600" />
                  <span className="text-gray-700">
                    Detected Timezone: <span className="font-semibold text-purple-700">{detectedTimezoneInfo.label} - {detectedTimezone}</span>
                  </span>
                </div>
                <div className="flex gap-3 mt-4">
                  <button
                    onClick={handleUseDetectedTimezone}
                    disabled={(profileData?.timezone && selectedTimezone === detectedTimezone) || isSaving}
                    className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-sm font-medium transition-colors flex items-center gap-2"
                  >
                    {isSaving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      !profileData?.timezone ? 'Use This Timezone' : 'Switch to Detected Timezone'
                    )}
                  </button>
                  <button
                    onClick={handleRefreshTimezone}
                    disabled={isRefreshing}
                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed text-sm font-medium transition-colors flex items-center gap-2"
                  >
                    <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    {isRefreshing ? 'Refreshing...' : 'Refresh'}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Current Selection */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Selection</h3>
            {selectedTimezone && (
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-600" />
                  <span className="text-gray-700">
                    Selected: <span className="font-semibold text-gray-900">{getTimezoneDisplayInfo(selectedTimezone).label} - {selectedTimezone}</span>
                  </span>
                </div>
                <div className="flex items-center gap-3 mt-2">
                  <Clock className="h-5 w-5 text-gray-600" />
                  <span className="text-gray-700">
                    Current Time: <span className="font-semibold text-gray-900">{formatTimeInTimezone(new Date(), selectedTimezone)}</span>
                  </span>
                </div>
              </div>
            )}

            <div>
              {/* Select Timezone */}
              <div>
                <label htmlFor="timezone-select" className="block text-sm font-medium text-gray-700 mb-2">
                  Select Timezone
                </label>
                <select
                  id="timezone-select"
                  value={selectedTimezone}
                  onChange={(e) => setSelectedTimezone(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  {/* Show placeholder option only if no timezone is selected */}
                  {!selectedTimezone && (
                    <option value="">Select a timezone...</option>
                  )}

                  {COMMON_TIMEZONES.map((tz) => (
                    <option key={tz.value} value={tz.value}>
                      {tz.label} - {tz.value}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Timezone Preferences */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferences</h3>

            <div className="space-y-6">
              {/* Auto-detect Timezone */}
              <div className="flex items-start gap-4">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <RefreshCw className="h-5 w-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Auto-detect Timezone</h4>
                      <p className="text-sm text-gray-600">Automatically detect and update your timezone based on your location</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={autoDetect}
                        onChange={(e) => handleAutoDetectToggle(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Timezone Notifications */}
              <div className="flex items-start gap-4">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <Bell className="h-5 w-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Timezone Change Notifications</h4>
                      <p className="text-sm text-gray-600">Get notified when your timezone changes automatically</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={timezoneNotifications}
                        onChange={(e) => setTimezoneNotifications(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Show Multiple Timezones */}
              <div className="flex items-start gap-4">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <Sun className="h-5 w-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Show Multiple Timezones</h4>
                      <p className="text-sm text-gray-600">Display times in multiple timezones for better coordination</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={showMultipleTimezones}
                        onChange={(e) => setShowMultipleTimezones(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSaveTimezone}
              disabled={!hasChanges || isSaving}
              className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed font-medium transition-colors flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>

          {/* Last Updated */}
          {lastUpdated && (
            <div className="mt-4 text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleString()}
            </div>
          )}
        </div>

        {/* Future Settings Sections Placeholder */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Preferences</h2>
          <div className="text-gray-500 text-center py-8">
            <Settings className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>More preference options coming soon!</p>
            <p className="text-sm mt-1">Notification settings, language preferences, and more.</p>
          </div>
        </div>
      </div>
    </StudentPageLayout>
  );
};

export default AccountPreferences;

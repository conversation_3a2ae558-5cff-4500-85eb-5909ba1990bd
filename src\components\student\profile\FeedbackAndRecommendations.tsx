import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Edit, Plus, MessageSquare } from "lucide-react";

const FeedbackAndRecommendations: React.FC = () => {
  // This would typically come from props or a data fetch
  const feedbackCount = 0;
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center">
          <CardTitle className="text-xl">Feedback & recomendations</CardTitle>
          <span className="ml-2 text-sm text-gray-500">{feedbackCount} records</span>
        </div>
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-center py-6 text-gray-500">
          <MessageSquare className="h-10 w-10 mx-auto mb-2 opacity-20" />
          <p>No feedback or recommendations yet</p>
          <p className="text-sm mt-1">Feedback from tutors will appear here</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default FeedbackAndRecommendations;

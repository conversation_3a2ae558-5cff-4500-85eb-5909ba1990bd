// src/store/notificationStore.ts
import { create } from "zustand";
import { supabase } from "@/lib/supabaseClient";
import { RealtimeChannel } from "@supabase/supabase-js";

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'billing' | 'academic' | 'system';
  is_read: boolean;
  is_system_managed?: boolean;
  notification_key?: string;
  created_at: string;
}

interface NotificationStore {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  subscription: RealtimeChannel | null;

  // Core actions
  fetchNotifications: (userId: string) => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: (userId: string) => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;

  // Real-time actions
  subscribeToNotifications: (userId: string) => void;
  unsubscribeFromNotifications: () => void;

  // Utility actions
  getUnreadCount: () => number;
  refreshUnreadCount: (userId: string) => Promise<void>;
  clearError: () => void;
  addNotification: (notification: Notification) => void;
}

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  subscription: null,

  fetchNotifications: async (userId) => {
    set({ isLoading: true, error: null });

    try {
      // Fetch notifications
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50); // Limit to recent 50 notifications

      if (error) throw error;

      // Get unread count using database function for better performance
      const { data: unreadCount, error: countError } = await supabase.rpc('get_unread_notification_count', {
        p_user_id: userId
      });

      if (countError) {
        console.error("Error getting unread count:", countError);
        // Fallback to client-side calculation with same logic as database function
        const fallbackCount = (data || []).filter(n => !n.is_read || n.is_system_managed).length;
        set({
          notifications: data || [],
          unreadCount: fallbackCount,
          isLoading: false
        });
      } else {
        set({
          notifications: data || [],
          unreadCount: unreadCount || 0,
          isLoading: false
        });
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      set({
        error: error instanceof Error ? error.message : "Failed to load notifications",
        isLoading: false
      });
    }
  },

  markAsRead: async (notificationId) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;

      // Update the notification in the store
      set(state => {
        const updatedNotifications = state.notifications.map(n =>
          n.id === notificationId ? { ...n, is_read: true } : n
        );

        // Count unread notifications + system-managed notifications (regardless of read status)
        const unreadCount = updatedNotifications.filter(n => !n.is_read || n.is_system_managed).length;

        return {
          notifications: updatedNotifications,
          unreadCount
        };
      });
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  },

  markAllAsRead: async (userId) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      if (error) throw error;

      // Update all notifications in the store
      set(state => {
        const updatedNotifications = state.notifications.map(n => ({ ...n, is_read: true }));
        // System-managed notifications still count even when marked as read
        const unreadCount = updatedNotifications.filter(n => n.is_system_managed).length;

        return {
          notifications: updatedNotifications,
          unreadCount
        };
      });
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  },

  deleteNotification: async (notificationId) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) throw error;

      // Remove the notification from the store
      set(state => {
        const updatedNotifications = state.notifications.filter(n => n.id !== notificationId);
        // Count unread notifications + system-managed notifications (regardless of read status)
        const unreadCount = updatedNotifications.filter(n => !n.is_read || n.is_system_managed).length;

        return {
          notifications: updatedNotifications,
          unreadCount
        };
      });
    } catch (error) {
      console.error("Error deleting notification:", error);
    }
  },

  // Real-time subscription methods
  subscribeToNotifications: (userId: string) => {
    const { subscription } = get();

    // Unsubscribe from existing subscription if any
    if (subscription) {
      subscription.unsubscribe();
    }

    // Create new subscription
    const newSubscription = supabase
      .channel(`notifications:${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          const newNotification = payload.new as Notification;
          get().addNotification(newNotification);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          const updatedNotification = payload.new as Notification;
          set(state => {
            const updatedNotifications = state.notifications.map(n =>
              n.id === updatedNotification.id ? updatedNotification : n
            );
            // Count unread notifications + system-managed notifications (regardless of read status)
            const unreadCount = updatedNotifications.filter(n => !n.is_read || n.is_system_managed).length;

            return {
              notifications: updatedNotifications,
              unreadCount
            };
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          const deletedId = payload.old.id;
          set(state => {
            const updatedNotifications = state.notifications.filter(n => n.id !== deletedId);
            // Count unread notifications + system-managed notifications (regardless of read status)
            const unreadCount = updatedNotifications.filter(n => !n.is_read || n.is_system_managed).length;

            return {
              notifications: updatedNotifications,
              unreadCount
            };
          });
        }
      )
      .subscribe();

    set({ subscription: newSubscription });
  },

  unsubscribeFromNotifications: () => {
    const { subscription } = get();
    if (subscription) {
      subscription.unsubscribe();
      set({ subscription: null });
    }
  },

  // Utility methods
  getUnreadCount: () => {
    // Count unread notifications + system-managed notifications (regardless of read status)
    return get().notifications.filter(n => !n.is_read || n.is_system_managed).length;
  },

  // Refresh unread count using database function
  refreshUnreadCount: async (userId: string) => {
    try {
      const { data, error } = await supabase.rpc('get_unread_notification_count', {
        p_user_id: userId
      });

      if (error) {
        console.error("Error refreshing unread count:", error);
        return;
      }

      set({ unreadCount: data || 0 });
    } catch (error) {
      console.error("Failed to refresh unread count:", error);
    }
  },

  clearError: () => {
    set({ error: null });
  },

  addNotification: (notification: Notification) => {
    set(state => ({
      notifications: [notification, ...state.notifications],
      unreadCount: state.unreadCount + (notification.is_read ? 0 : 1)
    }));
  }
}));
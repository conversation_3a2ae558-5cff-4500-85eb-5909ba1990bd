import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Avatar, AvatarFallback } from "@/components/ui/Avatar";
import { Edit, Plus, Award } from "lucide-react";

interface PerformanceProps {
  achievements: {
    id: string;
    title: string;
    organization: string;
    date: string;
  }[];
}

const Performance: React.FC<PerformanceProps> = ({ achievements }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center">
          <CardTitle className="text-xl">Performance</CardTitle>
          <span className="ml-2 text-sm text-gray-500">{achievements.length} records</span>
        </div>
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {achievements.map((achievement) => (
            <div key={achievement.id} className="flex">
              <Avatar className="h-10 w-10 mr-4 mt-1">
                <AvatarFallback className="bg-gray-100 text-gray-600">
                  <Award className="h-5 w-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-medium">{achievement.title}</h3>
                <p className="text-sm text-gray-600">{achievement.organization}</p>
                <div className="text-xs text-gray-500 mt-1">
                  {achievement.date}
                </div>
              </div>
              <div className="ml-auto">
                <div className="h-2 w-2 rounded-full bg-gray-200"></div>
              </div>
            </div>
          ))}
          
          {achievements.length === 0 && (
            <div className="text-center py-6 text-gray-500">
              <Award className="h-10 w-10 mx-auto mb-2 opacity-20" />
              <p>No achievements added yet</p>
              <Button variant="outline" size="sm" className="mt-2">
                <Plus className="h-4 w-4 mr-1" />
                Add Achievement
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default Performance;

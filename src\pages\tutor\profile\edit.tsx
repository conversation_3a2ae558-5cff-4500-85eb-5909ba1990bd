import React from "react";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Edit3, Al<PERSON><PERSON>riangle } from "lucide-react";

const EditProfilePage: React.FC = () => {
  return (
    <TutorPageLayout
      title="Edit Profile"
      description="Update your tutor profile information."
    >
      <Card className="border-2 border-dashed border-gray-300 bg-white">
        <CardHeader className="text-center pb-2">
          <CardTitle className="flex items-center justify-center gap-2 text-2xl font-bold text-gray-700">
            <Edit3 className="h-6 w-6 text-rfpurple-500" />
            Edit Profile
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-10">
          <div className="mx-auto max-w-md">
            <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Coming Soon</h3>
            <p className="text-gray-600 mb-6">
              We're currently building this feature to help you customize your tutor profile.
              You'll soon be able to update your personal information, qualifications,
              teaching preferences, and more.
            </p>
            <p className="text-gray-500 text-sm mb-6">
              In the meantime, you can view your current profile information on the main profile page.
            </p>
            <Button
              onClick={() => window.history.back()}
              className="bg-rfpurple-600 hover:bg-rfpurple-700"
            >
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </TutorPageLayout>
  );
};

export default EditProfilePage;

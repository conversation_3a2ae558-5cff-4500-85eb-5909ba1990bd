
import { Link } from "react-router-dom";
import {
  // Github,
  // Twitter,
  // Linkedin,
  Mail,
  MessageSquare,
  Phone,
  MapPin
} from "lucide-react";
import { ROUTES } from "@/routes/RouteConfig";

const Footer = () => {
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          <div className="col-span-1 md:col-span-1">
            <Link to="/" className="text-2xl font-bold bg-gradient-to-r from-rfpurple-600 to-rfpurple-500 bg-clip-text text-transparent">
              rfLearn
            </Link>
            <p className="mt-2 text-sm text-gray-500">
              Reinforcement-based learning platform combining AI and human expertise.
            </p>
            {/* TODO: Social media links temporarily hidden - will be configured later */}
            {/* <div className="flex space-x-4 mt-4">
              <a
                href="#"
                className="text-gray-400 hover:text-rfpurple-500"
                aria-label="Github"
              >
                <Github size={20} />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-rfpurple-500"
                aria-label="Twitter"
              >
                <Twitter size={20} />
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-rfpurple-500"
                aria-label="LinkedIn"
              >
                <Linkedin size={20} />
              </a>
            </div> */}
          </div>

          <div className="col-span-1">
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase">
              Platform
            </h3>
            <ul className="mt-4 space-y-2">
              <li>
                <Link to={ROUTES.COURSES.path} className="text-base text-gray-500 hover:text-rfpurple-500">
                  Courses
                </Link>
              </li>
              <li>
                <Link to={ROUTES.TUTORS.path} className="text-base text-gray-500 hover:text-rfpurple-500">
                  Tutors
                </Link>
              </li>
              <li>
                <Link to="/resources" className="text-base text-gray-500 hover:text-rfpurple-500">
                  Resources
                </Link>
              </li>
              <li>
                <Link to={ROUTES.PRICING.path} className="text-base text-gray-500 hover:text-rfpurple-500">
                  Pricing
                </Link>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase">
              Company
            </h3>
            <ul className="mt-4 space-y-2">
              <li>
                <Link to="/about" className="text-base text-gray-500 hover:text-rfpurple-500">
                  About
                </Link>
              </li>
              <li>
                <Link to="/careers" className="text-base text-gray-500 hover:text-rfpurple-500">
                  Careers
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-base text-gray-500 hover:text-rfpurple-500">
                  Privacy
                </Link>
              </li>
              <li>
                <Link to={ROUTES.TERMS.path} className="text-base text-gray-500 hover:text-rfpurple-500">
                  Terms
                </Link>
              </li>
            </ul>
          </div>

          <div className="col-span-1 md:col-span-2">
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase">
              Contact
            </h3>
            <ul className="mt-4 space-y-2">
              <li className="flex items-center">
                <Mail size={16} className="text-gray-400 mr-2" />
                <a href="mailto:<EMAIL>" className="text-base text-gray-500 hover:text-rfpurple-500">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center">
                <MessageSquare size={16} className="text-gray-400 mr-2" />
                <Link to="/inquiry" className="text-base text-gray-500 hover:text-rfpurple-500">
                  Send an inquiry
                </Link>
              </li>
              <li className="flex items-center">
                <MessageSquare size={16} className="text-gray-400 mr-2" />
                <Link to="/feedback" className="text-base text-gray-500 hover:text-rfpurple-500">
                  Leave feedback
                </Link>
              </li>
              <li className="flex items-center">
                <Phone size={16} className="text-gray-400 mr-2" />
                <a href="tel:+************" className="text-base text-gray-500 hover:text-rfpurple-500">
                  (+91) 8690845205
                </a>
              </li>
              <li className="flex items-start">
                <MapPin size={16} className="text-gray-400 mr-2 mt-1 flex-shrink-0" />
                <div className="text-base text-gray-500">
                  <div>D-213, 4th Floor, 244/6, Sangam Vihar,</div>
                  <div>Wazirabad, Delhi-110084, India</div>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-400 text-center">
            &copy; {new Date().getFullYear()} rfLearn. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

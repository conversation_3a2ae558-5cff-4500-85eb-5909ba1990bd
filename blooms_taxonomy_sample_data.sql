-- Sample data for <PERSON>'s Taxonomy integration

-- Assuming we have the following subtopics from the original schema
-- (These would normally be created by the original schema, but we're including them here for reference)
/*
INSERT INTO subjects (id, name, description) VALUES
('11111111-1111-1111-1111-111111111111', 'Mathematics', 'Mathematics curriculum covering algebra, calculus, and more');

INSERT INTO topics (id, subject_id, name, description) VALUES
('55555555-5555-5555-5555-555555555555', '11111111-1111-1111-1111-111111111111', 'Calculus', 'Differential and integral calculus');

INSERT INTO subtopics (id, topic_id, name, description, state_standard) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '55555555-5555-5555-5555-555555555555', 'Limits and Continuity', 'Understanding limits and continuity of functions', 'MTK.CC.1'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '55555555-5555-5555-5555-555555555555', 'Derivatives', 'Differentiation rules and applications', 'MTK.CC.2'),
('cccccccc-cccc-cccc-cccc-cccccccccccc', '55555555-5555-5555-5555-555555555555', 'Integration', 'Integration techniques and applications', 'MTK.CC.3');
*/

-- Update subtopics with primary taxonomy levels
UPDATE subtopics 
SET primary_taxonomy_level_id = (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Comprehension')
WHERE id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'; -- Limits and Continuity

UPDATE subtopics 
SET primary_taxonomy_level_id = (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application')
WHERE id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'; -- Derivatives

UPDATE subtopics 
SET primary_taxonomy_level_id = (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Analysis')
WHERE id = 'cccccccc-cccc-cccc-cccc-cccccccccccc'; -- Integration

-- Add learning objectives for Limits and Continuity (Knowledge and Comprehension levels)
INSERT INTO learning_objectives (subtopic_id, taxonomy_level_id, objective_text, display_order) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Knowledge'),
 'Define the concept of a limit', 1),
 
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Knowledge'),
 'List the properties of limits', 2),
 
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Comprehension'),
 'Explain the relationship between limits and continuity', 3),
 
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Comprehension'),
 'Interpret the meaning of a limit graphically', 4);

-- Add learning objectives for Derivatives (Application and Analysis levels)
INSERT INTO learning_objectives (subtopic_id, taxonomy_level_id, objective_text, display_order) VALUES
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Knowledge'),
 'State the definition of a derivative', 1),
 
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Comprehension'),
 'Explain the relationship between derivatives and rates of change', 2),
 
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'),
 'Apply differentiation rules to find derivatives of various functions', 3),
 
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Analysis'),
 'Analyze the behavior of a function using its derivatives', 4);

-- Add learning objectives for Integration (Analysis, Synthesis, and Evaluation levels)
INSERT INTO learning_objectives (subtopic_id, taxonomy_level_id, objective_text, display_order) VALUES
('cccccccc-cccc-cccc-cccc-cccccccccccc', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Knowledge'),
 'Recall basic integration formulas', 1),
 
('cccccccc-cccc-cccc-cccc-cccccccccccc', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'),
 'Apply integration techniques to solve problems', 2),
 
('cccccccc-cccc-cccc-cccc-cccccccccccc', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Analysis'),
 'Analyze which integration technique is most appropriate for a given problem', 3),
 
('cccccccc-cccc-cccc-cccc-cccccccccccc', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Synthesis'),
 'Develop a strategy for solving complex integration problems', 4),
 
('cccccccc-cccc-cccc-cccc-cccccccccccc', 
 (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Evaluation'),
 'Evaluate the accuracy and efficiency of different integration methods', 5);

-- Create sample assessments for each subtopic
-- Limits and Continuity Assessment
INSERT INTO assessments (subtopic_id, assessment_type_id, title, description, instructions, duration_min, passing_score) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
 (SELECT id FROM assessment_types WHERE name = 'Multiple Choice Quiz'),
 'Limits and Continuity Quiz',
 'Basic quiz to test understanding of limits and continuity concepts',
 'Answer all questions. Each question is worth 5 points.',
 30,
 70.00);

-- Get the ID of the assessment we just created
DO $$
DECLARE
    limits_assessment_id UUID;
BEGIN
    SELECT id INTO limits_assessment_id FROM assessments 
    WHERE title = 'Limits and Continuity Quiz';
    
    -- Add questions to the Limits assessment
    INSERT INTO assessment_questions (assessment_id, question_text, question_type, taxonomy_level_id, points, display_order) VALUES
    (limits_assessment_id,
     'What is the definition of the limit of a function f(x) as x approaches a?',
     'multiple_choice',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Knowledge'),
     5,
     1),
     
    (limits_assessment_id,
     'Explain why the function f(x) = (x² - 1)/(x - 1) is not continuous at x = 1.',
     'short_answer',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Comprehension'),
     10,
     2),
     
    (limits_assessment_id,
     'Calculate the limit: lim(x→0) (sin x)/x',
     'multiple_choice',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'),
     5,
     3);
END $$;

-- Derivatives Assessment
INSERT INTO assessments (subtopic_id, assessment_type_id, title, description, instructions, duration_min, passing_score) VALUES
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
 (SELECT id FROM assessment_types WHERE name = 'Problem Set'),
 'Derivatives Problem Set',
 'Collection of problems to test application of differentiation rules',
 'Solve all problems. Show your work for full credit.',
 45,
 75.00);

-- Get the ID of the assessment we just created
DO $$
DECLARE
    derivatives_assessment_id UUID;
BEGIN
    SELECT id INTO derivatives_assessment_id FROM assessments 
    WHERE title = 'Derivatives Problem Set';
    
    -- Add questions to the Derivatives assessment
    INSERT INTO assessment_questions (assessment_id, question_text, question_type, taxonomy_level_id, points, display_order) VALUES
    (derivatives_assessment_id,
     'Find the derivative of f(x) = x³ - 2x² + 4x - 7.',
     'short_answer',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'),
     10,
     1),
     
    (derivatives_assessment_id,
     'Use the chain rule to find the derivative of g(x) = sin(x²).',
     'short_answer',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'),
     15,
     2),
     
    (derivatives_assessment_id,
     'Analyze the function f(x) = x³ - 3x² + 2 to find its critical points, intervals of increase/decrease, and concavity.',
     'essay',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Analysis'),
     25,
     3);
END $$;

-- Integration Assessment
INSERT INTO assessments (subtopic_id, assessment_type_id, title, description, instructions, duration_min, passing_score) VALUES
('cccccccc-cccc-cccc-cccc-cccccccccccc', 
 (SELECT id FROM assessment_types WHERE name = 'Case Study Analysis'),
 'Integration Techniques Analysis',
 'In-depth analysis of various integration techniques and their applications',
 'Complete all sections. Part 1: Solve the problems. Part 2: Analyze the techniques used.',
 60,
 80.00);

-- Get the ID of the assessment we just created
DO $$
DECLARE
    integration_assessment_id UUID;
BEGIN
    SELECT id INTO integration_assessment_id FROM assessments 
    WHERE title = 'Integration Techniques Analysis';
    
    -- Add questions to the Integration assessment
    INSERT INTO assessment_questions (assessment_id, question_text, question_type, taxonomy_level_id, points, display_order) VALUES
    (integration_assessment_id,
     'Evaluate the integral: ∫(x² + 2x + 1) dx',
     'short_answer',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'),
     10,
     1),
     
    (integration_assessment_id,
     'Use integration by parts to evaluate: ∫x·ln(x) dx',
     'short_answer',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application'),
     15,
     2),
     
    (integration_assessment_id,
     'Compare and contrast the techniques of substitution, integration by parts, and partial fractions. When is each most appropriate?',
     'essay',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Analysis'),
     20,
     3),
     
    (integration_assessment_id,
     'Design a strategy for approaching complex integration problems. Create a decision tree to guide the selection of appropriate techniques.',
     'essay',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Synthesis'),
     25,
     4),
     
    (integration_assessment_id,
     'Evaluate the efficiency of different integration techniques for the given set of problems. Justify your reasoning.',
     'essay',
     (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Evaluation'),
     30,
     5);
END $$;

-- Update resources with taxonomy levels
UPDATE resources 
SET taxonomy_level_id = (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Knowledge')
WHERE name = 'Integration Basics';

UPDATE resources 
SET taxonomy_level_id = (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application')
WHERE name = 'Integration by Parts Video';

UPDATE resources 
SET taxonomy_level_id = (SELECT id FROM bloom_taxonomy_levels WHERE level_name = 'Application')
WHERE name = 'Integration Practice Problems';

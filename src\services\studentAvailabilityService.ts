import { supabase } from '@/lib/supabaseClient';

// Types matching the database schema
export interface StudentAvailabilitySlot {
  id: string;
  student_id: string;
  day_of_week: number; // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  status: 'available' | 'preferred' | 'unavailable';
  created_at: string;
  updated_at: string;
  timezone?: string;
}

// Helper functions for day conversion
export const dayNameToDayOfWeek = (dayName: string): number => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days.indexOf(dayName);
};

export const dayOfWeekToDayName = (dayOfWeek: number): string => {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayOfWeek] || 'Monday';
};

// Transform functions between UI and database formats
export const transformStudentAvailabilitySlotForUI = (dbSlot: StudentAvailabilitySlot) => {
  // Strip seconds from time strings to match UI format (HH:MM)
  const formatTime = (timeStr: string) => {
    if (timeStr.includes(':')) {
      const parts = timeStr.split(':');
      return `${parts[0]}:${parts[1]}`;
    }
    return timeStr;
  };

  return {
    id: dbSlot.id,
    day: dayOfWeekToDayName(dbSlot.day_of_week),
    startTime: formatTime(dbSlot.start_time),
    endTime: formatTime(dbSlot.end_time),
    status: dbSlot.status
  };
};

export const transformStudentAvailabilitySlotForDB = (uiSlot: any, studentId: string) => {
  return {
    student_id: studentId,
    day_of_week: dayNameToDayOfWeek(uiSlot.day),
    start_time: uiSlot.startTime,
    end_time: uiSlot.endTime,
    status: uiSlot.status,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  };
};

// Student Availability CRUD operations
export const studentAvailabilityService = {
  // Fetch all availability slots for a student
  async fetchAvailabilitySlots(studentId: string): Promise<StudentAvailabilitySlot[]> {
    const { data, error } = await supabase
      .from('student_availability')
      .select('*')
      .eq('student_id', studentId)
      .order('day_of_week', { ascending: true })
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching student availability slots:', error);
      throw new Error(`Failed to fetch availability slots: ${error.message}`);
    }

    return data || [];
  },

  // Create a new availability slot
  async createAvailabilitySlot(slot: {
    student_id: string;
    day_of_week: number;
    start_time: string;
    end_time: string;
    status: 'available' | 'preferred' | 'unavailable';
    timezone?: string;
  }): Promise<StudentAvailabilitySlot> {
    const { data, error } = await supabase
      .from('student_availability')
      .insert([slot])
      .select()
      .single();

    if (error) {
      console.error('Error creating student availability slot:', error);
      throw new Error(`Failed to create availability slot: ${error.message}`);
    }

    return data;
  },

  // Update an existing availability slot
  async updateAvailabilitySlot(id: string, updates: Partial<{
    student_id: string;
    day_of_week: number;
    start_time: string;
    end_time: string;
    status: 'available' | 'preferred' | 'unavailable';
    timezone?: string;
  }>): Promise<StudentAvailabilitySlot> {
    const { data, error } = await supabase
      .from('student_availability')
      .update(updates) // Let the database handle updated_at automatically
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating student availability slot:', error);
      throw new Error(`Failed to update availability slot: ${error.message}`);
    }

    return data;
  },

  // Delete an availability slot
  async deleteAvailabilitySlot(id: string): Promise<void> {
    const { error } = await supabase
      .from('student_availability')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting student availability slot:', error);
      throw new Error(`Failed to delete availability slot: ${error.message}`);
    }
  },

  // Bulk update availability slots (useful for saving multiple changes at once)
  async bulkUpdateAvailabilitySlots(slots: StudentAvailabilitySlot[]): Promise<StudentAvailabilitySlot[]> {
    const { data, error } = await supabase
      .from('student_availability')
      .upsert(slots.map(slot => ({ ...slot, updated_at: new Date().toISOString() })))
      .select();

    if (error) {
      console.error('Error bulk updating student availability slots:', error);
      throw new Error(`Failed to bulk update availability slots: ${error.message}`);
    }

    return data || [];
  }
};

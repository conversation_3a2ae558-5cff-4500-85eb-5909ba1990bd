import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/Dialog";
import { But<PERSON> } from "@/components/ui/Button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/Tabs";
import { Badge } from "@/components/ui/Badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Checkbox } from "@/components/ui/Checkbox";
import { ScrollArea } from "@/components/ui/ScrollArea";
import { BookOpen, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { useSubscriptionCurriculumStore } from "@/store/subscriptionCurriculumStore";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/useToast";

interface CurriculumConfigurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  subscriptionId: string;
  productType: 'booster' | 'custom' | 'preparation';
  productName: string;
}

const CurriculumConfigurationModal: React.FC<CurriculumConfigurationModalProps> = ({
  isOpen,
  onClose,
  subscriptionId,
  productType,
  productName
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const {
    availableSubjects,
    availableTopics,
    availableSubtopics,
    isLoading,
    error,
    fetchAvailableSubjects,
    fetchAvailableTopics,
    fetchAvailableSubtopics,
    configureCurriculum,
    getCurriculumConfig
  } = useSubscriptionCurriculumStore();

  // State for selections
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [selectedSubtopics, setSelectedSubtopics] = useState<string[]>([]);
  const [selectedSubjectForTopics, setSelectedSubjectForTopics] = useState<string>('');
  const [selectedTopicForSubtopics, setSelectedTopicForSubtopics] = useState<string>('');
  const [isConfiguring, setIsConfiguring] = useState(false);

  // Load existing configuration if any
  useEffect(() => {
    if (isOpen) {
      const existingConfig = getCurriculumConfig(subscriptionId);
      if (existingConfig) {
        setSelectedSubjects(existingConfig.subjects.map(s => s.id));
        setSelectedTopics(existingConfig.topics.map(t => t.id));
        setSelectedSubtopics(existingConfig.subtopics.map(s => s.id));
      }
    }
  }, [isOpen, subscriptionId, getCurriculumConfig]);

  // Fetch available data when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchAvailableSubjects();
    }
  }, [isOpen, fetchAvailableSubjects]);

  // Fetch topics when subject is selected (for custom configuration)
  useEffect(() => {
    if (selectedSubjectForTopics) {
      fetchAvailableTopics(selectedSubjectForTopics);
    }
  }, [selectedSubjectForTopics, fetchAvailableTopics]);

  // Fetch subtopics when topic is selected (for custom configuration)
  useEffect(() => {
    if (selectedTopicForSubtopics) {
      fetchAvailableSubtopics(selectedTopicForSubtopics);
    }
  }, [selectedTopicForSubtopics, fetchAvailableSubtopics]);

  // Handle subject selection (for booster)
  const handleSubjectToggle = (subjectId: string) => {
    setSelectedSubjects(prev => 
      prev.includes(subjectId) 
        ? prev.filter(id => id !== subjectId)
        : [...prev, subjectId]
    );
  };

  // Handle topic selection (for custom)
  const handleTopicToggle = (topicId: string) => {
    setSelectedTopics(prev => 
      prev.includes(topicId) 
        ? prev.filter(id => id !== topicId)
        : [...prev, topicId]
    );
  };

  // Handle subtopic selection (for custom)
  const handleSubtopicToggle = (subtopicId: string) => {
    setSelectedSubtopics(prev => 
      prev.includes(subtopicId) 
        ? prev.filter(id => id !== subtopicId)
        : [...prev, subtopicId]
    );
  };

  // Handle configuration save
  const handleSave = async () => {
    if (!user?.id) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive"
      });
      return;
    }

    setIsConfiguring(true);

    try {
      let configData;

      if (productType === 'booster') {
        if (selectedSubjects.length === 0) {
          toast({
            title: "Selection Required",
            description: "Please select at least one subject for your booster package",
            variant: "destructive"
          });
          setIsConfiguring(false);
          return;
        }
        configData = {
          product_type: productType,
          subjects: selectedSubjects
        };
      } else if (productType === 'custom') {
        if (selectedTopics.length === 0 && selectedSubtopics.length === 0) {
          toast({
            title: "Selection Required",
            description: "Please select at least one topic or subtopic for your custom package",
            variant: "destructive"
          });
          setIsConfiguring(false);
          return;
        }
        configData = {
          product_type: productType,
          topics: selectedTopics,
          subtopics: selectedSubtopics
        };
      } else {
        // preparation - similar to custom for now
        configData = {
          product_type: productType,
          topics: selectedTopics,
          subtopics: selectedSubtopics
        };
      }

      const success = await configureCurriculum(subscriptionId, configData, user.id);

      if (success) {
        toast({
          title: "Configuration Saved",
          description: "Your curriculum has been configured successfully",
          variant: "default"
        });
        onClose();
      } else {
        toast({
          title: "Configuration Failed",
          description: error || "Failed to save curriculum configuration",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Configuration Failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsConfiguring(false);
    }
  };

  // Reset selections when modal closes
  const handleClose = () => {
    setSelectedSubjects([]);
    setSelectedTopics([]);
    setSelectedSubtopics([]);
    setSelectedSubjectForTopics('');
    setSelectedTopicForSubtopics('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <BookOpen className="h-5 w-5 mr-2" />
            Configure Curriculum - {productName}
          </DialogTitle>
          <DialogDescription>
            {productType === 'booster' 
              ? "Select the subjects you want to include in your booster package. All topics and subtopics for selected subjects will be included."
              : "Select specific topics and subtopics for your custom learning package."
            }
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh]">
          {productType === 'booster' ? (
            // Booster Configuration - Subject Selection
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Select Subjects</h3>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  Loading subjects...
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableSubjects.map((subject) => (
                    <Card key={subject.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{subject.name}</CardTitle>
                          <Checkbox
                            checked={selectedSubjects.includes(subject.id)}
                            onCheckedChange={() => handleSubjectToggle(subject.id)}
                          />
                        </div>
                        {subject.description && (
                          <CardDescription className="text-sm">
                            {subject.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      <CardContent className="pt-0">
                        <Badge variant="secondary" className="text-xs">
                          Complete Subject
                        </Badge>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ) : (
            // Custom Configuration - Topic/Subtopic Selection
            <Tabs defaultValue="topics" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="topics">Select Topics</TabsTrigger>
                <TabsTrigger value="subtopics">Select Subtopics</TabsTrigger>
              </TabsList>

              <TabsContent value="topics" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Choose Subject:</label>
                    <select
                      className="w-full mt-1 p-2 border rounded-md"
                      value={selectedSubjectForTopics}
                      onChange={(e) => setSelectedSubjectForTopics(e.target.value)}
                    >
                      <option value="">Select a subject</option>
                      {availableSubjects.map((subject) => (
                        <option key={subject.id} value={subject.id}>
                          {subject.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {selectedSubjectForTopics && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {availableTopics.map((topic) => (
                        <Card key={topic.id} className="cursor-pointer hover:shadow-md transition-shadow">
                          <CardHeader className="pb-2">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-base">{topic.name}</CardTitle>
                              <Checkbox
                                checked={selectedTopics.includes(topic.id)}
                                onCheckedChange={() => handleTopicToggle(topic.id)}
                              />
                            </div>
                            {topic.description && (
                              <CardDescription className="text-sm">
                                {topic.description}
                              </CardDescription>
                            )}
                          </CardHeader>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="subtopics" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Choose Topic:</label>
                    <select
                      className="w-full mt-1 p-2 border rounded-md"
                      value={selectedTopicForSubtopics}
                      onChange={(e) => setSelectedTopicForSubtopics(e.target.value)}
                    >
                      <option value="">Select a topic</option>
                      {availableTopics.map((topic) => (
                        <option key={topic.id} value={topic.id}>
                          {topic.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {selectedTopicForSubtopics && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {availableSubtopics.map((subtopic) => (
                        <Card key={subtopic.id} className="cursor-pointer hover:shadow-md transition-shadow">
                          <CardHeader className="pb-2">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-base">{subtopic.name}</CardTitle>
                              <Checkbox
                                checked={selectedSubtopics.includes(subtopic.id)}
                                onCheckedChange={() => handleSubtopicToggle(subtopic.id)}
                              />
                            </div>
                            {subtopic.description && (
                              <CardDescription className="text-sm">
                                {subtopic.description}
                              </CardDescription>
                            )}
                          </CardHeader>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          )}

          {/* Selection Summary */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Selection Summary:</h4>
            <div className="space-y-1 text-sm">
              {productType === 'booster' && (
                <p>Subjects selected: {selectedSubjects.length}</p>
              )}
              {productType !== 'booster' && (
                <>
                  <p>Topics selected: {selectedTopics.length}</p>
                  <p>Subtopics selected: {selectedSubtopics.length}</p>
                </>
              )}
            </div>
          </div>
        </ScrollArea>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isConfiguring}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isConfiguring}>
            {isConfiguring ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Save Configuration
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CurriculumConfigurationModal;

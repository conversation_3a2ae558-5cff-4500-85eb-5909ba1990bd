import React from "react";
import { useAuth } from "@/context/AuthContext";
import {
  Users,
  BookOpen,
  MessageSquare,
  <PERSON>tings,
  Bar<PERSON>hart,
  Calendar,
} from "lucide-react";
import AdminPageLayout from "@/components/layouts/AdminPageLayout";

const AdminDashboard = () => {
  const { user } = useAuth();

  return (
    <AdminPageLayout
      title="Admin Dashboard"
      actions={
        <span className="bg-rfpurple-100 text-rfpurple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
          Admin Portal
        </span>
      }
    >

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Users"
          value="845"
          icon={<Users className="h-6 w-6" />}
          description="Active user accounts"
          change="+12.5% from last month"
          positive={true}
        />
        <StatCard
          title="Active Tutors"
          value="123"
          icon={<BookOpen className="h-6 w-6" />}
          description="Verified tutors"
          change="+4.3% from last month"
          positive={true}
        />
        <StatCard
          title="Total Sessions"
          value="2456"
          icon={<Calendar className="h-6 w-6" />}
          description="Completed sessions"
          change="+18.2% from last month"
          positive={true}
        />
        <StatCard
          title="Pending Inquiries"
          value="34"
          icon={<MessageSquare className="h-6 w-6" />}
          description="Waiting for response"
          change="-2.5% from last month"
          positive={false}
        />
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Management */}
        <div className="lg:col-span-2 bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">User Management</h2>
            <button className="text-sm text-rfpurple-600 hover:text-rfpurple-800">
              View All
            </button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {[
                  {
                    name: "Jane Cooper",
                    role: "Student",
                    status: "Active",
                  },
                  {
                    name: "Michael Scott",
                    role: "Tutor",
                    status: "Active",
                  },
                  {
                    name: "Alex Johnson",
                    role: "Student",
                    status: "Inactive",
                  },
                  {
                    name: "Sarah Williams",
                    role: "Tutor",
                    status: "Active",
                  },
                ].map((user, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {user.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {user.role}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.status === "Active"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button className="text-rfpurple-600 hover:text-rfpurple-900 mr-3">
                        Edit
                      </button>
                      <button className="text-red-600 hover:text-red-900">
                        Disable
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
          <div className="space-y-4">
            {[
              { action: "New user registered", time: "2 minutes ago" },
              { action: "Session completed", time: "1 hour ago" },
              { action: "New inquiry submitted", time: "3 hours ago" },
              { action: "Tutor profile updated", time: "5 hours ago" },
              { action: "Payment processed", time: "Yesterday" },
            ].map((activity, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 h-4 w-4 rounded-full bg-rfpurple-500 mt-1"></div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.action}
                  </p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Additional Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        {/* Inquiries Overview */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Inquiries Overview</h2>
            <button className="text-sm text-rfpurple-600 hover:text-rfpurple-800">
              Manage Inquiries
            </button>
          </div>
          <div className="space-y-3">
            {[
              {
                topic: "Machine Learning Help",
                status: "New",
                date: "Today",
              },
              {
                topic: "Python Tutoring",
                status: "In Progress",
                date: "Yesterday",
              },
              {
                topic: "Data Structures",
                status: "New",
                date: "Yesterday",
              },
            ].map((inquiry, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 bg-gray-50 rounded-md"
              >
                <div>
                  <p className="font-medium text-gray-800">
                    {inquiry.topic}
                  </p>
                  <p className="text-sm text-gray-500">{inquiry.date}</p>
                </div>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    inquiry.status === "New"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-yellow-100 text-yellow-800"
                  }`}
                >
                  {inquiry.status}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-2 gap-4">
            <ActionCard
              title="Add New User"
              icon={<Users className="h-5 w-5" />}
              onClick={() => console.log("Add user")}
            />
            <ActionCard
              title="Content Library"
              icon={<BookOpen className="h-5 w-5" />}
              onClick={() => console.log("Content library")}
            />
            <ActionCard
              title="System Settings"
              icon={<Settings className="h-5 w-5" />}
              onClick={() => console.log("Settings")}
            />
            <ActionCard
              title="View Reports"
              icon={<BarChart className="h-5 w-5" />}
              onClick={() => console.log("Reports")}
            />
          </div>
        </div>
      </div>
    </AdminPageLayout>
  );
};

// Helper components
const StatCard = ({ title, value, icon, description, change, positive }) => (
  <div className="bg-white p-6 rounded-lg shadow-md">
    <div className="flex justify-between items-center">
      <div>
        <p className="text-sm text-gray-500">{title}</p>
        <p className="text-3xl font-bold">{value}</p>
        <p className="text-sm text-gray-500">{description}</p>
        <p
          className={`text-sm ${positive ? "text-green-500" : "text-red-500"}`}
        >
          {change}
        </p>
      </div>
      <div className="text-rfpurple-500">{icon}</div>
    </div>
  </div>
);

const ActionCard = ({ title, icon, onClick }) => (
  <button
    onClick={onClick}
    className="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
  >
    <div className="rounded-full bg-rfpurple-100 p-3 mb-2">
      {React.cloneElement(icon, { className: "text-rfpurple-600" })}
    </div>
    <span className="text-sm font-medium text-gray-700">{title}</span>
  </button>
);

export default AdminDashboard;

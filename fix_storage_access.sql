-- =====================================================
-- QUICK FIX FOR PROFILE PICTURE DISPLAY ISSUE
-- =====================================================

-- This script fixes the CORS/access issue with profile pictures
-- Run this in your Supabase SQL Editor

-- =====================================================
-- STEP 1: CREATE OR UPDATE BUCKET
-- =====================================================

-- Create student-uploads bucket with public access
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'student-uploads',
    'student-uploads',
    true, -- This is crucial - makes the bucket public
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
    public = true, -- Ensure it's public
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- =====================================================
-- STEP 2: DROP EXISTING CONFLICTING POLICIES
-- =====================================================

-- Remove any existing policies that might be blocking access
DROP POLICY IF EXISTS "Students can upload their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Public can view student uploads" ON storage.objects;
DROP POLICY IF EXISTS "Admins can manage all student uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete" ON storage.objects;

-- =====================================================
-- STEP 3: CREATE SIMPLE PUBLIC POLICIES
-- =====================================================

-- Policy 1: Allow authenticated users to upload
CREATE POLICY "Allow authenticated uploads to student-uploads" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Policy 2: Allow EVERYONE to read (this fixes the display issue)
CREATE POLICY "Allow public read access to student-uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'student-uploads'
    );

-- Policy 3: Allow authenticated users to update their files
CREATE POLICY "Allow authenticated update to student-uploads" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Policy 4: Allow authenticated users to delete their files
CREATE POLICY "Allow authenticated delete from student-uploads" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- =====================================================
-- STEP 4: GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA storage TO authenticated, anon;
GRANT ALL ON storage.objects TO authenticated;
GRANT SELECT ON storage.objects TO anon; -- This is crucial for public read
GRANT ALL ON storage.buckets TO authenticated;
GRANT SELECT ON storage.buckets TO anon;

-- =====================================================
-- STEP 5: VERIFICATION
-- =====================================================

-- Check bucket configuration
SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'student-uploads';

-- Check policies
SELECT 
    policyname,
    cmd as command,
    permissive,
    roles
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects'
AND policyname LIKE '%student-uploads%'
ORDER BY policyname;

-- =====================================================
-- EXPECTED RESULTS
-- =====================================================

/*
After running this script, you should see:

1. Bucket 'student-uploads' with public = true
2. Four policies for the student-uploads bucket
3. Profile pictures should load immediately in the UI

If you still have issues:
1. Check the browser Network tab for 403/404 errors
2. Verify the image URL in browser directly
3. Check Supabase Storage dashboard for the bucket
*/

-- Migration Script: Enhance Existing Invoices Table for Stripe Integration
-- This script adds Stripe-related columns to your existing invoices table
-- while preserving all existing data and functionality

-- =====================================================
-- INVOICES TABLE ENHANCEMENT
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Starting invoices table enhancement for Stripe integration...';
    
    -- Add new columns for enhanced invoice management
    ALTER TABLE invoices 
    ADD COLUMN IF NOT EXISTS subscription_id UUID,
    ADD COLUMN IF NOT EXISTS stripe_invoice_id TEXT,
    ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
    ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT,
    ADD COLUMN IF NOT EXISTS invoice_number TEXT,
    ADD COLUMN IF NOT EXISTS amount_due DECIMAL(10,2),
    ADD COLUMN IF NOT EXISTS amount_paid DECIMAL(10,2) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS amount_remaining DECIMAL(10,2) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'usd',
    ADD COLUMN IF NOT EXISTS due_date TIMESTAMP,
    ADD COLUMN IF NOT EXISTS paid_at TIMESTAMP,
    ADD COLUMN IF NOT EXISTS invoice_pdf_url TEXT,
    ADD COLUMN IF NOT EXISTS hosted_invoice_url TEXT,
    ADD COLUMN IF NOT EXISTS receipt_number TEXT,
    ADD COLUMN IF NOT EXISTS stripe_created_at TIMESTAMP;
    
    RAISE NOTICE 'Added new columns to invoices table.';
    
    -- Add foreign key constraint for subscription_id if subscriptions table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscriptions') THEN
        ALTER TABLE invoices 
        ADD CONSTRAINT IF NOT EXISTS invoices_subscription_id_fkey 
        FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key constraint for subscription_id.';
    END IF;
    
    -- Add unique constraint for stripe_invoice_id
    ALTER TABLE invoices 
    ADD CONSTRAINT IF NOT EXISTS invoices_stripe_invoice_id_unique 
    UNIQUE (stripe_invoice_id);
    
    RAISE NOTICE 'Added unique constraint for stripe_invoice_id.';
    
    -- Update existing data to populate new columns
    UPDATE invoices 
    SET 
        amount_due = COALESCE(amount_due, amount),
        amount_paid = COALESCE(amount_paid, CASE WHEN status = 'paid' THEN amount ELSE 0 END),
        amount_remaining = COALESCE(amount_remaining, CASE WHEN status = 'paid' THEN 0 ELSE amount END),
        currency = COALESCE(currency, 'usd'),
        paid_at = COALESCE(paid_at, CASE WHEN status = 'paid' THEN updated_at END)
    WHERE amount_due IS NULL OR amount_paid IS NULL OR amount_remaining IS NULL;
    
    RAISE NOTICE 'Updated existing invoice records with calculated values.';
    
    -- Update status check constraint to include new Stripe statuses
    ALTER TABLE invoices DROP CONSTRAINT IF EXISTS invoices_status_check;
    ALTER TABLE invoices ADD CONSTRAINT invoices_status_check 
    CHECK (status IN ('pending', 'paid', 'failed', 'refunded', 'draft', 'open', 'uncollectible', 'void'));
    
    RAISE NOTICE 'Updated status check constraint to include Stripe statuses.';
    
END $$;

-- =====================================================
-- ADD INDEXES FOR PERFORMANCE
-- =====================================================

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_invoices_subscription_id ON invoices(subscription_id);
CREATE INDEX IF NOT EXISTS idx_invoices_workflow_id ON invoices(workflow_id);
CREATE INDEX IF NOT EXISTS idx_invoices_stripe_invoice_id ON invoices(stripe_invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoices_stripe_customer_id ON invoices(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);
CREATE INDEX IF NOT EXISTS idx_invoices_paid_at ON invoices(paid_at);

-- =====================================================
-- ADD TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Create or replace function to update timestamps
CREATE OR REPLACE FUNCTION update_invoice_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    
    -- Automatically set paid_at when status changes to paid
    IF NEW.status = 'paid' AND OLD.status != 'paid' THEN
        NEW.paid_at = now();
        NEW.amount_paid = NEW.amount;
        NEW.amount_remaining = 0;
    END IF;
    
    -- Reset paid_at if status changes from paid to something else
    IF NEW.status != 'paid' AND OLD.status = 'paid' THEN
        NEW.paid_at = NULL;
        NEW.amount_paid = 0;
        NEW.amount_remaining = NEW.amount;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger for automatic timestamp and payment status updates
DROP TRIGGER IF EXISTS update_invoices_timestamp ON invoices;
CREATE TRIGGER update_invoices_timestamp 
BEFORE UPDATE ON invoices 
FOR EACH ROW EXECUTE FUNCTION update_invoice_timestamp();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to link invoice to subscription based on workflow
CREATE OR REPLACE FUNCTION link_invoice_to_subscription(invoice_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_record UUID;
BEGIN
    -- Find subscription based on workflow_id
    SELECT s.id INTO subscription_record
    FROM invoices i
    JOIN subscription_workflows sw ON i.workflow_id = sw.id
    JOIN subscriptions s ON sw.id = s.workflow_id
    WHERE i.id = invoice_id;
    
    -- Update invoice with subscription_id
    IF subscription_record IS NOT NULL THEN
        UPDATE invoices 
        SET subscription_id = subscription_record
        WHERE id = invoice_id;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to generate invoice number
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    invoice_number TEXT;
BEGIN
    -- Get next invoice number (simple sequential)
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM '[0-9]+') AS INTEGER)), 0) + 1
    INTO next_number
    FROM invoices 
    WHERE invoice_number IS NOT NULL 
    AND invoice_number ~ '^INV-[0-9]+$';
    
    -- Format as INV-000001
    invoice_number := 'INV-' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN invoice_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update invoice number for existing records
CREATE OR REPLACE FUNCTION update_missing_invoice_numbers()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    invoice_record RECORD;
BEGIN
    -- Update invoices that don't have invoice numbers
    FOR invoice_record IN 
        SELECT id FROM invoices WHERE invoice_number IS NULL
    LOOP
        UPDATE invoices 
        SET invoice_number = generate_invoice_number()
        WHERE id = invoice_record.id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- DATA MIGRATION AND CLEANUP
-- =====================================================

DO $$
DECLARE
    updated_invoices INTEGER;
    linked_subscriptions INTEGER := 0;
    invoice_record RECORD;
BEGIN
    RAISE NOTICE 'Starting data migration and cleanup...';
    
    -- Generate invoice numbers for existing invoices
    SELECT update_missing_invoice_numbers() INTO updated_invoices;
    RAISE NOTICE 'Generated invoice numbers for % invoices.', updated_invoices;
    
    -- Link existing invoices to subscriptions where possible
    FOR invoice_record IN 
        SELECT id FROM invoices WHERE subscription_id IS NULL AND workflow_id IS NOT NULL
    LOOP
        IF link_invoice_to_subscription(invoice_record.id) THEN
            linked_subscriptions := linked_subscriptions + 1;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Linked % invoices to subscriptions.', linked_subscriptions;
    
    RAISE NOTICE 'Data migration completed successfully.';
END $$;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    total_invoices INTEGER;
    invoices_with_numbers INTEGER;
    invoices_with_subscriptions INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_invoices FROM invoices;
    SELECT COUNT(*) INTO invoices_with_numbers FROM invoices WHERE invoice_number IS NOT NULL;
    SELECT COUNT(*) INTO invoices_with_subscriptions FROM invoices WHERE subscription_id IS NOT NULL;
    
    RAISE NOTICE 'Migration verification:';
    RAISE NOTICE '- Total invoices: %', total_invoices;
    RAISE NOTICE '- Invoices with numbers: %', invoices_with_numbers;
    RAISE NOTICE '- Invoices linked to subscriptions: %', invoices_with_subscriptions;
    
    IF total_invoices = invoices_with_numbers THEN
        RAISE NOTICE '✓ All invoices have invoice numbers';
    ELSE
        RAISE WARNING '⚠ Some invoices missing invoice numbers';
    END IF;
END $$;

RAISE NOTICE 'Invoices table enhancement completed successfully!';

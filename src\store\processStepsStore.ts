import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface ProcessStep {
  id: string;
  label: string;
  status: "pending" | "loading" | "complete" | "error";
}

interface ProcessStepsState {
  // State
  isOpen: boolean;
  title: string;
  steps: ProcessStep[];
  timeoutId: NodeJS.Timeout | null;
  timedOut: boolean;
  persistedState: boolean;
  preventReset: boolean;

  // Actions
  open: () => void;
  close: () => void;
  setTitle: (title: string) => void;
  setSteps: (steps: ProcessStep[]) => void;
  updateStep: (stepId: string, status: ProcessStep["status"]) => void;
  resetState: () => void;
  setPersisted: (persisted: boolean) => void;
  preventStateReset: (prevent: boolean) => void;

  // Convenience methods
  startProcess: (title: string, steps: ProcessStep[]) => void;
  persistForNavigation: () => void;
  clearPersistence: () => void;
}

// Create the store with persistence
export const useProcessStepsStore = create<ProcessStepsState>()(
  persist(
    (set, get) => ({
      // Initial state
      isOpen: false,
      title: "",
      steps: [],
      timeoutId: null,
      timedOut: false,
      persistedState: false,
      preventReset: false,

      // Actions
      open: () => set({ isOpen: true }),
      close: () => {
        const state = get();
        if (!state.persistedState) {
          console.log("ProcessStepsStore: Closing modal (not persisted)");
          set({ isOpen: false });
        } else {
          console.log("ProcessStepsStore: Not closing modal (persisted for navigation)");
        }
      },
      setTitle: (title) => set({ title }),
      setSteps: (steps) => set({ steps }),
      updateStep: (stepId, status) => {
        set((state) => ({
          steps: state.steps.map(step =>
            step.id === stepId ? { ...step, status } : step
          )
        }));

        // Log the update for debugging
        console.log(`Updated step ${stepId} to ${status}`);
      },
      resetState: () => {
        // Check if resets are currently prevented
        if (get().preventReset) {
          console.log("ProcessStepsStore: Reset prevented - operation in progress");
          return;
        }

        // Clear any existing timeout
        const timeoutId = get().timeoutId;
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        // Reset to initial state
        set({
          isOpen: false,
          title: "",
          steps: [],
          timeoutId: null,
          timedOut: false,
          persistedState: false
        });

        // Also clear the persisted state from storage
        if (typeof window !== 'undefined') {
          localStorage.removeItem("process-steps-storage");
        }

        console.log("ProcessStepsStore: State completely reset");
      },
      setPersisted: (persisted) => set({ persistedState: persisted }),
      preventStateReset: (prevent) => set({ preventReset: prevent }),

      // Convenience methods
      startProcess: (title, steps) => {
        // Check if we're on an onboarding page before starting the process
        const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
        const isOnOnboardingPage = currentPath.includes('/onboard-');

        if (!isOnOnboardingPage) {
          console.log("ProcessStepsStore: Attempted to start process outside onboarding page, ignoring");
          return;
        }

        // Prevent resets during the process
        set({ preventReset: true });

        // Clear any existing timeout
        if (get().timeoutId) {
          clearTimeout(get().timeoutId);
        }

        // Set a new timeout to show timeout UI after 15 seconds (reduced from 30)
        const timeoutId = setTimeout(() => {
          console.log("Process modal timed out after 15 seconds");
          set({ timedOut: true });
        }, 15000);

        set({
          isOpen: true,
          title,
          steps,
          timeoutId,
          timedOut: false,
          persistedState: false
        });

        console.log("Started process:", { title, steps });
      },

      persistForNavigation: () => {
        set({ persistedState: true });

        // Log the current state to verify it's been updated
        const currentState = get();
        console.log("Process modal state persisted for navigation", {
          steps: currentState.steps.map(step => ({ id: step.id, status: step.status })),
          persistedState: currentState.persistedState // This should be true
        });
      },

      clearPersistence: () => {
        set({ persistedState: false });
        console.log("Process modal persistence cleared");
      }
    }),
    {
      name: "process-steps-storage",
      partialize: (state) => ({
        isOpen: state.isOpen,
        title: state.title,
        steps: state.steps,
        timedOut: state.timedOut,
        persistedState: state.persistedState
      })
    }
  )
);








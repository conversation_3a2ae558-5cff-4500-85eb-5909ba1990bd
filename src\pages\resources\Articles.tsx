
import { useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import { Link } from "react-router-dom";
import { BookOpen, Clock, Search, User } from "lucide-react";

const articlesData = [
  {
    id: 1,
    title: "Understanding Reinforcement Learning Fundamentals",
    description: "An in-depth look at the core principles behind reinforcement learning algorithms.",
    readTime: "8 min read",
    author: "Dr. <PERSON>",
    image: "https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["AI", "Machine Learning", "Reinforcement Learning"],
    date: "April 5, 2025"
  },
  {
    id: 2,
    title: "Mathematical Foundations for AI: What You Need to Know",
    description: "Exploring the essential mathematical concepts that power modern machine learning algorithms.",
    readTime: "12 min read",
    author: "Prof. <PERSON>",
    image: "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Mathematics", "AI Foundations", "Linear Algebra"],
    date: "April 2, 2025"
  },
  {
    id: 3,
    title: "Practical Applications of Reinforcement Learning in Robotics",
    description: "How reinforcement learning is revolutionizing the field of robotics with real-world examples.",
    readTime: "10 min read",
    author: "Dr. Robert Kim",
    image: "https://images.unsplash.com/photo-1581092921461-7031e4bfb83a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Robotics", "Applied AI", "Case Studies"],
    date: "March 28, 2025"
  },
  {
    id: 4,
    title: "Getting Started with Natural Language Processing",
    description: "A beginner's guide to understanding and implementing NLP techniques in your projects.",
    readTime: "7 min read",
    author: "Dr. Emily Rodriguez",
    image: "https://images.unsplash.com/photo-1516321165247-4aa89a48be28?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["NLP", "Tutorial", "AI Applications"],
    date: "March 25, 2025"
  },
  {
    id: 5,
    title: "Advanced Deep Reinforcement Learning Techniques",
    description: "Exploring cutting-edge approaches in deep reinforcement learning research and implementation.",
    readTime: "15 min read",
    author: "Prof. James Wilson",
    image: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Deep Learning", "Research", "Advanced Techniques"],
    date: "March 20, 2025"
  },
  {
    id: 6,
    title: "Building Intelligent Game Agents with Reinforcement Learning",
    description: "How to create sophisticated AI opponents and helpers in game environments using RL principles.",
    readTime: "9 min read",
    author: "Dr. Lisa Park",
    image: "https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Game AI", "Development", "Tutorial"],
    date: "March 15, 2025"
  },
];

const Articles = () => {
  const [searchTerm, setSearchTerm] = useState("");
  
  const filteredArticles = articlesData.filter(
    (article) => 
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Latest Articles
            </h1>
            <p className="mt-4 text-xl text-gray-500 max-w-2xl mx-auto">
              Explore our collection of in-depth articles on reinforcement learning and AI topics.
            </p>
          </div>
          
          <div className="flex justify-center mb-10">
            <div className="relative w-full max-w-xl">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                placeholder="Search articles by title, description, or tags..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {filteredArticles.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900">No articles found</h3>
              <p className="mt-2 text-gray-500">
                Try adjusting your search terms or browse all our articles.
              </p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setSearchTerm("")}
              >
                View All Articles
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredArticles.map((article) => (
                <Card key={article.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="h-48 overflow-hidden">
                    <img 
                      src={article.image} 
                      alt={article.title} 
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div className="text-sm text-gray-500">
                        {article.date}
                      </div>
                      <div className="flex items-center text-gray-500 text-sm">
                        <Clock size={14} className="mr-1" />
                        {article.readTime}
                      </div>
                    </div>
                    <CardTitle className="text-xl mt-2">{article.title}</CardTitle>
                    <CardDescription className="text-gray-600 line-clamp-2">
                      {article.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {article.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="bg-gray-100 text-gray-700">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between pt-2">
                      <div className="text-sm text-gray-700 flex items-center">
                        <User size={14} className="mr-1" />
                        by {article.author}
                      </div>
                    </div>
                    
                    <Button className="w-full mt-4 button-gradient text-white" asChild>
                      <Link to={`/articles/${article.id}`}>Read Article</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Articles;

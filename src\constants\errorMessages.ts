// Error messages for form validations and API responses

// Form field validation messages
export const fieldErrors = {
  // User Type
  userType: {
    required: "Please select a valid user type",
  },

  // Password validations
  password: {
    required: "Password is required",
    tooShort: "Password must be at least 8 characters",
    weakPassword:
      "Please use a stronger password with a mix of letters, numbers, and symbols.",
    complexity: "Password must contain at least 8 characters, including uppercase, lowercase, and numbers"
  },

  // Name validations
  firstName: {
    required: "First name is required",
    tooShort: "First name must be at least 2 characters",
    empty: "First name cannot be empty",
    specialChars: "First name cannot contain special characters or numbers",
  },
  lastName: {
    required: "Last name is required",
    tooShort: "Last name must be at least 2 characters",
    empty: "Last name cannot be empty",
    specialChars: "Last name cannot contain special characters or numbers",
  },

  // Contact information
  email: {
    required: "Email is required",
    invalid: "Please enter a valid email address",
    disposable: "Please use a non-disposable email address",
  },
  phoneNumber: {
    required: "Phone number is required",
    invalid: "Please enter a valid phone number",
    tooShort: "Phone number must be at least 10 digits",
  },

  // Tutor specific fields
  subjects: {
    required: "Please select at least one subject",
    empty: "You must select at least one subject",
  },
  hourlyRate: {
    required: "Hourly rate is required",
    invalid: "Must be a positive number",
    tooLow: "Hourly rate must be at least $15",
  },
  cvFile: {
    required: "CV file is required",
    tooLarge: "File size must be less than 2MB",
    invalidType: "Please upload a PDF, DOC, or DOCX file",
  },
};

// Authentication error messages
export const authErrors = {
  login: {
    invalidCredentials:
      "The email or password you entered is incorrect. Please try again.",
    rateLimit: "Too many login attempts. Please try again later.",
    emailNotConfirmed: "Please verify your email address before logging in.",
  },
  passwordReset: {
    samePassword: "New password cannot be the same as your current password.",
    expired: "Your password reset link has expired. Please request a new one.",
    mismatch: "Passwords don't match",
  },
  registration: {
    emailExists:
      "This email is already registered. Please use a different email or try logging in.",
  },
};

// Email verification error messages
export const emailVerificationErrors = {
  signupNotAllowed:
    "This email cannot be used. Please try a different email address.",
  alreadyRegistered: "This email is already registered. Please log in instead.",
  invalidEmail: "Please enter a valid email address.",
  rateLimit:
    "Too many attempts. Please wait a few minutes before trying again.",
  generic: "Failed to send verification email. Please try again.",
};

// API error messages
export const apiErrors = {
  generic: "An error occurred while communicating with the server.",
  timeout:
    "Request timed out. Please check your internet connection and try again.",
  serverError: "Server error. Please try again later.",
  notFound: "The requested resource was not found.",
  unauthorized: "You are not authorized to perform this action.",
};

// Number error messages
export const numberErrors = {
  notFinite: "Please enter a valid number",
  notPositive: "Please enter a positive number",
  minLength: "Please select at least one option",
};

// text content error messages
export const textErrors = {
  required: "This field is required",
  tooShort: "This field is too short",
  tooLong: "This field is too long",
  invalidString: "Invalid input",
  invalidType: "Invalid type",
  shortText: "Text must be 100 characters or less",
  longText: "Text must be 1000 characters or less",
  richText: "Content must be 5000 characters or less",
};

// Date error messages
export const dateErrors = {
  futureDate: "Date must be in the future",
  pastDate: "Date must be in the past",
};

// Amount error messages
export const amountErrors = {
  notPositive: "Please enter a positive amount",
  notValid: "Must be a valid currency amount",
};


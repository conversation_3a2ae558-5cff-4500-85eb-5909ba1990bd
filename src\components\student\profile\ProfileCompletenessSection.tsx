import React, { use<PERSON>emo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/Card";
import { StudentExtendedProfileData } from "@/pages/student/Profile";
import { CheckCircle2 } from "lucide-react";

interface ProfileCompletenessSectionProps {
  profileData: StudentExtendedProfileData;
}

// Define the profile completeness fields and their weights
interface CompletenessField {
  name: string;
  displayName: string;
  weight: number;
  check: (data: StudentExtendedProfileData) => boolean;
}

const ProfileCompletenessSection: React.FC<ProfileCompletenessSectionProps> = ({ profileData }) => {
  // Define all fields that contribute to profile completeness with their weights
  const completenessFields: CompletenessField[] = [
    {
      name: "education_level",
      displayName: "Add grade information",
      weight: 20,
      check: (data) => !!data.education_level
    },
    {
      name: "subjects_of_interest",
      displayName: "Add subjects of interest",
      weight: 20,
      check: (data) => Array.isArray(data.subjects_of_interest) && data.subjects_of_interest.length > 0
    },
    {
      name: "profilePicture",
      displayName: "Add profile photo",
      weight: 15,
      check: (data) => !!data.profilePictureUrl
    },
    {
      name: "learning_goals",
      displayName: "Add learning goals",
      weight: 10,
      check: (data) => Array.isArray(data.learning_goals) && data.learning_goals.length > 0
    },
    {
      name: "school_name",
      displayName: "Add school information",
      weight: 5,
      check: (data) => !!(data.academic_history && data.academic_history.school_name)
    },
    {
      name: "location",
      displayName: "Add location",
      weight: 5,
      check: (data) => !!data.location
    },
    {
      name: "achievements",
      displayName: "Add achievements",
      weight: 5,
      check: (data) => !!(data.academic_history &&
        Array.isArray(data.academic_history.achievements) &&
        data.academic_history.achievements.length > 0)
    },
    {
      name: "interests",
      displayName: "Add interests",
      weight: 5,
      check: (data) => Array.isArray(data.interests) && data.interests.length > 0
    },
    {
      name: "hobbies",
      displayName: "Add hobbies",
      weight: 5,
      check: (data) => Array.isArray(data.hobbies) && data.hobbies.length > 0
    },
    {
      name: "preferred_time",
      displayName: "Add preferred study time",
      weight: 2.5,
      check: (data) => !!(data.study_preferences && data.study_preferences.preferred_time)
    },
    {
      name: "preferred_days",
      displayName: "Add preferred study days",
      weight: 2.5,
      check: (data) => !!(data.study_preferences &&
        Array.isArray(data.study_preferences.preferred_days) &&
        data.study_preferences.preferred_days.length > 0)
    },
    {
      name: "learning_style",
      displayName: "Add learning style",
      weight: 2.5,
      check: (data) => !!(data.study_preferences && data.study_preferences.learning_style)
    },
    {
      name: "communication_preference",
      displayName: "Add communication preference",
      weight: 2.5,
      check: (data) => !!(data.study_preferences && data.study_preferences.communication_preference)
    }
  ];

  // Calculate the completeness score and field status
  const { completenessScore, fieldStatus } = useMemo(() => {
    let score = 0;
    const status = completenessFields.map(field => {
      const isComplete = field.check(profileData);
      if (isComplete) {
        score += field.weight;
      }
      return {
        ...field,
        isComplete
      };
    });

    return {
      completenessScore: score,
      fieldStatus: status
    };
  }, [profileData, completenessFields]);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">Profile Completeness</CardTitle>
        <div
          className="relative group cursor-help"
        >
          <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 hover:bg-blue-200 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
          </div>
          <div className="absolute right-0 w-64 p-2 mt-2 bg-black text-white text-xs rounded shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-50">
            Complete your profile to enhance your learning experience
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium">Overall</span>
              <span className="text-sm font-medium">{completenessScore}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${completenessScore}%` }}
              ></div>
            </div>
          </div>

          <div className="space-y-2">
            {fieldStatus.map((field) => (
              <div key={field.name} className="flex justify-between items-center">
                <span className="text-sm flex items-center">
                  {field.isComplete ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                  ) : null}
                  <span className={field.isComplete ? "text-gray-600" : "text-gray-900"}>
                    {field.displayName}
                  </span>
                </span>
                <span className={`text-xs ${field.isComplete ? "text-green-500" : "text-gray-500"}`}>
                  +{field.weight}%
                </span>
              </div>
            ))}
          </div>

          {completenessScore === 100 && (
            <div className="text-center py-2 mt-2">
              <span className="text-sm text-green-600 font-medium">
                Your profile is complete! 🎉
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileCompletenessSection;

import React from "react";
import {
  LayoutDashboard,
  Calendar,
  MessageSquare,
  LogOut,
  Menu,
  X,
  Home,
  Eye,
  GraduationCap,
  BookMarked,
  LineChart,
  FileBox,
  CalendarCheck,
  History,
  PlusCircle,
  CheckCircle,
  User,
  UserCog,
  ShoppingCart,
  CreditCard,
  Receipt,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import Sidebar, { SidebarItemType } from "@/components/ui/Sidebar";
import { ROUTES } from "@/routes/RouteConfig";

const StudentSidebar: React.FC = () => {
  const { signOut } = useAuth();

  const menuItems: SidebarItemType[] = [
    {
      icon: <LayoutDashboard size={18} />,
      label: "Dashboard",
      path: "/student/dashboard",
    },
    {
      icon: <CalendarCheck size={18} />,
      label: "My Schedule",
      path: "/student/schedule",
    },
    {
      icon: <GraduationCap size={18} />,
      label: "Learning Journey",
      subItems: [
        {
          icon: <LineChart size={16} />,
          label: "Progress Map",
          path: "/student/journey",
        },
        {
          icon: <FileBox size={16} />,
          label: "My Materials",
          path: "/student/materials",
        },
      ],
    },
    {
      icon: <BookMarked size={18} />,
      label: "Bookings",
      subItems: [
        {
          icon: <PlusCircle size={16} />,
          label: "Request Bookings",
          path: ROUTES.STUDENT_REQUEST_BOOKING.path,
        },
        {
          icon: <CheckCircle size={16} />,
          label: "Accept Bookings",
          path: ROUTES.STUDENT_ACCEPT_SESSION.path,
        }
      ],
    },
    {
      icon: <MessageSquare size={18} />,
      label: "Sessions",
      subItems: [
        {
          icon: <Calendar size={16} />,
          label: "Upcoming Sessions",
          path: "/student/sessions/upcoming",
        },
        {
          icon: <History size={16} />,
          label: "Session History",
          path: "/student/sessions/history",
        },
      ],
    },
    {
      icon: <ShoppingCart size={18} />,
      label: "Subscriptions & Billing",
      subItems: [
        {
          icon: <ShoppingCart size={16} />,
          label: "Products",
          path: ROUTES.STUDENT_PRODUCTS.path,
        },
        {
          icon: <CreditCard size={16} />,
          label: "My Subscriptions",
          path: ROUTES.STUDENT_SUBSCRIPTIONS.path,
        },
        {
          icon: <Receipt size={16} />,
          label: "Billing History",
          path: ROUTES.STUDENT_BILLING_HISTORY.path,
        }
      ],
    },
    {
      icon: <User size={18} />,
      label: "Profile",
      subItems: [
        {
          icon: <Eye size={16} />,
          label: "View Profile",
          path: ROUTES.STUDENT_PROFILE.path,
        },
        {
          icon: <UserCog size={16} />,
          label: "Account Preferences",
          path: ROUTES.STUDENT_ACCOUNT_PREFERENCES.path,
        },
      ],
    },
  ];

  const handleLogout = async () => {
    try {
      await signOut();
      window.location.href = ROUTES.LOGIN.path;
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  return (
    <Sidebar
      title={
        <div className="flex flex-col">
          <span className="text-rfpurple-500 font-bold">rfLearn</span>
          <span className="text-sm border border-rfpurple-500 rounded-full px-2 text-center">
            Student
          </span>
        </div>
      }
      menuItems={menuItems}
      onLogout={handleLogout}
      logoutIcon={<LogOut size={18} />}
      headerColor="text-rfpurple-500"
      homeLink="/"
      homeIcon={<Home size={20} />}
      expandIcon={<Menu size={16} />}
      collapseIcon={<X size={16} />}
    />
  );
};

export default StudentSidebar;

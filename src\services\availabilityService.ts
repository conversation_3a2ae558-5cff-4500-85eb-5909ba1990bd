import { useTutorAvailabilityStore, AvailabilitySlot as TutorAvailabilitySlot } from "@/store/tutorAvailabilityStore";
import { useStudentAvailabilityStore, AvailabilitySlot as StudentAvailabilitySlot } from "@/store/studentAvailabilityStore";
import { useSessionStore, Session } from "@/store/sessionStore";

// Types for availability checking
export interface TimeSlot {
  day: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  conflictReason?: string;
}

export interface DailyAvailability {
  date: string;
  dayOfWeek: string;
  timeSlots: TimeSlot[];
}

export interface AvailabilityCheckResult {
  isAvailable: boolean;
  conflictReason?: string;
  conflictingSession?: Session;
}

// Helper function to convert day of week to string
export const getDayOfWeek = (date: Date): string => {
  const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
  return days[date.getDay()];
};

// Helper function to convert date to day of week
export const getDateDayOfWeek = (dateStr: string): string => {
  const date = new Date(dateStr);
  return getDayOfWeek(date);
};

// Helper function to check if a time is within a slot
export const isTimeInSlot = (
  time: string,
  slotStartTime: string,
  slotEndTime: string
): boolean => {
  const [timeHours, timeMinutes] = time.split(":").map(Number);
  const [startHours, startMinutes] = slotStartTime.split(":").map(Number);
  const [endHours, endMinutes] = slotEndTime.split(":").map(Number);

  const timeInMinutes = timeHours * 60 + timeMinutes;
  const startInMinutes = startHours * 60 + startMinutes;
  const endInMinutes = endHours * 60 + endMinutes;

  return timeInMinutes >= startInMinutes && timeInMinutes < endInMinutes;
};

// Helper function to check if two time ranges overlap
export const doTimeRangesOverlap = (
  range1Start: string,
  range1End: string,
  range2Start: string,
  range2End: string
): boolean => {
  const [start1Hours, start1Minutes] = range1Start.split(":").map(Number);
  const [end1Hours, end1Minutes] = range1End.split(":").map(Number);
  const [start2Hours, start2Minutes] = range2Start.split(":").map(Number);
  const [end2Hours, end2Minutes] = range2End.split(":").map(Number);

  const start1InMinutes = start1Hours * 60 + start1Minutes;
  const end1InMinutes = end1Hours * 60 + end1Minutes;
  const start2InMinutes = start2Hours * 60 + start2Minutes;
  const end2InMinutes = end2Hours * 60 + end2Minutes;

  return (
    (start1InMinutes < end2InMinutes && end1InMinutes > start2InMinutes) ||
    (start2InMinutes < end1InMinutes && end2InMinutes > start1InMinutes)
  );
};

/**
 * Check if a tutor is available at a specific date and time
 * 
 * @param tutorId The tutor ID
 * @param date The date in YYYY-MM-DD format
 * @param startTime The start time in HH:MM format
 * @param endTime The end time in HH:MM format
 * @returns An object indicating availability and conflict details if any
 */
export const checkTutorAvailability = (
  tutorId: string,
  date: string,
  startTime: string,
  endTime: string
): AvailabilityCheckResult => {
  const dayOfWeek = getDateDayOfWeek(date);
  
  // Get tutor's availability slots
  const { availabilitySlots } = useTutorAvailabilityStore.getState();
  
  // Get tutor's sessions
  const { sessions } = useSessionStore.getState();
  const tutorSessions = sessions.filter(session => session.tutorId === tutorId);
  
  // Check if the tutor has marked this time as available
  const daySlots = availabilitySlots.filter(slot => slot.day === dayOfWeek);
  
  const isInAvailabilitySlot = daySlots.some(slot => 
    doTimeRangesOverlap(startTime, endTime, slot.startTime, slot.endTime) &&
    (slot.status === "available" || slot.status === "auto-accept" || slot.status === "manual-approval")
  );
  
  if (!isInAvailabilitySlot) {
    return {
      isAvailable: false,
      conflictReason: "Tutor is not available during this time"
    };
  }
  
  // Check if the tutor has any sessions during this time
  const conflictingSession = tutorSessions.find(session => {
    const sessionDate = new Date(session.scheduledAt).toISOString().split('T')[0];
    if (sessionDate !== date) return false;
    
    const sessionStartTime = new Date(session.scheduledAt).toISOString().split('T')[1].substring(0, 5);
    const sessionEndTimeDate = new Date(new Date(session.scheduledAt).getTime() + session.durationMin * 60000);
    const sessionEndTime = sessionEndTimeDate.toISOString().split('T')[1].substring(0, 5);
    
    return doTimeRangesOverlap(startTime, endTime, sessionStartTime, sessionEndTime);
  });
  
  if (conflictingSession) {
    return {
      isAvailable: false,
      conflictReason: "Tutor has another session during this time",
      conflictingSession
    };
  }
  
  return { isAvailable: true };
};

/**
 * Check if a student is available at a specific date and time
 * 
 * @param studentId The student ID
 * @param date The date in YYYY-MM-DD format
 * @param startTime The start time in HH:MM format
 * @param endTime The end time in HH:MM format
 * @returns An object indicating availability and conflict details if any
 */
export const checkStudentAvailability = (
  studentId: string,
  date: string,
  startTime: string,
  endTime: string
): AvailabilityCheckResult => {
  const dayOfWeek = getDateDayOfWeek(date);
  
  // Get student's availability slots
  const { availabilitySlots } = useStudentAvailabilityStore.getState();
  
  // Get student's sessions
  const { sessions } = useSessionStore.getState();
  const studentSessions = sessions.filter(session => session.studentId === studentId);
  
  // Check if the student has marked this time as available or preferred
  const daySlots = availabilitySlots.filter(slot => slot.day === dayOfWeek);
  
  const isInAvailabilitySlot = daySlots.some(slot => 
    doTimeRangesOverlap(startTime, endTime, slot.startTime, slot.endTime) &&
    (slot.status === "available" || slot.status === "preferred")
  );
  
  // Check if the student has explicitly marked this time as unavailable
  const isExplicitlyUnavailable = daySlots.some(slot => 
    doTimeRangesOverlap(startTime, endTime, slot.startTime, slot.endTime) &&
    slot.status === "unavailable"
  );
  
  if (isExplicitlyUnavailable) {
    return {
      isAvailable: false,
      conflictReason: "Student has marked this time as unavailable"
    };
  }
  
  if (!isInAvailabilitySlot && daySlots.length > 0) {
    return {
      isAvailable: false,
      conflictReason: "Student is not available during this time"
    };
  }
  
  // Check if the student has any sessions during this time
  const conflictingSession = studentSessions.find(session => {
    const sessionDate = new Date(session.scheduledAt).toISOString().split('T')[0];
    if (sessionDate !== date) return false;
    
    const sessionStartTime = new Date(session.scheduledAt).toISOString().split('T')[1].substring(0, 5);
    const sessionEndTimeDate = new Date(new Date(session.scheduledAt).getTime() + session.durationMin * 60000);
    const sessionEndTime = sessionEndTimeDate.toISOString().split('T')[1].substring(0, 5);
    
    return doTimeRangesOverlap(startTime, endTime, sessionStartTime, sessionEndTime);
  });
  
  if (conflictingSession) {
    return {
      isAvailable: false,
      conflictReason: "Student has another session during this time",
      conflictingSession
    };
  }
  
  return { isAvailable: true };
};

/**
 * Get available time slots for a specific date range for a tutor and student
 * 
 * @param tutorId The tutor ID
 * @param studentId The student ID
 * @param startDate The start date in YYYY-MM-DD format
 * @param endDate The end date in YYYY-MM-DD format
 * @returns An array of daily availability objects
 */
export const getAvailableTimeSlots = (
  tutorId: string,
  studentId: string,
  startDate: string,
  endDate: string
): DailyAvailability[] => {
  const result: DailyAvailability[] = [];
  
  // Generate dates between start and end
  const start = new Date(startDate);
  const end = new Date(endDate);
  const dates: string[] = [];
  
  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    dates.push(date.toISOString().split('T')[0]);
  }
  
  // For each date, generate time slots and check availability
  dates.forEach(date => {
    const dayOfWeek = getDateDayOfWeek(date);
    const timeSlots: TimeSlot[] = [];
    
    // Generate time slots from 8:00 to 20:00 in 30-minute increments
    for (let hour = 8; hour < 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const endHour = minute === 30 ? hour + 1 : hour;
        const endMinute = minute === 30 ? 0 : 30;
        const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
        
        // Check tutor availability
        const tutorAvailability = checkTutorAvailability(tutorId, date, startTime, endTime);
        
        // Check student availability
        const studentAvailability = checkStudentAvailability(studentId, date, startTime, endTime);
        
        // A slot is available only if both tutor and student are available
        const isAvailable = tutorAvailability.isAvailable && studentAvailability.isAvailable;
        
        // Determine conflict reason if any
        let conflictReason: string | undefined;
        if (!tutorAvailability.isAvailable) {
          conflictReason = tutorAvailability.conflictReason;
        } else if (!studentAvailability.isAvailable) {
          conflictReason = studentAvailability.conflictReason;
        }
        
        timeSlots.push({
          day: dayOfWeek,
          startTime,
          endTime,
          isAvailable,
          conflictReason
        });
      }
    }
    
    result.push({
      date,
      dayOfWeek,
      timeSlots
    });
  });
  
  return result;
};

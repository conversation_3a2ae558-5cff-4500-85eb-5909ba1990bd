import { useAuth } from "../context/AuthContext";

const Navigation = () => {
  const { user, isAdmin } = useAuth();
  
  // No need for manual admin check - use isAdmin from context
  
  return (
    <nav className="bg-primary text-white p-4">
      <div className="container mx-auto flex justify-between items-center">
        <Link to="/" className="text-xl font-bold">Your App Name</Link>
        
        <div className="flex space-x-4">
          <Link to="/" className="hover:text-gray-200">Home</Link>
          <Link to="/services" className="hover:text-gray-200">Services</Link>
          <Link to="/contact" className="hover:text-gray-200">Contact</Link>
          
          {isAdmin && (
            <div className="relative group">
              <button className="hover:text-gray-200">Admin</button>
              <div className="absolute hidden group-hover:block bg-white text-black shadow-lg rounded mt-1 p-2 w-48 right-0 z-10">
                <Link to="/admin/dashboard" className="block p-2 hover:bg-gray-100">Dashboard</Link>
                <Link to="/admin/inquiries" className="block p-2 hover:bg-gray-100">Inquiries</Link>
              </div>
            </div>
          )}
          
          {user ? (
            <button onClick={() => /* logout function */} className="hover:text-gray-200">Logout</button>
          ) : (
            <Link to="/login" className="hover:text-gray-200">Login</Link>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;




import { PaymentProvider, PaymentProviderConfig } from './types';
import { RazorpayProvider } from './providers/RazorpayProvider';
import { StripeProvider } from './providers/StripeProvider';
import { config } from '@/config/environment';

export class PaymentProviderFactory {
  private static providers: Map<string, PaymentProvider> = new Map();

  /**
   * Create a payment provider instance
   */
  static create(providerName: string): PaymentProvider {
    // Check if provider is already instantiated
    if (this.providers.has(providerName)) {
      return this.providers.get(providerName)!;
    }

    // Get provider configuration
    const providerConfig = this.getProviderConfig(providerName);
    if (!providerConfig.enabled) {
      throw new Error(`Payment provider ${providerName} is not enabled`);
    }

    // Create provider instance
    let provider: PaymentProvider;

    switch (providerName.toLowerCase()) {
      case 'razorpay':
        provider = new RazorpayProvider({
          keyId: providerConfig.configuration.keyId || '',
          enabled: providerConfig.enabled
        });
        break;
      case 'stripe':
        provider = new StripeProvider({
          publishableKey: providerConfig.configuration.publishableKey || '',
          enabled: providerConfig.enabled
        });
        break;
      default:
        throw new Error(`Unsupported payment provider: ${providerName}`);
    }

    // Cache the provider instance
    this.providers.set(providerName, provider);
    return provider;
  }

  /**
   * Get available payment providers
   */
  static getAvailableProviders(): string[] {
    const providers: string[] = [];
    
    Object.entries(config.payment.providers).forEach(([name, config]) => {
      if (config.enabled) {
        providers.push(name);
      }
    });

    return providers;
  }

  /**
   * Get default payment provider
   */
  static getDefaultProvider(): string {
    return config.payment.defaultProvider;
  }

  /**
   * Check if a provider is available
   */
  static isProviderAvailable(providerName: string): boolean {
    const providerConfig = config.payment.providers[providerName as keyof typeof config.payment.providers];
    return providerConfig?.enabled || false;
  }

  /**
   * Get provider configuration
   */
  private static getProviderConfig(providerName: string): PaymentProviderConfig {
    const providerConfig = config.payment.providers[providerName as keyof typeof config.payment.providers];
    
    if (!providerConfig) {
      throw new Error(`Payment provider ${providerName} not found in configuration`);
    }

    return {
      name: providerName,
      enabled: providerConfig.enabled,
      configuration: providerConfig
    };
  }

  /**
   * Get provider for currency
   */
  static getProviderForCurrency(currency: string): string {
    // Currency-based provider selection
    const currencyProviderMap: Record<string, string> = {
      'inr': 'razorpay',
      'usd': 'stripe',
      'eur': 'stripe',
      'gbp': 'stripe',
      'cad': 'stripe'
    };

    const preferredProvider = currencyProviderMap[currency.toLowerCase()];
    
    if (preferredProvider && this.isProviderAvailable(preferredProvider)) {
      return preferredProvider;
    }

    // Fallback to default provider
    return this.getDefaultProvider();
  }

  /**
   * Clear provider cache (useful for testing)
   */
  static clearCache(): void {
    this.providers.clear();
  }
}

// Export singleton instance
export const paymentProviderFactory = PaymentProviderFactory;

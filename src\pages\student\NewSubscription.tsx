import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useSubscriptionWorkflowStore } from "@/store/subscriptionWorkflowStore";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage
} from "@/components/ui/Breadcrumb";
import {
  Package,
  BookOpen,
  Calculator,
  CreditCard,
  ArrowRight,
  ArrowLeft,
  Home,
  Rocket,
  Target,
  Check,
  AlertCircle
} from "lucide-react";
import { Link } from "react-router-dom";
import { ROUTES } from "@/routes/RouteConfig";
import { useToast } from "@/hooks/useToast";

const NewSubscription: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { displayName } = useProfileData();
  const { toast } = useToast();
  const [activeWorkflows, setActiveWorkflows] = useState<any[]>([]);
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const {
    availableProducts,
    isLoading,
    isLoadingProducts,
    error,
    getAllActiveWorkflows,
    getActiveWorkflowForProduct,
    createWorkflow,
    cleanupLegacyWorkflows,
    fetchAvailableProducts,
    loadWorkflowById
  } = useSubscriptionWorkflowStore();

  useEffect(() => {
    const initializeWorkflow = async () => {
      if (!user?.id) return;

      // Fetch available products
      await fetchAvailableProducts();

      // Clean up any legacy workflows with NULL product_id
      await cleanupLegacyWorkflows(user.id);

      // Fetch all active workflows for the user
      const workflows = await getAllActiveWorkflows(user.id);
      setActiveWorkflows(workflows);

      console.log('Found active workflows:', workflows);
    };

    initializeWorkflow();
  }, [user?.id, getAllActiveWorkflows, cleanupLegacyWorkflows, fetchAvailableProducts]);

  // Handle new workflow creation
  const handleStartNewWorkflow = async (productId: string, productType: 'booster' | 'custom' | 'preparation') => {
    if (!user?.id) return;

    // Check if there's already an active workflow for this specific product
    const existingWorkflow = await getActiveWorkflowForProduct(user.id, productId);
    if (existingWorkflow) {
      toast({
        title: "Active Workflow Found",
        description: `You already have an active workflow for this product. Resuming from step ${existingWorkflow.current_step}.`
      });

      // Resume the existing workflow
      handleResumeWorkflow(existingWorkflow);
      return;
    }

    // Create workflow with the specific product ID
    const workflowId = await createWorkflow(user.id, productType, productId);
    if (workflowId) {
      toast({
        title: "Workflow Created",
        description: `Starting your ${productType} subscription workflow.`
      });

      // Navigate to the appropriate step based on product type
      if (productType === 'booster') {
        // Booster products skip curriculum configuration and go to pricing
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PRICING.path);
      } else {
        // Custom and preparation products need curriculum configuration
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_CONFIGURE.path);
      }
    }
  };

  // Handle resuming existing workflow
  const handleResumeWorkflow = async (workflow: any) => {
    toast({
      title: "Resuming Workflow",
      description: `Continuing your ${workflow.product_type} workflow from step ${workflow.current_step}`
    });

    // Load the workflow data into the store (including selected product)
    await loadWorkflowById(workflow.id);

    // Navigate to the appropriate step
    switch (workflow.current_step) {
      case 1:
        // If workflow is at step 1, it means no product was selected yet
        // This shouldn't happen with our new workflow creation logic, but handle it gracefully
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_SELECT.path, {
          state: { productType: workflow.product_type, resumingWorkflow: workflow.id }
        });
        break;
      case 2:
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_CONFIGURE.path);
        break;
      case 3:
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PRICING.path);
        break;
      case 4:
        navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PURCHASE.path);
        break;
    }
  };

  // Helper function to get icon for product type
  const getProductIcon = (type: string) => {
    switch (type) {
      case 'booster':
        return <Rocket className="h-6 w-6 text-gray-600" />;
      case 'custom':
        return <Target className="h-6 w-6 text-gray-600" />;
      case 'preparation':
        return <BookOpen className="h-6 w-6 text-gray-600" />;
      default:
        return <Package className="h-6 w-6 text-gray-600" />;
    }
  };

  // Helper function to get default features if not provided in database
  const getDefaultFeatures = (type: string) => {
    switch (type) {
      case 'booster':
        return ['All subject topics included', 'Complete subtopic access', 'Curated learning path'];
      case 'custom':
        return ['Pick specific topics', 'Select individual subtopics', 'Admin assistance available'];
      case 'preparation':
        return ['Exam-focused content', 'Flexible topic selection', 'Specialized study materials'];
      default:
        return ['Comprehensive learning experience'];
    }
  };

  // Helper function to extract and format features from database
  const extractFeatures = (product: any) => {
    // If features exist in database and is an object, extract them
    if (product.features && typeof product.features === 'object' && !Array.isArray(product.features)) {
      const featureList = [];

      // Add duration from duration_days
      featureList.push(`${product.duration_days} days access`);

      // Extract features from JSONB object
      Object.entries(product.features).forEach(([key, value]) => {
        if (value) {
          const formattedKey = key.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' ');

          if (typeof value === 'boolean') {
            if (value) featureList.push(formattedKey);
          } else {
            featureList.push(`${formattedKey}: ${value}`);
          }
        }
      });

      return featureList.length > 0 ? featureList : getDefaultFeatures(product.type);
    }

    // If features is an array, use it directly but add duration
    if (Array.isArray(product.features)) {
      return [`${product.duration_days} days access`, ...product.features];
    }

    // Fallback to defaults with duration
    return [`${product.duration_days} days access`, ...getDefaultFeatures(product.type)];
  };

  // Helper function to format price
  const formatPrice = (price: number, type: string) => {
    if (type === 'booster') {
      return `$${price}`;
    } else {
      return `From $${price}`;
    }
  };

  // Helper function to get price note
  const getPriceNote = (type: string) => {
    switch (type) {
      case 'booster':
        return 'Full package';
      case 'custom':
      case 'preparation':
        return 'Based on selection';
      default:
        return 'Contact for pricing';
    }
  };

  return (
    <StudentPageLayout
      title="Subscription Process"
      profileData={{
        displayName: displayName || "Student",
        email: "",
        photoUrl: ""
      }}
      description="Complete the following steps to customize your learning experience"
    >
      {/* Breadcrumb Navigation */}
      <div className="mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_DASHBOARD.path} className="flex items-center">
                  <Home className="h-4 w-4 mr-1" />
                  Dashboard
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_SUBSCRIPTIONS.path}>
                  Subscriptions
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>New Subscription</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Progress Steps */}
      <div className="mb-12">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {[
            { step: 1, label: "Select Plan", icon: "📋", active: true },
            { step: 2, label: "Configure", icon: "⚙️", active: false },
            { step: 3, label: "Review Price", icon: "💰", active: false },
            { step: 4, label: "Purchase", icon: "🛒", active: false }
          ].map((item, index) => (
            <div key={item.step} className="flex items-center relative">
              {/* Step Circle */}
              <div className="flex flex-col items-center">
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300 relative z-10
                  ${item.active
                    ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-200 scale-110 ring-4 ring-blue-100'
                    : 'bg-white border-2 border-gray-300 text-gray-500 hover:border-blue-300 hover:shadow-md'
                  }
                `}>
                  {item.active ? (
                    <span>{item.icon}</span>
                  ) : (
                    <span className="text-sm font-semibold">{item.step}</span>
                  )}
                </div>

                {/* Step Label */}
                <div className="mt-3 text-center">
                  <span className={`
                    text-sm font-medium transition-colors duration-300
                    ${item.active
                      ? 'text-blue-600 font-semibold'
                      : 'text-gray-500'
                    }
                  `}>
                    {item.label}
                  </span>
                  {item.active && (
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mx-auto mt-2"></div>
                  )}
                </div>
              </div>

              {/* Connector Line */}
              {index < 3 && (
                <div className="flex-1 mx-6 relative">
                  <div className="h-1 bg-gray-200 rounded-full relative overflow-hidden">
                    <div className={`
                      h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full transition-all duration-700 ease-out
                      ${item.active ? 'w-1/3' : 'w-0'}
                    `}></div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {isLoading || isLoadingProducts ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8 bg-red-50 rounded-lg">
          <p className="text-red-600 mb-4">{error}</p>
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Step Title */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Step 1: Select Your Subscription Package
            </h2>
          </div>

          {/* Package Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {availableProducts.map((product) => {
              // Extract features from database
              const features = extractFeatures(product);

              const pkg = {
                id: product.id,
                type: product.type,
                title: product.name,
                description: product.description || `${product.name} learning package`,
                icon: getProductIcon(product.type),
                features: features,
                popular: product.type === 'booster', // Mark booster as popular
                price: formatPrice(product.price, product.type),
                priceNote: getPriceNote(product.type)
              };
              // Check if there's an active workflow for this specific product
              const hasActiveWorkflow = activeWorkflows.some(workflow => workflow.product_id === product.id);
              const isDisabled = !!hasActiveWorkflow;
              const isSelected = selectedPackage === product.id;

              return (
                <div key={product.id} className="relative">
                  {pkg.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                      <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-1.5 text-xs font-semibold shadow-md">
                        ⭐ Most Popular
                      </Badge>
                    </div>
                  )}

                  <Card
                    className={`
                      relative transition-all duration-300 cursor-pointer h-full group
                      ${isDisabled
                        ? 'opacity-50 cursor-not-allowed border-gray-200 bg-gray-50'
                        : 'hover:shadow-xl hover:shadow-blue-100/50 border-gray-200 bg-white hover:-translate-y-1'
                      }
                      ${isSelected
                        ? 'ring-2 ring-blue-500 border-blue-500 shadow-lg shadow-blue-100/50 bg-gradient-to-br from-blue-50 to-indigo-50'
                        : ''
                      }
                      ${pkg.popular && !isSelected
                        ? 'border-purple-300 shadow-md shadow-purple-100/30'
                        : ''
                      }
                    `}
                    onClick={() => !isDisabled && setSelectedPackage(product.id)}
                  >
                    <CardContent className="p-8 h-full flex flex-col">
                      {/* Radio button */}
                      <div className="absolute top-6 right-6">
                        <div className={`
                          w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-200
                          ${isSelected
                            ? 'border-blue-500 bg-blue-500 shadow-md shadow-blue-200'
                            : 'border-gray-300 bg-white group-hover:border-blue-300'
                          }
                        `}>
                          {isSelected && (
                            <div className="w-2 h-2 rounded-full bg-white animate-pulse"></div>
                          )}
                        </div>
                      </div>

                      {/* Icon */}
                      <div className="mb-6">
                        <div className={`
                          w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-300
                          ${isSelected
                            ? 'bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg shadow-blue-200'
                            : 'bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-blue-100 group-hover:to-indigo-100'
                          }
                        `}>
                          <div className={`
                            ${isSelected ? 'text-white' : 'text-gray-600 group-hover:text-blue-600'}
                            transition-colors duration-300
                          `}>
                            {React.cloneElement(pkg.icon, {
                              className: "h-8 w-8"
                            })}
                          </div>
                        </div>
                      </div>

                      {/* Title and Description */}
                      <div className="mb-4">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {pkg.title}
                        </h3>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          {pkg.description}
                        </p>
                      </div>

                      {/* Price */}
                      <div className="mb-6">
                        <div className={`
                          text-3xl font-bold transition-colors duration-300
                          ${isSelected
                            ? 'text-blue-600'
                            : 'text-gray-900 group-hover:text-blue-600'
                          }
                        `}>
                          {pkg.price}
                        </div>
                        <div className="text-sm text-gray-500 font-medium">
                          {pkg.priceNote}
                        </div>
                      </div>

                      {/* Features */}
                      <div className="space-y-3 mb-8 flex-grow">
                        {pkg.features.map((feature, index) => (
                          <div key={index} className="flex items-start">
                            <div className={`
                              w-5 h-5 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5
                              ${isSelected
                                ? 'bg-blue-100 text-blue-600'
                                : 'bg-green-100 text-green-600'
                              }
                            `}>
                              <Check className="h-3 w-3" />
                            </div>
                            <span className="text-gray-700 text-sm font-medium leading-relaxed">
                              {feature}
                            </span>
                          </div>
                        ))}
                      </div>



                      {/* Button */}
                      <Button
                        className={`
                          w-full mt-auto py-3 px-6 rounded-xl font-semibold transition-all duration-300 transform
                          ${isSelected
                            ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg shadow-blue-200 hover:shadow-xl hover:shadow-blue-300 hover:scale-105'
                            : hasActiveWorkflow
                              ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg shadow-green-200 hover:shadow-xl hover:shadow-green-300 hover:scale-105'
                              : 'bg-white hover:bg-gray-50 text-gray-700 border-2 border-gray-200 hover:border-blue-300 hover:text-blue-600 hover:shadow-md'
                          }
                        `}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (hasActiveWorkflow) {
                            // Find the active workflow for this product
                            const activeWorkflow = activeWorkflows.find(workflow => workflow.product_id === product.id);
                            if (activeWorkflow) {
                              handleResumeWorkflow(activeWorkflow);
                            }
                          } else {
                            setSelectedPackage(product.id);
                          }
                        }}
                      >
                        {isSelected
                          ? `✓ ${pkg.title} Selected`
                          : hasActiveWorkflow
                            ? `Resume ${pkg.title}`
                            : `Select ${pkg.title}`
                        }
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between pt-8">
            <Button variant="outline" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <Button
              className={`
                flex items-center px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform
                ${selectedPackage
                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg shadow-blue-200 hover:shadow-xl hover:shadow-blue-300 hover:scale-105'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }
              `}
              disabled={!selectedPackage}
              onClick={() => {
                if (selectedPackage) {
                  const selectedProduct = availableProducts.find(p => p.id === selectedPackage);
                  if (selectedProduct) {
                    handleStartNewWorkflow(selectedProduct.id, selectedProduct.type);
                  }
                }
              }}
            >
              Continue to Next Step
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
          </div>

          {/* Help Section */}
          <Card className="bg-blue-50 border-blue-200 mt-8">
            <CardContent className="p-6">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <AlertCircle className="h-4 w-4 text-white" />
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="text-sm font-medium text-blue-900 mb-1">
                    Need help selecting the right package?
                  </h3>
                  <p className="text-sm text-blue-700 mb-3">
                    Our administrators can help configure your curriculum based on your learning needs. Contact us for personalized assistance.
                  </p>
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                    Request Admin Assistance
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </StudentPageLayout>
  );
};

export default NewSubscription;

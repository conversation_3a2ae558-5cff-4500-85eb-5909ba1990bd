// src/components/student/billing/SubscriptionCard.tsx
import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Calendar, Clock, AlertTriangle, BookOpen, Settings } from "lucide-react";
import { Subscription } from "@/store/billingStore";
import { format } from "date-fns";
import { Progress } from "@/components/ui/Progress";
import { useSubscriptionCurriculumStore } from "@/store/subscriptionCurriculumStore";

interface SubscriptionCardProps {
  subscription: Subscription;
  onCancel: (subscriptionId: string) => void;
  onConfigureCurriculum?: (subscriptionId: string, productType: string) => void;
}

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({ subscription, onCancel, onConfigureCurriculum }) => {
  const { isSubscriptionConfigured, fetchCurriculumConfig } = useSubscriptionCurriculumStore();
  const [isConfigured, setIsConfigured] = useState(false);
  const [isCheckingConfig, setIsCheckingConfig] = useState(true);

  // Check curriculum configuration status
  useEffect(() => {
    const checkConfiguration = async () => {
      setIsCheckingConfig(true);
      await fetchCurriculumConfig(subscription.id);
      setIsConfigured(isSubscriptionConfigured(subscription.id));
      setIsCheckingConfig(false);
    };

    if (subscription.status === 'active') {
      checkConfiguration();
    } else {
      setIsCheckingConfig(false);
    }
  }, [subscription.id, subscription.status, fetchCurriculumConfig, isSubscriptionConfigured]);

  // Determine product type from subscription
  const getProductType = (): string => {
    const productName = subscription.product_name?.toLowerCase() || '';
    if (productName.includes('booster')) return 'booster';
    if (productName.includes('custom')) return 'custom';
    if (productName.includes('preparation') || productName.includes('prep')) return 'preparation';
    return 'custom'; // default
  };
  // Format date
  const formatDate = (date: Date) => {
    return format(date, "MMM d, yyyy");
  };

  // Calculate progress percentage
  const calculateProgress = () => {
    const startDate = new Date(subscription.start_date);
    const endDate = new Date(subscription.end_date);
    const now = new Date();

    const totalDuration = endDate.getTime() - startDate.getTime();
    const elapsed = now.getTime() - startDate.getTime();

    return Math.min(100, Math.max(0, Math.floor((elapsed / totalDuration) * 100)));
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return "bg-green-100 text-green-800 hover:bg-green-200";
      case 'expired':
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
      case 'cancelled':
        return "bg-red-100 text-red-800 hover:bg-red-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  // Check if subscription is about to expire (less than 7 days)
  const isAboutToExpire = subscription.status === 'active' && (subscription.days_remaining || 0) < 7;

  return (
    <Card className="flex flex-col h-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle>{subscription.product_name}</CardTitle>
          <Badge className={getStatusColor(subscription.status)}>
            {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
          </Badge>
        </div>
        <CardDescription>
          Subscription ID: {subscription.id.substring(0, 8)}...
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="space-y-4">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-2 text-gray-500" />
            <div>
              <p className="text-sm font-medium">Subscription Period</p>
              <p className="text-sm text-gray-500">
                {formatDate(subscription.start_date)} - {formatDate(subscription.end_date)}
              </p>
            </div>
          </div>

          {subscription.status === 'active' && (
            <>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Time Remaining</p>
                  <p className="text-sm text-gray-500">
                    {subscription.days_remaining} days left
                  </p>
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{calculateProgress()}%</span>
                </div>
                <Progress value={calculateProgress()} className="h-2" />
              </div>

              {isAboutToExpire && (
                <div className="flex items-start p-3 bg-yellow-50 rounded-md">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-yellow-700">
                    Your subscription is about to expire. Consider renewing to maintain access.
                  </p>
                </div>
              )}

              {/* Curriculum Configuration Status */}
              <div className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Curriculum Configuration</p>
                  {isCheckingConfig ? (
                    <p className="text-sm text-gray-500">Checking...</p>
                  ) : isConfigured ? (
                    <p className="text-sm text-green-600">✅ Configured</p>
                  ) : (
                    <p className="text-sm text-orange-600">⚠️ Not configured yet</p>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </CardContent>
      <CardFooter>
        {subscription.status === 'active' && (
          <div className="w-full space-y-2">
            {/* Curriculum Configuration Button */}
            {!isCheckingConfig && onConfigureCurriculum && (
              <Button
                variant={isConfigured ? "outline" : "default"}
                className="w-full"
                onClick={() => onConfigureCurriculum(subscription.id, getProductType())}
              >
                <Settings className="h-4 w-4 mr-2" />
                {isConfigured ? 'Update Curriculum' : 'Configure Curriculum'}
              </Button>
            )}

            {/* Cancel Subscription Button */}
            <Button
              variant="outline"
              className="w-full text-red-600 hover:bg-red-50 hover:text-red-700"
              onClick={() => onCancel(subscription.id)}
            >
              Cancel Subscription
            </Button>
          </div>
        )}

        {subscription.status === 'expired' && (
          <Button className="w-full">
            Renew Subscription
          </Button>
        )}

        {subscription.status === 'cancelled' && (
          <Button className="w-full">
            Reactivate
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default SubscriptionCard;
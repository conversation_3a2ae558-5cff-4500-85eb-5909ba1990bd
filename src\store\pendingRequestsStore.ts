import { create } from 'zustand';

// Define the types for the store
export interface Student {
  id: string;
  name: string;
  rating: number;
  image: string | null;
  sessionHistory: number;
}

export interface SessionRequest {
  id: string;
  studentId: string;
  studentName: string;
  studentRating: number;
  studentImage: string | null;
  requestedDate: string;
  requestedTime: string;
  topic: string;
  subtopic: string;
  sessionType: string;
  notes: string | null;
  urgency: 'high' | 'medium' | 'low';
  autoAccept: boolean;
  conflictsWith: string | null;
  studentHistory: number;
  createdAt: string;
}

interface PendingRequestsState {
  requests: SessionRequest[];
  isLoading: boolean;
  filter: string;
  searchQuery: string;

  // Actions
  setFilter: (filter: string) => void;
  setSearchQuery: (query: string) => void;
  fetchRequests: () => Promise<void>;
  acceptRequest: (requestId: string, message?: string) => Promise<void>;
  rejectRequest: (requestId: string, reason?: string) => Promise<void>;
}

// Mock data for pending requests
const MOCK_REQUESTS: SessionRequest[] = [
  {
    id: "req-001",
    studentId: "student-001",
    studentName: "<PERSON>",
    studentRating: 4.8,
    studentImage: null,
    requestedDate: "2023-07-15",
    requestedTime: "14:00",
    topic: "Machine Learning",
    subtopic: "Neural Networks",
    sessionType: "One-on-one",
    notes: "I need help understanding backpropagation algorithms.",
    urgency: "high", // within 24 hours
    autoAccept: true,
    conflictsWith: null,
    studentHistory: 3, // previous sessions
    createdAt: "2023-07-14T10:30:00Z",
  },
  {
    id: "req-002",
    studentId: "student-002",
    studentName: "Jamie Smith",
    studentRating: 4.2,
    studentImage: null,
    requestedDate: "2023-07-16",
    requestedTime: "10:30",
    topic: "Data Structures",
    subtopic: "Binary Trees",
    sessionType: "Group",
    notes: "Would like to review tree traversal methods.",
    urgency: "medium", // within 48 hours
    autoAccept: false,
    conflictsWith: "Another session at 11:00",
    studentHistory: 0, // new student
    createdAt: "2023-07-14T09:15:00Z",
  },
  {
    id: "req-003",
    studentId: "student-003",
    studentName: "Taylor Wilson",
    studentRating: 5.0,
    studentImage: null,
    requestedDate: "2023-07-18",
    requestedTime: "16:00",
    topic: "Algorithms",
    subtopic: "Dynamic Programming",
    sessionType: "One-on-one",
    notes: "Need to prepare for a technical interview.",
    urgency: "low", // more than 48 hours
    autoAccept: false,
    conflictsWith: null,
    studentHistory: 7, // regular student
    createdAt: "2023-07-14T14:45:00Z",
  },
];

// Create the store
export const usePendingRequestsStore = create<PendingRequestsState>((set, get) => ({
  requests: MOCK_REQUESTS,
  isLoading: false,
  filter: 'all',
  searchQuery: '',

  // Set the filter
  setFilter: (filter) => set({ filter }),

  // Set the search query
  setSearchQuery: (searchQuery) => set({ searchQuery }),

  // Fetch requests from the API
  fetchRequests: async () => {
    set({ isLoading: true });

    try {
      // In a real app, this would be an API call
      // const response = await fetch('/api/tutor/requests/pending');
      // const data = await response.json();

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Use mock data for now
      set({
        requests: MOCK_REQUESTS,
        isLoading: false
      });
    } catch (error) {
      console.error('Error fetching pending requests:', error);
      set({ isLoading: false });
    }
  },

  // Accept a request
  acceptRequest: async (requestId, message) => {
    set({ isLoading: true });

    try {
      // In a real app, this would be an API call
      // await fetch(`/api/tutor/requests/${requestId}/accept`, {
      //   method: 'POST',
      //   body: JSON.stringify({ message }),
      // });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update local state
      set(state => ({
        requests: state.requests.filter(req => req.id !== requestId),
        isLoading: false
      }));

      console.log(`Accepted request ${requestId} with message: ${message || 'None'}`);
    } catch (error) {
      console.error('Error accepting request:', error);
      set({ isLoading: false });
    }
  },

  // Reject a request
  rejectRequest: async (requestId, reason) => {
    set({ isLoading: true });

    try {
      // In a real app, this would be an API call
      // await fetch(`/api/tutor/requests/${requestId}/reject`, {
      //   method: 'POST',
      //   body: JSON.stringify({ reason }),
      // });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update local state
      set(state => ({
        requests: state.requests.filter(req => req.id !== requestId),
        isLoading: false
      }));

      console.log(`Rejected request ${requestId} with reason: ${reason || 'None'}`);
    } catch (error) {
      console.error('Error rejecting request:', error);
      set({ isLoading: false });
    }
  },
}));

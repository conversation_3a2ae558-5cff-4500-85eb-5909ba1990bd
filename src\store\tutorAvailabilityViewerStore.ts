import { create } from 'zustand';
import { DailyAvailability, TimeSlot } from '@/services/availabilityService';

interface TutorAvailabilityViewerState {
  selectedDate: Date | undefined;
  selectedTimeSlot: TimeSlot | null;
  duration: string;
  notes: string;
  urgency: 'high' | 'medium' | 'low';
  availabilityData: DailyAvailability[];
  isLoading: boolean;
  topics: { id: string; name: string; subtopics: { id: string; name: string }[] }[];
  tutorInfo: { name: string; rating: number; photoUrl?: string };

  // Actions
  setSelectedDate: (date: Date | undefined) => void;
  setSelectedTimeSlot: (timeSlot: TimeSlot | null) => void;
  setDuration: (duration: string) => void;
  setNotes: (notes: string) => void;
  setUrgency: (urgency: 'high' | 'medium' | 'low') => void;
  setAvailabilityData: (data: DailyAvailability[]) => void;
  setIsLoading: (isLoading: boolean) => void;
  setTopics: (topics: { id: string; name: string; subtopics: { id: string; name: string }[] }[]) => void;
  setTutorInfo: (tutorInfo: { name: string; rating: number; photoUrl?: string }) => void;
  resetForm: () => void;
}

export const useTutorAvailabilityViewerStore = create<TutorAvailabilityViewerState>((set) => ({
  selectedDate: new Date(),
  selectedTimeSlot: null,
  duration: "60",
  notes: "",
  urgency: "medium",
  availabilityData: [],
  isLoading: false,
  topics: [],
  tutorInfo: { name: "Loading...", rating: 0 },

  // Actions
  setSelectedDate: (date) => set({ selectedDate: date }),
  setSelectedTimeSlot: (timeSlot) => set({ selectedTimeSlot: timeSlot }),
  setDuration: (duration) => set({ duration }),
  setNotes: (notes) => set({ notes }),
  setUrgency: (urgency) => set({ urgency }),
  setAvailabilityData: (data) => set({ availabilityData: data }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setTopics: (topics) => set({ topics }),
  setTutorInfo: (tutorInfo) => set({ tutorInfo }),
  resetForm: () => set({
    selectedDate: new Date(),
    selectedTimeSlot: null,
    duration: "60",
    notes: "",
    urgency: "medium",
    availabilityData: []
  })
}));

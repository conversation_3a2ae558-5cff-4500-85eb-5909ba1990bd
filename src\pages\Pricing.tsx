import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";

import { Link } from "react-router-dom";
import { useCountryPricing } from '@/hooks/useCountryPricing';
import { ROUTES } from '@/routes/RouteConfig';
import {
  Check,
  Star,
  Zap,
  Target,
  BookOpen,
  Calculator,
  Users,
  Clock,
  ArrowRight,
  Sparkles,
  Award,
  TrendingUp,
  Shield,
  Globe
} from "lucide-react";

// Icon mapping for dynamic icon rendering
const iconMap = {
  Zap,
  Target,
  Award
};

const Pricing = () => {
  const { localizedPlans, countryConfig, isDetected, hasDiscount, discountPercentage } = useCountryPricing();

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl mb-6">
              Choose Your Learning Path
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Flexible subscription plans designed to fit your learning goals and budget.
              Get personalized tutoring with expert educators.
            </p>

            {/* Country-based pricing indicator */}
            {isDetected && (
              <>
                {/* Discount message if applicable */}
                {hasDiscount ? (
                  <div className="flex items-center justify-center space-x-2 mb-4 p-3 bg-green-50 border border-green-200 rounded-lg max-w-2xl mx-auto">
                    <img
                      src={countryConfig.flag}
                      alt={`${countryConfig.name} flag`}
                      width="20"
                      height="15"
                      className="inline-block"
                    />
                    <span className="text-sm text-green-800 font-medium">
                      Looks like you are from {countryConfig.name}, we are offering {discountPercentage}% discount
                    </span>
                  </div>
                ) : (
                  /* Regular pricing indicator - only show when no discount */
                  <div className="flex items-center justify-center space-x-2 mb-4">
                    <Globe className="w-5 h-5 text-blue-600" />
                    <span className="text-sm text-gray-600">
                      Prices shown in {countryConfig.currency.name} ({countryConfig.currency.code}) for {countryConfig.name}
                    </span>
                  </div>
                )}
              </>
            )}

            <div className="flex items-center justify-center space-x-2 mb-8">
              <Shield className="w-5 h-5 text-green-600" />
              <span className="text-sm text-gray-600">7-day money-back guarantee</span>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {localizedPlans.map((plan) => {
              const IconComponent = iconMap[plan.icon as keyof typeof iconMap];
              return (
                <Card
                  key={plan.id}
                  className={`relative overflow-hidden transition-all duration-300 hover:shadow-xl ${
                    plan.popular ? 'ring-2 ring-rfpurple-500 shadow-lg scale-105' : 'hover:shadow-lg'
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-rfpurple-600 to-rfpurple-500 text-white text-center py-2 text-sm font-semibold">
                      <Sparkles className="inline w-4 h-4 mr-1" />
                      Most Popular
                    </div>
                  )}

                  <CardHeader className={`text-center ${plan.popular ? 'pt-12' : 'pt-6'}`}>
                    <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center mx-auto mb-4`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                    <CardDescription className="text-gray-600 mt-2">
                      {plan.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="text-center">
                    {/* Pricing */}
                    <div className="mb-6">
                      {plan.priceType === 'fixed' ? (
                        <div>
                          <div className="flex items-center justify-center space-x-2 mb-2">
                            {plan.formattedOriginalPrice && (
                              <span className="text-lg text-gray-400 line-through">
                                {plan.formattedOriginalPrice}
                              </span>
                            )}
                            <span className="text-4xl font-bold text-gray-900">
                              {plan.formattedPrice}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">{plan.duration} access</p>
                        </div>
                      ) : (
                        <div>
                          <div className="text-4xl font-bold text-gray-900 mb-2">
                            {plan.formattedPrice}
                            <span className="text-lg font-normal text-gray-500">/session</span>
                          </div>
                          <p className="text-sm text-gray-500">{plan.duration} duration</p>
                          {plan.note && (
                            <p className="text-xs text-orange-600 mt-1 font-medium">
                              {plan.note}
                            </p>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Sessions Info */}
                    <div className="flex items-center justify-center space-x-2 mb-6 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>{plan.sessions}</span>
                    </div>

                    {/* Features */}
                    <div className="space-y-3 mb-8 text-left">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <Button
                      className={`w-full ${
                        plan.popular
                          ? 'button-gradient text-white'
                          : 'bg-gray-900 hover:bg-gray-800 text-white'
                      }`}
                      asChild
                    >
                      <Link to="/register">
                        Get Started
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Features Comparison */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
              Why Choose Our Platform?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                {
                  icon: Users,
                  title: 'Expert Tutors',
                  description: 'Qualified educators with proven track records'
                },
                {
                  icon: TrendingUp,
                  title: 'Progress Tracking',
                  description: 'Real-time analytics and performance insights'
                },
                {
                  icon: BookOpen,
                  title: 'Comprehensive Materials',
                  description: 'Access to extensive learning resources'
                },
                {
                  icon: Calculator,
                  title: 'Flexible Pricing',
                  description: 'Pay only for what you need with transparent pricing'
                }
              ].map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow duration-300">
                    <CardHeader>
                      <div className="w-12 h-12 bg-rfpurple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <IconComponent className="w-6 h-6 text-rfpurple-600" />
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
              Frequently Asked Questions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              {[
                {
                  question: 'How does the pricing work?',
                  answer: 'Complete Booster has fixed pricing for full subject coverage. Custom and Preparation packages are priced per session based on your specific needs.'
                },
                {
                  question: 'Can I change my plan later?',
                  answer: 'Yes, you can upgrade or modify your learning plan at any time. Contact our support team for assistance with plan changes.'
                },
                {
                  question: 'What if I\'m not satisfied?',
                  answer: 'We offer a 7-day money-back guarantee. If you\'re not completely satisfied, we\'ll refund your subscription.'
                },
                {
                  question: 'How are tutors assigned?',
                  answer: 'Our system matches you with qualified tutors based on your subject needs, learning style, and schedule preferences.'
                }
              ].map((faq, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{faq.answer}</CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-gradient-to-r from-rfpurple-600 to-rfpurple-500 rounded-2xl p-8 md:p-12 text-center text-white">
            <h2 className="text-3xl font-bold mb-4">Ready to Start Learning?</h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Join thousands of students who are achieving their academic goals with our personalized tutoring platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-rfpurple-600 hover:bg-gray-100" asChild>
                <Link to="/register">
                  <Star className="mr-2 h-5 w-5" />
                  Start Free Trial
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-rfpurple-600 bg-transparent" asChild>
                <Link to={ROUTES.CONTACT.path}>
                  <BookOpen className="mr-2 h-5 w-5" />
                  Contact Sales
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Pricing;

import React, { useState } from "react";
import { useTutorAvailabilityStore } from "@/store/tutorAvailabilityStore";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Switch } from "@/components/ui/Switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/Dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { Badge } from "@/components/ui/Badge";
import { Plus, Edit, Trash2, Check, X, Clock, Users, BookOpen } from "lucide-react";

const AutoAcceptRules: React.FC = () => {
  const {
    autoAcceptRules,
    addAutoAcceptRule,
    updateAutoAcceptRule,
    deleteAutoAcceptRule,
    toggleAutoAcceptRule,
  } = useTutorAvailabilityStore();

  const [isAddingRule, setIsAddingRule] = useState(false);
  const [editingRuleId, setEditingRuleId] = useState<string | null>(null);
  const [ruleName, setRuleName] = useState("");
  const [topics, setTopics] = useState<string[]>([]);
  const [existingStudentsOnly, setExistingStudentsOnly] = useState(false);
  const [timeRanges, setTimeRanges] = useState<
    { days: string[]; startTime?: string; endTime?: string }[]
  >([]);
  const [newTopic, setNewTopic] = useState("");

  // Reset form state
  const resetForm = () => {
    setRuleName("");
    setTopics([]);
    setExistingStudentsOnly(false);
    setTimeRanges([]);
    setNewTopic("");
  };

  // Open dialog to add a new rule
  const handleAddRule = () => {
    resetForm();
    setIsAddingRule(true);
    setEditingRuleId(null);
  };

  // Open dialog to edit an existing rule
  const handleEditRule = (ruleId: string) => {
    const rule = autoAcceptRules.find((r) => r.id === ruleId);
    if (rule) {
      setRuleName(rule.name);
      setTopics(rule.conditions.topics || []);
      setExistingStudentsOnly(rule.conditions.existingStudentsOnly || false);
      setTimeRanges(rule.conditions.timeRanges || []);
      setEditingRuleId(ruleId);
      setIsAddingRule(true);
    }
  };

  // Save a new or edited rule
  const handleSaveRule = () => {
    const ruleData = {
      name: ruleName,
      active: true,
      conditions: {
        topics: topics.length > 0 ? topics : undefined,
        existingStudentsOnly: existingStudentsOnly,
        timeRanges: timeRanges.length > 0 ? timeRanges : undefined,
      },
    };

    if (editingRuleId) {
      updateAutoAcceptRule(editingRuleId, ruleData);
    } else {
      addAutoAcceptRule(ruleData);
    }

    setIsAddingRule(false);
    resetForm();
  };

  // Add a topic to the list
  const handleAddTopic = () => {
    if (newTopic && !topics.includes(newTopic)) {
      setTopics([...topics, newTopic]);
      setNewTopic("");
    }
  };

  // Remove a topic from the list
  const handleRemoveTopic = (topic: string) => {
    setTopics(topics.filter((t) => t !== topic));
  };

  // Add a time range
  const handleAddTimeRange = () => {
    setTimeRanges([
      ...timeRanges,
      { days: ["Monday"], startTime: "09:00", endTime: "17:00" },
    ]);
  };

  // Update a time range
  const handleUpdateTimeRange = (
    index: number,
    field: "days" | "startTime" | "endTime",
    value: any
  ) => {
    const updatedRanges = [...timeRanges];
    updatedRanges[index] = { ...updatedRanges[index], [field]: value };
    setTimeRanges(updatedRanges);
  };

  // Remove a time range
  const handleRemoveTimeRange = (index: number) => {
    setTimeRanges(timeRanges.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Auto-Accept Rules</h2>
        <Button onClick={handleAddRule}>
          <Plus className="h-4 w-4 mr-2" /> Add Rule
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {autoAcceptRules.map((rule) => (
          <Card key={rule.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{rule.name}</CardTitle>
                <Switch
                  checked={rule.active}
                  onCheckedChange={() => toggleAutoAcceptRule(rule.id)}
                />
              </div>
              <CardDescription>
                {rule.active ? "Active" : "Inactive"}
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="space-y-3">
                {rule.conditions.topics && rule.conditions.topics.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium flex items-center mb-1">
                      <BookOpen className="h-4 w-4 mr-1 text-blue-500" /> Topics
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {rule.conditions.topics.map((topic) => (
                        <Badge key={topic} variant="outline">
                          {topic}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {rule.conditions.existingStudentsOnly && (
                  <div>
                    <h4 className="text-sm font-medium flex items-center">
                      <Users className="h-4 w-4 mr-1 text-blue-500" /> Students
                    </h4>
                    <p className="text-sm text-gray-500">
                      Only for existing students
                    </p>
                  </div>
                )}

                {rule.conditions.timeRanges &&
                  rule.conditions.timeRanges.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium flex items-center mb-1">
                        <Clock className="h-4 w-4 mr-1 text-blue-500" /> Time
                        Ranges
                      </h4>
                      <div className="space-y-1">
                        {rule.conditions.timeRanges.map((range, index) => (
                          <div key={index} className="text-sm text-gray-500">
                            {range.days.join(", ")}:{" "}
                            {range.startTime || "Any"} -{" "}
                            {range.endTime || "Any"}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditRule(rule.id)}
                >
                  <Edit className="h-4 w-4 mr-1" /> Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-500 hover:text-red-700"
                  onClick={() => deleteAutoAcceptRule(rule.id)}
                >
                  <Trash2 className="h-4 w-4 mr-1" /> Delete
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Add/Edit Rule Dialog */}
      <Dialog open={isAddingRule} onOpenChange={setIsAddingRule}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingRuleId ? "Edit Rule" : "Add New Rule"}
            </DialogTitle>
            <DialogDescription>
              Create rules for automatically accepting session requests that
              match specific criteria.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="rule-name">Rule Name</Label>
              <Input
                id="rule-name"
                value={ruleName}
                onChange={(e) => setRuleName(e.target.value)}
                placeholder="e.g., Python Sessions"
              />
            </div>

            <div className="space-y-2">
              <Label>Topics (Optional)</Label>
              <div className="flex space-x-2">
                <Input
                  value={newTopic}
                  onChange={(e) => setNewTopic(e.target.value)}
                  placeholder="e.g., Python, Math, Chemistry"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleAddTopic();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleAddTopic}
                >
                  Add
                </Button>
              </div>
              {topics.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {topics.map((topic) => (
                    <Badge key={topic} variant="secondary">
                      {topic}
                      <button
                        className="ml-1 text-gray-500 hover:text-red-500"
                        onClick={() => handleRemoveTopic(topic)}
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="existing-students"
                  checked={existingStudentsOnly}
                  onCheckedChange={(checked) =>
                    setExistingStudentsOnly(checked === true)
                  }
                />
                <Label htmlFor="existing-students">
                  Only for existing students
                </Label>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label>Time Ranges (Optional)</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddTimeRange}
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Time Range
                </Button>
              </div>

              {timeRanges.map((range, index) => (
                <Card key={index} className="p-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Days</Label>
                      <Select
                        value={range.days[0]}
                        onValueChange={(value) =>
                          handleUpdateTimeRange(index, "days", [value])
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select day" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Monday">Monday</SelectItem>
                          <SelectItem value="Tuesday">Tuesday</SelectItem>
                          <SelectItem value="Wednesday">Wednesday</SelectItem>
                          <SelectItem value="Thursday">Thursday</SelectItem>
                          <SelectItem value="Friday">Friday</SelectItem>
                          <SelectItem value="Saturday">Saturday</SelectItem>
                          <SelectItem value="Sunday">Sunday</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Start Time</Label>
                        <Input
                          type="time"
                          value={range.startTime || ""}
                          onChange={(e) =>
                            handleUpdateTimeRange(
                              index,
                              "startTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>End Time</Label>
                        <Input
                          type="time"
                          value={range.endTime || ""}
                          onChange={(e) =>
                            handleUpdateTimeRange(
                              index,
                              "endTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>

                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="text-red-500 hover:text-red-700"
                      onClick={() => handleRemoveTimeRange(index)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" /> Remove Range
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingRule(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveRule}>Save Rule</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AutoAcceptRules;

import { create } from "zustand";

interface DateField {
  value: string;
  error: string | null;
  touched: boolean;
}

interface DateOfBirthState {
  month: DateField;
  day: DateField;
  year: DateField;
  formattedDate: string | null;

  // Actions
  setMonth: (value: string) => void;
  setDay: (value: string) => void;
  setYear: (value: string) => void;
  touchField: (field: "month" | "day" | "year") => void;
  reset: (initialDate?: string | null) => void;
  validateDate: () => void;
}

export const useDateOfBirthStore = create<DateOfBirthState>((set, get) => ({
  month: { value: "", error: null, touched: false },
  day: { value: "", error: null, touched: false },
  year: { value: "", error: null, touched: false },
  formattedDate: null,

  setMonth: (value: string) => {
    const numValue = value.replace(/\D/g, "").slice(0, 2);
    let error: string | null = null;

    if (numValue && (parseInt(numValue) < 1 || parseInt(numValue) > 12)) {
      error = "Month must be between 1-12";
    }

    set((state) => ({
      month: {
        value: numValue,
        error,
        touched: true,
      },
    }));

    get().validateDate();
  },

  setDay: (value: string) => {
    const numValue = value.replace(/\D/g, "").slice(0, 2);
    let error: string | null = null;

    if (numValue && (parseInt(numValue) < 1 || parseInt(numValue) > 31)) {
      error = "Day must be between 1-31";
    }

    // More specific validation based on month
    if (!error && numValue && get().month.value) {
      const month = parseInt(get().month.value);
      const day = parseInt(numValue);

      // Check for months with 30 days
      if ([4, 6, 9, 11].includes(month) && day > 30) {
        error = "This month has only 30 days";
      }

      // Check February
      if (month === 2) {
        const year = parseInt(get().year.value);
        const isLeapYear =
          year && ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0);

        if (isLeapYear && day > 29) {
          error = "February has only 29 days in leap years";
        } else if (!isLeapYear && day > 28) {
          error = "February has only 28 days in non-leap years";
        } else if (day > 29) {
          error = "February has at most 29 days";
        }
      }
    }

    set((state) => ({
      day: {
        value: numValue,
        error,
        touched: true,
      },
    }));

    get().validateDate();
  },

  setYear: (value: string) => {
    const numValue = value.replace(/\D/g, "").slice(0, 4);
    let error: string | null = null;

    if (numValue.length === 4) {
      const year = parseInt(numValue);
      const currentYear = new Date().getFullYear();

      if (year < 1900) {
        error = "Year must be after 1900";
      } else if (year > currentYear) {
        error = "Year cannot be in the future";
      }
    }

    set((state) => ({
      year: {
        value: numValue,
        error,
        touched: true,
      },
    }));

    // If we have a day value, revalidate it since year affects February validation
    if (get().day.value) {
      get().setDay(get().day.value);
    }

    get().validateDate();
  },

  touchField: (field: "month" | "day" | "year") => {
    set((state) => ({
      [field]: {
        ...state[field],
        touched: true,
      },
    }));
  },

  validateDate: () => {
    const { month, day, year } = get();

    // Only create a date if all fields have values and no errors
    if (
      month.value &&
      day.value &&
      year.value.length === 4 &&
      !month.error &&
      !day.error &&
      !year.error
    ) {
      const dateStr = `${year.value}-${month.value.padStart(
        2,
        "0"
      )}-${day.value.padStart(2, "0")}`;
      set({ formattedDate: dateStr });
    } else {
      set({ formattedDate: null });
    }
  },

  reset: (initialDate: string | null = null) => {
    if (initialDate) {
      const date = new Date(initialDate);
      if (!isNaN(date.getTime())) {
        set({
          month: {
            value: (date.getMonth() + 1).toString().padStart(2, "0"),
            error: null,
            touched: false,
          },
          day: {
            value: date.getDate().toString().padStart(2, "0"),
            error: null,
            touched: false,
          },
          year: {
            value: date.getFullYear().toString(),
            error: null,
            touched: false,
          },
          formattedDate: initialDate,
        });
        return;
      }
    }

    // Reset to empty state if no valid date provided
    set({
      month: { value: "", error: null, touched: false },
      day: { value: "", error: null, touched: false },
      year: { value: "", error: null, touched: false },
      formattedDate: null,
    });
  },
}));


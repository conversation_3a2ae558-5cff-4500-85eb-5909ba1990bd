import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import BookingSteps from "@/components/booking/BookingSteps";
import UserInfoForm from "@/components/booking/UserInfoForm";
import { useToast } from "@/hooks/use-toast";

type UserFormData = {
  userType: "parent" | "student";
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  message?: string;
};

const BookingForm = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(2); // Start at the UserInfo step (step 2)
  
  // For demo purposes, use a hardcoded date/time
  const selectedDateTime = "Tuesday, April 15, 2025 5:00 PM";
  
  // Placeholder data for form submission
  const handleFormSubmit = (data: UserFormData) => {
    console.log("Form submitted:", data);
    toast({
      title: "Booking confirmed!",
      description: "Your session has been scheduled successfully.",
    });
    
    // Move to confirmation page or step
    setCurrentStep(3);
    
    // In a real implementation, you would likely navigate to a confirmation page
    // or show a confirmation step here
    setTimeout(() => {
      navigate("/student/dashboard");
    }, 2000);
  };
  
  // Go back to time selection
  const handleBack = () => {
    // In a real implementation, you would likely navigate back to the time selection page
    setCurrentStep(1);
    navigate(-1); // Navigate back to previous page instead of hardcoding
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-8 px-4 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <BookingSteps currentStep={currentStep} />
          
          {currentStep === 2 && (
            <UserInfoForm 
              onBack={handleBack}
              onSubmit={handleFormSubmit}
              selectedDateTime={selectedDateTime}
            />
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BookingForm;


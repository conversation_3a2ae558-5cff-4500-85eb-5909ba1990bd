-- Sample data for the learning platform database

-- Sample subjects
INSERT INTO subjects (name, description, icon) VALUES
('Mathematics', 'Mathematics curriculum covering algebra, calculus, and more', '📐'),
('Science', 'Science curriculum covering physics, chemistry, and biology', '🔬'),
('Computer Science', 'Computer science curriculum covering programming and algorithms', '💻');

-- Sample topics for Mathematics
INSERT INTO topics (subject_id, name, description, display_order) VALUES
('1f67f5f4-72d2-45de-8553-61e4173e0f9b', 'Number Systems', 'Fundamental of Number System', 1),
('1f67f5f4-72d2-45de-8553-61e4173e0f9b', 'Addition and Subtraction', 'NA', 2),
('1f67f5f4-72d2-45de-8553-61e4173e0f9b' ,'An Introduction to Multiplication', 'NA', 3);

-- Sample topics for Science
INSERT INTO topics (subject_id, name, description, display_order) VALUES
('*************-7777-7777-************', '22222222-2222-2222-2222-222222222222', 'Physics', 'Classical and modern physics', 1),
('*************-8888-8888-************', '22222222-2222-2222-2222-222222222222', 'Chemistry', 'Organic and inorganic chemistry', 2),
('*************-9999-9999-************', '22222222-2222-2222-2222-222222222222', 'Biology', 'Cell biology, genetics, and ecology', 3);

-- Sample subtopics for Number Systems
INSERT INTO subtopics (topic_id, name, description, state_standard, display_order) VALUES
('eb74a152-311a-4515-972b-943070582efa' ,'Numbers up to 99', 'NA', 'MTK.CC.1', 1),
('eb74a152-311a-4515-972b-943070582efa' ,'Comparing and Ordering Numbers up to 99', 'NA', 'MTK.CC.2', 2),
('eb74a152-311a-4515-972b-943070582efa' ,'Consolidation of Numbers up to 120', 'NA', 'MTK.CC.3', 3);

-- Sample subtopics for Biology
INSERT INTO subtopics (id, topic_id, name, description, state_standard, display_order) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', '*************-9999-9999-************', 'Cell Structure', 'Structure and function of cells', 'SCI.BIO.1', 1),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '*************-9999-9999-************', 'Genetics', 'Principles of inheritance and variation', 'SCI.BIO.2', 2),
('ffffffff-ffff-ffff-ffff-ffffffffffff', '*************-9999-9999-************', 'Ecology', 'Interactions between organisms and their environment', 'SCI.BIO.3', 3);

-- Sample resources for Integration subtopic
INSERT INTO resources (id, subtopic_id, name, type, url) VALUES
('11111111-aaaa-bbbb-cccc-dddddddddddd', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'Integration Basics', 'document', 'https://example.com/resources/integration-basics.pdf'),
('22222222-aaaa-bbbb-cccc-dddddddddddd', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'Integration by Parts Video', 'video', 'https://example.com/resources/integration-by-parts.mp4'),
('33333333-aaaa-bbbb-cccc-dddddddddddd', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'Integration Practice Problems', 'exercise', 'https://example.com/resources/integration-practice.pdf');

-- Sample users (these would be created through Supabase Auth)
-- For demonstration purposes only, assuming these users exist in auth.users

-- Sample profiles
INSERT INTO profiles (id, first_name, last_name, user_type, email, timezone) VALUES
('student1-uuid', 'Alice', 'Johnson', 'student', '<EMAIL>', 'America/New_York'),
('student2-uuid', 'Bob', 'Smith', 'student', '<EMAIL>', 'America/Chicago'),
('tutor1-uuid', 'Carol', 'Davis', 'tutor', '<EMAIL>', 'America/Los_Angeles'),
('tutor2-uuid', 'David', 'Wilson', 'tutor', '<EMAIL>', 'Europe/London'),
('admin1-uuid', 'Eve', 'Brown', 'admin', '<EMAIL>', 'America/New_York');

-- Sample batches
INSERT INTO batches (id, name, student_id, package_type, package_name, default_tutor_id, status, start_date, end_date, total_sessions, remaining_sessions) VALUES
('batch1-uuid', 'Fall 2023 - Mathematics', 'student1-uuid', 'complete_booster', 'Mathematics Booster', 'tutor1-uuid', 'active', '2023-09-01', '2023-12-15', 24, 14),
('batch2-uuid', 'Summer 2023 - Science', 'student2-uuid', 'preparation', 'Science SAT Prep', 'tutor2-uuid', 'active', '2023-06-01', '2023-08-30', 18, 0);

-- Sample batch topics
INSERT INTO batch_topics (id, batch_id, topic_id, custom_tutor_id, status) VALUES
('bt1-uuid', 'batch1-uuid', '*************-4444-4444-************', NULL, 'completed'), -- Algebra with default tutor
('bt2-uuid', 'batch1-uuid', '*************-5555-5555-************', 'tutor2-uuid', 'in_progress'), -- Calculus with custom tutor
('bt3-uuid', 'batch2-uuid', '*************-7777-7777-************', NULL, 'completed'), -- Physics with default tutor
('bt4-uuid', 'batch2-uuid', '*************-8888-8888-************', NULL, 'completed'); -- Chemistry with default tutor

-- Sample batch subtopics
INSERT INTO batch_subtopics (id, batch_topic_id, subtopic_id, custom_tutor_id, status) VALUES
('bs1-uuid', 'bt2-uuid', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', NULL, 'completed'), -- Limits with topic's custom tutor
('bs2-uuid', 'bt2-uuid', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', NULL, 'in_progress'), -- Derivatives with topic's custom tutor
('bs3-uuid', 'bt2-uuid', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'tutor1-uuid', 'not_started'); -- Integration with subtopic's custom tutor

-- Sample tutor availability
INSERT INTO tutor_availability (id, tutor_id, day_of_week, start_time, end_time, status) VALUES
('ta1-uuid', 'tutor1-uuid', 1, '09:00:00', '17:00:00', 'available'), -- Monday 9 AM - 5 PM
('ta2-uuid', 'tutor1-uuid', 3, '09:00:00', '17:00:00', 'available'), -- Wednesday 9 AM - 5 PM
('ta3-uuid', 'tutor1-uuid', 5, '09:00:00', '12:00:00', 'auto_accept'), -- Friday 9 AM - 12 PM (auto-accept)
('ta4-uuid', 'tutor2-uuid', 2, '10:00:00', '18:00:00', 'available'), -- Tuesday 10 AM - 6 PM
('ta5-uuid', 'tutor2-uuid', 4, '10:00:00', '18:00:00', 'available'); -- Thursday 10 AM - 6 PM

-- Sample auto-accept rules
INSERT INTO tutor_auto_accept_rules (id, tutor_id, name, is_active, existing_students_only) VALUES
('rule1-uuid', 'tutor1-uuid', 'Calculus Sessions', true, false),
('rule2-uuid', 'tutor1-uuid', 'Existing Students Only', true, true),
('rule3-uuid', 'tutor2-uuid', 'Morning Physics Sessions', true, false);

-- Sample rule topics
INSERT INTO rule_topics (id, rule_id, topic_id) VALUES
('rt1-uuid', 'rule1-uuid', '*************-5555-5555-************'), -- Calculus for rule1
('rt2-uuid', 'rule3-uuid', '*************-7777-7777-************'); -- Physics for rule3

-- Sample rule time ranges
INSERT INTO rule_time_ranges (id, rule_id, day_of_week, start_time, end_time) VALUES
('rtr1-uuid', 'rule1-uuid', 1, '09:00:00', '12:00:00'), -- Monday 9 AM - 12 PM for rule1
('rtr2-uuid', 'rule1-uuid', 3, '09:00:00', '12:00:00'), -- Wednesday 9 AM - 12 PM for rule1
('rtr3-uuid', 'rule3-uuid', 2, '09:00:00', '12:00:00'), -- Tuesday 9 AM - 12 PM for rule3
('rtr4-uuid', 'rule3-uuid', 4, '09:00:00', '12:00:00'); -- Thursday 9 AM - 12 PM for rule3

-- Sample sessions
INSERT INTO sessions (id, batch_id, topic_id, subtopic_id, tutor_id, student_id, scheduled_at, duration_min, status, mode, created_by) VALUES
('session1-uuid', 'batch1-uuid', '*************-4444-4444-************', NULL, 'tutor1-uuid', 'student1-uuid', '2023-09-15 10:00:00', 60, 'completed', 'video', 'admin'),
('session2-uuid', 'batch1-uuid', '*************-5555-5555-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'tutor2-uuid', 'student1-uuid', '2023-10-01 14:00:00', 60, 'completed', 'video', 'admin'),
('session3-uuid', 'batch1-uuid', '*************-5555-5555-************', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'tutor2-uuid', 'student1-uuid', '2023-10-15 14:00:00', 60, 'completed', 'video', 'admin'),
('session4-uuid', 'batch1-uuid', '*************-5555-5555-************', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'tutor2-uuid', 'student1-uuid', '2023-11-01 14:00:00', 60, 'scheduled', 'video', 'student'),
('session5-uuid', 'batch2-uuid', '*************-7777-7777-************', NULL, 'tutor2-uuid', 'student2-uuid', '2023-06-15 10:00:00', 90, 'completed', 'video', 'admin'),
('session6-uuid', 'batch2-uuid', '*************-8888-8888-************', NULL, 'tutor2-uuid', 'student2-uuid', '2023-07-01 10:00:00', 90, 'completed', 'video', 'admin');

-- Sample session details
INSERT INTO session_details (session_id, tutor_talk_time, whiteboard_interactions, messages_sent, messages_received, participation_score, participation_level, notes) VALUES
('session1-uuid', 35, 8, 12, 15, 4.2, 'Engaged', 'Alice showed good understanding of algebraic concepts.'),
('session2-uuid', 40, 10, 15, 18, 4.5, 'Highly Engaged', 'Alice asked excellent questions about limits.'),
('session3-uuid', 38, 12, 14, 16, 4.3, 'Engaged', 'Alice is making good progress with derivatives.'),
('session5-uuid', 50, 15, 20, 25, 4.8, 'Highly Engaged', 'Bob was very enthusiastic about physics concepts.'),
('session6-uuid', 45, 12, 18, 20, 4.5, 'Engaged', 'Bob showed good understanding of chemical reactions.');

-- Sample session feedback
INSERT INTO session_feedback (id, session_id, submitted_by, rating, comments) VALUES
('sf1-uuid', 'session1-uuid', 'student1-uuid', 5, 'Great session! The tutor explained everything clearly.'),
('sf2-uuid', 'session1-uuid', 'tutor1-uuid', 4, 'Alice is a dedicated student who asks good questions.'),
('sf3-uuid', 'session2-uuid', 'student1-uuid', 5, 'I finally understand limits! Thank you.'),
('sf4-uuid', 'session5-uuid', 'student2-uuid', 5, 'The physics concepts make much more sense now.'),
('sf5-uuid', 'session5-uuid', 'tutor2-uuid', 5, 'Bob is an excellent student with great analytical skills.');

-- Sample session requests
INSERT INTO session_requests (id, batch_id, topic_id, subtopic_id, requested_tutor_id, student_id, requested_date, requested_time, duration_min, notes, status, urgency, auto_accepted) VALUES
('req1-uuid', 'batch1-uuid', '*************-5555-5555-************', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'tutor1-uuid', 'student1-uuid', '2023-11-15', '14:00:00', 60, 'I need help with integration by parts.', 'pending', 'medium', false),
('req2-uuid', 'batch1-uuid', '*************-6666-6666-************', NULL, 'tutor1-uuid', 'student1-uuid', '2023-11-20', '10:00:00', 60, 'I would like to start learning geometry.', 'accepted', 'low', true),
('req3-uuid', 'batch2-uuid', '*************-9999-9999-************', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'tutor2-uuid', 'student2-uuid', '2023-08-25', '15:00:00', 90, 'I need help with cell structure before my exam.', 'rejected', 'high', false);


// Sample data for development
const sampleAvailabilitySlots: AvailabilitySlot[] = [
  {
    id: "slot1",
    day: "Monday",
    startTime: "09:00",
    endTime: "12:00",
    status: "available",
  },
  {
    id: "slot2",
    day: "Monday",
    startTime: "14:00",
    endTime: "17:00",
    status: "auto-accept",
  },
  {
    id: "slot3",
    day: "Wednesday",
    startTime: "10:00",
    endTime: "14:00",
    status: "manual-approval",
  },
  {
    id: "slot4",
    day: "Friday",
    startTime: "13:00",
    endTime: "18:00",
    status: "available",
  },
];

const sampleAutoAcceptRules: AutoAcceptRule[] = [
  {
    id: "rule1",
    name: "Python Sessions",
    active: true,
    conditions: {
      topics: ["Python", "Programming Basics"],
      existingStudentsOnly: false,
      timeRanges: [
        {
          days: ["Monday", "Wednesday"],
          startTime: "09:00",
          endTime: "12:00",
        },
      ],
    },
  },
  {
    id: "rule2",
    name: "Existing Students Only",
    active: true,
    conditions: {
      existingStudentsOnly: true,
    },
  },
];
import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { useToast } from "@/components/ui/UseToast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/Form";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Loader2 } from "lucide-react";
import SubjectSelection from "@/components/ui/SubjectSelection";
import FileUploadCV from "@/components/ui/FileUploadCV";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import { InfoIcon } from "lucide-react";
import { Separator } from "@/components/ui/Separator";
import GuestTutorPersonalInfo from "@/components/tutor/GuestTutorPersonalInfo";
import { useEmailVerification } from "@/hooks/useEmailVerification";
import { useFileUpload } from "@/hooks/useFileUpload";
import {
  TutorFormData,
  createGuestTutor,
  createAuthenticatedTutor,
  updateUserProfileToTutor,
  getUserSession,
} from "@/services/guestTutorService";
import { useGuestTutorFormStore } from "@/store/guestTutorFormStore";
import { useGuestTutorForm, TutorFormValues } from "@/hooks/useGuestTutorForm";

const BecomeTutor = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use the email verification hook
  const {
    isVerifyingEmail,
    emailVerified,
    verificationCountdown,
    isCheckingEmail,
    emailChecked,
    emailValid,
    validateEmailFormat,
    handleVerifyEmail,
    checkVerificationStatus,
  } = useEmailVerification({
    source: "become-tutor",
    onVerificationSuccess: (email) => {
      if (email) {
        form.setValue("email", email);
      }
    },
  });

  const form = useGuestTutorForm();

  // Get form state from the store including the new formErrors property
  const {
    isFormValid,
    firstName,
    lastName,
    email,
    phoneNumber,
    subjects,
    cvFile,
    formErrors,
    setFormErrors,
    setSubjects,
    setCvFile,
  } = useGuestTutorFormStore();

  useEffect(() => {
    // Check if there's a pending tutor email from the Hero component
    const pendingEmail = localStorage.getItem("pendingTutorEmail");
    if (pendingEmail) {
      form.setValue("email", pendingEmail);
      // Validate the pre-filled email
      validateEmailFormat(pendingEmail);
    }
  }, []);

  // Function to handle the verify email button click
  const handleVerifyEmailClick = () => {
    const emailValue = form.getValues("email");
    handleVerifyEmail(emailValue);
  };

  // Add a useEffect to use checkVerificationStatus from the hook
  useEffect(() => {
    // Check verification status on mount
    checkVerificationStatus();

    // You can also set up additional checks if needed
    // For example, check when returning to the page
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && !emailVerified) {
        checkVerificationStatus();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  const {
    uploadStatus: cvUploadStatus,
    fileName: cvFileName,
    uploadError: cvUploadError,
    fileInputRef: cvFileInputRef,
    handleFileSelect: handleCVFileSelect,
    handleFileRemove: handleCVFileRemove,
    uploadFile: uploadCVFile,
  } = useFileUpload({
    maxSize: 10 * 1024 * 1024, // 10MB
    acceptedFileTypes: [
      ".pdf",
      ".doc",
      ".docx",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
    bucketName: "tutor-uploads",
    folderPath: "cv",
    onSuccess: (filePath) => {
      // Update form value with the file path
      form.setValue("cvFile", filePath);
      form.clearErrors("cvFile");

      // Validate using the Zustand store's validateField function
      const validation = useGuestTutorFormStore
        .getState()
        .validateField("cvFile", filePath);
      setCvFile(filePath, validation.isValid, validation.error);
    },
  });

  // Add a useEffect to handle upload errors
  useEffect(() => {
    if (cvUploadError) {
      setCvFile("", false, cvUploadError);
    }
  }, [cvUploadError, setCvFile]);

  const onSubmit = async (values: TutorFormValues) => {
    if (!emailVerified) {
      toast({
        title: "Email verification required",
        description:
          "Please verify your email before submitting your application",
        variant: "destructive",
      });
      return;
    }

    // Touch all fields to show validation errors
    useGuestTutorFormStore.getState().touchAllFields();

    // Check if form is valid using the Zustand store
    if (!isFormValid) {
      // Show specific errors from form
      const errorMessage =
        Object.values(formErrors)[0] ||
        "Please complete all required fields correctly";

      toast({
        title: "Validation Error",
        description: errorMessage,
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Create a properly typed object to pass to the service
      const tutorData: TutorFormData = {
        email: values.email,
        firstName: values.firstName,
        lastName: values.lastName,
        hourlyRate: values.hourlyRate,
        subjects: values.subjects,
        cvFile: values.cvFile,
        phoneNumber: values.phoneNumber,
      };

      // First, check if user is authenticated
      const session = await getUserSession();

      if (!session) {
        // User is not authenticated, this is a guest tutor
        await createGuestTutor(tutorData);

        toast({
          title: "Application submitted",
          description:
            "Thank you for applying to be a tutor! We'll review your application soon.",
        });

        // Clear the pending email from localStorage
        localStorage.removeItem("pendingTutorEmail");

        navigate("/dashboard");
        return;
      }

      // User is authenticated, proceed with tutor registration
      await createAuthenticatedTutor(session.user.id, tutorData);

      // Update the user type to 'tutor' in profiles table
      await updateUserProfileToTutor(
        session.user.id,
        values.firstName,
        values.lastName
      );

      toast({
        title: "Application submitted",
        description:
          "Thank you for applying to be a tutor! We'll review your application soon.",
      });

      navigate("/dashboard");
    } catch (error) {
      console.error("Error in tutor application:", error);

      // Use our error handler for all errors
      let errorMessage = "Please try again later";

      toast({
        title: "Submission failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render submit button based on status
  const renderSubmitButton = () => {
    if (isSubmitting) {
      return (
        <Button type="submit" className="button-gradient text-white" disabled>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Submitting...
        </Button>
      );
    }

    return (
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          type="submit"
          className="button-gradient text-white"
          disabled={!isFormValid || !emailVerified || !form.formState.isValid}
        >
          Submit Application
        </Button>
      </motion.div>
    );
  };

  // Add this useEffect to sync Zustand store with form when needed
  useEffect(() => {
    // Only update form if values exist in store and form is empty
    // This prevents overwriting user input
    if (firstName.value && !form.getValues("firstName")) {
      form.setValue("firstName", firstName.value);
    }
    if (lastName.value && !form.getValues("lastName")) {
      form.setValue("lastName", lastName.value);
    }
    if (email.value && !form.getValues("email")) {
      form.setValue("email", email.value);
    }
    if (phoneNumber.value && !form.getValues("phoneNumber")) {
      form.setValue("phoneNumber", phoneNumber.value);
    }
  }, [firstName.value, lastName.value, email.value, phoneNumber.value]);

  // Add this useEffect to sync Zustand store with form on initial load
  useEffect(() => {
    // Pre-fill form with store values if they exist
    if (firstName.value) {
      form.setValue("firstName", firstName.value, {
        shouldValidate: true, // This triggers validation
      });
    }
    // Do the same for other fields...
  }, []);

  // Update the handleFileRemove function to clear the CV file in the Zustand store
  const handleCVFileRemoveWithState = () => {
    handleCVFileRemove();
    form.setValue("cvFile", "");

    // Validate the empty value
    const validation = useGuestTutorFormStore
      .getState()
      .validateField("cvFile", "");
    setCvFile("", validation.isValid, validation.error);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow bg-gray-50 py-12">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Become a Tutor
            </h1>
            <p className="mt-4 text-lg text-gray-600">
              Share your knowledge and help students succeed while earning
              flexible income
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Tutor Application</CardTitle>
              <CardDescription>
                Fill out the form below to apply as a tutor on our platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  {/* Use the TutorFormPersonalInfo component */}
                  <GuestTutorPersonalInfo
                    form={form}
                    isCheckingEmail={isCheckingEmail}
                    emailChecked={emailChecked}
                    emailValid={emailValid}
                    isVerifyingEmail={isVerifyingEmail}
                    emailVerified={emailVerified}
                    verificationCountdown={verificationCountdown}
                    handleVerifyEmail={handleVerifyEmailClick}
                    // Pass the store validation state
                    storeValidationState={{
                      firstName,
                      lastName,
                      email,
                      phoneNumber,
                      isFormValid,
                    }}
                  />

                  {/* Separator for Teaching Information */}
                  <div className="relative py-4">
                    <div className="absolute inset-0 flex items-center px-4">
                      <Separator className="w-full" />
                    </div>
                    <div className="relative flex justify-start">
                      <span className="bg-white px-2 text-lg font-medium">
                        Teaching Information
                      </span>
                    </div>
                  </div>

                  {/* Subject Selection */}
                  <FormField
                    control={form.control}
                    name="subjects"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex text-base font-medium">
                          Subjects You Can Teach{" "}
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <SubjectSelection useSimpleToggle={true} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Hourly Rate */}
                  <FormField
                    control={form.control}
                    name="hourlyRate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center">
                          Hourly Rate (USD){" "}
                          <span className="text-red-500 ml-1">*</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <InfoIcon className="h-4 w-4 text-muted-foreground ml-2 cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                Set your hourly rate. You can adjust this later.
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="absolute left-3 top-2.5">$</span>
                            <Input
                              {...field}
                              type="number"
                              min="1"
                              step="0.01"
                              className="pl-7"
                              placeholder="0.00"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Separator for Documents */}
                  <div className="relative py-4">
                    <div className="absolute inset-0 flex items-center px-4">
                      <Separator className="w-full" />
                    </div>
                    <div className="relative flex justify-start">
                      <span className="bg-white px-2 text-lg font-medium">
                        Documents
                      </span>
                    </div>
                  </div>

                  {/* CV Upload */}
                  <FormField
                    control={form.control}
                    name="cvFile"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex">
                          Upload Your CV{" "}
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <FileUploadCV
                            status={cvUploadStatus}
                            fileName={cvFileName}
                            errorMessage={cvUploadError}
                            onFileSelect={handleCVFileSelect}
                            onFileRemove={handleCVFileRemoveWithState}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end pt-4">
                    {renderSubmitButton()}
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>

          <div className="mt-12 bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-8">
              <h2 className="text-2xl font-bold text-gray-900">
                Why Become a Tutor With Us?
              </h2>
              <div className="mt-6 grid grid-cols-1 gap-8 md:grid-cols-2">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-rfpurple-500 text-white">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Competitive Pay
                    </h3>
                    <p className="mt-2 text-base text-gray-500">
                      Set your own rates and earn what you deserve for your
                      expertise and time.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-rfpurple-500 text-white">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Flexible Schedule
                    </h3>
                    <p className="mt-2 text-base text-gray-500">
                      Choose when you tutor and how many hours you want to
                      commit each week.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-rfpurple-500 text-white">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Make an Impact
                    </h3>
                    <p className="mt-2 text-base text-gray-500">
                      Help students achieve their goals and see the direct
                      results of your teaching.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-rfpurple-500 text-white">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={1.5}
                        stroke="currentColor"
                        className="w-6 h-6"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Advanced Platform
                    </h3>
                    <p className="mt-2 text-base text-gray-500">
                      Use our AI-enhanced tools to improve your teaching and
                      student outcomes.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BecomeTutor;

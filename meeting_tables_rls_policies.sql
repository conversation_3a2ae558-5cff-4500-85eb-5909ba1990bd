-- RLS Policies for Meeting Integration Tables
-- This file contains Row Level Security policies for meeting-related tables

-- =====================================================
-- 1. MEETING_PROVIDERS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on meeting_providers table
ALTER TABLE public.meeting_providers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Public can view active meeting providers" ON meeting_providers;
DROP POLICY IF EXISTS "Admins can manage meeting providers" ON meeting_providers;
DROP POLICY IF EXISTS "Service role full access meeting providers" ON meeting_providers;

-- Public can view active meeting providers (for provider selection)
CREATE POLICY "Public can view active meeting providers"
ON public.meeting_providers
FOR SELECT
TO authenticated
USING (is_active = true);

-- <PERSON><PERSON> can manage all meeting providers
CREATE POLICY "<PERSON><PERSON> can manage meeting providers"
ON public.meeting_providers
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access meeting providers"
ON public.meeting_providers
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 2. USER_MEETING_PREFERENCES TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on user_meeting_preferences table
ALTER TABLE public.user_meeting_preferences ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can manage their own meeting preferences" ON user_meeting_preferences;
DROP POLICY IF EXISTS "Admins can view all meeting preferences" ON user_meeting_preferences;
DROP POLICY IF EXISTS "Service role full access meeting preferences" ON user_meeting_preferences;

-- Users can manage their own meeting preferences
CREATE POLICY "Users can manage their own meeting preferences"
ON public.user_meeting_preferences
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Admins can view all meeting preferences (read-only for support)
CREATE POLICY "Admins can view all meeting preferences"
ON public.user_meeting_preferences
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access meeting preferences"
ON public.user_meeting_preferences
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 3. MEETING_SESSIONS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on meeting_sessions table
ALTER TABLE public.meeting_sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view meeting sessions for their sessions" ON meeting_sessions;
DROP POLICY IF EXISTS "Admins can view all meeting sessions" ON meeting_sessions;
DROP POLICY IF EXISTS "Users can create meeting sessions for their sessions" ON meeting_sessions;
DROP POLICY IF EXISTS "Admins can create meeting sessions" ON meeting_sessions;
DROP POLICY IF EXISTS "Users can update meeting sessions for their sessions" ON meeting_sessions;
DROP POLICY IF EXISTS "Admins can update all meeting sessions" ON meeting_sessions;
DROP POLICY IF EXISTS "Service role full access meeting sessions" ON meeting_sessions;

-- Users can view meeting sessions for sessions they participate in
CREATE POLICY "Users can view meeting sessions for their sessions"
ON public.meeting_sessions
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    WHERE s.id = meeting_sessions.session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
);

-- Admins can view all meeting sessions
CREATE POLICY "Admins can view all meeting sessions"
ON public.meeting_sessions
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Users can create meeting sessions for their sessions
CREATE POLICY "Users can create meeting sessions for their sessions"
ON public.meeting_sessions
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.sessions s
    WHERE s.id = meeting_sessions.session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
);

-- Admins can create meeting sessions
CREATE POLICY "Admins can create meeting sessions"
ON public.meeting_sessions
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Users can update meeting sessions for their sessions
CREATE POLICY "Users can update meeting sessions for their sessions"
ON public.meeting_sessions
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.sessions s
    WHERE s.id = meeting_sessions.session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.sessions s
    WHERE s.id = meeting_sessions.session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
);

-- Admins can update all meeting sessions
CREATE POLICY "Admins can update all meeting sessions"
ON public.meeting_sessions
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access meeting sessions"
ON public.meeting_sessions
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 4. MEETING_PARTICIPANTS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on meeting_participants table
ALTER TABLE public.meeting_participants ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view meeting participants for their meetings" ON meeting_participants;
DROP POLICY IF EXISTS "Admins can view all meeting participants" ON meeting_participants;
DROP POLICY IF EXISTS "Users can create their own meeting participation" ON meeting_participants;
DROP POLICY IF EXISTS "Admins can create meeting participants" ON meeting_participants;
DROP POLICY IF EXISTS "Users can update their own meeting participation" ON meeting_participants;
DROP POLICY IF EXISTS "Admins can update all meeting participants" ON meeting_participants;
DROP POLICY IF EXISTS "Service role full access meeting participants" ON meeting_participants;

-- Users can view meeting participants for meetings they participate in
CREATE POLICY "Users can view meeting participants for their meetings"
ON public.meeting_participants
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.meeting_sessions ms
    JOIN public.sessions s ON s.id = ms.session_id
    WHERE ms.id = meeting_participants.meeting_session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
);

-- Admins can view all meeting participants
CREATE POLICY "Admins can view all meeting participants"
ON public.meeting_participants
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Users can create their own meeting participation records
CREATE POLICY "Users can create their own meeting participation"
ON public.meeting_participants
FOR INSERT
TO authenticated
WITH CHECK (
  user_id = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.meeting_sessions ms
    JOIN public.sessions s ON s.id = ms.session_id
    WHERE ms.id = meeting_participants.meeting_session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
);

-- Admins can create meeting participants
CREATE POLICY "Admins can create meeting participants"
ON public.meeting_participants
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Users can update their own meeting participation
CREATE POLICY "Users can update their own meeting participation"
ON public.meeting_participants
FOR UPDATE
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Admins can update all meeting participants
CREATE POLICY "Admins can update all meeting participants"
ON public.meeting_participants
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access meeting participants"
ON public.meeting_participants
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 5. MEETING_EVENTS TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on meeting_events table
ALTER TABLE public.meeting_events ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view meeting events for their meetings" ON meeting_events;
DROP POLICY IF EXISTS "Admins can view all meeting events" ON meeting_events;
DROP POLICY IF EXISTS "Users can create meeting events for their meetings" ON meeting_events;
DROP POLICY IF EXISTS "Admins can create meeting events" ON meeting_events;
DROP POLICY IF EXISTS "Service role full access meeting events" ON meeting_events;

-- Users can view meeting events for meetings they participate in
CREATE POLICY "Users can view meeting events for their meetings"
ON public.meeting_events
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.meeting_sessions ms
    JOIN public.sessions s ON s.id = ms.session_id
    WHERE ms.id = meeting_events.meeting_session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
);

-- Admins can view all meeting events
CREATE POLICY "Admins can view all meeting events"
ON public.meeting_events
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Users can create meeting events for their meetings
CREATE POLICY "Users can create meeting events for their meetings"
ON public.meeting_events
FOR INSERT
TO authenticated
WITH CHECK (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.meeting_sessions ms
    JOIN public.sessions s ON s.id = ms.session_id
    WHERE ms.id = meeting_events.meeting_session_id
    AND (s.student_id = auth.uid() OR s.tutor_id = auth.uid())
  )
);

-- Admins can create meeting events
CREATE POLICY "Admins can create meeting events"
ON public.meeting_events
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND user_type = 'admin'
  )
);

-- Service role full access
CREATE POLICY "Service role full access meeting events"
ON public.meeting_events
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- =====================================================
-- 6. VERIFICATION FUNCTIONS
-- =====================================================

-- Function to verify RLS policies are enabled on all meeting tables
CREATE OR REPLACE FUNCTION verify_meeting_rls_policies()
RETURNS TABLE (
  table_name TEXT,
  rls_enabled BOOLEAN,
  policy_count INTEGER
) AS $$
BEGIN
  SET search_path TO public;

  RETURN QUERY
  SELECT
    t.tablename::TEXT,
    t.rowsecurity as rls_enabled,
    (
      SELECT COUNT(*)::INTEGER
      FROM pg_policies p
      WHERE p.tablename = t.tablename
    ) as policy_count
  FROM pg_tables t
  WHERE t.schemaname = 'public'
  AND t.tablename IN (
    'meeting_providers', 'user_meeting_preferences', 'meeting_sessions',
    'meeting_participants', 'meeting_events'
  )
  ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to list all meeting-related policies
CREATE OR REPLACE FUNCTION list_meeting_policies()
RETURNS TABLE (
  table_name TEXT,
  policy_name TEXT,
  command TEXT,
  roles TEXT[],
  policy_condition TEXT
) AS $$
BEGIN
  SET search_path TO public;

  RETURN QUERY
  SELECT
    p.tablename::TEXT,
    p.policyname::TEXT,
    p.cmd::TEXT,
    p.roles,
    p.qual::TEXT
  FROM pg_policies p
  WHERE p.tablename IN (
    'meeting_providers', 'user_meeting_preferences', 'meeting_sessions',
    'meeting_participants', 'meeting_events'
  )
  ORDER BY p.tablename, p.policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 7. VERIFICATION QUERIES
-- =====================================================

-- Check RLS status for all meeting tables
SELECT
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    CASE
        WHEN rowsecurity THEN 'RLS is ENABLED'
        ELSE 'RLS is DISABLED'
    END as status
FROM pg_tables
WHERE tablename IN ('meeting_providers', 'user_meeting_preferences', 'meeting_sessions', 'meeting_participants', 'meeting_events')
ORDER BY tablename;

-- Count policies per table
SELECT
    tablename,
    COUNT(*) as policy_count
FROM pg_policies
WHERE tablename IN ('meeting_providers', 'user_meeting_preferences', 'meeting_sessions', 'meeting_participants', 'meeting_events')
GROUP BY tablename
ORDER BY tablename;

-- Usage examples:
-- SELECT * FROM verify_meeting_rls_policies();
-- SELECT * FROM list_meeting_policies();

-- End of Meeting Tables RLS Policies

-- Fix: <PERSON><PERSON> should fire on INSERT when onboarding_completed = true
-- This handles the case where students complete onboarding and get inserted with onboarding_completed = true
DROP TRIGGER IF EXISTS on_student_candidate_completed ON candidate_student;

CREATE TRIGGER on_student_candidate_completed
    AFTER INSERT
    ON public.candidate_student
    FOR EACH ROW
    WHEN (NEW.onboarding_completed = true)
    EXECUTE FUNCTION handle_student_candidate_completion();

-- Also create a trigger for UPDATE operations in case onboarding status changes from false to true
DROP TRIGGER IF EXISTS on_student_candidate_updated ON candidate_student;

CREATE TRIGGER on_student_candidate_updated
    AFTER UPDATE OF onboarding_completed
    ON public.candidate_student
    FOR EACH ROW
    WHEN (
        OLD.onboarding_completed IS DISTINCT FROM NEW.onboarding_completed
        AND NEW.onboarding_completed = true
    )
    EXECUTE FUNCTION handle_student_candidate_completion();

-- =====================================================
-- TUTOR TRIGGERS - Similar logic for tutor onboarding
-- =====================================================

-- Trigger should fire on INSERT when onboarding_completed = true
-- This handles the case where tutors complete onboarding and get inserted with onboarding_completed = true
DROP TRIGGER IF EXISTS on_tutor_candidate_completed ON candidate_tutor;

CREATE TRIGGER on_tutor_candidate_completed
    AFTER INSERT
    ON public.candidate_tutor
    FOR EACH ROW
    WHEN (NEW.onboarding_completed = true)
    EXECUTE FUNCTION handle_tutor_candidate_completion();

-- Also create a trigger for UPDATE operations in case onboarding status changes from false to true
DROP TRIGGER IF EXISTS on_tutor_candidate_updated ON candidate_tutor;

CREATE TRIGGER on_tutor_candidate_updated
    AFTER UPDATE OF onboarding_completed
    ON public.candidate_tutor
    FOR EACH ROW
    WHEN (
        OLD.onboarding_completed IS DISTINCT FROM NEW.onboarding_completed
        AND NEW.onboarding_completed = true
    )
    EXECUTE FUNCTION handle_tutor_candidate_completion();

-- Verify all triggers were created correctly
SELECT
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement,
    action_condition
FROM information_schema.triggers
WHERE trigger_name IN (
    'on_student_candidate_completed',
    'on_student_candidate_updated',
    'on_tutor_candidate_completed',
    'on_tutor_candidate_updated'
)
ORDER BY trigger_name;

-- Test the fix by manually triggering it
-- For the existing record that already has onboarding_completed = true
UPDATE candidate_student
SET onboarding_completed = true,
    updated_at = NOW()
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Check if records were created after the fix
SELECT 'After fix - Profiles:' as check_type, COUNT(*) as count
FROM profiles
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d'

UNION ALL

SELECT 'After fix - Students:' as check_type, COUNT(*) as count
FROM students
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d'

UNION ALL

SELECT 'After fix - Logs:' as check_type, COUNT(*) as count
FROM logs
WHERE user_id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Show the actual records if they exist
SELECT
    'Profile:' as type,
    id,
    first_name,
    last_name,
    user_type,
    email,
    created_at
FROM profiles
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d'

UNION ALL

SELECT
    'Student:' as type,
    s.id,
    s.education_level,
    array_to_string(s.subjects_of_interest, ', ') as subjects,
    array_to_string(s.learning_goals, ', ') as goals,
    s.date_of_birth::text,
    s.created_at::text
FROM students s
WHERE s.id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Show any logs
SELECT
    created_at,
    level,
    message,
    data->>'error_detail' as error_detail
FROM logs
WHERE user_id = '715d2b84-cc4a-443b-bee1-74e80725b21d'
ORDER BY created_at DESC;

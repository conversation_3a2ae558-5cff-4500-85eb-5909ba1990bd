import { supabase } from "@/lib/supabaseClient";

export interface TutorFormData {
  email: string;
  firstName: string;
  lastName: string;
  hourlyRate: string;
  subjects: string[];
  cvFile: string;
  phoneNumber?: string; // Make this optional to match your form
}

/**
 * Creates or updates a guest tutor record in the database
 * @param formData The tutor form data
 * @returns The ID of the created/updated guest tutor
 */
export const createGuestTutor = async (formData: TutorFormData) => {
  const { data: guestTutorId, error } = await supabase.rpc(
    "create_guest_tutor",
    {
      p_email: formData.email,
      p_first_name: formData.firstName,
      p_last_name: formData.lastName,
      p_hourly_rate: parseFloat(formData.hourlyRate),
      p_subjects_taught: formData.subjects,
      p_cv_file_path: formData.cvFile,
    }
  );

  if (error) {
    console.error("Error creating guest tutor:", error);
    throw new Error(error.message);
  }

  return guestTutorId;
};

/**
 * Creates a tutor record for an authenticated user
 * @param userId The authenticated user's ID
 * @param formData The tutor form data
 */
export const createAuthenticatedTutor = async (
  userId: string,
  formData: TutorFormData
) => {
  const { error } = await supabase.from("candidate_tutor").insert({
    user_id: userId,
    first_name: formData.firstName,
    last_name: formData.lastName,
    hourly_rate: parseFloat(formData.hourlyRate),
    subjects: formData.subjects,
    cv_file_path: formData.cvFile,
  });

  if (error) {
    console.error("Error submitting tutor application:", error);
    throw new Error(error.message);
  }
};

/**
 * Updates the user profile to mark them as a tutor
 * @param userId The user's ID
 * @param firstName The user's first name
 * @param lastName The user's last name
 */
export const updateUserProfileToTutor = async (
  userId: string,
  firstName: string,
  lastName: string
) => {
  const { error } = await supabase
    .from("profiles")
    .update({
      first_name: firstName,
      last_name: lastName,
      user_type: "tutor",
    })
    .eq("id", userId);

  if (error) {
    console.error("Error updating profile:", error);
    throw new Error(error.message);
  }
};

/**
 * Gets the current user session
 * @returns The user session or null if not authenticated
 */
export const getUserSession = async () => {
  const { data } = await supabase.auth.getSession();
  return data.session;
};

import { ReactNode } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { User, MapPin } from "lucide-react";
import { CalendarEvent } from "./StudentCalendar";

interface UpcomingEventsProps {
  calendarEvents: CalendarEvent[];
}

const UpcomingEvents = ({ calendarEvents }: UpcomingEventsProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Upcoming Events</CardTitle>
        <CardDescription>
          Your scheduled sessions and activities for the next 7 days
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {calendarEvents
            .sort((a, b) => a.date.getTime() - b.date.getTime())
            .slice(0, 5)
            .map(event => (
              <div key={event.id} className={`p-3 rounded-lg border ${event.color}`}>
                <div className="flex items-start">
                  <div className="mr-3 mt-0.5">{event.icon}</div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium">{event.title}</h4>
                      <Badge variant="outline" className="ml-2">
                        {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                      </Badge>
                    </div>
                    <div className="text-sm mt-1">
                      {event.date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}, {event.startTime} - {event.endTime}
                    </div>
                    <div className="flex items-center mt-2 text-sm">
                      <div className="flex items-center mr-4">
                        <User className="h-3.5 w-3.5 mr-1 opacity-70" />
                        <span>{event.tutorName}</span>
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-3.5 w-3.5 mr-1 opacity-70" />
                        <span>{event.location}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default UpcomingEvents;

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Settings, Clock, RefreshCw } from "lucide-react";

interface UnderMaintenanceProps {
  title?: string;
  message?: string;
  showHomeButton?: boolean;
  showBackButton?: boolean;
  estimatedTime?: string;
}

// TicTacToe component
const TicTacToe = () => {
  const [board, setBoard] = useState(Array(9).fill(null));
  const [isXNext, setIsXNext] = useState(true);
  const [winner, setWinner] = useState<string | null>(null);
  const [score, setScore] = useState({ X: 0, O: 0, ties: 0 });

  const calculateWinner = (squares: Array<string | null>) => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
      [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
      [0, 4, 8], [2, 4, 6]             // diagonals
    ];
    
    for (let i = 0; i < lines.length; i++) {
      const [a, b, c] = lines[i];
      if (squares[a] && squares[a] === squares[b] && squares[a] === squares[c]) {
        return squares[a];
      }
    }
    
    // Check for tie
    if (squares.every(square => square !== null)) {
      return 'tie';
    }
    
    return null;
  };

  const handleClick = (i: number) => {
    if (board[i] || winner) return;
    
    const newBoard = [...board];
    newBoard[i] = isXNext ? 'X' : 'O';
    setBoard(newBoard);
    setIsXNext(!isXNext);
    
    const gameWinner = calculateWinner(newBoard);
    if (gameWinner) {
      setWinner(gameWinner);
      if (gameWinner === 'tie') {
        setScore({...score, ties: score.ties + 1});
      } else {
        setScore({...score, [gameWinner]: score[gameWinner as keyof typeof score] + 1});
      }
    }
  };

  const resetGame = () => {
    setBoard(Array(9).fill(null));
    setIsXNext(true);
    setWinner(null);
  };

  const renderSquare = (i: number) => {
    return (
      <button 
        className={`w-16 h-16 border border-gray-300 flex items-center justify-center text-2xl font-bold
          ${board[i] === 'X' ? 'text-blue-500' : 'text-red-500'}
          hover:bg-gray-100 transition-colors`}
        onClick={() => handleClick(i)}
      >
        {board[i]}
      </button>
    );
  };

  let status;
  if (winner === 'tie') {
    status = "Game ended in a tie!";
  } else if (winner) {
    status = `Winner: ${winner}`;
  } else {
    status = `Next player: ${isXNext ? 'X' : 'O'}`;
  }

  return (
    <div className="flex flex-col items-center mt-6">
      <h3 className="text-lg font-medium mb-2">Play Tic Tac Toe while you wait!</h3>
      
      <div className="flex justify-between w-full max-w-[200px] mb-4 text-sm">
        <div className="text-blue-500">X: {score.X}</div>
        <div className="text-gray-500">Ties: {score.ties}</div>
        <div className="text-red-500">O: {score.O}</div>
      </div>
      
      <div className="mb-4">
        <div className="flex">
          {renderSquare(0)}
          {renderSquare(1)}
          {renderSquare(2)}
        </div>
        <div className="flex">
          {renderSquare(3)}
          {renderSquare(4)}
          {renderSquare(5)}
        </div>
        <div className="flex">
          {renderSquare(6)}
          {renderSquare(7)}
          {renderSquare(8)}
        </div>
      </div>
      
      <div className="text-center mb-4">
        <p className="mb-2">{status}</p>
        <Button 
          variant="outline" 
          size="sm"
          onClick={resetGame}
          className="flex items-center gap-1"
        >
          <RefreshCw className="h-3 w-3" />
          New Game
        </Button>
      </div>
    </div>
  );
};

const UnderMaintenance: React.FC<UnderMaintenanceProps> = ({
  title = "Under Maintenance",
  message = "We're currently performing scheduled maintenance. Please check back soon.",
  showHomeButton = true,
  showBackButton = true,
  estimatedTime,
}) => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
        <div className="flex justify-center mb-6">
          <Settings className="h-16 w-16 text-primary animate-spin-slow" />
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 mb-4">{title}</h1>
        
        <p className="text-gray-600 mb-6">{message}</p>
        
        {estimatedTime && (
          <div className="flex items-center justify-center gap-2 text-sm text-gray-500 mb-6">
            <Clock className="h-4 w-4" />
            <span>Estimated completion: {estimatedTime}</span>
          </div>
        )}
        
        {/* Tic Tac Toe Game */}
        <TicTacToe />
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center mt-6">
          {showBackButton && (
            <Button 
              variant="outline" 
              onClick={() => navigate(-1)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Go Back
            </Button>
          )}
          
          {showHomeButton && (
            <Button 
              onClick={() => navigate("/")}
            >
              Return to Home
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default UnderMaintenance;


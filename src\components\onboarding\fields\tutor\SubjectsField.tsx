import React from "react";
import { Input } from "@/components/ui/input";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

interface SubjectsFieldProps {
  form: any;
  name?: string;
  label?: string;
  placeholder?: string;
}

const SubjectsField: React.FC<SubjectsFieldProps> = ({
  form,
  name = "subjects",
  label = "Subjects You Teach",
  placeholder = "Math, Physics, Programming",
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input placeholder={placeholder} {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SubjectsField;

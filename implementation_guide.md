# Implementation Guide

This guide outlines how to implement the database schema in your application, focusing on key API endpoints and data access patterns.

## 1. Setting Up the Database

### Initial Setup

1. Create the database tables using the SQL in `db_schema.sql`
2. Set up appropriate indexes for performance
3. Configure Supabase RLS (Row Level Security) policies for each table

### Example RLS Policy for Batches

```sql
-- Allow students to read their own batches
CREATE POLICY "Students can view their own batches"
ON batches FOR SELECT
USING (auth.uid() = student_id);

-- Allow tutors to read batches they are assigned to
CREATE POLICY "Tutors can view batches they are assigned to"
ON batches FOR SELECT
USING (auth.uid() = default_tutor_id OR
       EXISTS (SELECT 1 FROM batch_topics WHERE batch_id = batches.id AND custom_tutor_id = auth.uid()) OR
       EXISTS (SELECT 1 FROM batch_topics bt JOIN batch_subtopics bs ON bt.id = bs.batch_topic_id
               WHERE bt.batch_id = batches.id AND bs.custom_tutor_id = auth.uid()));

-- Allow admins to read and write all batches
CREATE POLICY "<PERSON><PERSON> can manage all batches"
ON batches FOR ALL
USING (EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND user_type = 'admin'));
```

## 2. Key API Endpoints

### Batch Management

#### Create Batch

```typescript
// API: POST /api/batches
async function createBatch(batchData) {
  const { data, error } = await supabase
    .from('batches')
    .insert({
      name: batchData.name,
      student_id: batchData.studentId,
      package_type: batchData.packageType,
      package_name: batchData.packageName,
      default_tutor_id: batchData.defaultTutorId,
      status: 'active',
      start_date: batchData.startDate,
      end_date: batchData.endDate,
      total_sessions: batchData.totalSessions,
      remaining_sessions: batchData.totalSessions
    })
    .select();

  if (error) throw error;

  // Add topics to the batch
  for (const topicId of batchData.topicIds) {
    await supabase
      .from('batch_topics')
      .insert({
        batch_id: data[0].id,
        topic_id: topicId,
        status: 'not_started'
      });
  }

  return data[0];
}
```

#### Assign Custom Tutor to Topic

```typescript
// API: PATCH /api/batches/:batchId/topics/:topicId/tutor
async function assignTutorToTopic(batchId, topicId, tutorId) {
  const { data, error } = await supabase
    .from('batch_topics')
    .update({ custom_tutor_id: tutorId })
    .match({ batch_id: batchId, topic_id: topicId })
    .select();

  if (error) throw error;
  return data[0];
}
```

### Session Management

#### Create Session Request

```typescript
// API: POST /api/session-requests
async function createSessionRequest(requestData) {
  const { data, error } = await supabase
    .from('session_requests')
    .insert({
      batch_id: requestData.batchId,
      topic_id: requestData.topicId,
      subtopic_id: requestData.subtopicId,
      requested_tutor_id: requestData.tutorId,
      student_id: requestData.studentId,
      requested_date: requestData.date,
      requested_time: requestData.time,
      duration_min: requestData.durationMin,
      notes: requestData.notes,
      status: 'pending',
      urgency: requestData.urgency || 'medium'
    })
    .select();

  if (error) throw error;

  // Check if request should be auto-accepted
  await checkAutoAcceptRules(data[0].id);

  return data[0];
}
```

#### Accept Session Request

```typescript
// API: PATCH /api/session-requests/:requestId/accept
async function acceptSessionRequest(requestId, tutorId) {
  // First get the request details
  const { data: request, error: requestError } = await supabase
    .from('session_requests')
    .select('*')
    .eq('id', requestId)
    .single();

  if (requestError) throw requestError;

  // Update request status
  const { error: updateError } = await supabase
    .from('session_requests')
    .update({
      status: 'accepted',
      updated_at: new Date()
    })
    .eq('id', requestId);

  if (updateError) throw updateError;

  // If no specific tutor is provided, get the assigned tutor based on the hierarchy
  if (!tutorId) {
    // Call the database function to get the assigned tutor
    const { data: assignedTutor, error: tutorError } = await supabase
      .rpc('get_assigned_tutor_for_session', {
        p_batch_id: request.batch_id,
        p_topic_id: request.topic_id,
        p_subtopic_id: request.subtopic_id
      });

    if (tutorError) throw tutorError;
    tutorId = assignedTutor;

    // If still no tutor (e.g., no default tutor for batch), use the requested tutor
    if (!tutorId) {
      tutorId = request.requested_tutor_id;
    }
  }

  // Create a session
  const { data: session, error: sessionError } = await supabase
    .from('sessions')
    .insert({
      batch_id: request.batch_id,
      topic_id: request.topic_id,
      subtopic_id: request.subtopic_id,
      tutor_id: tutorId,
      student_id: request.student_id,
      scheduled_at: new Date(`${request.requested_date}T${request.requested_time}`),
      duration_min: request.duration_min,
      status: 'scheduled',
      mode: 'video', // Default mode
      created_by: 'tutor'
    })
    .select();

  if (sessionError) throw sessionError;

  return session[0];
}
```

### Tutor Assignment

#### Get Assigned Tutor for Session

```typescript
// API: GET /api/assigned-tutor
async function getAssignedTutorForSession(batchId, topicId, subtopicId = null) {
  // Call the database function to get the assigned tutor
  const { data: tutorId, error } = await supabase
    .rpc('get_assigned_tutor_for_session', {
      p_batch_id: batchId,
      p_topic_id: topicId,
      p_subtopic_id: subtopicId
    });

  if (error) throw error;

  // If no tutor was assigned, return null
  if (!tutorId) return null;

  // Get the tutor profile details
  const { data: tutorProfile, error: profileError } = await supabase
    .from('profiles')
    .select(`
      id,
      first_name,
      last_name,
      avatar_url,
      tutor_specialties:tutor_specialties(specialty),
      tutor_ratings:session_feedback(rating)
    `)
    .eq('id', tutorId)
    .single();

  if (profileError) throw profileError;

  // Calculate average rating
  const ratings = tutorProfile.tutor_ratings || [];
  const avgRating = ratings.length > 0
    ? ratings.reduce((sum, item) => sum + item.rating, 0) / ratings.length
    : 0;

  // Format specialties
  const specialties = (tutorProfile.tutor_specialties || []).map(s => s.specialty);

  return {
    id: tutorProfile.id,
    name: `${tutorProfile.first_name} ${tutorProfile.last_name}`,
    photoUrl: tutorProfile.avatar_url,
    specialties,
    rating: parseFloat(avgRating.toFixed(1))
  };
}

### Learning Journey

#### Get Student Learning Journey

```typescript
// API: GET /api/students/:studentId/learning-journey
async function getStudentLearningJourney(studentId, topicId = null) {
  // Get all batches for the student
  const { data: batches, error: batchError } = await supabase
    .from('batches')
    .select('id, name, package_name')
    .eq('student_id', studentId)
    .eq('status', 'active');

  if (batchError) throw batchError;

  // Build query for batch topics
  let query = supabase
    .from('batch_topics')
    .select(`
      id,
      status,
      topics (id, name, description),
      batch_subtopics (
        id,
        status,
        subtopics (id, name, description)
      )
    `)
    .in('batch_id', batches.map(b => b.id));

  // Filter by topic if provided
  if (topicId) {
    query = query.eq('topic_id', topicId);
  }

  const { data: journey, error: journeyError } = await query;

  if (journeyError) throw journeyError;

  return {
    batches,
    journey
  };
}
```

## 3. Implementing Auto-Accept Rules

```typescript
async function checkAutoAcceptRules(requestId) {
  // Get the request details
  const { data: request, error: requestError } = await supabase
    .from('session_requests')
    .select(`
      *,
      batches (student_id),
      topics (name)
    `)
    .eq('id', requestId)
    .single();

  if (requestError) throw requestError;

  // If a specific tutor was requested, check their rules
  if (request.requested_tutor_id) {
    const tutorId = request.requested_tutor_id;

    // Get tutor's auto-accept rules
    const { data: rules, error: rulesError } = await supabase
      .from('tutor_auto_accept_rules')
      .select(`
        *,
        rule_topics (topic_id),
        rule_time_ranges (day_of_week, start_time, end_time)
      `)
      .eq('tutor_id', tutorId)
      .eq('is_active', true);

    if (rulesError) throw rulesError;

    // Check each rule
    for (const rule of rules) {
      let matchesRule = true;

      // Check if rule requires existing students only
      if (rule.existing_students_only) {
        const { count, error: sessionError } = await supabase
          .from('sessions')
          .select('id', { count: 'exact' })
          .eq('tutor_id', tutorId)
          .eq('student_id', request.batches.student_id);

        if (sessionError) throw sessionError;

        if (count === 0) {
          matchesRule = false;
          continue; // Skip to next rule
        }
      }

      // Check if rule has topic restrictions
      if (rule.rule_topics && rule.rule_topics.length > 0) {
        const topicIds = rule.rule_topics.map(rt => rt.topic_id);
        if (!topicIds.includes(request.topic_id)) {
          matchesRule = false;
          continue; // Skip to next rule
        }
      }

      // Check if rule has time range restrictions
      if (rule.rule_time_ranges && rule.rule_time_ranges.length > 0) {
        const requestDate = new Date(`${request.requested_date}T${request.requested_time}`);
        const dayOfWeek = requestDate.getDay(); // 0 = Sunday, 6 = Saturday
        const requestTime = request.requested_time;

        const matchesTimeRange = rule.rule_time_ranges.some(tr => {
          return tr.day_of_week === dayOfWeek &&
                 (!tr.start_time || tr.start_time <= requestTime) &&
                 (!tr.end_time || tr.end_time >= requestTime);
        });

        if (!matchesTimeRange) {
          matchesRule = false;
          continue; // Skip to next rule
        }
      }

      // If we get here, the request matches all rule conditions
      if (matchesRule) {
        // Auto-accept the request
        await acceptSessionRequest(requestId, tutorId);

        // Update the auto_accepted flag
        await supabase
          .from('session_requests')
          .update({ auto_accepted: true })
          .eq('id', requestId);

        return true;
      }
    }
  }

  return false;
}
```

## 4. Billing, Subscription, and Enrollment System

### Database Schema

The billing system uses the following core tables:

```sql
-- Products table - Available learning products
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    price NUMERIC NOT NULL,
    duration_days INTEGER NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('booster', 'custom', 'preparation')),
    features JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Invoices table - Purchase records
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) NOT NULL,
    amount NUMERIC NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed', 'refunded')),
    payment_method TEXT NOT NULL,
    payment_id TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Invoice Items table - Line items for invoices
CREATE TABLE invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID REFERENCES invoices(id) NOT NULL,
    product_id UUID REFERENCES products(id) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price NUMERIC NOT NULL,
    subtotal NUMERIC NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Subscriptions table - Active subscriptions (renamed from student_subscriptions)
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES profiles(id) NOT NULL,
    product_id UUID REFERENCES products(id) NOT NULL,
    invoice_id UUID REFERENCES invoices(id) NOT NULL,
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('active', 'expired', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Notifications table - System notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('billing', 'session', 'system', 'reminder')),
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);
```

### Billing Store Implementation

The system uses Zustand for state management with the following structure:

```typescript
interface BillingStore {
  // State
  products: Product[];
  invoices: Invoice[];
  subscriptions: Subscription[];
  activeSubscriptions: Subscription[];
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProducts: () => Promise<void>;
  fetchInvoices: (studentId: string) => Promise<void>;
  fetchSubscriptions: (studentId: string) => Promise<void>;
  fetchActiveSubscriptions: (studentId: string) => Promise<void>;
  purchaseProduct: (studentId: string, productId: string, paymentMethod: string) => Promise<boolean>;
  getActiveSubscriptions: () => Subscription[];
  getInvoiceDetails: (invoiceId: string) => Promise<Invoice | null>;
  cancelSubscription: (subscriptionId: string) => Promise<boolean>;
  isEnrolled: (studentId: string) => boolean;
}
```

### Product Management

#### Fetch Available Products

```typescript
// API: GET /api/products
async function fetchProducts() {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('is_active', true)
    .order('price', { ascending: true });

  if (error) throw error;
  return data;
}
```

#### Product Purchase Flow

```typescript
// API: POST /api/purchases
async function purchaseProduct(studentId: string, productId: string, paymentMethod: string) {
  // 1. Get product details
  const { data: productData, error: productError } = await supabase
    .from('products')
    .select('*')
    .eq('id', productId)
    .single();

  if (productError) throw productError;

  // 2. Create invoice
  const { data: invoiceData, error: invoiceError } = await supabase
    .from('invoices')
    .insert({
      student_id: studentId,
      amount: productData.price,
      status: 'paid', // Assuming immediate payment for simplicity
      payment_method: paymentMethod,
      payment_id: `payment_${Date.now()}` // In a real app, this would come from payment processor
    })
    .select()
    .single();

  if (invoiceError) throw invoiceError;

  // 3. Create invoice item
  const { error: itemError } = await supabase
    .from('invoice_items')
    .insert({
      invoice_id: invoiceData.id,
      product_id: productId,
      quantity: 1,
      unit_price: productData.price,
      subtotal: productData.price
    });

  if (itemError) throw itemError;

  // 4. Create subscription
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + productData.duration_days);

  const { error: subscriptionError } = await supabase
    .from('subscriptions')
    .insert({
      student_id: studentId,
      product_id: productId,
      invoice_id: invoiceData.id,
      current_period_start: startDate.toISOString(),
      current_period_end: endDate.toISOString(),
      status: 'active'
    });

  if (subscriptionError) throw subscriptionError;

  // 5. Create notification
  const { error: notificationError } = await supabase
    .from('notifications')
    .insert({
      user_id: studentId,
      title: 'Purchase Successful',
      message: `You have successfully purchased ${productData.name}. Your subscription is now active.`,
      type: 'billing'
    });

  if (notificationError) throw notificationError;

  return true;
}
```

### Subscription Management

#### Fetch Student Subscriptions

```typescript
// API: GET /api/students/:studentId/subscriptions
async function fetchSubscriptions(studentId: string) {
  const { data, error } = await supabase
    .from('subscriptions')
    .select(`
      *,
      products:product_id (name)
    `)
    .eq('student_id', studentId)
    .order('current_period_end', { ascending: false });

  if (error) throw error;

  // Calculate days remaining for each subscription
  const now = new Date();
  const subscriptionsWithDaysRemaining = data.map(sub => {
    const endDate = new Date(sub.current_period_end);
    const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

    return {
      ...sub,
      product_name: sub.products.name,
      start_date: new Date(sub.current_period_start),
      end_date: new Date(sub.current_period_end),
      days_remaining: daysRemaining
    };
  });

  return subscriptionsWithDaysRemaining;
}
```

#### Cancel Subscription

```typescript
// API: PATCH /api/subscriptions/:subscriptionId/cancel
async function cancelSubscription(subscriptionId: string) {
  const { error } = await supabase
    .from('subscriptions')
    .update({ status: 'cancelled' })
    .eq('id', subscriptionId);

  if (error) throw error;
  return true;
}
```

### Enrollment Status

#### Check Enrollment Status

```typescript
// API: GET /api/students/:studentId/enrollment-status
function isEnrolled(studentId: string, activeSubscriptions: Subscription[]) {
  return activeSubscriptions.some(sub =>
    sub.student_id === studentId &&
    sub.status === 'active' &&
    sub.days_remaining > 0
  );
}
```

### Invoice Management

#### Fetch Student Invoices

```typescript
// API: GET /api/students/:studentId/invoices
async function fetchInvoices(studentId: string) {
  // Fetch invoices with their items
  const { data: invoicesData, error: invoicesError } = await supabase
    .from('invoices')
    .select('*')
    .eq('student_id', studentId)
    .order('created_at', { ascending: false });

  if (invoicesError) throw invoicesError;

  // For each invoice, fetch its items
  const invoicesWithItems = await Promise.all(
    invoicesData.map(async (invoice) => {
      const { data: itemsData, error: itemsError } = await supabase
        .from('invoice_items')
        .select(`
          *,
          products:product_id (name)
        `)
        .eq('invoice_id', invoice.id);

      if (itemsError) throw itemsError;

      // Format items with product name
      const items = itemsData.map(item => ({
        ...item,
        product_name: item.products.name
      }));

      return {
        ...invoice,
        created_at: new Date(invoice.created_at),
        items
      };
    })
  );

  return invoicesWithItems;
}
```

#### Get Invoice Details

```typescript
// API: GET /api/invoices/:invoiceId
async function getInvoiceDetails(invoiceId: string) {
  const { data: invoice, error: invoiceError } = await supabase
    .from('invoices')
    .select('*')
    .eq('id', invoiceId)
    .single();

  if (invoiceError) throw invoiceError;

  const { data: items, error: itemsError } = await supabase
    .from('invoice_items')
    .select(`
      *,
      products:product_id (name)
    `)
    .eq('invoice_id', invoiceId);

  if (itemsError) throw itemsError;

  return {
    ...invoice,
    created_at: new Date(invoice.created_at),
    items: items.map(item => ({
      ...item,
      product_name: item.products.name
    }))
  };
}
```

### Batch Creation from Subscriptions

When a student purchases a product, the system automatically creates batches:

```typescript
// API: POST /api/batches/from-subscription
async function createBatchFromSubscription(subscriptionId: string) {
  // Get subscription details
  const { data: subscription, error: subError } = await supabase
    .from('subscriptions')
    .select(`
      *,
      products:product_id (name, type, features)
    `)
    .eq('id', subscriptionId)
    .single();

  if (subError) throw subError;

  // Create batch based on product type
  const batchData = {
    name: `${subscription.products.name} - ${new Date().getFullYear()}`,
    student_id: subscription.student_id,
    package_type: subscription.products.type,
    package_name: subscription.products.name,
    status: 'active',
    start_date: subscription.current_period_start,
    end_date: subscription.current_period_end,
    total_sessions: subscription.products.features?.total_sessions || 10,
    remaining_sessions: subscription.products.features?.total_sessions || 10
  };

  const { data: batch, error: batchError } = await supabase
    .from('batches')
    .insert(batchData)
    .select()
    .single();

  if (batchError) throw batchError;

  return batch;
}
```

### UI Components

The system includes several reusable UI components:

1. **EnrollmentStatus** - Shows enrollment status and guides users to products
2. **ProductCard** - Displays product information with purchase button
3. **SubscriptionCard** - Shows subscription details with progress and cancel option
4. **InvoiceList** - Lists all invoices with status badges
5. **InvoiceDetailModal** - Shows detailed invoice information

### Integration with Student Dashboard

The billing system integrates with the student dashboard through:

1. **Enrollment checking** - Determines if student can access certain features
2. **Subscription display** - Shows active subscriptions and remaining time
3. **Product recommendations** - Suggests products based on enrollment status
4. **Billing history** - Provides access to invoices and payment history

### Payment Integration

The current implementation uses a simplified payment flow. For production, integrate with:

1. **Stripe** - For credit card processing
2. **PayPal** - For alternative payment methods
3. **Bank transfers** - For institutional payments
4. **Webhooks** - For real-time payment status updates

### Notification System

The system sends notifications for:

1. **Successful purchases** - Confirmation of product purchase
2. **Subscription expiry** - Warnings before subscription ends
3. **Payment failures** - Alerts for failed payments
4. **Cancellations** - Confirmation of subscription cancellations

## 5. Implementing the Learning Journey UI

The Learning Journey UI can be implemented using a tree-like structure:

1. Fetch the student's learning journey data
2. Render topics as main nodes
3. Render subtopics as child nodes
4. Implement lazy loading to only fetch detailed data when a node is expanded
5. Implement a side panel that shows details when a node is clicked

This approach supports your requirement for a lightweight journey with modal detail views and backend-powered lazy loading.

import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Bell, X, <PERSON>, <PERSON><PERSON>he<PERSON>, Trash2, Alert<PERSON>ircle, CreditCard, GraduationCap, Settings } from 'lucide-react';
import { useNotificationStore, Notification } from '@/store/notificationStore';
import { useNotificationDropdownStore } from '@/store/notificationPageStore';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { formatDistanceToNow } from 'date-fns';
import {
  CustomDropdownMenu,
  CustomDropdownMenuContent,
  CustomDropdownMenuTrigger,
  CustomDropdownMenuSeparator,
} from '@/components/ui/CustomDropdownMenu';

interface NotificationDropdownProps {
  className?: string;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ className = '' }) => {
  const { user, userType } = useAuth();
  const navigate = useNavigate();

  // Zustand stores
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    subscribeToNotifications,
    unsubscribeFromNotifications,
    clearError
  } = useNotificationStore();

  const {
    isOpen,
    setIsOpen,
    closeDropdown
  } = useNotificationDropdownStore();

  // Initialize notifications when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchNotifications(user.id);
      subscribeToNotifications(user.id);
    }

    // Cleanup subscription on unmount
    return () => {
      unsubscribeFromNotifications();
    };
  }, [user?.id, fetchNotifications, subscribeToNotifications, unsubscribeFromNotifications]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'billing':
        return <CreditCard className="h-4 w-4 text-green-600" />;
      case 'academic':
        return <GraduationCap className="h-4 w-4 text-blue-600" />;
      case 'system':
        return <Settings className="h-4 w-4 text-gray-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getNotificationTypeColor = (type: Notification['type']) => {
    switch (type) {
      case 'billing':
        return 'bg-green-100 text-green-800';
      case 'academic':
        return 'bg-blue-100 text-blue-800';
      case 'system':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    // Navigate to notifications page with the specific notification highlighted
    const notificationsPath = userType === 'tutor' ? '/tutor/notifications' : '/student/notifications';
    navigate(`${notificationsPath}?notificationId=${notification.id}`);
    closeDropdown();
  };

  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    await markAsRead(notificationId);
  };

  const handleDelete = async (notificationId: string, event: React.MouseEvent, isSystemManaged?: boolean) => {
    event.stopPropagation();

    // Prevent deletion of system-managed notifications
    if (isSystemManaged) {
      return;
    }

    await deleteNotification(notificationId);
  };

  const handleMarkAllAsRead = async () => {
    if (user?.id) {
      await markAllAsRead(user.id);
    }
  };

  const handleViewAllNotifications = () => {
    const notificationsPath = userType === 'tutor' ? '/tutor/notifications' : '/student/notifications';
    navigate(notificationsPath);
    closeDropdown();
  };

  // Helper function to safely render HTML content for system notifications
  const renderNotificationMessage = (notification: Notification) => {
    if (notification.is_system_managed && notification.message.includes('<a href=')) {
      return (
        <span
          dangerouslySetInnerHTML={{ __html: notification.message }}
          className="text-sm text-gray-600 mt-1 line-clamp-2"
        />
      );
    }
    return (
      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
        {notification.message}
      </p>
    );
  };

  const recentNotifications = notifications.slice(0, 10); // Show only recent 10

  return (
    <CustomDropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <CustomDropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`relative p-2 hover:bg-gray-100 ${className}`}
        >
          <Bell className="h-5 w-5 text-gray-600" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </CustomDropdownMenuTrigger>

      <CustomDropdownMenuContent align="end" className="w-80 max-h-96 overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold text-lg">Notifications</h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              <CheckCheck className="h-4 w-4 mr-1" />
              Mark all read
            </Button>
          )}
        </div>

        {error && (
          <div className="p-3 bg-red-50 border-b">
            <div className="flex items-center justify-between">
              <p className="text-sm text-red-600">{error}</p>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="text-red-600 hover:text-red-800"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        <div className="max-h-80 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Loading notifications...</p>
            </div>
          ) : recentNotifications.length === 0 ? (
            <div className="p-6 text-center">
              <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No notifications yet</p>
            </div>
          ) : (
            <div className="divide-y">
              {recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 hover:bg-gray-50 transition-colors cursor-pointer ${
                    !notification.is_read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {notification.title}
                          </p>
                          {renderNotificationMessage(notification)}
                          <div className="flex items-center mt-2 space-x-2">
                            <Badge
                              variant="secondary"
                              className={`text-xs ${getNotificationTypeColor(notification.type)}`}
                            >
                              {notification.type}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 ml-2">
                          {!notification.is_read && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleMarkAsRead(notification.id, e)}
                              className="p-1 h-6 w-6 hover:bg-blue-100"
                              title="Mark as read"
                            >
                              <Check className="h-3 w-3 text-blue-600" />
                            </Button>
                          )}
                          {!notification.is_system_managed && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleDelete(notification.id, e, notification.is_system_managed)}
                              className="p-1 h-6 w-6 hover:bg-red-100"
                              title="Delete notification"
                            >
                              <Trash2 className="h-3 w-3 text-red-600" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {notifications.length > 10 && (
          <>
            <CustomDropdownMenuSeparator />
            <div className="p-3 text-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewAllNotifications}
                className="text-blue-600 hover:text-blue-800"
              >
                View all notifications
              </Button>
            </div>
          </>
        )}
      </CustomDropdownMenuContent>
    </CustomDropdownMenu>
  );
};

export default NotificationDropdown;

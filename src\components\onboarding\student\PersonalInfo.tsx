import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/Label";
import { Card, CardContent } from "@/components/ui/Card";
import DateOfBirth from "@/components/ui/DateOfBirth";

interface PersonalInfoProps {
  firstName: string;
  setFirstName: (name: string) => void;
  lastName: string;
  setLastName: (name: string) => void;
  dateOfBirth: string | null;
  setDateOfBirth: (date: string | null) => void;
}

const PersonalInfo: React.FC<PersonalInfoProps> = ({
  firstName,
  setFirstName,
  lastName,
  setLastName,
  dateOfBirth,
  setDateOfBirth,
}) => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Personal Information</h2>
        <p className="text-gray-600">
          Please provide your personal details to complete your profile.
        </p>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="firstName" className="text-base">
                First Name
              </Label>
              <Input
                id="firstName"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="mt-1"
                placeholder="Enter your first name"
              />
            </div>

            <div>
              <Label htmlFor="lastName" className="text-base">
                Last Name
              </Label>
              <Input
                id="lastName"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="mt-1"
                placeholder="Enter your last name"
              />
            </div>

            <div>
              <Label htmlFor="dateOfBirth" className="text-base">
                Date of Birth
              </Label>
              <div className="mt-3">
                <DateOfBirth
                  id="dateOfBirth"
                  value={dateOfBirth}
                  onChange={setDateOfBirth}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PersonalInfo;

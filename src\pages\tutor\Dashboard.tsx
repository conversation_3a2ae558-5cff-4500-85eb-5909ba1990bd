import React, { useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { useProfileData } from "@/hooks/useProfileData";
import { useLocation, useNavigate, Link } from "react-router-dom";
import { useProcessStepsStore } from "@/store/processStepsStore";
import { useTutorDashboardStore } from "@/store/tutorDashboardStore";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import LoadingSpinner from "@/components/LoadingSpinner";
import NotificationDropdown from "@/components/notifications/NotificationDropdown";
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  Calendar,
  FileText,
  Upload,
  Eye,
  Star,
  MoreHorizontal,
  RefreshCcw
} from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/Avatar";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/Table";
import { format } from "date-fns";

// Timeline Navigator Component
const TimelineNavigator = ({
  selectedDate,
  setSelectedDate
}: {
  selectedDate: Date;
  setSelectedDate: (date: Date) => void
}) => {

  const handlePrevDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(selectedDate.getDate() - 1);
    setSelectedDate(newDate);
  };

  const handleNextDay = () => {
    const newDate = new Date(selectedDate);
    newDate.setDate(selectedDate.getDate() + 1);
    setSelectedDate(newDate);
  };

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <button
          onClick={handlePrevDay}
          className="p-1 rounded-full hover:bg-gray-100"
        >
          <ChevronLeft size={20} />
        </button>
        <span className="text-sm font-medium">
          {format(selectedDate, "MMM d, yyyy")}
        </span>
        <button
          onClick={handleNextDay}
          className="p-1 rounded-full hover:bg-gray-100"
        >
          <ChevronRight size={20} />
        </button>
      </div>
      <div className="text-sm text-gray-500">
        {format(selectedDate, "EEEE, MMMM d, yyyy")}
      </div>
    </div>
  );
};

// Timeline Dots Component
const TimelineDots = ({ sessions }: { sessions: Array<any> }) => {
  return (
    <div className="flex items-center space-x-2 mb-4 px-2">
      <div className="h-1 bg-gray-200 flex-grow"></div>
      {sessions.map((session) => (
        <div
          key={session.id}
          className={`h-3 w-3 rounded-full ${
            session.status === "completed" ? "bg-gray-400" :
            session.status === "current" ? "bg-green-500" :
            "bg-gray-300"
          }`}
        />
      ))}
      <div className="h-1 bg-gray-200 flex-grow"></div>
    </div>
  );
};

// Session Card Component
const SessionCard = ({ session }: { session: any }) => {
  return (
    <div className="mb-4 relative">
      <div className="flex items-start">
        <div className="w-20 text-sm text-gray-500 pt-1">{session.time}</div>
        <div className={`flex-1 p-4 rounded-lg border ${
          session.status === "completed" ? "bg-gray-50" :
          session.status === "current" ? "bg-green-50 border-green-200" :
          "bg-white"
        }`}>
          <div className="font-medium">{session.subject}</div>
          <div className="text-sm text-gray-600">{session.studentName} • {session.grade}</div>

          {session.status === "current" && (
            <div className="mt-2">
              <Button size="sm" className="bg-green-600 hover:bg-green-700">
                Join Now
              </Button>
            </div>
          )}

          {session.status === "upcoming" && session.timeRemaining && (
            <div className="mt-1 text-xs text-gray-500">
              In {session.timeRemaining}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Quick Action Card Component
const QuickActionCard = ({
  icon,
  title,
  onClick
}: {
  icon: React.ReactNode;
  title: string;
  onClick: () => void
}) => {
  return (
    <Card
      className="flex flex-col items-center justify-center p-4 hover:bg-gray-50 cursor-pointer transition-colors"
      onClick={onClick}
    >
      <div className="p-3 rounded-lg bg-purple-100 text-purple-600 mb-2">
        {icon}
      </div>
      <div className="text-sm text-center">{title}</div>
    </Card>
  );
};

// Student Card Component
const StudentCard = ({ student }: { student: any }) => {
  return (
    <div className="flex items-center justify-between p-3 border-b last:border-b-0">
      <div className="flex items-center">
        <Avatar className="h-10 w-10">
          <AvatarImage src={student.photo} alt={student.name} />
          <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="ml-3">
          <div className="font-medium">{student.name}</div>
          <div className="text-xs text-gray-500">{student.grade}</div>
        </div>
      </div>
      <div className="flex flex-col items-end">
        <div className="flex space-x-1 mb-1">
          {student.subjects.map((subject: string, index: number) => (
            <Badge key={index} variant="outline" className="text-xs bg-gray-50">
              {subject}
            </Badge>
          ))}
        </div>
        <button className="text-gray-400 hover:text-gray-600">
          <MoreHorizontal size={16} />
        </button>
      </div>
    </div>
  );
};

// Upcoming Session Card Component
const UpcomingSessionCard = ({ session }: { session: any }) => {
  return (
    <div className="mb-6 last:mb-0">
      <div className="flex justify-between items-start mb-2">
        <div>
          <div className="font-medium">{session.subject}</div>
          <div className="text-sm text-gray-600">{session.studentName} • {session.grade}</div>
        </div>
        <div className="text-right">
          <div className="text-sm font-medium">
            {session.day}
          </div>
          <div className="text-xs text-gray-500">
            {session.date}
          </div>
          <div className="text-xs text-gray-500">
            {session.time}
          </div>
        </div>
      </div>

      <div className="flex items-center text-xs text-gray-500 mb-2">
        {session.platform === "zoom" && (
          <span className="flex items-center mr-4">
            <img src="/zoom-icon.png" alt="Zoom" className="w-4 h-4 mr-1" />
            Zoom
          </span>
        )}
        {session.platform === "google-meet" && (
          <span className="flex items-center mr-4">
            <img src="/meet-icon.png" alt="Google Meet" className="w-4 h-4 mr-1" />
            Google Meet
          </span>
        )}
        <span className="flex items-center">
          <Clock size={14} className="mr-1" />
          {session.duration}
        </span>
      </div>

      <div className="mb-3">
        <div className="text-xs font-medium mb-1">Topics:</div>
        <div className="flex flex-wrap gap-1">
          {session.topics.map((topic: string, index: number) => (
            <Badge key={index} variant="outline" className="text-xs bg-gray-50">
              {topic}
            </Badge>
          ))}
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div className="text-xs text-green-600 font-medium">
          {session.timeUntil}
        </div>
        <div className="flex space-x-2">
          <Button size="sm" variant="outline">
            Reschedule
          </Button>
          <Button size="sm">
            Prepare
          </Button>
        </div>
      </div>
    </div>
  );
};

// Rating Stars Component
const RatingStars = ({ rating }: { rating: number }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  return (
    <div className="flex items-center">
      {[...Array(5)].map((_, i: number) => (
        <Star
          key={i}
          size={16}
          className={i < fullStars ? "text-yellow-400 fill-yellow-400" :
                    (i === fullStars && hasHalfStar) ? "text-yellow-400 fill-yellow-400" :
                    "text-gray-300"}
        />
      ))}
      <span className="ml-1 text-sm">{rating.toFixed(1)}</span>
    </div>
  );
};

// Pagination Component
const Pagination = ({
  currentPage,
  totalPages,
  onPageChange
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void
}) => {
  return (
    <div className="flex justify-center items-center space-x-1 mt-4">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
      >
        <ChevronLeft size={16} />
      </button>

      {[...Array(totalPages)].map((_, i: number) => (
        <button
          key={i}
          onClick={() => onPageChange(i + 1)}
          className={`w-8 h-8 rounded-full text-sm ${
            currentPage === i + 1 ?
            "bg-purple-100 text-purple-700 font-medium" :
            "hover:bg-gray-100"
          }`}
        >
          {i + 1}
        </button>
      ))}

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
      >
        <ChevronRight size={16} />
      </button>
    </div>
  );
};

const TutorDashboard: React.FC = () => {
  const { user, isInitialized, loading } = useAuth();
  const { displayName } = useProfileData();
  const location = useLocation();
  const processStepsStore = useProcessStepsStore.getState();
  const navigate = useNavigate();

  // Get state and actions from the dashboard store
  const {
    showWelcomeMessage,
    setShowWelcomeMessage,
    selectedDate,
    setSelectedDate,
    todaySessions,
    upcomingSessions,
    pastSessions,
    students,
    pastSessionsPage,
    setPastSessionsPage,
    pastSessionsFilter,
    setPastSessionsFilter
  } = useTutorDashboardStore();

  // Show loading state while auth is initializing
  if (loading || !isInitialized) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Fetching your details...</p>
        </div>
      </div>
    );
  }

  // Handle persisted process modal state
  useEffect(() => {
    console.log(
      "Dashboard: Checking for persisted process modal state",
      processStepsStore
    );

    // Check if we have a persisted process modal state
    if (processStepsStore.persistedState && processStepsStore.isOpen) {
      console.log("Dashboard: Found persisted process modal state");

      // Show success toast if coming from onboarding
      if (location.state?.onboardingComplete) {
        // Set the welcome message flag instead of using toast
        setShowWelcomeMessage(true);
      }

      // Clear the persisted state after a delay
      const timer = setTimeout(() => {
        console.log("Dashboard: Clearing persistence and closing modal");
        processStepsStore.preventStateReset(false); // Allow resets now
        processStepsStore.clearPersistence();
        processStepsStore.close();
      }, 5000);

      return () => clearTimeout(timer);
    } else {
      console.log("Dashboard: No persisted process modal state found");

      // Make sure resets are allowed when there's no persisted state
      processStepsStore.preventStateReset(false);
    }
  }, [location.state, processStepsStore, setShowWelcomeMessage]);

  // Handle welcome message
  useEffect(() => {
    if (location.state?.showWelcome) {
      setShowWelcomeMessage(true);

      // Clear the state after showing the message
      navigate(location.pathname, { replace: true });
    }
  }, [location, navigate, setShowWelcomeMessage]);

  // Handle quick action clicks
  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'schedule':
        navigate('/tutor/schedule');
        break;
      case 'notes':
        navigate('/tutor/notes');
        break;
      case 'feedback':
        navigate('/tutor/feedback');
        break;
      default:
        break;
    }
  };

  // Create a notification dropdown for the actions slot
  const notificationActions = <NotificationDropdown />;

  return (
    <TutorPageLayout
      title="Tutor Dashboard"
      description={`Welcome back, ${displayName || user?.email?.split("@")[0] || "Tutor"}`}
      actions={notificationActions}
    >
      {showWelcomeMessage && (
        <div className="bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg shadow-md p-6 mb-6 border border-blue-200">
          <h2 className="text-xl font-semibold text-blue-800 mb-2">
            Welcome aboard!
          </h2>
          <p className="text-gray-700">
            Your tutor profile has been set up successfully. You're now ready
            to start your teaching journey!
          </p>
          <button
            onClick={() => setShowWelcomeMessage(false)}
            className="mt-3 text-sm text-blue-600 hover:text-blue-800"
          >
            Dismiss
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content - Left and Center */}
        <div className="lg:col-span-2 space-y-6">
          {/* Today's Schedule */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Today's Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <TimelineNavigator
                selectedDate={selectedDate}
                setSelectedDate={setSelectedDate}
              />
              <TimelineDots sessions={todaySessions} />

              <div className="mt-6">
                {todaySessions.map(session => (
                  <SessionCard key={session.id} session={session} />
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Sessions */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">Upcoming Sessions</CardTitle>
              <Link to="/tutor/sessions/upcoming" className="text-sm text-purple-600 hover:text-purple-800 flex items-center">
                View All <ChevronRight size={16} />
              </Link>
            </CardHeader>
            <CardContent>
              {upcomingSessions.length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  No upcoming sessions scheduled.
                </div>
              ) : (
                upcomingSessions.map(session => (
                  <UpcomingSessionCard key={session.id} session={session} />
                ))
              )}
            </CardContent>
          </Card>

          {/* Past Sessions */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">Past Sessions</CardTitle>
              <div className="flex items-center">
                <select
                  value={pastSessionsFilter}
                  onChange={(e) => setPastSessionsFilter(e.target.value)}
                  className="text-sm border rounded-md mr-2 p-1"
                >
                  <option>Last 7 days</option>
                  <option>Last 30 days</option>
                  <option>All time</option>
                </select>
                <Link to="/tutor/sessions/history" className="text-sm text-purple-600 hover:text-purple-800 flex items-center">
                  View All <ChevronRight size={16} />
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>DATE & TIME</TableHead>
                      <TableHead>STUDENT</TableHead>
                      <TableHead>TOPIC</TableHead>
                      <TableHead>STATUS</TableHead>
                      <TableHead>RATING</TableHead>
                      <TableHead>ACTIONS</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pastSessions.map(session => (
                      <TableRow key={session.id}>
                        <TableCell className="font-medium">
                          {session.date}
                          <div className="text-xs text-gray-500">{session.time}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Avatar className="h-6 w-6 mr-2">
                              <AvatarFallback>{session.studentName.charAt(0)}</AvatarFallback>
                            </Avatar>
                            {session.studentName}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>{session.subject}</div>
                          <div className="text-xs text-gray-500">{session.topic}</div>
                        </TableCell>
                        <TableCell>
                          {session.status === "completed" ? (
                            <Badge className="bg-green-100 text-green-800 border-green-200">
                              Completed
                            </Badge>
                          ) : session.status === "missed" ? (
                            <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                              Missed
                            </Badge>
                          ) : (
                            <Badge className="bg-red-100 text-red-800 border-red-200">
                              Canceled
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <RatingStars rating={session.rating} />
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <button className="text-gray-400 hover:text-gray-600">
                              <Eye size={16} />
                            </button>
                            <button className="text-gray-400 hover:text-gray-600">
                              <MoreHorizontal size={16} />
                            </button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <Pagination
                currentPage={pastSessionsPage}
                totalPages={3}
                onPageChange={setPastSessionsPage}
              />

              <div className="text-center text-xs text-gray-500 mt-2">
                Showing 1-3 of 15 sessions
              </div>

              <div className="flex justify-center mt-4">
                <Button variant="outline" className="text-sm">
                  <RefreshCcw size={14} className="mr-1" /> Load More Sessions
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                <QuickActionCard
                  icon={<Calendar size={20} />}
                  title="Mark Availability"
                  onClick={() => handleQuickAction('schedule')}
                />
                <QuickActionCard
                  icon={<FileText size={20} />}
                  title="New Curriculum"
                  onClick={() => handleQuickAction('curriculum')}
                />
                <QuickActionCard
                  icon={<Upload size={20} />}
                  title="Upload Notes"
                  onClick={() => handleQuickAction('notes')}
                />
                <QuickActionCard
                  icon={<Eye size={20} />}
                  title="View Feedback"
                  onClick={() => handleQuickAction('feedback')}
                />
              </div>
            </CardContent>
          </Card>

          {/* Student Roster */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">Student Roster</CardTitle>
              <Link to="/tutor/students" className="text-sm text-purple-600 hover:text-purple-800 flex items-center">
                View All <ChevronRight size={16} />
              </Link>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {students.map(student => (
                  <StudentCard key={student.id} student={student} />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </TutorPageLayout>
  );
};

export default TutorDashboard;

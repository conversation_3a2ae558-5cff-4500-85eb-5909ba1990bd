import * as React from "react"
import { createPortal } from "react-dom"
import { cn } from "@/lib/utils"

interface DropdownMenuContextType {
  open: boolean
  setOpen: (open: boolean) => void
  triggerRef: React.MutableRefObject<HTMLElement | null>
}

const DropdownMenuContext = React.createContext<DropdownMenuContextType | undefined>(undefined)

const useDropdownMenu = () => {
  const context = React.useContext(DropdownMenuContext)
  if (!context) {
    throw new Error("useDropdownMenu must be used within a DropdownMenu")
  }
  return context
}

// Root component
interface CustomDropdownMenuProps {
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

const CustomDropdownMenu = ({ children, open: controlledOpen, onOpenChange }: CustomDropdownMenuProps) => {
  const [internalOpen, setInternalOpen] = React.useState(false)
  const triggerRef = React.useRef<HTMLElement | null>(null)

  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen = React.useCallback((newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen)
    } else {
      setInternalOpen(newOpen)
    }
  }, [onOpenChange])

  // Close on escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        setOpen(false)
      }
    }

    if (open) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [open, setOpen])

  return (
    <DropdownMenuContext.Provider value={{ open, setOpen, triggerRef }}>
      {children}
    </DropdownMenuContext.Provider>
  )
}

// Trigger component
interface CustomDropdownMenuTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  asChild?: boolean
  children: React.ReactNode
}

const CustomDropdownMenuTrigger = React.forwardRef<HTMLButtonElement, CustomDropdownMenuTriggerProps>(
  ({ asChild, children, className, onClick, ...props }, ref) => {
    const { open, setOpen, triggerRef } = useDropdownMenu()

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault()
      setOpen(!open)
      onClick?.(e)
    }

    const combinedRef = React.useCallback((node: HTMLButtonElement | null) => {
      // Set the trigger ref for positioning
      triggerRef.current = node

      // Handle the forwarded ref
      if (typeof ref === 'function') {
        ref(node)
      } else if (ref) {
        ref.current = node
      }
    }, [ref, triggerRef])

    if (asChild && React.isValidElement(children)) {
      return React.cloneElement(children, {
        ...children.props,
        ref: combinedRef,
        onClick: handleClick,
        'aria-expanded': open,
        'aria-haspopup': 'menu',
      })
    }

    return (
      <button
        ref={combinedRef}
        onClick={handleClick}
        aria-expanded={open}
        aria-haspopup="menu"
        className={className}
        {...props}
      >
        {children}
      </button>
    )
  }
)
CustomDropdownMenuTrigger.displayName = "CustomDropdownMenuTrigger"

// Content component
interface CustomDropdownMenuContentProps extends React.HTMLAttributes<HTMLDivElement> {
  align?: 'start' | 'center' | 'end'
  sideOffset?: number
  children: React.ReactNode
}

const CustomDropdownMenuContent = React.forwardRef<HTMLDivElement, CustomDropdownMenuContentProps>(
  ({ align = 'end', sideOffset = 4, className, children, ...props }, ref) => {
    const { open, setOpen, triggerRef } = useDropdownMenu()
    const contentRef = React.useRef<HTMLDivElement>(null)
    const [isPositioned, setIsPositioned] = React.useState(false)

    // Reset positioned state when dropdown closes
    React.useEffect(() => {
      if (!open) {
        setIsPositioned(false)
      }
    }, [open])

    // Position the dropdown
    React.useEffect(() => {
      if (open && contentRef.current && triggerRef.current) {
        const positionDropdown = () => {
          const trigger = triggerRef.current
          const content = contentRef.current

          if (!trigger || !content) return

          const triggerRect = trigger.getBoundingClientRect()

          // Calculate position
          const top = triggerRect.bottom + sideOffset
          const left = align === 'end' ? triggerRect.right - 160 : triggerRect.left

          // Apply positioning
          content.style.position = 'fixed'
          content.style.top = `${top}px`
          content.style.left = `${left}px`
          content.style.zIndex = '9999'

          // Mark as positioned to make it visible
          setIsPositioned(true)
        }

        // Use a longer timeout to ensure everything is rendered
        const timeoutId = setTimeout(() => {
          positionDropdown()
        }, 10)

        return () => clearTimeout(timeoutId)
      }
    }, [open, align, sideOffset, triggerRef])

    // Close on click outside
    React.useEffect(() => {
      const handleClickOutside = (e: MouseEvent) => {
        if (
          open &&
          contentRef.current &&
          triggerRef.current &&
          !contentRef.current.contains(e.target as Node) &&
          !triggerRef.current.contains(e.target as Node)
        ) {
          setOpen(false)
        }
      }

      if (open) {
        document.addEventListener('mousedown', handleClickOutside)
        return () => document.removeEventListener('mousedown', handleClickOutside)
      }
    }, [open, setOpen, triggerRef])

    if (!open) return null

    return createPortal(
      <div
        ref={(node) => {
          contentRef.current = node
          if (typeof ref === 'function') ref(node)
          else if (ref) ref.current = node
        }}
        className={cn(
          "bg-white border border-gray-200 rounded-md shadow-lg py-1",
          className
        )}
        style={{
          position: 'fixed',
          zIndex: 9999,
          minWidth: '160px',
          maxWidth: '300px',
          top: '0px',
          left: '0px',
          opacity: isPositioned ? 1 : 0,
          visibility: isPositioned ? 'visible' : 'hidden'
        }}
        {...props}
      >
        {children}
      </div>,
      document.body
    )
  }
)
CustomDropdownMenuContent.displayName = "CustomDropdownMenuContent"

// Menu Item component
interface CustomDropdownMenuItemProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const CustomDropdownMenuItem = React.forwardRef<HTMLDivElement, CustomDropdownMenuItemProps>(
  ({ className, onClick, children, ...props }, ref) => {
    const { setOpen } = useDropdownMenu()

    const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
      onClick?.(e)
      setOpen(false)
    }

    return (
      <div
        ref={ref}
        className={cn(
          "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
          className
        )}
        onClick={handleClick}
        {...props}
      >
        {children}
      </div>
    )
  }
)
CustomDropdownMenuItem.displayName = "CustomDropdownMenuItem"

// Label component
interface CustomDropdownMenuLabelProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const CustomDropdownMenuLabel = React.forwardRef<HTMLDivElement, CustomDropdownMenuLabelProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("px-2 py-1.5 text-sm font-semibold", className)}
      {...props}
    >
      {children}
    </div>
  )
)
CustomDropdownMenuLabel.displayName = "CustomDropdownMenuLabel"

// Separator component
interface CustomDropdownMenuSeparatorProps extends React.HTMLAttributes<HTMLDivElement> {}

const CustomDropdownMenuSeparator = React.forwardRef<HTMLDivElement, CustomDropdownMenuSeparatorProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("-mx-1 my-1 h-px bg-muted", className)}
      {...props}
    />
  )
)
CustomDropdownMenuSeparator.displayName = "CustomDropdownMenuSeparator"

export {
  CustomDropdownMenu,
  CustomDropdownMenuTrigger,
  CustomDropdownMenuContent,
  CustomDropdownMenuItem,
  CustomDropdownMenuLabel,
  CustomDropdownMenuSeparator,
}

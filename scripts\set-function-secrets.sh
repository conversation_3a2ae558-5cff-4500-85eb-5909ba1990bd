#!/bin/bash

# Set Supabase Edge Function Secrets for Production
# Usage: ./scripts/set-function-secrets.sh [PROJECT_REF]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    print_error "Supabase CLI is not installed. Please install it first."
    exit 1
fi

# Get project ref from argument or prompt
PROJECT_REF=$1
if [ -z "$PROJECT_REF" ]; then
    echo -n "Enter your Supabase project reference: "
    read PROJECT_REF
fi

if [ -z "$PROJECT_REF" ]; then
    print_error "Project reference is required"
    exit 1
fi

print_status "Setting Edge Function secrets for project: $PROJECT_REF"

# Function to set a secret
set_secret() {
    local name=$1
    local default_value=$2
    local is_sensitive=${3:-false}
    
    local value=""
    
    if [ "$is_sensitive" = true ]; then
        echo -n "Enter $name (sensitive): "
        read -s value
        echo ""
    else
        echo -n "Enter $name [$default_value]: "
        read value
        if [ -z "$value" ]; then
            value="$default_value"
        fi
    fi
    
    if [ -z "$value" ]; then
        print_warning "Skipping $name as no value was provided"
        return
    fi
    
    print_status "Setting $name..."
    if supabase secrets set --project-ref "$PROJECT_REF" "$name=$value"; then
        print_status "✅ Successfully set $name"
    else
        print_error "❌ Failed to set $name"
    fi
}

# Set general secrets
set_secret "FRONTEND_URL" "https://www.rflearn.com"
set_secret "ALLOWED_ORIGINS" "http://localhost:8080,https://www.rflearn.com"

# Set Microsoft Teams secrets
print_status "Setting Microsoft Teams integration secrets..."
set_secret "AZURE_TENANT_ID" "" true
set_secret "AZURE_CLIENT_ID" "" true
set_secret "AZURE_CLIENT_SECRET" "" true

# Set Razorpay secrets
print_status "Setting Razorpay integration secrets..."
set_secret "RAZORPAY_KEY_ID" "" true
set_secret "RAZORPAY_KEY_SECRET" "" true

print_status "🎉 All secrets set successfully!"
print_status "You can now deploy your functions using ./scripts/deploy-functions.sh $PROJECT_REF"

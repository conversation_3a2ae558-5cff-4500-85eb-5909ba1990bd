// src/components/admin/batches/BatchList.tsx
import React from "react";
import { useAdminBatchStore, Batch } from "@/store/adminBatchStore";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/Table";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Edit, Trash2, UserPlus } from "lucide-react";
import { format } from "date-fns";

interface BatchListProps {
  batches?: Batch[];
  onEdit: (batchId: string) => void;
  onDelete: (batchId: string) => void;
  onAssignTutor: (batchId: string) => void;
}

const BatchList: React.FC<BatchListProps> = ({ batches: propBatches, onEdit, onDelete, onAssignTutor }) => {
  const { batches: storeBatches } = useAdminBatchStore();

  // Use provided batches or fall back to store batches
  const batches = propBatches || storeBatches;

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy");
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Student</TableHead>
            <TableHead>Subject</TableHead>
            <TableHead>Product</TableHead>
            <TableHead>Default Tutor</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {batches.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-6 text-gray-500">
                No batches found
              </TableCell>
            </TableRow>
          ) : (
            batches.map((batch) => (
              <TableRow key={batch.id}>
                <TableCell className="font-medium">{batch.student_name}</TableCell>
                <TableCell>{batch.subject_name}</TableCell>
                <TableCell>
                  <Badge variant="outline" className="bg-blue-50 text-blue-800 hover:bg-blue-100">
                    {batch.product_name}
                  </Badge>
                </TableCell>
                <TableCell>
                  {batch.tutor_name ? (
                    batch.tutor_name
                  ) : (
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-800 hover:bg-yellow-100">
                      No Tutor Assigned
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{formatDate(batch.created_at)}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit(batch.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onAssignTutor(batch.id)}
                    >
                      <UserPlus className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:bg-red-50 hover:text-red-700"
                      onClick={() => onDelete(batch.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default BatchList;
import React from "react";
import { Loader2 } from "lucide-react";

interface AuthLoadingSpinnerProps {
  message?: string;
  className?: string;
}

const AuthLoadingSpinner: React.FC<AuthLoadingSpinnerProps> = ({ 
  message = "Loading...", 
  className = "" 
}) => {
  return (
    <div className={`flex flex-col items-center justify-center min-h-screen bg-gray-50 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-gray-600 text-sm">{message}</p>
      </div>
    </div>
  );
};

export default AuthLoadingSpinner;

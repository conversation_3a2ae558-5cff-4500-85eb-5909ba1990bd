import React, { useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useNotificationStore, Notification } from '@/store/notificationStore';
import { useNotificationPageStore } from '@/store/notificationPageStore';
import { useAuth } from '@/context/AuthContext';
import StudentPageLayout from '@/components/layouts/StudentPageLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import LoadingSpinner from '@/components/LoadingSpinner';
import {
  Bell,
  Check,
  CheckCheck,
  Trash2,
  AlertCircle,
  CreditCard,
  GraduationCap,
  Settings,
  Filter,
  Search
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { Input } from '@/components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';

const NotificationsPage: React.FC = () => {
  const { user } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const notificationRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Zustand stores
  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    subscribeToNotifications,
    unsubscribeFromNotifications,
    clearError
  } = useNotificationStore();

  const {
    searchQuery,
    filterType,
    filterStatus,
    highlightedNotificationId,
    setSearchQuery,
    setFilterType,
    setFilterStatus,
    setHighlightedNotificationId,
    clearHighlight,
    resetState
  } = useNotificationPageStore();

  // Initialize notifications when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchNotifications(user.id);
      subscribeToNotifications(user.id);
    }

    // Cleanup subscription and reset state on unmount
    return () => {
      unsubscribeFromNotifications();
      resetState();
    };
  }, [user?.id, fetchNotifications, subscribeToNotifications, unsubscribeFromNotifications, resetState]);

  // Handle URL parameters for highlighting specific notifications
  useEffect(() => {
    const notificationId = searchParams.get('notificationId');
    if (notificationId && notifications.length > 0) {
      // Check if the notification exists
      const notification = notifications.find(n => n.id === notificationId);
      if (notification) {
        setHighlightedNotificationId(notificationId);

        // Scroll to the notification after a short delay to ensure it's rendered
        setTimeout(() => {
          const element = notificationRefs.current[notificationId];
          if (element) {
            element.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
          }
        }, 100);

        // Clear the URL parameter after highlighting
        setTimeout(() => {
          setSearchParams(prev => {
            const newParams = new URLSearchParams(prev);
            newParams.delete('notificationId');
            return newParams;
          });
        }, 2000);
      }
    }
  }, [searchParams, notifications, setHighlightedNotificationId, setSearchParams]);

  // Clear highlight when component unmounts
  useEffect(() => {
    return () => {
      clearHighlight();
    };
  }, [clearHighlight]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'billing':
        return <CreditCard className="h-5 w-5 text-green-600" />;
      case 'academic':
        return <GraduationCap className="h-5 w-5 text-blue-600" />;
      case 'system':
        return <Settings className="h-5 w-5 text-gray-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getNotificationTypeColor = (type: Notification['type']) => {
    switch (type) {
      case 'billing':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'academic':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'system':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = searchQuery === '' ||
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = filterType === 'all' || notification.type === filterType;

    const matchesStatus = filterStatus === 'all' ||
      (filterStatus === 'read' && notification.is_read) ||
      (filterStatus === 'unread' && !notification.is_read);

    return matchesSearch && matchesType && matchesStatus;
  });

  const handleMarkAsRead = async (notificationId: string) => {
    await markAsRead(notificationId);
  };

  const handleDelete = async (notificationId: string, isSystemManaged?: boolean) => {
    // Prevent deletion of system-managed notifications
    if (isSystemManaged) {
      return;
    }
    await deleteNotification(notificationId);
  };

  const handleMarkAllAsRead = async () => {
    if (user?.id) {
      await markAllAsRead(user.id);
    }
  };

  // Helper function to safely render HTML content for system notifications
  const renderNotificationMessage = (notification: Notification) => {
    if (notification.is_system_managed && notification.message.includes('<a href=')) {
      return (
        <div
          dangerouslySetInnerHTML={{ __html: notification.message }}
          className="text-gray-700 mb-3 leading-relaxed"
        />
      );
    }
    return (
      <p className="text-gray-700 mb-3 leading-relaxed">
        {notification.message}
      </p>
    );
  };

  const unreadNotifications = filteredNotifications.filter(n => !n.is_read);
  const readNotifications = filteredNotifications.filter(n => n.is_read);

  return (
    <StudentPageLayout
      title="Notifications"
      description="View and manage all your notifications"
    >
      <div className="space-y-6">
        {/* Header with actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-gray-600" />
              <span className="text-lg font-semibold">
                {unreadCount > 0 ? `${unreadCount} unread` : 'All caught up!'}
              </span>
            </div>
            {unreadCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <CheckCheck className="h-4 w-4 mr-1" />
                Mark all read
              </Button>
            )}
          </div>

          {error && (
            <div className="flex items-center space-x-2 text-red-600 bg-red-50 px-3 py-2 rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="text-red-600 hover:text-red-800"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search notifications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="billing">Billing</SelectItem>
                  <SelectItem value="academic">Academic</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="unread">Unread</SelectItem>
                  <SelectItem value="read">Read</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : filteredNotifications.length === 0 ? (
          <Card>
            <CardContent className="py-12 text-center">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
              <p className="text-gray-500">
                {searchQuery || filterType !== 'all' || filterStatus !== 'all'
                  ? 'Try adjusting your filters to see more notifications.'
                  : 'You\'re all caught up! New notifications will appear here.'}
              </p>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue="all" className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">
                All ({filteredNotifications.length})
              </TabsTrigger>
              <TabsTrigger value="unread">
                Unread ({unreadNotifications.length})
              </TabsTrigger>
              <TabsTrigger value="read">
                Read ({readNotifications.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all">
              <NotificationList
                notifications={filteredNotifications}
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDelete}
                getNotificationIcon={getNotificationIcon}
                getNotificationTypeColor={getNotificationTypeColor}
                highlightedNotificationId={highlightedNotificationId}
                notificationRefs={notificationRefs}
                renderNotificationMessage={renderNotificationMessage}
              />
            </TabsContent>

            <TabsContent value="unread">
              <NotificationList
                notifications={unreadNotifications}
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDelete}
                getNotificationIcon={getNotificationIcon}
                getNotificationTypeColor={getNotificationTypeColor}
                highlightedNotificationId={highlightedNotificationId}
                notificationRefs={notificationRefs}
                renderNotificationMessage={renderNotificationMessage}
              />
            </TabsContent>

            <TabsContent value="read">
              <NotificationList
                notifications={readNotifications}
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDelete}
                getNotificationIcon={getNotificationIcon}
                getNotificationTypeColor={getNotificationTypeColor}
                highlightedNotificationId={highlightedNotificationId}
                notificationRefs={notificationRefs}
                renderNotificationMessage={renderNotificationMessage}
              />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </StudentPageLayout>
  );
};

// Notification List Component
interface NotificationListProps {
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string, isSystemManaged?: boolean) => void;
  getNotificationIcon: (type: Notification['type']) => React.ReactNode;
  getNotificationTypeColor: (type: Notification['type']) => string;
  highlightedNotificationId: string | null;
  notificationRefs: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>;
  renderNotificationMessage: (notification: Notification) => React.ReactNode;
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  onMarkAsRead,
  onDelete,
  getNotificationIcon,
  getNotificationTypeColor,
  highlightedNotificationId,
  notificationRefs,
  renderNotificationMessage
}) => {
  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read when clicked if it's unread
    if (!notification.is_read) {
      await onMarkAsRead(notification.id);
    }
  };

  return (
    <div className="space-y-4">
      {notifications.map((notification) => {
        const isHighlighted = highlightedNotificationId === notification.id;

        return (
          <Card
            key={notification.id}
            ref={(el) => {
              notificationRefs.current[notification.id] = el;
            }}
            className={`transition-all hover:shadow-md cursor-pointer ${
              !notification.is_read ? 'border-l-4 border-l-blue-500 bg-blue-50/30' : ''
            } ${
              isHighlighted ? 'ring-2 ring-blue-400 ring-opacity-75 shadow-lg' : ''
            }`}
            onClick={() => handleNotificationClick(notification)}
          >
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 mt-1">
                {getNotificationIcon(notification.type)}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {notification.title}
                    </h3>
                    {renderNotificationMessage(notification)}
                    <div className="flex items-center space-x-3">
                      <Badge
                        variant="secondary"
                        className={`${getNotificationTypeColor(notification.type)}`}
                      >
                        {notification.type}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {format(new Date(notification.created_at), 'MMM d, yyyy')} • {' '}
                        {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                      </span>
                      {!notification.is_read && (
                        <Badge variant="destructive" className="text-xs">
                          New
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {!notification.is_read && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onMarkAsRead(notification.id);
                        }}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Mark read
                      </Button>
                    )}
                    {!notification.is_system_managed && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(notification.id, notification.is_system_managed);
                        }}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        );
      })}
    </div>
  );
};

export default NotificationsPage;

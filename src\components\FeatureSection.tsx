
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Refresh<PERSON><PERSON>, Award, BookOpen } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";

const features = [
  {
    name: "AI-Powered Learning",
    description:
      "Our intelligent algorithms adapt to your learning style and pace, providing customized reinforcement.",
    icon: Brain,
  },
  {
    name: "Expert Human Tutors",
    description:
      "Connect with qualified tutors who specialize in your area of interest for personalized guidance.",
    icon: Users,
  },
  {
    name: "Reinforcement Techniques",
    description:
      "Scientifically-backed spaced repetition and active recall methods to strengthen memory retention.",
    icon: RefreshCw,
  },
  {
    name: "Instant Feedback",
    description:
      "Get immediate corrections and suggestions to improve your understanding in real-time.",
    icon: Zap,
  },
  {
    name: "Certification Paths",
    description:
      "Follow structured learning paths that lead to recognized certifications in your field.",
    icon: Award,
  },
  {
    name: "Comprehensive Library",
    description:
      "Access a wide range of courses, from beginner to advanced levels, across multiple disciplines.",
    icon: BookO<PERSON>,
  },
];

const FeatureSection = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Reinforce Your Knowledge with AI and Human Expertise
          </h2>
          <p className="mt-4 max-w-2xl text-xl text-gray-500 mx-auto">
            Our platform combines the best of technology and human guidance to
            create an effective learning experience.
          </p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {features.map((feature) => (
              <Card key={feature.name} className="card-gradient border-gray-100 shadow-sm hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="h-12 w-12 rounded-md bg-rfpurple-100 text-rfpurple-600 flex items-center justify-center mb-2">
                    <feature.icon size={24} />
                  </div>
                  <CardTitle className="text-xl font-semibold text-gray-900">{feature.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-500">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureSection;

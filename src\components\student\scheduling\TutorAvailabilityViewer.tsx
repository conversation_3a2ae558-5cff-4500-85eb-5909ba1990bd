import React, { useEffect } from "react";
import { Calendar } from "@/components/ui/Calendar";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/Card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/Select";
import { Textarea } from "@/components/ui/TextArea";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import { useToast } from "@/components/ui/UseToast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/Alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";
import { useSessionStore } from "@/store/sessionStore";
import { useSessionRequestStore } from "@/store/sessionRequestStore";
import { getAvailableTimeSlots, DailyAvailability, TimeSlot } from "@/services/availabilityService";
import { Calendar as CalendarIcon, AlertCircle, Info, CheckCircle, X } from "lucide-react";
import { useTutorAvailabilityViewerStore } from "@/store/tutorAvailabilityViewerStore";

interface TutorAvailabilityViewerProps {
  tutorId: string;
  studentId: string;
  batchId: string;
  topicId?: string;
  subtopicId?: string;
  onRequestSent?: () => void;
}

const TutorAvailabilityViewer: React.FC<TutorAvailabilityViewerProps> = ({
  tutorId,
  studentId,
  batchId,
  topicId,
  subtopicId,
  onRequestSent
}) => {
  const { toast } = useToast();
  const {
    selectedDate,
    selectedTimeSlot,
    duration,
    notes,
    urgency,
    availabilityData,
    isLoading,
    topics,
    tutorInfo,
    setSelectedDate,
    setSelectedTimeSlot,
    setDuration,
    setNotes,
    setUrgency,
    setAvailabilityData,
    setIsLoading,
    setTopics,
    setTutorInfo,
    resetForm
  } = useTutorAvailabilityViewerStore();

  const { createSessionRequest } = useSessionRequestStore();

  // Fetch topics and subtopics for the batch
  useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    setTopics([
      {
        id: "topic-001",
        name: "Mathematics",
        subtopics: [
          { id: "subtopic-001", name: "Algebra" },
          { id: "subtopic-002", name: "Calculus" },
          { id: "subtopic-003", name: "Geometry" }
        ]
      },
      {
        id: "topic-002",
        name: "Computer Science",
        subtopics: [
          { id: "subtopic-004", name: "Data Structures" },
          { id: "subtopic-005", name: "Algorithms" },
          { id: "subtopic-006", name: "Machine Learning" }
        ]
      }
    ]);

    // Fetch tutor info based on tutorId
    const fetchTutorInfo = async () => {
      setIsLoading(true);
      try {
        // In a real app, this would be an API call to get tutor details
        // For now, we'll simulate with mock data based on tutorId

        // Mock API call delay
        await new Promise(resolve => setTimeout(resolve, 300));

        // Mock tutor data - in a real app, this would come from the API
        const tutorData = {
          "tutor1": {
            name: "Dr. Sarah Johnson",
            rating: 4.9,
            photoUrl: "https://randomuser.me/api/portraits/women/1.jpg"
          },
          "tutor2": {
            name: "Prof. Michael Chen",
            rating: 4.8,
            photoUrl: "https://randomuser.me/api/portraits/men/2.jpg"
          },
          "tutor3": {
            name: "Dr. Emily Rodriguez",
            rating: 4.7,
            photoUrl: "https://randomuser.me/api/portraits/women/3.jpg"
          }
        };

        // Get tutor info based on tutorId, or use a default if not found
        const tutor = tutorData[tutorId as keyof typeof tutorData] || {
          name: "Unknown Tutor",
          rating: 4.0,
          photoUrl: `https://ui-avatars.com/api/?name=Unknown+Tutor&background=random`
        };

        setTutorInfo(tutor);
      } catch (error) {
        console.error("Error fetching tutor info:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTutorInfo();
  }, [batchId, tutorId, setTopics, setTutorInfo, setIsLoading]);

  // Fetch availability data when date changes
  useEffect(() => {
    if (!selectedDate) return;

    setIsLoading(true);

    // Calculate start and end dates (7 days from selected date)
    const startDate = new Date(selectedDate);
    const endDate = new Date(selectedDate);
    endDate.setDate(endDate.getDate() + 6);

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    // Get available time slots
    const availableSlots = getAvailableTimeSlots(
      tutorId,
      studentId,
      startDateStr,
      endDateStr
    );

    setAvailabilityData(availableSlots);
    setIsLoading(false);
  }, [selectedDate, tutorId, studentId, setIsLoading, setAvailabilityData]);

  // Handle time slot selection
  const handleTimeSlotSelect = (timeSlot: TimeSlot) => {
    // Allow selection of both available and conflict slots
    if (timeSlot) {
      // If the same slot is clicked again, toggle selection
      if (selectedTimeSlot &&
          selectedTimeSlot.startTime === timeSlot.startTime &&
          selectedTimeSlot.endTime === timeSlot.endTime) {
        console.log("Unselecting time slot:", timeSlot);
        setSelectedTimeSlot(null);
      } else {
        console.log("Selecting time slot:", timeSlot);
        setSelectedTimeSlot(timeSlot);

        // If selecting a conflict slot, set urgency to high by default
        if (!timeSlot.isAvailable) {
          setUrgency("high");
        } else {
          setUrgency("medium");
        }
      }
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedDate || !duration || !selectedTimeSlot) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // We now allow conflict slots, so we don't need to check availability

    // Validate batch, topic, and subtopic
    if (!batchId) {
      toast({
        title: "Missing batch information",
        description: "No batch ID was provided. Please try again or contact support.",
        variant: "destructive",
      });
      console.error("Missing batchId in TutorAvailabilityViewer");
      return;
    }

    setIsLoading(true);

    try {
      // Format date and time
      const dateStr = selectedDate.toISOString().split('T')[0];
      const timeStr = selectedTimeSlot.startTime;

      // Use the topic and subtopic IDs passed from the parent component
      // If not provided, fall back to the first topic and subtopic from our mock data
      const topicToUse = topicId || topics[0]?.id;
      const subtopicToUse = subtopicId || topics[0]?.subtopics[0]?.id;

      if (!topicToUse) {
        toast({
          title: "Missing topic information",
          description: "No topic was selected. Please try again or contact support.",
          variant: "destructive",
        });
        console.error("Missing topicId in TutorAvailabilityViewer");
        setIsLoading(false);
        return;
      }

      console.log("Submitting session request with:", {
        batchId,
        topicId: topicToUse,
        subtopicId: subtopicToUse,
        tutorId,
        studentId,
        date: dateStr,
        time: timeStr,
        duration: parseInt(duration),
        urgency,
        isConflictSlot: !selectedTimeSlot.isAvailable
      });

      // Create session request
      await createSessionRequest({
        batchId,
        topicId: topicToUse,
        subtopicId: subtopicToUse,
        requestedTutorId: tutorId,
        studentId,
        requestedDate: dateStr,
        requestedTime: timeStr,
        durationMin: parseInt(duration),
        notes,
        urgency: urgency
        // The hasConflict will be determined by the store's checkForConflicts method
      });

      toast({
        title: "Request sent",
        description: "Session request has been sent successfully.",
      });

      // Reset form using Zustand store's resetForm function
      resetForm();

      // Call callback if provided
      if (onRequestSent) {
        onRequestSent();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send session request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // No longer needed since we removed the topic/subtopic selection

  // Get time slots for selected date and duration
  const getTimeSlotsForSelectedDate = () => {
    if (!selectedDate || !duration) return [];

    const dateStr = selectedDate.toISOString().split('T')[0];
    const dailyAvailability = availabilityData.find((day: { date: string }) => day.date === dateStr);

    if (!dailyAvailability) return [];

    const allTimeSlots = dailyAvailability.timeSlots;
    const durationMinutes = parseInt(duration);

    // If duration is 30 minutes, return all slots as is
    if (durationMinutes === 30) {
      return allTimeSlots;
    }

    // For longer durations, we need to combine slots
    const combinedSlots: TimeSlot[] = [];

    // Number of 30-minute slots needed for the selected duration
    const slotsNeeded = durationMinutes / 30;

    for (let i = 0; i < allTimeSlots.length; i++) {
      // Check if we have enough consecutive slots from this position
      if (i + slotsNeeded > allTimeSlots.length) {
        break; // Not enough slots left
      }

      // Check if all consecutive slots are available
      let allAvailable = true;
      let conflictReason = "";

      for (let j = 0; j < slotsNeeded; j++) {
        if (!allTimeSlots[i + j] || !allTimeSlots[i + j].isAvailable) {
          allAvailable = false;
          conflictReason = allTimeSlots[i + j]?.conflictReason || "Time slot unavailable";
          break;
        }
      }

      // Make sure we have valid start and end slots
      const startSlot = allTimeSlots[i];
      const endSlot = allTimeSlots[i + slotsNeeded - 1];

      // Only create a slot if both start and end slots exist
      if (startSlot && endSlot && endSlot.endTime) {
        combinedSlots.push({
          day: startSlot.day,
          startTime: startSlot.startTime,
          endTime: endSlot.endTime,
          isAvailable: allAvailable,
          conflictReason: allAvailable ? undefined : conflictReason
        });
      }

      // Skip to the next potential starting slot (non-overlapping)
      i += slotsNeeded - 1;
    }

    return combinedSlots;
  };

  // Render star rating
  const renderStarRating = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span key={i} className={`text-${i <= rating ? 'yellow' : 'gray'}-400`}>
          ★
        </span>
      );
    }
    return stars;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Book a Session</CardTitle>
            <CardDescription>
              View tutor availability and request a session
            </CardDescription>
          </div>
          <div className="flex items-center">
            <div className="mr-3 text-right">
              <div className="font-medium">{tutorInfo.name}</div>
              <div className="text-sm text-yellow-500">
                {renderStarRating(tutorInfo.rating)} {tutorInfo.rating.toFixed(1)}
              </div>
            </div>
            <div className="h-10 w-10 rounded-full overflow-hidden">
              <img
                src={tutorInfo.photoUrl || "https://ui-avatars.com/api/?name=Tutor"}
                alt={tutorInfo.name}
                className="h-full w-full object-cover"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Alert className="mb-4">
          <Info className="h-4 w-4" />
          <AlertTitle>Booking Information</AlertTitle>
          <AlertDescription>
            Select a date, duration, and time slot to request a session with your assigned tutor.
            The tutor will need to confirm your request before the session is scheduled.
          </AlertDescription>
        </Alert>

        <div className="space-y-6">
          {/* Step 1: Select Date */}
          <div>
            <h3 className="text-lg font-medium mb-4">Step 1: Select Date</h3>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={(date) => {
                setSelectedDate(date);
                // Reset time slot selection when date changes
                setSelectedTimeSlot(null);
              }}
              className="rounded-md border mx-auto"
            />
          </div>

          {/* Step 2: Select Duration */}
          <div>
            <h3 className="text-lg font-medium mb-4">Step 2: Select Duration</h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Session Duration</label>
                <Select
                  value={duration}
                  onValueChange={(value) => {
                    setDuration(value);
                    // Reset time slot selection when duration changes
                    setSelectedTimeSlot(null);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select duration" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="45">45 minutes</SelectItem>
                    <SelectItem value="60">60 minutes</SelectItem>
                    <SelectItem value="90">90 minutes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Step 3: Select Time Slot (only shown when duration is selected) */}
          {duration ? (
            <div>
              <h3 className="text-lg font-medium mb-4">Step 3: Select Time Slot</h3>
              {isLoading ? (
                <div className="flex justify-center items-center h-20">
                  <p>Loading availability data...</p>
                </div>
              ) : (
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 max-h-60 overflow-y-auto">
                  {(() => {
                    const timeSlots = getTimeSlotsForSelectedDate();

                    if (!timeSlots || timeSlots.length === 0) {
                      return (
                        <div className="col-span-full text-center py-4 text-gray-500">
                          No {duration}-minute time slots available for this date
                        </div>
                      );
                    }

                    return timeSlots.map((slot: TimeSlot, index: number) => (
                      <Button
                        key={`${slot.startTime}-${index}`}
                        variant={selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime ? "default" : "outline"}
                        className={`
                          relative flex items-center justify-center p-2 h-auto w-full
                          ${!slot.isAvailable && !(selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime) ? "opacity-80" : ""}
                          ${selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime
                            ? slot.isAvailable
                              ? "bg-green-700 text-white border-2 border-green-500"
                              : "bg-amber-600 text-white border-2 border-amber-400"
                            : !slot.isAvailable
                              ? "bg-red-50 hover:bg-red-100 border border-red-200"
                              : "hover:bg-gray-100"}
                        `}
                        onClick={() => slot ? handleTimeSlotSelect(slot) : null}
                        disabled={!slot}
                      >
                        <div className="text-center">
                          <span className={`text-xs block font-bold ${selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime ? "text-white" : ""}`}>
                            {slot?.startTime || "--:--"} - {slot?.endTime || "--:--"}
                          </span>
                          <span className={`text-xs block ${selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime ? "text-white opacity-90" : "text-gray-500"}`}>
                            ({duration} min)
                          </span>
                          {slot && !slot.isAvailable ? (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className={`text-xs flex items-center justify-center mt-1 ${
                                    selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime
                                    ? "text-white bg-red-600 px-2 py-0.5 rounded-full"
                                    : "text-red-500"
                                  }`}>
                                    <AlertCircle className="h-3 w-3 mr-1" />
                                    {selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime
                                      ? "Selected (Conflict)"
                                      : "Conflict"
                                    }
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{slot.conflictReason || "Time slot unavailable"}</p>
                                  {selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime && (
                                    <p className="font-medium mt-1">You can still request this slot with high urgency</p>
                                  )}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          ) : (
                            <span className={`text-xs flex items-center justify-center mt-1 ${
                              selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime
                              ? "text-white bg-green-700 px-2 py-0.5 rounded-full"
                              : "text-green-500"
                            }`}>
                              <CheckCircle className="h-3 w-3 mr-1" />
                              {selectedTimeSlot && selectedTimeSlot.startTime === slot.startTime && selectedTimeSlot.endTime === slot.endTime
                                ? "Selected"
                                : "Available"
                              }
                            </span>
                          )}
                        </div>
                      </Button>
                    ));
                  })()}
                </div>
              )}
            </div>
          ) : null}

          {/* Step 4: Additional Information (only shown when date, duration and time are selected) */}
          {selectedDate && duration && selectedTimeSlot ? (
            <div>
              <h3 className="text-lg font-medium mb-4">Step 4: Additional Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-1 block">Additional Notes (Optional)</label>
                  <Textarea
                    placeholder="Add any specific topics you want to cover or questions you have"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                {selectedTimeSlot && !selectedTimeSlot.isAvailable && (
                  <div className="space-y-4">
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Scheduling Conflict</AlertTitle>
                      <AlertDescription>
                        There is a scheduling conflict at this time. Your request will be marked as conflicting,
                        and the tutor will need to decide whether to accept it.
                      </AlertDescription>
                    </Alert>

                    <div>
                      <label className="text-sm font-medium mb-1 block">Request Urgency</label>
                      <Select
                        value={urgency}
                        onValueChange={(value: 'high' | 'medium' | 'low') => setUrgency(value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select urgency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low - Can be rescheduled</SelectItem>
                          <SelectItem value="medium">Medium - Preferred time</SelectItem>
                          <SelectItem value="high">High - Urgent need</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-gray-500 mt-1">
                        Higher urgency requests are more likely to be considered despite conflicts
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : null}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between items-center">
        <div className="text-sm">
          {!selectedTimeSlot && duration && selectedDate ? (
            <span className="text-amber-600 font-medium flex items-center">
              <AlertCircle className="h-4 w-4 mr-1" />
              Please select a time slot to continue (available or conflict)
            </span>
          ) : null}
        </div>
        <Button
          onClick={handleSubmit}
          disabled={isLoading || !selectedDate || !duration || !selectedTimeSlot}
          className={`
            ${isLoading || !selectedDate || !duration || !selectedTimeSlot
              ? "bg-gray-400 cursor-not-allowed opacity-50"
              : selectedTimeSlot && !selectedTimeSlot.isAvailable
                ? "bg-amber-600 hover:bg-amber-700"
                : "bg-rfpurple-600 hover:bg-rfpurple-700"}
          `}
        >
          {isLoading
            ? "Sending Request..."
            : selectedTimeSlot && !selectedTimeSlot.isAvailable
              ? "Submit Conflict Request"
              : "Submit Request"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TutorAvailabilityViewer;

import React from "react";
import { Textarea } from "@/components/ui/TextArea";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

interface ExperienceFieldProps {
  form: any;
  name?: string;
  label?: string;
  placeholder?: string;
}

const ExperienceField: React.FC<ExperienceFieldProps> = ({
  form,
  name = "experience",
  label = "Teaching Experience",
  placeholder = "Describe your teaching experience, including years taught and institutions...",
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Textarea
              placeholder={placeholder}
              className="min-h-[150px]"
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default ExperienceField;

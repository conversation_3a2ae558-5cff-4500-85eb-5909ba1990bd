/* Hero Buttons Container */
.hero-buttons-container {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16px;
  z-index: 30;
  width: auto;
  max-width: 400px;
}

/* <PERSON> Styles */
.hero-button {
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  min-width: 160px;
  white-space: nowrap;
  border-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-buttons-container {
    flex-direction: column;
    width: 90%;
    max-width: 300px;
    gap: 12px;
    bottom: 30px;
  }
  
  .hero-button {
    width: 100%;
    min-width: auto;
    padding: 12px 20px;
  }
}

@media (max-width: 480px) {
  .hero-buttons-container {
    bottom: 20px;
  }
}

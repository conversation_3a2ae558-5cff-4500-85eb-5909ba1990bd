import { useAuth } from "@/context/AuthContext";

// Define the interface for the return type
interface FormattedProfileData {
  displayName: string;
  email: string;
  photoUrl: string;
}

// Hook to access and format profile data
// Note: Profile data loading is now centralized in AuthContext for better performance
export function useProfileData(): FormattedProfileData {
  const { user, profileData } = useAuth();

  // No longer fetching profile data here - it's handled centrally in AuthContext
  // This hook now only formats the data that's already available

  // Format the profile data for UI consumption
  const formattedProfileData: FormattedProfileData = {
    // Create display name from first and last name
    displayName: `${profileData.firstName || ""} ${profileData.lastName || ""}`.trim() ||
                 (profileData.email ? profileData.email.split('@')[0] : "User"),

    // Use email from profile data
    email: profileData.email || "",

    // Use profile picture URL from profile data or fallback to user metadata
    photoUrl: profileData.profilePictureUrl ||
              (user?.user_metadata?.profile_picture_url as string) ||
              (user?.user_metadata?.avatar_url as string) || ""
  };

  return formattedProfileData;
}


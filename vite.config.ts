import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    // Development settings (localhost)
    ...(mode === "development"
      ? {
          host: "::",
          port: 8080,
        }
      : {}),

    // Production settings
    ...(mode === "production"
      ? {
          // No specific server settings needed for production build
        }
      : {}),
  },
  plugins: [react(), mode === "development" && componentTagger()].filter(
    Boolean
  ),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    sourcemap: true,
  },
  // Base URL configuration
  base: mode === "production" ? "/" : "/",
}));

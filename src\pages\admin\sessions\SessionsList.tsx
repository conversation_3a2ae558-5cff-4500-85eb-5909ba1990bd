import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import AdminSidebar from "@/components/admin/Sidebar";
import UserProfileMenu from "@/components/UserProfileMenu";
import { useProfileData } from "@/hooks/useProfileData";
import { List, Search, Calendar, Edit, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/Card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const SessionsList = () => {
  const navigate = useNavigate();
  const profileData = useProfileData();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock session data - in a real app, you'd fetch this from your backend
  const sessions = [
    {
      id: "1",
      subject: "Mathematics",
      date: "2023-12-15",
      time: "14:00",
      tutor: "<PERSON>",
      student: "Alice <PERSON>",
      status: "upcoming",
    },
    {
      id: "2",
      subject: "Physics",
      date: "2023-12-16",
      time: "10:00",
      tutor: "<PERSON> <PERSON>",
      student: "Bob Brown",
      status: "upcoming",
    },
    {
      id: "3",
      subject: "Computer Science",
      date: "2023-12-10",
      time: "15:30",
      tutor: "Michael Davis",
      student: "Charlie Wilson",
      status: "completed",
    },
    {
      id: "4",
      subject: "Chemistry",
      date: "2023-12-08",
      time: "11:00",
      tutor: "Emily Taylor",
      student: "David Miller",
      status: "cancelled",
    },
    {
      id: "5",
      subject: "Biology",
      date: "2023-12-20",
      time: "16:00",
      tutor: "Robert Johnson",
      student: "Eva Martinez",
      status: "upcoming",
    },
  ];

  // Filter sessions based on search query and status filter
  const filteredSessions = sessions.filter((session) => {
    const matchesSearch =
      session.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.tutor.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.student.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || session.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "upcoming":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <AdminSidebar />
      <div className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">All Sessions</h1>
            <UserProfileMenu
              isAdmin={true}
              isAdminPage={true}
            />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Sessions List</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="text"
                    placeholder="Search by subject, tutor, or student..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sessions</SelectItem>
                    <SelectItem value="upcoming">Upcoming</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  className="button-gradient"
                  onClick={() => navigate("/admin/sessions/schedule")}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule New
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Subject</TableHead>
                      <TableHead>Date & Time</TableHead>
                      <TableHead>Tutor</TableHead>
                      <TableHead>Student</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSessions.length > 0 ? (
                      filteredSessions.map((session) => (
                        <TableRow key={session.id}>
                          <TableCell className="font-medium">
                            {session.subject}
                          </TableCell>
                          <TableCell>
                            {session.date} at {session.time}
                          </TableCell>
                          <TableCell>{session.tutor}</TableCell>
                          <TableCell>{session.student}</TableCell>
                          <TableCell>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                                session.status
                              )}`}
                            >
                              {session.status.charAt(0).toUpperCase() +
                                session.status.slice(1)}
                            </span>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                navigate(`/admin/sessions/view/${session.id}`)
                              }
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {session.status === "upcoming" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  navigate(
                                    `/admin/sessions/edit?id=${session.id}`
                                  )
                                }
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={6}
                          className="text-center py-4 text-gray-500"
                        >
                          No sessions found matching your criteria
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SessionsList;

import React from "react";
import { Card, CardContent } from "@/components/ui/Card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";
import { Edit } from "lucide-react";
import AuthenticatedImage from "@/components/ui/AuthenticatedImage";
import { StudentExtendedProfileData } from "@/pages/student/Profile";

interface ProfileCardSectionProps {
  profileData: StudentExtendedProfileData;
  handleOpenPhotoUpload: (type: 'profile' | 'gallery') => void;
  setIsEditing: (isEditing: boolean) => void;
}

const ProfileCardSection: React.FC<ProfileCardSectionProps> = ({
  profileData,
  handleOpenPhotoUpload,
  setIsEditing
}) => {
  // Debug: Log the profile picture URL being used
  console.log('ProfileCardSection - profilePictureUrl:', profileData.profilePictureUrl);

  return (
    <Card>
      <CardContent className="pt-6 pb-6 flex flex-col items-center">
        <div className="relative group cursor-pointer" onClick={() => handleOpenPhotoUpload('profile')}>
          <div className="w-32 h-32 border-2 border-gray-200 mb-4 relative rounded-full overflow-hidden">
            {profileData.profilePictureUrl ? (
              <AuthenticatedImage
                src={profileData.profilePictureUrl}
                alt={`${profileData.firstName} ${profileData.lastName}`}
                className="w-full h-full object-cover"
                onLoad={() => console.log('Profile picture loaded successfully:', profileData.profilePictureUrl)}
                onError={(e) => console.error('Profile picture failed to load:', profileData.profilePictureUrl, e)}
                fallback={
                  <div className="w-full h-full flex items-center justify-center bg-rfpurple-100 text-rfpurple-700 text-2xl font-semibold">
                    {profileData.firstName.charAt(0)}{profileData.lastName.charAt(0)}
                  </div>
                }
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-rfpurple-100 text-rfpurple-700 text-2xl font-semibold">
                {profileData.firstName.charAt(0)}{profileData.lastName.charAt(0)}
              </div>
            )}
          </div>
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="bg-white p-2 rounded-full shadow-md">
              <Edit className="h-5 w-5 text-rfpurple-500" />
            </div>
          </div>
        </div>

        <div className="flex items-center justify-center">
          <h2 className="text-xl font-bold text-center">
            {profileData.firstName} {profileData.lastName}
          </h2>
          <div
            className="ml-2 cursor-pointer hover:text-rfpurple-500 transition-colors"
            onClick={() => setIsEditing(true)}
          >
            <Edit className="h-4 w-4" />
          </div>
        </div>
        <p className="text-gray-500 text-center">{profileData.email}</p>

        {/* Social Links */}
        <div className="flex justify-center space-x-3 mt-4">
          <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600 hover:text-[#E4405F]"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg>
          </button>
          <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600 hover:text-[#1877F2]"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
          </button>
          <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600 hover:text-[#0A66C2]"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg>
          </button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileCardSection;

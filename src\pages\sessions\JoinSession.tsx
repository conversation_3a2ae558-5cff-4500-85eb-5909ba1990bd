import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabaseClient';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Separator } from '@/components/ui/Separator';
import { useToast } from '@/hooks/use-toast';
import TeamsMeetingManager from '@/components/meetings/TeamsMeetingManager';
import {
  Video,
  Clock,
  User,
  Calendar,
  ExternalLink,
  Shield,
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface SessionData {
  id: string;
  scheduled_at: string;
  duration_min: number;
  status: string;
  mode: string;
  meeting_url?: string;
  student_id: string;
  tutor_id: string;
  student_profile?: {
    first_name: string;
    last_name: string;
  };
  tutor_profile?: {
    first_name: string;
    last_name: string;
  };
  session_details?: {
    meeting_metadata?: {
      provider: string;
      join_url: string;
      meeting_id: string;
    };
  };
}

const JoinSession: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { user, userType } = useAuth();
  const { toast } = useToast();
  
  const [session, setSession] = useState<SessionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [joining, setJoining] = useState(false);

  useEffect(() => {
    if (!sessionId || !user) {
      setError('Invalid session or user not authenticated');
      setLoading(false);
      return;
    }

    fetchSessionData();
  }, [sessionId, user]);

  const fetchSessionData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch session with participant validation
      const { data: sessionData, error: sessionError } = await supabase
        .from('sessions')
        .select(`
          *,
          student_profile:profiles!sessions_student_id_fkey(first_name, last_name),
          tutor_profile:profiles!sessions_tutor_id_fkey(first_name, last_name),
          session_details(meeting_metadata)
        `)
        .eq('id', sessionId)
        .single();

      if (sessionError) {
        throw new Error('Session not found');
      }

      // Validate user authorization
      const isAuthorized = 
        sessionData.student_id === user.id ||
        sessionData.tutor_id === user.id ||
        userType === 'admin';

      if (!isAuthorized) {
        throw new Error('You are not authorized to join this session');
      }

      // Check session status and timing
      const now = new Date();
      const sessionStart = new Date(sessionData.scheduled_at);
      const sessionEnd = new Date(sessionStart.getTime() + sessionData.duration_min * 60000);
      const canJoinEarly = sessionStart.getTime() - now.getTime() <= 15 * 60 * 1000; // 15 minutes early

      if (sessionData.status === 'cancelled') {
        throw new Error('This session has been cancelled');
      }

      if (sessionData.status === 'completed') {
        throw new Error('This session has already ended');
      }

      if (!canJoinEarly && now < sessionStart) {
        const minutesUntilStart = Math.ceil((sessionStart.getTime() - now.getTime()) / (1000 * 60));
        throw new Error(`Session starts in ${minutesUntilStart} minutes. You can join 15 minutes before the start time.`);
      }

      if (now > sessionEnd) {
        throw new Error('This session has ended');
      }

      setSession(sessionData);
    } catch (err) {
      console.error('Error fetching session:', err);
      setError(err instanceof Error ? err.message : 'Failed to load session');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinMeeting = async () => {
    if (!session) return;

    try {
      setJoining(true);

      // Get meeting URL from session_details.meeting_metadata or fallback to sessions.meeting_url
      const meetingUrl = 
        session.session_details?.meeting_metadata?.join_url ||
        session.meeting_url;

      if (!meetingUrl) {
        throw new Error('No meeting URL available for this session');
      }

      // Log the join event
      await supabase
        .from('meeting_events')
        .insert({
          meeting_session_id: session.id,
          user_id: user.id,
          event_type: 'participant_joined',
          event_data: {
            user_type: userType,
            join_time: new Date().toISOString(),
            meeting_url: meetingUrl
          }
        });

      // Open meeting in new window/tab
      window.open(meetingUrl, '_blank', 'noopener,noreferrer');

      toast({
        title: "Joining session",
        description: "Opening meeting in a new window...",
      });

      // Update session status if needed
      if (session.status === 'scheduled') {
        await supabase
          .from('sessions')
          .update({ status: 'in_progress' })
          .eq('id', session.id);
      }

    } catch (err) {
      console.error('Error joining meeting:', err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to join meeting',
        variant: "destructive",
      });
    } finally {
      setJoining(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getParticipantName = () => {
    if (!session) return '';
    
    if (userType === 'student') {
      return `${session.tutor_profile?.first_name} ${session.tutor_profile?.last_name}`;
    } else {
      return `${session.student_profile?.first_name} ${session.student_profile?.last_name}`;
    }
  };

  const getSessionStatus = () => {
    if (!session) return null;

    const now = new Date();
    const sessionStart = new Date(session.scheduled_at);
    const sessionEnd = new Date(sessionStart.getTime() + session.duration_min * 60000);

    if (now < sessionStart) {
      return { status: 'upcoming', color: 'blue', text: 'Upcoming' };
    } else if (now >= sessionStart && now <= sessionEnd) {
      return { status: 'live', color: 'green', text: 'Live Now' };
    } else {
      return { status: 'ended', color: 'gray', text: 'Ended' };
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading session...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Unable to Join Session
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600">{error}</p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => navigate(-1)}
                className="flex-1"
              >
                Go Back
              </Button>
              <Button 
                onClick={() => navigate('/student/dashboard')}
                className="flex-1"
              >
                Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const sessionStatus = getSessionStatus();
  const { date, time } = formatDateTime(session.scheduled_at);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Video className="h-5 w-5" />
              Join Session
            </CardTitle>
            {sessionStatus && (
              <Badge 
                variant={sessionStatus.color === 'green' ? 'default' : 'secondary'}
                className={sessionStatus.color === 'green' ? 'bg-green-100 text-green-800' : ''}
              >
                {sessionStatus.text}
              </Badge>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Session Details */}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm">{date}</span>
            </div>
            <div className="flex items-center gap-3">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm">{time} ({session.duration_min} minutes)</span>
            </div>
            <div className="flex items-center gap-3">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {userType === 'student' ? 'Tutor: ' : 'Student: '}
                {getParticipantName()}
              </span>
            </div>
          </div>

          <Separator />

          {/* Teams Meeting Manager */}
          {userType === 'tutor' && (
            <TeamsMeetingManager
              sessionId={session.id}
              sessionData={{
                subject: `Tutoring Session - ${getParticipantName()}`,
                startTime: session.scheduled_at,
                endTime: new Date(new Date(session.scheduled_at).getTime() + session.duration_min * 60000).toISOString(),
                studentEmail: session.student_profile ? `${session.student_profile.first_name}.${session.student_profile.last_name}@example.com` : '<EMAIL>',
                tutorEmail: session.tutor_profile ? `${session.tutor_profile.first_name}.${session.tutor_profile.last_name}@example.com` : '<EMAIL>',
                description: `Online tutoring session between ${session.tutor_profile?.first_name} and ${session.student_profile?.first_name}`,
              }}
              existingMeetingUrl={session.session_details?.meeting_metadata?.join_url || session.meeting_url}
              onMeetingCreated={() => {
                // Refresh session data after meeting creation
                fetchSessionData();
              }}
              className="mb-4"
            />
          )}

          {/* Security Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <Shield className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">Secure Session</p>
                <p className="text-blue-600">This session is private and only accessible to authorized participants.</p>
              </div>
            </div>
          </div>

          {/* Join Button */}
          <Button
            onClick={handleJoinMeeting}
            disabled={joining || sessionStatus?.status === 'ended'}
            className="w-full button-gradient"
            size="lg"
          >
            {joining ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Joining...
              </>
            ) : (
              <>
                <ExternalLink className="h-4 w-4 mr-2" />
                Join Meeting
              </>
            )}
          </Button>

          {/* Additional Actions */}
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => navigate(-1)}
              className="flex-1"
            >
              Go Back
            </Button>
            <Button 
              variant="outline"
              onClick={() => navigate(`/${userType}/dashboard`)}
              className="flex-1"
            >
              Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default JoinSession;

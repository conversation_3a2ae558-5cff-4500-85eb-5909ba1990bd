// Test script to verify notification database functions are working
// Run this in your browser console or as a Node.js script

import { NotificationService } from './src/services/notificationService';

// Test function to verify database functions
export async function testNotificationFunctions() {
  console.log('🧪 Testing Notification Database Functions...\n');

  // Replace with a real user ID from your profiles table
  const testUserId = '715d2b84-cc4a-443b-bee1-74e80725b21d'; // UPDATE THIS!

  try {
    // Test 1: Create a notification using database function
    console.log('1️⃣ Testing notification creation...');
    const notification = await NotificationService.createNotification({
      userId: testUserId,
      title: 'Test Notification',
      message: 'This is a test notification created using the database function.',
      type: 'system'
    });

    if (notification) {
      console.log('✅ Notification created successfully:', notification.id);
    } else {
      console.log('❌ Failed to create notification');
      return;
    }

    // Test 2: Get unread count using database function
    console.log('\n2️⃣ Testing unread count function...');
    const unreadCount = await NotificationService.getUnreadCount(testUserId);
    console.log('✅ Unread count:', unreadCount);

    // Test 3: Test template creation
    console.log('\n3️⃣ Testing template notification...');
    const templateNotification = await NotificationService.createFromTemplate(
      testUserId,
      NotificationService.templates.paymentSuccess('Test Product')
    );

    if (templateNotification) {
      console.log('✅ Template notification created:', templateNotification.id);
    } else {
      console.log('❌ Failed to create template notification');
    }

    // Test 4: Test quick notify helper
    console.log('\n4️⃣ Testing quick notify helper...');
    const quickNotification = await NotificationService.quickNotify(
      testUserId,
      'Quick Test',
      'This is a quick notification test.'
    );

    if (quickNotification) {
      console.log('✅ Quick notification created:', quickNotification.id);
    } else {
      console.log('❌ Failed to create quick notification');
    }

    // Test 5: Final unread count check
    console.log('\n5️⃣ Final unread count check...');
    const finalUnreadCount = await NotificationService.getUnreadCount(testUserId);
    console.log('✅ Final unread count:', finalUnreadCount);

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📝 Summary:');
    console.log(`- Created ${finalUnreadCount - unreadCount} new notifications`);
    console.log('- Database functions are working correctly');
    console.log('- Notification service is properly integrated');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔍 Troubleshooting:');
    console.log('1. Make sure you have run the setup_notifications_rls.sql script');
    console.log('2. Check that the user ID exists in your profiles table');
    console.log('3. Verify RLS policies are correctly set up');
    console.log('4. Check Supabase logs for any database errors');
  }
}

// Instructions for running the test
console.log(`
🚀 To test the notification functions:

1. Update the testUserId variable with a real user ID from your profiles table
2. Run this function in your browser console or Node.js environment:
   
   testNotificationFunctions()

3. Check the console output for test results

📋 Prerequisites:
- Database functions created (setup_notifications_rls.sql executed)
- Valid user ID in profiles table
- RLS policies properly configured
`);

// Export for use in other files
export { NotificationService };

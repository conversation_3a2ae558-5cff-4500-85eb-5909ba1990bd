// Environment configuration helper
export type Environment = 'development' | 'production' | 'staging';

export interface EnvironmentConfig {
  environment: Environment;
  apiUrl: string;
  appUrl: string;
  supabase: {
    url: string;
    anonKey: string;
  };
  payment: {
    defaultProvider: string;
    providers: {
      stripe: {
        publishableKey: string;
        enabled: boolean;
      };
      razorpay: {
        keyId: string;
        enabled: boolean;
      };
    };
  };
  meeting: {
    defaultProvider: string;
    providers: {
      microsoft_teams: {
        enabled: boolean;
      };
    };
  };
  features: {
    enableAnalytics: boolean;
    enableNotifications: boolean;
    enableDebugLogs: boolean;
  };
  oauth: {
    redirectBaseUrl: string;
  };
  tawkTo: {
    enabled: boolean;
    propertyId: string;
    widgetId: string;
  };
}

// Get current environment
export const getCurrentEnvironment = (): Environment => {
  const env = import.meta.env.VITE_ENVIRONMENT || import.meta.env.MODE || 'development';
  return env as Environment;
};

// Environment-specific configurations
const environmentConfigs: Record<Environment, EnvironmentConfig> = {
  development: {
    environment: 'development',
    apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:8080/api',
    appUrl: import.meta.env.VITE_APP_URL || 'http://localhost:8080',
    supabase: {
      url: import.meta.env.VITE_SUPABASE_URL || '',
      anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
    },
    payment: {
      defaultProvider: 'razorpay',
      providers: {
        stripe: {
          publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
          enabled: false,
        },
        razorpay: {
          keyId: import.meta.env.VITE_RAZORPAY_KEY_ID || '',
          enabled: true,
        },
      },
    },
    meeting: {
      defaultProvider: 'microsoft_teams',
      providers: {
        microsoft_teams: {
          enabled: true,
        },
      },
    },
    features: {
      enableAnalytics: false,
      enableNotifications: true,
      enableDebugLogs: true,
    },
    oauth: {
      redirectBaseUrl: 'http://localhost:8080',
    },
    tawkTo: {
      enabled: import.meta.env.VITE_TAWKTO_ENABLED === 'true' || false,
      propertyId: import.meta.env.VITE_TAWKTO_PROPERTY_ID || '',
      widgetId: import.meta.env.VITE_TAWKTO_WIDGET_ID || '',
    },
  },
  production: {
    environment: 'production',
    apiUrl: import.meta.env.VITE_API_URL || 'https://rflearn.com/api',
    appUrl: import.meta.env.VITE_APP_URL || 'https://rflearn.com',
    supabase: {
      url: import.meta.env.VITE_SUPABASE_URL || '',
      anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
    },
    payment: {
      defaultProvider: 'razorpay',
      providers: {
        stripe: {
          publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
          enabled: false,
        },
        razorpay: {
          keyId: import.meta.env.VITE_RAZORPAY_KEY_ID || '',
          enabled: true,
        },
      },
    },
    meeting: {
      defaultProvider: 'microsoft_teams',
      providers: {
        microsoft_teams: {
          enabled: true,
        },
      },
    },
    features: {
      enableAnalytics: true,
      enableNotifications: true,
      enableDebugLogs: false,
    },
    oauth: {
      redirectBaseUrl: 'https://rflearn.com',
    },
    tawkTo: {
      enabled: import.meta.env.VITE_TAWKTO_ENABLED === 'true' || false,
      propertyId: import.meta.env.VITE_TAWKTO_PROPERTY_ID || '',
      widgetId: import.meta.env.VITE_TAWKTO_WIDGET_ID || '',
    },
  },
  staging: {
    environment: 'staging',
    apiUrl: import.meta.env.VITE_API_URL || 'https://staging.rflearn.com/api',
    appUrl: import.meta.env.VITE_APP_URL || 'https://staging.rflearn.com',
    supabase: {
      url: import.meta.env.VITE_SUPABASE_URL || '',
      anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
    },
    payment: {
      defaultProvider: 'razorpay',
      providers: {
        stripe: {
          publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
          enabled: false,
        },
        razorpay: {
          keyId: import.meta.env.VITE_RAZORPAY_KEY_ID || '',
          enabled: true,
        },
      },
    },
    meeting: {
      defaultProvider: 'microsoft_teams',
      providers: {
        microsoft_teams: {
          enabled: true,
        },
      },
    },
    features: {
      enableAnalytics: false,
      enableNotifications: true,
      enableDebugLogs: true,
    },
    oauth: {
      redirectBaseUrl: 'https://staging.rflearn.com',
    },
    tawkTo: {
      enabled: import.meta.env.VITE_TAWKTO_ENABLED === 'true' || false,
      propertyId: import.meta.env.VITE_TAWKTO_PROPERTY_ID || '',
      widgetId: import.meta.env.VITE_TAWKTO_WIDGET_ID || '',
    },
  },
};

// Get current environment configuration
export const getEnvironmentConfig = (): EnvironmentConfig => {
  const currentEnv = getCurrentEnvironment();
  const config = environmentConfigs[currentEnv];
  
  if (!config) {
    console.warn(`Unknown environment: ${currentEnv}, falling back to development`);
    return environmentConfigs.development;
  }
  
  return config;
};

// Validate environment configuration
export const validateEnvironmentConfig = (config: EnvironmentConfig): boolean => {
  const errors: string[] = [];
  
  if (!config.supabase.url) {
    errors.push('VITE_SUPABASE_URL is missing');
  }
  
  if (!config.supabase.anonKey) {
    errors.push('VITE_SUPABASE_ANON_KEY is missing');
  }
  
  if (!config.apiUrl) {
    errors.push('VITE_API_URL is missing');
  }
  
  if (!config.appUrl) {
    errors.push('VITE_APP_URL is missing');
  }
  
  if (errors.length > 0) {
    console.error('Environment configuration errors:', errors);
    return false;
  }
  
  return true;
};

// Export the current configuration
export const config = getEnvironmentConfig();

// Log configuration in development
if (config.environment === 'development' && config.features.enableDebugLogs) {
  console.log('🔧 Environment Configuration:', {
    environment: config.environment,
    apiUrl: config.apiUrl,
    appUrl: config.appUrl,
    supabaseUrl: config.supabase.url,
    oauthRedirectBase: config.oauth.redirectBaseUrl,
    features: config.features,
  });
}

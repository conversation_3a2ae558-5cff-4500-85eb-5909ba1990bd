import { create } from "zustand";
import { supabase } from "@/lib/supabaseClient";

// Types for the 4-step subscription workflow
export interface SubscriptionWorkflow {
  id: string;
  student_id: string;
  product_type: 'booster' | 'custom' | 'preparation';
  product_id?: string;
  current_step: number;
  status: 'in_progress' | 'completed' | 'cancelled' | 'admin_assistance_requested';

  // Step completion tracking
  step_completions: {
    step_1_completed: boolean;
    step_2_completed: boolean;
    step_3_completed: boolean;
    step_4_completed: boolean;
    step_1_completed_at?: string;
    step_2_completed_at?: string;
    step_3_completed_at?: string;
    step_4_completed_at?: string;
  };

  // Admin assistance
  admin_assistance: {
    admin_assistance_requested: boolean;
    admin_assistance_message?: string;
    assigned_admin_id?: string;
    admin_configured: boolean;
    student_confirmation_required: boolean;
    student_confirmed: boolean;
  };

  curriculum_selections?: CurriculumSelections;
  pricing_data?: PricingData;
  created_at: string;
  updated_at: string;
}

export interface CurriculumSelections {
  id: string;
  workflow_id: string;
  selection_type: 'complete_subjects' | 'selected_topics' | 'selected_subtopics';
  selected_subjects: string[];
  selected_topics: string[];
  selected_subtopics: string[];
  estimated_sessions: number;
  configured_by: string;
  configured_by_role: 'student' | 'admin';
  configuration_notes?: string;
}

export interface PricingData {
  id: string;
  workflow_id: string;
  base_price: number;
  sessions_count: number;
  price_per_session?: number;
  admin_override_applied: boolean;
  admin_override_price?: number;
  admin_override_reason?: string;
  admin_override_by?: string;
  final_price: number;
  discount_amount: number;
  discount_percentage: number;
  calculation_method: 'fixed_product_price' | 'dynamic_session_based';
  calculation_details?: any;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  duration_days: number;
  type: 'booster' | 'custom' | 'preparation';
  features?: any;
  is_active: boolean;
}

interface SubscriptionWorkflowStore {
  // State
  currentWorkflow: SubscriptionWorkflow | null;
  availableProducts: Product[];
  isLoading: boolean;
  isLoadingProducts: boolean;
  error: string | null;

  // Step-specific data
  selectedProductType: 'booster' | 'custom' | 'preparation' | null;
  selectedProduct: Product | null;
  curriculumSelections: {
    selectedTopics: string[];
    selectedSubtopics: string[];
    estimatedSessions: number;
  };
  pricingCalculation: {
    basePrice: number;
    finalPrice: number;
    sessionsCount: number;
    discountApplied: number;
  };

  // Actions
  fetchAvailableProducts: () => Promise<void>;
  getActiveWorkflow: (studentId: string) => Promise<SubscriptionWorkflow | null>;
  getAllActiveWorkflows: (studentId: string) => Promise<SubscriptionWorkflow[]>;
  getActiveWorkflowForProduct: (studentId: string, productId: string) => Promise<SubscriptionWorkflow | null>;
  loadWorkflowById: (workflowId: string) => Promise<SubscriptionWorkflow | null>;
  createWorkflow: (studentId: string, productType: 'booster' | 'custom' | 'preparation', productId: string) => Promise<string | null>;
  updateWorkflowStep: (workflowId: string, step: number, data?: any) => Promise<boolean>;

  // Step 1: Product Selection
  selectProductType: (productType: 'booster' | 'custom' | 'preparation') => void;
  selectProduct: (product: Product) => void;
  completeStep1: (workflowId: string) => Promise<boolean>;

  // Step 2: Curriculum Configuration
  updateCurriculumSelections: (selections: { topics: string[]; subtopics: string[] }) => void;
  saveCurriculumSelections: (workflowId: string, userId: string, userRole: 'student' | 'admin') => Promise<boolean>;
  completeStep2: (workflowId: string) => Promise<boolean>;

  // Step 3: Pricing Calculation
  calculatePricing: (workflowId: string) => Promise<boolean>;
  applyAdminOverride: (workflowId: string, overridePrice: number, reason: string, adminId: string) => Promise<boolean>;
  completeStep3: (workflowId: string) => Promise<boolean>;

  // Step 4: Purchase
  completePurchase: (workflowId: string, paymentData: any) => Promise<boolean>;

  // Admin assistance
  requestAdminAssistance: (workflowId: string, message: string) => Promise<boolean>;
  assignAdminToWorkflow: (workflowId: string, adminId: string) => Promise<boolean>;
  adminConfigureCurriculum: (workflowId: string, adminId: string, selections: any) => Promise<boolean>;
  studentConfirmConfiguration: (workflowId: string) => Promise<boolean>;

  // Utility
  resetWorkflow: () => void;
  cancelWorkflow: (workflowId: string) => Promise<boolean>;
  goToStep: (step: number) => void;
  cleanupLegacyWorkflows: (studentId: string) => Promise<boolean>;
}

export const useSubscriptionWorkflowStore = create<SubscriptionWorkflowStore>((set, get) => ({
  // Initial state
  currentWorkflow: null,
  availableProducts: [],
  isLoading: false,
  isLoadingProducts: false,
  error: null,
  selectedProductType: null,
  selectedProduct: null,
  curriculumSelections: {
    selectedTopics: [],
    selectedSubtopics: [],
    estimatedSessions: 0
  },
  pricingCalculation: {
    basePrice: 0,
    finalPrice: 0,
    sessionsCount: 0,
    discountApplied: 0
  },

  // Fetch available products
  fetchAvailableProducts: async () => {
    set({ isLoadingProducts: true, error: null });

    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_active', true)
        .order('type', { ascending: true });

      if (error) throw error;

      set({ availableProducts: data || [], isLoadingProducts: false });
    } catch (error) {
      console.error('Error fetching products:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch products',
        isLoadingProducts: false
      });
    }
  },

  // Get active workflow for student
  getActiveWorkflow: async (studentId: string) => {
    console.log('getActiveWorkflow called with studentId:', studentId);
    set({ isLoading: true, error: null });

    try {
      console.log('Calling get_active_workflow_for_student RPC...');
      const { data, error } = await supabase
        .rpc('get_active_workflow_for_student', { p_student_id: studentId });

      console.log('RPC response:', { data, error });

      if (error) {
        console.error('RPC error:', error);
        throw error;
      }

      if (data && data.length > 0) {
        const workflowId = data[0].workflow_id;
        console.log('Found active workflow, fetching complete data for ID:', workflowId);

        try {
          // Try to fetch complete workflow data using RPC function
          const { data: workflowData, error: workflowError } = await supabase
            .rpc('get_subscription_workflow_data', { p_workflow_id: workflowId });

          console.log('Complete workflow data response:', { workflowData, workflowError });

          if (workflowError) {
            console.error('Workflow data RPC error, falling back to direct table query:', workflowError);
            throw workflowError;
          }

          if (workflowData && workflowData.length > 0) {
            const rawWorkflow = workflowData[0];

            // Transform the RPC response to match our interface
            const workflow: SubscriptionWorkflow = {
              id: rawWorkflow.workflow_id || rawWorkflow.id,
              student_id: rawWorkflow.student_id,
              product_type: rawWorkflow.product_type,
              product_id: rawWorkflow.product_id,
              current_step: rawWorkflow.current_step,
              status: rawWorkflow.status,
              step_completions: rawWorkflow.step_completions || {
                step_1_completed: false,
                step_2_completed: false,
                step_3_completed: false,
                step_4_completed: false,
              },
              admin_assistance: rawWorkflow.admin_assistance || {
                admin_assistance_requested: false,
                admin_configured: false,
                student_confirmation_required: false,
                student_confirmed: false,
              },
              curriculum_selections: rawWorkflow.curriculum_selections || {},
              pricing_data: rawWorkflow.pricing_data || {},
              created_at: rawWorkflow.created_at,
              updated_at: rawWorkflow.updated_at,
            };

            console.log('Setting current workflow from RPC (transformed):', workflow);
            console.log('About to set store state: isLoading: false, currentWorkflow exists');
            set({ currentWorkflow: workflow, isLoading: false });
            console.log('Store state set. Current store state:', get());
            return workflow;
          }
        } catch (rpcError) {
          console.log('RPC failed, trying direct table query as fallback...');

          // Fallback: fetch workflow data directly from table
          const { data: directWorkflowData, error: directError } = await supabase
            .from('subscription_workflows')
            .select('*')
            .eq('id', workflowId)
            .single();

          console.log('Direct table query response:', { directWorkflowData, directError });

          if (directError) {
            console.error('Direct table query also failed:', directError);
            throw directError;
          }

          if (directWorkflowData) {
            // Transform the raw data into the expected format
            const workflow: SubscriptionWorkflow = {
              id: directWorkflowData.id,
              student_id: directWorkflowData.student_id,
              product_type: directWorkflowData.product_type,
              product_id: directWorkflowData.product_id,
              current_step: directWorkflowData.current_step,
              status: directWorkflowData.status,
              step_completions: {
                step_1_completed: directWorkflowData.step_1_completed || false,
                step_2_completed: directWorkflowData.step_2_completed || false,
                step_3_completed: directWorkflowData.step_3_completed || false,
                step_4_completed: directWorkflowData.step_4_completed || false,
                step_1_completed_at: directWorkflowData.step_1_completed_at,
                step_2_completed_at: directWorkflowData.step_2_completed_at,
                step_3_completed_at: directWorkflowData.step_3_completed_at,
                step_4_completed_at: directWorkflowData.step_4_completed_at,
              },
              admin_assistance: {
                admin_assistance_requested: directWorkflowData.admin_assistance_requested || false,
                admin_assistance_message: directWorkflowData.admin_assistance_message,
                assigned_admin_id: directWorkflowData.assigned_admin_id,
                admin_configured: directWorkflowData.admin_configured || false,
                student_confirmation_required: directWorkflowData.student_confirmation_required || false,
                student_confirmed: directWorkflowData.student_confirmed || false,
              },
              curriculum_selections: {},
              pricing_data: {},
              created_at: directWorkflowData.created_at,
              updated_at: directWorkflowData.updated_at,
            };

            console.log('Setting current workflow from direct query:', workflow);
            set({ currentWorkflow: workflow, isLoading: false });
            return workflow;
          }
        }
      }

      console.log('No active workflow found, setting to null');
      set({ currentWorkflow: null, isLoading: false });
      return null;
    } catch (error) {
      console.error('Error fetching active workflow:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch workflow',
        isLoading: false
      });
      return null;
    }
  },

  // Get all active workflows for student
  getAllActiveWorkflows: async (studentId: string) => {
    console.log('getAllActiveWorkflows called with studentId:', studentId);
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('subscription_workflows')
        .select(`
          *,
          products (
            id,
            name,
            description,
            price,
            type
          )
        `)
        .eq('student_id', studentId)
        .in('status', ['in_progress', 'admin_assistance_requested'])
        .order('created_at', { ascending: false });

      if (error) throw error;

      const workflows: SubscriptionWorkflow[] = (data || []).map((rawWorkflow: any) => ({
        id: rawWorkflow.id,
        student_id: rawWorkflow.student_id,
        product_type: rawWorkflow.product_type,
        product_id: rawWorkflow.product_id,
        current_step: rawWorkflow.current_step,
        status: rawWorkflow.status,
        step_completions: {
          step_1_completed: rawWorkflow.step_1_completed || false,
          step_2_completed: rawWorkflow.step_2_completed || false,
          step_3_completed: rawWorkflow.step_3_completed || false,
          step_4_completed: rawWorkflow.step_4_completed || false,
          step_1_completed_at: rawWorkflow.step_1_completed_at,
          step_2_completed_at: rawWorkflow.step_2_completed_at,
          step_3_completed_at: rawWorkflow.step_3_completed_at,
          step_4_completed_at: rawWorkflow.step_4_completed_at,
        },
        admin_assistance: {
          admin_assistance_requested: rawWorkflow.admin_assistance_requested || false,
          admin_assistance_message: rawWorkflow.admin_assistance_message,
          assigned_admin_id: rawWorkflow.assigned_admin_id,
          admin_configured: rawWorkflow.admin_configured || false,
          student_confirmation_required: rawWorkflow.student_confirmation_required || false,
          student_confirmed: rawWorkflow.student_confirmed || false,
        },
        curriculum_selections: {},
        pricing_data: {},
        created_at: rawWorkflow.created_at,
        updated_at: rawWorkflow.updated_at,
        // Add product info for display
        product: rawWorkflow.products
      }));

      console.log('Found active workflows:', workflows);
      set({ isLoading: false });
      return workflows;
    } catch (error) {
      console.error('Error fetching all active workflows:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch workflows',
        isLoading: false
      });
      return [];
    }
  },

  // Get active workflow for specific product
  getActiveWorkflowForProduct: async (studentId: string, productId: string) => {
    console.log('getActiveWorkflowForProduct called with studentId:', studentId, 'productId:', productId);
    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from('subscription_workflows')
        .select('*')
        .eq('student_id', studentId)
        .eq('product_id', productId)
        .in('status', ['in_progress', 'admin_assistance_requested', 'completed'])
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (data && data.length > 0) {
        const rawWorkflow = data[0];

        // Transform the raw data into the expected format
        const workflow: SubscriptionWorkflow = {
          id: rawWorkflow.id,
          student_id: rawWorkflow.student_id,
          product_type: rawWorkflow.product_type,
          product_id: rawWorkflow.product_id,
          current_step: rawWorkflow.current_step,
          status: rawWorkflow.status,
          step_completions: {
            step_1_completed: rawWorkflow.step_1_completed || false,
            step_2_completed: rawWorkflow.step_2_completed || false,
            step_3_completed: rawWorkflow.step_3_completed || false,
            step_4_completed: rawWorkflow.step_4_completed || false,
            step_1_completed_at: rawWorkflow.step_1_completed_at,
            step_2_completed_at: rawWorkflow.step_2_completed_at,
            step_3_completed_at: rawWorkflow.step_3_completed_at,
            step_4_completed_at: rawWorkflow.step_4_completed_at,
          },
          admin_assistance: {
            admin_assistance_requested: rawWorkflow.admin_assistance_requested || false,
            admin_assistance_message: rawWorkflow.admin_assistance_message,
            assigned_admin_id: rawWorkflow.assigned_admin_id,
            admin_configured: rawWorkflow.admin_configured || false,
            student_confirmation_required: rawWorkflow.student_confirmation_required || false,
            student_confirmed: rawWorkflow.student_confirmed || false,
          },
          curriculum_selections: {},
          pricing_data: {},
          created_at: rawWorkflow.created_at,
          updated_at: rawWorkflow.updated_at,
        };

        console.log('Found active workflow for product:', workflow);
        set({ currentWorkflow: workflow, isLoading: false });
        return workflow;
      }

      console.log('No active workflow found for product');
      set({ isLoading: false });
      return null;
    } catch (error) {
      console.error('Error fetching active workflow for product:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch workflow for product',
        isLoading: false
      });
      return null;
    }
  },

  // Load workflow by ID and set all related state
  loadWorkflowById: async (workflowId: string) => {
    console.log('loadWorkflowById called with workflowId:', workflowId);
    set({ isLoading: true, error: null });

    try {
      // Fetch workflow with product data
      const { data: workflowData, error: workflowError } = await supabase
        .from('subscription_workflows')
        .select(`
          *,
          products (
            id,
            name,
            description,
            price,
            duration_days,
            type,
            features,
            is_active
          )
        `)
        .eq('id', workflowId)
        .single();

      if (workflowError) {
        console.error('Error fetching workflow by ID:', workflowError);
        throw workflowError;
      }

      if (workflowData) {
        // Transform workflow data
        const workflow: SubscriptionWorkflow = {
          id: workflowData.id,
          student_id: workflowData.student_id,
          product_type: workflowData.product_type,
          product_id: workflowData.product_id,
          current_step: workflowData.current_step,
          status: workflowData.status,
          step_completions: {
            step_1_completed: workflowData.step_1_completed || false,
            step_2_completed: workflowData.step_2_completed || false,
            step_3_completed: workflowData.step_3_completed || false,
            step_4_completed: workflowData.step_4_completed || false,
            step_1_completed_at: workflowData.step_1_completed_at,
            step_2_completed_at: workflowData.step_2_completed_at,
            step_3_completed_at: workflowData.step_3_completed_at,
            step_4_completed_at: workflowData.step_4_completed_at,
          },
          admin_assistance: {
            admin_assistance_requested: workflowData.admin_assistance_requested || false,
            admin_assistance_message: workflowData.admin_assistance_message,
            assigned_admin_id: workflowData.assigned_admin_id,
            admin_configured: workflowData.admin_configured || false,
            student_confirmation_required: workflowData.student_confirmation_required || false,
            student_confirmed: workflowData.student_confirmed || false,
          },
          curriculum_selections: {},
          pricing_data: {},
          created_at: workflowData.created_at,
          updated_at: workflowData.updated_at,
        };

        // Set the workflow in state
        set({ currentWorkflow: workflow });

        // If there's a product associated, set it as selected product
        if (workflowData.products) {
          const product: Product = {
            id: workflowData.products.id,
            name: workflowData.products.name,
            description: workflowData.products.description,
            price: workflowData.products.price,
            duration_days: workflowData.products.duration_days,
            type: workflowData.products.type,
            features: workflowData.products.features,
            is_active: workflowData.products.is_active,
            created_at: '', // Not needed for this use case
            updated_at: '', // Not needed for this use case
          };

          set({
            selectedProduct: product,
            selectedProductType: product.type as 'booster' | 'custom' | 'preparation'
          });

          console.log('Loaded workflow and set selected product:', product);
        }

        set({ isLoading: false });
        return workflow;
      }

      set({ isLoading: false });
      return null;
    } catch (error) {
      console.error('Error loading workflow by ID:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to load workflow',
        isLoading: false
      });
      return null;
    }
  },

  // Create new workflow - now requires product_id
  createWorkflow: async (studentId: string, productType: 'booster' | 'custom' | 'preparation', productId: string) => {
    set({ isLoading: true, error: null });

    try {
      // Check for existing active workflow for this product
      const existingWorkflow = await get().getActiveWorkflowForProduct(studentId, productId);
      if (existingWorkflow) {
        console.log('Found existing active workflow for product, returning existing workflow ID');
        set({ isLoading: false });
        return existingWorkflow.id;
      }

      // For booster products, skip to step 3 (pricing) since no curriculum configuration is needed
      // For custom/preparation products, go to step 2 (curriculum configuration)
      const currentStep = productType === 'booster' ? 3 : 2;

      const insertData = {
        student_id: studentId,
        product_type: productType,
        product_id: productId,
        current_step: currentStep,
        status: 'in_progress' as const,
        step_1_completed: true, // Mark step 1 as completed since we have the product
        step_1_completed_at: new Date().toISOString()
      };

      console.log('Creating workflow with data:', insertData);

      const { data, error } = await supabase
        .from('subscription_workflows')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Error inserting workflow:', error);
        throw error;
      }

      console.log('Workflow created successfully:', data);

      // Set the current workflow directly from the created data
      const workflow: SubscriptionWorkflow = {
        id: data.id,
        student_id: data.student_id,
        product_type: data.product_type,
        product_id: data.product_id,
        current_step: data.current_step,
        status: data.status,
        step_completions: {
          step_1_completed: data.step_1_completed || false,
          step_2_completed: data.step_2_completed || false,
          step_3_completed: data.step_3_completed || false,
          step_4_completed: data.step_4_completed || false,
          step_1_completed_at: data.step_1_completed_at,
          step_2_completed_at: data.step_2_completed_at,
          step_3_completed_at: data.step_3_completed_at,
          step_4_completed_at: data.step_4_completed_at,
        },
        admin_assistance: {
          admin_assistance_requested: data.admin_assistance_requested || false,
          admin_assistance_message: data.admin_assistance_message,
          assigned_admin_id: data.assigned_admin_id,
          admin_configured: data.admin_configured || false,
          student_confirmation_required: data.student_confirmation_required || false,
          student_confirmed: data.student_confirmed || false,
        },
        curriculum_selections: {},
        pricing_data: {},
        created_at: data.created_at,
        updated_at: data.updated_at,
      };

      console.log('Setting current workflow from created data:', workflow);
      set({ currentWorkflow: workflow, isLoading: false });
      return data.id;
    } catch (error) {
      console.error('Error creating workflow:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create workflow',
        isLoading: false
      });
      return null;
    }
  },

  // Update workflow step
  updateWorkflowStep: async (workflowId: string, step: number, data?: any) => {
    try {
      const updateData: any = {
        current_step: step,
        [`step_${step}_completed`]: true,
        [`step_${step}_completed_at`]: new Date().toISOString()
      };

      // Add step-specific data
      if (step === 1 && data?.product_id) {
        updateData.product_id = data.product_id;
        console.log('Setting product_id in updateData:', data.product_id);
      }

      console.log('Updating workflow with data:', updateData);

      const { error } = await supabase
        .from('subscription_workflows')
        .update(updateData)
        .eq('id', workflowId);

      if (error) {
        console.error('Supabase error updating workflow:', error);
        throw error;
      }

      console.log('Workflow step updated successfully');

      // Refresh the current workflow to reflect the changes
      const { currentWorkflow } = get();
      if (currentWorkflow) {
        console.log('Refreshing current workflow after update');
        await get().getActiveWorkflow(currentWorkflow.student_id);
      }

      return true;
    } catch (error) {
      console.error('Error updating workflow step:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to update workflow' });
      return false;
    }
  },

  // Step 1: Product Selection
  selectProductType: (productType) => {
    set({ selectedProductType: productType });
  },

  selectProduct: (product) => {
    set({ selectedProduct: product });
  },

  // Legacy function - no longer needed since workflows are created with product_id
  completeStep1: async (workflowId: string) => {
    console.warn('completeStep1 called - this function is deprecated. Workflows should be created with product_id.');
    return true;
  },

  // Step 2: Curriculum Configuration
  updateCurriculumSelections: (selections) => {
    // Calculate estimated sessions
    const estimatedSessions = selections.topics.length * 2 + selections.subtopics.length;

    set({
      curriculumSelections: {
        selectedTopics: selections.topics,
        selectedSubtopics: selections.subtopics,
        estimatedSessions: Math.max(1, estimatedSessions)
      }
    });
  },

  saveCurriculumSelections: async (workflowId: string, userId: string, userRole: 'student' | 'admin') => {
    const { curriculumSelections } = get();

    try {
      const { error } = await supabase
        .from('subscription_curriculum_selections')
        .upsert({
          workflow_id: workflowId,
          selection_type: curriculumSelections.selectedSubtopics.length > 0 ? 'selected_subtopics' : 'selected_topics',
          selected_topics: curriculumSelections.selectedTopics,
          selected_subtopics: curriculumSelections.selectedSubtopics,
          estimated_sessions: curriculumSelections.estimatedSessions,
          configured_by: userId,
          configured_by_role: userRole
        });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error saving curriculum selections:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to save curriculum selections' });
      return false;
    }
  },

  completeStep2: async (workflowId: string) => {
    return await get().updateWorkflowStep(workflowId, 2);
  },

  // Step 3: Pricing Calculation
  calculatePricing: async (workflowId: string) => {
    const { currentWorkflow, selectedProduct, curriculumSelections } = get();

    try {
      let basePrice = 0;
      let sessionsCount = 0;
      let calculationMethod: 'fixed_product_price' | 'dynamic_session_based' = 'fixed_product_price';

      if (currentWorkflow?.product_type === 'booster' && selectedProduct) {
        // Fixed price for booster
        basePrice = selectedProduct.price;
        sessionsCount = 10; // Default sessions for booster
        calculationMethod = 'fixed_product_price';
      } else {
        // Dynamic pricing for custom/preparation
        sessionsCount = curriculumSelections.estimatedSessions;
        const pricePerSession = 50; // This should come from configuration
        basePrice = sessionsCount * pricePerSession;
        calculationMethod = 'dynamic_session_based';
      }

      const { error } = await supabase
        .from('subscription_pricing')
        .upsert({
          workflow_id: workflowId,
          base_price: basePrice,
          sessions_count: sessionsCount,
          price_per_session: calculationMethod === 'dynamic_session_based' ? 50 : null,
          final_price: basePrice,
          calculation_method: calculationMethod,
          calculation_details: {
            estimated_sessions: sessionsCount,
            price_per_session: calculationMethod === 'dynamic_session_based' ? 50 : null
          }
        });

      if (error) throw error;

      set({
        pricingCalculation: {
          basePrice,
          finalPrice: basePrice,
          sessionsCount,
          discountApplied: 0
        }
      });

      return true;
    } catch (error) {
      console.error('Error calculating pricing:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to calculate pricing' });
      return false;
    }
  },

  applyAdminOverride: async (workflowId: string, overridePrice: number, reason: string, adminId: string) => {
    try {
      const { error } = await supabase
        .from('subscription_pricing')
        .update({
          admin_override_applied: true,
          admin_override_price: overridePrice,
          admin_override_reason: reason,
          admin_override_by: adminId,
          admin_override_at: new Date().toISOString(),
          final_price: overridePrice
        })
        .eq('workflow_id', workflowId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error applying admin override:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to apply admin override' });
      return false;
    }
  },

  completeStep3: async (workflowId: string) => {
    return await get().updateWorkflowStep(workflowId, 3);
  },

  // Step 4: Purchase
  completePurchase: async (workflowId: string, paymentData: any) => {
    try {
      // Update workflow to completed
      const { error: workflowError } = await supabase
        .from('subscription_workflows')
        .update({
          step_4_completed: true,
          step_4_completed_at: new Date().toISOString(),
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', workflowId);

      if (workflowError) throw workflowError;

      // Create subscription and invoice records here
      // This would integrate with the existing billing system

      return true;
    } catch (error) {
      console.error('Error completing purchase:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to complete purchase' });
      return false;
    }
  },

  // Admin assistance
  requestAdminAssistance: async (workflowId: string, message: string) => {
    try {
      const { error } = await supabase
        .from('subscription_workflows')
        .update({
          admin_assistance_requested: true,
          admin_assistance_message: message,
          status: 'admin_assistance_requested'
        })
        .eq('id', workflowId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error requesting admin assistance:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to request admin assistance' });
      return false;
    }
  },

  assignAdminToWorkflow: async (workflowId: string, adminId: string) => {
    try {
      const { error } = await supabase
        .from('subscription_workflows')
        .update({
          assigned_admin_id: adminId
        })
        .eq('id', workflowId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error assigning admin:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to assign admin' });
      return false;
    }
  },

  adminConfigureCurriculum: async (workflowId: string, adminId: string, selections: any) => {
    try {
      // Save curriculum selections
      const saved = await get().saveCurriculumSelections(workflowId, adminId, 'admin');
      if (!saved) return false;

      // Update workflow to indicate admin configuration
      const { error } = await supabase
        .from('subscription_workflows')
        .update({
          admin_configured: true,
          student_confirmation_required: true
        })
        .eq('id', workflowId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error admin configuring curriculum:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to configure curriculum' });
      return false;
    }
  },

  studentConfirmConfiguration: async (workflowId: string) => {
    try {
      const { error } = await supabase
        .from('subscription_workflows')
        .update({
          student_confirmed: true,
          student_confirmation_required: false
        })
        .eq('id', workflowId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error confirming configuration:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to confirm configuration' });
      return false;
    }
  },

  // Utility functions
  resetWorkflow: () => {
    console.log('resetWorkflow called - clearing workflow state');
    set({
      currentWorkflow: null,
      selectedProductType: null,
      selectedProduct: null,
      curriculumSelections: {
        selectedTopics: [],
        selectedSubtopics: [],
        estimatedSessions: 0
      },
      pricingCalculation: {
        basePrice: 0,
        finalPrice: 0,
        sessionsCount: 0,
        discountApplied: 0
      },
      error: null,
      isLoading: false
    });
  },

  cancelWorkflow: async (workflowId: string) => {
    try {
      const { error } = await supabase
        .from('subscription_workflows')
        .update({
          status: 'cancelled'
        })
        .eq('id', workflowId);

      if (error) throw error;

      // Clear local state
      get().resetWorkflow();

      return true;
    } catch (error) {
      console.error('Error cancelling workflow:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to cancel workflow' });
      return false;
    }
  },

  goToStep: (step: number) => {
    if (get().currentWorkflow) {
      set(state => ({
        currentWorkflow: state.currentWorkflow ? {
          ...state.currentWorkflow,
          current_step: step
        } : null
      }));
    }
  },

  // Clean up legacy workflows with NULL product_id
  cleanupLegacyWorkflows: async (studentId: string) => {
    try {
      console.log('Cleaning up legacy workflows with NULL product_id for student:', studentId);

      const { error } = await supabase
        .from('subscription_workflows')
        .update({ status: 'cancelled' })
        .eq('student_id', studentId)
        .is('product_id', null)
        .in('status', ['in_progress', 'admin_assistance_requested']);

      if (error) {
        console.error('Error cleaning up legacy workflows:', error);
        return false;
      }

      console.log('Legacy workflows cleaned up successfully');
      return true;
    } catch (error) {
      console.error('Error cleaning up legacy workflows:', error);
      return false;
    }
  }
}));

import React, { useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";
import { Label } from "@/components/ui/Label";
import { CheckCircle, Package, ArrowRight, Star, Clock, Users } from "lucide-react";
import { useSubscriptionWorkflowStore, Product } from "@/store/subscriptionWorkflowStore";
import { useToast } from "@/hooks/useToast";

const ProductSelectionStep: React.FC = () => {
  const { toast } = useToast();
  const {
    currentWorkflow,
    availableProducts,
    selectedProduct,
    isLoadingProducts,
    error,
    fetchAvailableProducts,
    selectProduct,
    completeStep1,
    goToStep
  } = useSubscriptionWorkflowStore();

  // Fetch products on component mount
  useEffect(() => {
    fetchAvailableProducts();
  }, [fetchAvailableProducts]);

  // Filter products by workflow type
  const getFilteredProducts = () => {
    if (!currentWorkflow) return [];
    return availableProducts.filter(product => product.type === currentWorkflow.product_type);
  };

  // Handle product selection
  const handleProductSelect = (product: Product) => {
    selectProduct(product);
  };

  // Handle step completion
  const handleContinue = async () => {
    if (!currentWorkflow || !selectedProduct) {
      toast({
        title: "Selection Required",
        description: "Please select a product to continue",
        variant: "destructive"
      });
      return;
    }

    const success = await completeStep1(currentWorkflow.id);

    if (success) {
      toast({
        title: "Product Selected",
        description: `${selectedProduct.name} has been selected`,
        variant: "default"
      });

      // Move to next step
      if (currentWorkflow.product_type === 'booster') {
        // Skip curriculum configuration for booster, go to pricing
        goToStep(3);
      } else {
        // Go to curriculum configuration for custom/preparation
        goToStep(2);
      }
    } else {
      toast({
        title: "Error",
        description: "Failed to save product selection",
        variant: "destructive"
      });
    }
  };

  // Get product features for display
  const getProductFeatures = (product: Product) => {
    const features = product.features || {};
    const defaultFeatures = {
      sessions: product.type === 'booster' ? '10 sessions' : 'Variable sessions',
      duration: `${product.duration_days} days`,
      support: 'Email support',
      materials: 'Digital materials included'
    };

    return { ...defaultFeatures, ...features };
  };

  // Get product benefits based on type
  const getProductBenefits = (productType: string) => {
    switch (productType) {
      case 'booster':
        return [
          'Complete subject coverage',
          'All topics and subtopics included',
          'Structured learning path',
          'Fixed pricing',
          'Immediate start'
        ];
      case 'custom':
        return [
          'Choose specific topics',
          'Select individual subtopics',
          'Flexible learning path',
          'Pay for what you need',
          'Personalized curriculum'
        ];
      case 'preparation':
        return [
          'Exam-focused content',
          'Targeted topic selection',
          'Practice materials',
          'Performance tracking',
          'Flexible scheduling'
        ];
      default:
        return [];
    }
  };

  if (isLoadingProducts) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p>Loading available products...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-red-600">
            <p>Error loading products: {error}</p>
            <Button
              variant="outline"
              onClick={fetchAvailableProducts}
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const filteredProducts = getFilteredProducts();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Select Your {currentWorkflow?.product_type?.charAt(0).toUpperCase()}{currentWorkflow?.product_type?.slice(1)} Package
          </CardTitle>
          <CardDescription>
            Choose the package that best fits your learning needs
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Package Type Benefits */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">
              {currentWorkflow?.product_type?.charAt(0).toUpperCase()}{currentWorkflow?.product_type?.slice(1)} Package Benefits:
            </h3>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {getProductBenefits(currentWorkflow?.product_type || '').map((benefit, index) => (
                <li key={index} className="flex items-center text-blue-800">
                  <CheckCircle className="h-4 w-4 mr-2 text-blue-600" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>

          {/* Product Selection */}
          {filteredProducts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No products available for this package type.</p>
            </div>
          ) : (
            <RadioGroup
              value={selectedProduct?.id || ''}
              onValueChange={(value) => {
                const product = filteredProducts.find(p => p.id === value);
                if (product) handleProductSelect(product);
              }}
            >
              <div className="grid grid-cols-1 gap-4">
                {filteredProducts.map((product) => {
                  const features = getProductFeatures(product);
                  const isSelected = selectedProduct?.id === product.id;

                  return (
                    <div key={product.id} className="relative">
                      <Label
                        htmlFor={product.id}
                        className={`
                          block cursor-pointer rounded-lg border-2 p-6 transition-all
                          ${isSelected
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                          }
                        `}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <RadioGroupItem
                                value={product.id}
                                id={product.id}
                                className="mr-3"
                              />
                              <h3 className="text-lg font-semibold">{product.name}</h3>
                              {product.type === 'booster' && (
                                <Badge variant="default" className="ml-2">
                                  <Star className="h-3 w-3 mr-1" />
                                  Popular
                                </Badge>
                              )}
                            </div>

                            {product.description && (
                              <p className="text-gray-600 mb-4">{product.description}</p>
                            )}

                            {/* Features Grid */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                              <div className="flex items-center text-sm">
                                <Clock className="h-4 w-4 mr-2 text-gray-500" />
                                {features.duration}
                              </div>
                              <div className="flex items-center text-sm">
                                <Users className="h-4 w-4 mr-2 text-gray-500" />
                                {features.sessions}
                              </div>
                              <div className="flex items-center text-sm">
                                <Package className="h-4 w-4 mr-2 text-gray-500" />
                                {features.support}
                              </div>
                              <div className="flex items-center text-sm">
                                <CheckCircle className="h-4 w-4 mr-2 text-gray-500" />
                                {features.materials}
                              </div>
                            </div>
                          </div>

                          <div className="text-right ml-4">
                            <div className="text-2xl font-bold text-blue-600">
                              ${product.price}
                            </div>
                            {currentWorkflow?.product_type !== 'booster' && (
                              <p className="text-sm text-gray-500">Base price*</p>
                            )}
                          </div>
                        </div>
                      </Label>
                    </div>
                  );
                })}
              </div>
            </RadioGroup>
          )}

          {/* Pricing Note for Custom/Preparation */}
          {currentWorkflow?.product_type !== 'booster' && (
            <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> For {currentWorkflow?.product_type} packages,
                the final price will be calculated based on your curriculum selections in the next step.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={() => window.history.back()}>
          <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
          Back
        </Button>

        <Button
          onClick={handleContinue}
          disabled={!selectedProduct}
          className="min-w-[120px]"
        >
          Continue
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default ProductSelectionStep;

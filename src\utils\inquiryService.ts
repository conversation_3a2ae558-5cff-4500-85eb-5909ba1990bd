import { supabase } from "../lib/supabaseClient";
import { toast } from "@/components/ui/UseToast";

export interface Inquiry {
  id: string;
  created_at: string;
  name: string;
  email: string;
  phone: string | null;
  inquiry_type: string;
  message: string;
  status: string;
}

export const deleteInquiry = async (
  id: string,
  inquiries: Inquiry[],
  setInquiries: React.Dispatch<React.SetStateAction<Inquiry[]>>,
  setNewCount?: React.Dispatch<React.SetStateAction<number>>
) => {
  try {
    const { error } = await supabase.from("inquiries").delete().eq("id", id);

    if (error) throw error;

    // Update local state
    setInquiries(inquiries.filter((inquiry) => inquiry.id !== id));

    // Update new count if needed
    if (setNewCount) {
      const wasNew = inquiries.find((i) => i.id === id)?.status === "new";
      if (wasNew) {
        setNewCount((prev) => prev - 1);
      }
    }

    toast({
      title: "Inquiry Deleted",
      description: "The inquiry has been permanently removed",
    });

    return true;
  } catch (error: any) {
    console.error("Error deleting inquiry:", error);
    toast({
      title: "Error",
      description: "Failed to delete inquiry",
      variant: "destructive",
    });
    return false;
  }
};

import React, { useState, useRef, useCallback, useEffect } from "react";
import ReactCrop, { Crop, PixelCrop, centerCrop, makeAspectCrop } from "react-image-crop";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/Dialog";
import { Button } from "@/components/ui/Button";
import { RotateCw, ArrowLeft, RotateCcw } from "lucide-react";
import { cn } from "@/lib/utils";
import "react-image-crop/dist/ReactCrop.css";
import "./PhotoCropModal.css";

interface PhotoCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageFile: File | null;
  onCroppedFile: (file: File) => void;
  aspectRatio?: number;
}

const PhotoCropModal: React.FC<PhotoCropModalProps> = ({
  isOpen,
  onClose,
  imageFile,
  onCroppedFile,
  aspectRatio = 1, // Default to square crop
}) => {
  const [imageSrc, setImageSrc] = useState<string>("");
  const [rotatedImageSrc, setRotatedImageSrc] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [rotation, setRotation] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Load image when file changes
  useEffect(() => {
    if (imageFile) {
      const reader = new FileReader();
      reader.onload = () => {
        const src = reader.result as string;
        setImageSrc(src);
        setRotatedImageSrc(src);
        setRotation(0);
      };
      reader.readAsDataURL(imageFile);
    }
  }, [imageFile]);

  // Create rotated image when rotation changes
  useEffect(() => {
    if (imageSrc && rotation !== 0) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new canvas dimensions for any rotation angle
        const radians = (rotation * Math.PI) / 180;
        const sin = Math.abs(Math.sin(radians));
        const cos = Math.abs(Math.cos(radians));

        const newWidth = img.width * cos + img.height * sin;
        const newHeight = img.width * sin + img.height * cos;

        canvas.width = newWidth;
        canvas.height = newHeight;

        if (ctx) {
          // Clear canvas
          ctx.clearRect(0, 0, newWidth, newHeight);

          // Move to center and rotate
          ctx.translate(newWidth / 2, newHeight / 2);
          ctx.rotate(radians);

          // Draw image centered
          ctx.drawImage(img, -img.width / 2, -img.height / 2);

          // Convert to data URL
          setRotatedImageSrc(canvas.toDataURL('image/jpeg', 0.9));
        }
      };

      img.src = imageSrc;
    } else {
      setRotatedImageSrc(imageSrc);
    }
  }, [imageSrc, rotation]);

  // Initialize crop when image loads
  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: "%",
          width: 80,
        },
        aspectRatio,
        width,
        height
      ),
      width,
      height
    );
    setCrop(crop);
  }, [aspectRatio]);

  // Rotate image by 90 degrees clockwise
  const handleRotateClockwise = () => {
    setRotation((prev) => (prev + 90) % 360);
    // Reset crop when rotating to avoid invalid crop coordinates
    setCrop(undefined);
    setCompletedCrop(undefined);
  };

  // Rotate image by 90 degrees counter-clockwise
  const handleRotateCounterClockwise = () => {
    setRotation((prev) => (prev - 90 + 360) % 360);
    // Reset crop when rotating to avoid invalid crop coordinates
    setCrop(undefined);
    setCompletedCrop(undefined);
  };

  // Handle rotation slider change
  const handleRotationChange = (value: number) => {
    setRotation(value);
    // Reset crop when rotating to avoid invalid crop coordinates
    setCrop(undefined);
    setCompletedCrop(undefined);
  };

  // Reset rotation to 0 degrees
  const handleResetRotation = () => {
    setRotation(0);
    setCrop(undefined);
    setCompletedCrop(undefined);
  };

  // Generate cropped image
  const getCroppedImg = useCallback(async (): Promise<File | null> => {
    const image = imgRef.current;
    const canvas = canvasRef.current;
    const crop = completedCrop;

    if (!image || !canvas || !crop) {
      return null;
    }

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    // Set canvas size to the crop size
    canvas.width = crop.width * scaleX;
    canvas.height = crop.height * scaleY;

    const ctx = canvas.getContext("2d");
    if (!ctx) {
      return null;
    }

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw the cropped image (rotation is already applied to the source image)
    ctx.drawImage(
      image,
      crop.x * scaleX,
      crop.y * scaleY,
      crop.width * scaleX,
      crop.height * scaleY,
      0,
      0,
      canvas.width,
      canvas.height
    );

    // Convert canvas to blob
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          resolve(null);
          return;
        }

        const fileName = imageFile?.name || "cropped-image.jpg";
        const fileExtension = fileName.split(".").pop() || "jpg";
        const newFileName = fileName.replace(/\.[^/.]+$/, "") + "-cropped." + fileExtension;

        resolve(new File([blob], newFileName, {
          type: "image/jpeg",
        }));
      }, "image/jpeg", 0.9);
    });
  }, [completedCrop, imageFile]);

  // Handle next button click
  const handleNext = async () => {
    if (!completedCrop) return;

    setIsProcessing(true);
    setError(null);
    try {
      const croppedFile = await getCroppedImg();
      if (croppedFile) {
        console.log('PhotoCropModal: Cropping complete, sending file to parent:', croppedFile.name);
        onCroppedFile(croppedFile);
      } else {
        setError("Failed to process the image. Please try again.");
      }
    } catch (error) {
      console.error("Error cropping image:", error);
      setError("An error occurred while processing the image. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-white text-gray-900 crop-modal-content flex flex-col">
        <DialogHeader className="border-b border-gray-200 pb-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-700 hover:bg-gray-100"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <DialogTitle className="text-gray-900 text-lg font-medium">
              Crop & rotate
            </DialogTitle>
            <div className="w-10" /> {/* Spacer for centering */}
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="flex flex-col items-center p-6 space-y-6 min-h-full">
            {rotatedImageSrc && (
              <div className="relative w-full flex justify-center">
                <div className="relative max-w-full max-h-[50vh] overflow-hidden">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={aspectRatio}
                    className="max-w-full max-h-full"
                  >
                    <img
                      ref={imgRef}
                      alt="Crop preview"
                      src={rotatedImageSrc}
                      style={{
                        maxWidth: "100%",
                        maxHeight: "50vh",
                      }}
                      onLoad={onImageLoad}
                      className="block"
                    />
                  </ReactCrop>
                </div>
              </div>
            )}

            <div className="flex flex-col items-center space-y-6 w-full">
              {/* Rotation Controls */}
              <div className="flex flex-col items-center space-y-3 bg-gray-50 p-4 rounded-lg w-full max-w-md">
                <div className="flex items-center space-x-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRotateCounterClockwise}
                    className="text-gray-700 hover:bg-gray-200 p-2"
                    title="Rotate 90° counter-clockwise"
                  >
                    <RotateCcw className="h-5 w-5" />
                  </Button>

                  <div className="flex flex-col items-center space-y-2">
                    <span className="text-sm font-medium text-gray-700">Rotation</span>
                    <div className="flex items-center space-x-3">
                      <span className="text-xs text-gray-500 w-8 text-right">0°</span>
                      <input
                        type="range"
                        min="0"
                        max="360"
                        value={rotation}
                        onChange={(e) => handleRotationChange(parseInt(e.target.value))}
                        className="w-32 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                        style={{
                          background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(rotation / 360) * 100}%, #e5e7eb ${(rotation / 360) * 100}%, #e5e7eb 100%)`
                        }}
                      />
                      <span className="text-xs text-gray-500 w-8">360°</span>
                    </div>
                    <span className="text-sm font-mono text-blue-600">{rotation}°</span>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRotateClockwise}
                    className="text-gray-700 hover:bg-gray-200 p-2"
                    title="Rotate 90° clockwise"
                  >
                    <RotateCw className="h-5 w-5" />
                  </Button>
                </div>

                {rotation !== 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleResetRotation}
                    className="text-gray-600 hover:bg-gray-200 text-xs"
                  >
                    Reset Rotation
                  </Button>
                )}
              </div>

              <Button
                onClick={handleNext}
                disabled={!completedCrop || isProcessing}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2 rounded-full font-medium shadow-lg"
              >
                {isProcessing ? "Processing..." : "Next"}
              </Button>

              {error && (
                <div className="text-red-600 text-sm text-center max-w-xs">
                  {error}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Hidden canvas for image processing */}
        <canvas
          ref={canvasRef}
          style={{ display: "none" }}
        />
      </DialogContent>
    </Dialog>
  );
};

export default PhotoCropModal;

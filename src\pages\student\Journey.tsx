import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Progress } from "@/components/ui/Progress";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/Accordion";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Textarea } from "@/components/ui/TextArea";
import {
  BookOpen,
  FileText,
  GraduationCap,
  Star,
  Video,
  Clock,
  Edit,
  Save,
  Plus,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Calendar
} from "lucide-react";
import { create } from "zustand";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/Tabs";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/Tooltip";

// Define types for our data structure
interface Session {
  id: string;
  date: string;
  time: string;
  duration: number;
  tutorName: string;
  tutorPhoto?: string;
  status: "completed" | "scheduled" | "cancelled";
  feedback?: {
    rating: number;
    comment: string;
    tutorComment?: string;
  };
  notes?: string;
}

interface Subtopic {
  id: string;
  name: string;
  description: string;
  status: "not_started" | "in_progress" | "completed";
  progress: number;
  sessions: Session[];
}

interface Topic {
  id: string;
  name: string;
  description: string;
  status: "not_started" | "in_progress" | "completed";
  progress: number;
  subtopics: Subtopic[];
}

// Create a Zustand store for the Learning Journey
interface LearningJourneyState {
  topics: Topic[];
  selectedTopic: string | null;
  selectedSubtopic: string | null;
  selectedSession: string | null;
  isLoading: boolean;
  error: string | null;
  notes: Record<string, string>; // Key is subtopicId or sessionId, value is note content

  // Actions
  setSelectedTopic: (topicId: string | null) => void;
  setSelectedSubtopic: (subtopicId: string | null) => void;
  setSelectedSession: (sessionId: string | null) => void;
  updateNote: (id: string, content: string) => void;
  fetchJourneyData: () => Promise<void>;
}

// Sample data for development
const sampleTopics: Topic[] = [
  {
    id: "topic1",
    name: "Neural Networks",
    description: "Fundamentals of neural networks and deep learning",
    status: "in_progress",
    progress: 65,
    subtopics: [
      {
        id: "subtopic1",
        name: "Convolutional Neural Networks",
        description: "Understanding CNN architecture and applications",
        status: "completed",
        progress: 100,
        sessions: [
          {
            id: "session1",
            date: "2023-11-10",
            time: "10:00 AM",
            duration: 60,
            tutorName: "Dr. Sarah Johnson",
            tutorPhoto: "https://randomuser.me/api/portraits/women/1.jpg",
            status: "completed",
            feedback: {
              rating: 5,
              comment: "Excellent session! I now understand how CNN layers work.",
              tutorComment: "Great progress on understanding convolutional operations."
            },
            notes: "Covered filter operations, pooling layers, and feature maps. Need to review activation functions."
          },
          {
            id: "session2",
            date: "2023-11-17",
            time: "10:00 AM",
            duration: 60,
            tutorName: "Dr. Sarah Johnson",
            tutorPhoto: "https://randomuser.me/api/portraits/women/1.jpg",
            status: "completed",
            feedback: {
              rating: 4,
              comment: "Good session on CNN applications.",
              tutorComment: "Continue practicing with different CNN architectures."
            }
          }
        ]
      },
      {
        id: "subtopic2",
        name: "Recurrent Neural Networks",
        description: "Understanding RNN architecture for sequential data",
        status: "in_progress",
        progress: 50,
        sessions: [
          {
            id: "session3",
            date: "2023-11-24",
            time: "10:00 AM",
            duration: 60,
            tutorName: "Dr. Sarah Johnson",
            tutorPhoto: "https://randomuser.me/api/portraits/women/1.jpg",
            status: "completed",
            feedback: {
              rating: 5,
              comment: "Great introduction to RNNs!",
              tutorComment: "You're making excellent progress with sequential models."
            }
          },
          {
            id: "session4",
            date: "2023-12-01",
            time: "10:00 AM",
            duration: 60,
            tutorName: "Dr. Sarah Johnson",
            tutorPhoto: "https://randomuser.me/api/portraits/women/1.jpg",
            status: "scheduled"
          }
        ]
      },
      {
        id: "subtopic3",
        name: "Transformers",
        description: "Advanced attention-based models",
        status: "not_started",
        progress: 0,
        sessions: []
      }
    ]
  },
  {
    id: "topic2",
    name: "Reinforcement Learning",
    description: "Learning through interaction with an environment",
    status: "not_started",
    progress: 0,
    subtopics: [
      {
        id: "subtopic4",
        name: "Q-Learning",
        description: "Value-based reinforcement learning algorithm",
        status: "not_started",
        progress: 0,
        sessions: []
      },
      {
        id: "subtopic5",
        name: "Policy Gradients",
        description: "Policy-based reinforcement learning methods",
        status: "not_started",
        progress: 0,
        sessions: []
      }
    ]
  }
];

// Create the store
export const useLearningJourneyStore = create<LearningJourneyState>((set) => ({
  topics: [],
  selectedTopic: null,
  selectedSubtopic: null,
  selectedSession: null,
  isLoading: false,
  error: null,
  notes: {},

  setSelectedTopic: (topicId) => set({ selectedTopic: topicId, selectedSubtopic: null, selectedSession: null }),
  setSelectedSubtopic: (subtopicId) => set({ selectedSubtopic: subtopicId, selectedSession: null }),
  setSelectedSession: (sessionId) => set({ selectedSession: sessionId }),
  updateNote: (id, content) => set((state) => ({
    notes: { ...state.notes, [id]: content }
  })),

  fetchJourneyData: async () => {
    set({ isLoading: true, error: null });

    try {
      // In a real app, you would fetch data from an API here
      // For now, we'll use the sample data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay

      set({
        topics: sampleTopics,
        isLoading: false
      });
    } catch (error) {
      set({
        error: "Failed to load learning journey data. Please try again.",
        isLoading: false
      });
    }
  }
}));

const Journey: React.FC = () => {
  return (
    <StudentPageLayout
      title="Learning Journey"
      description="Track your progress and navigate your educational path"
    >
      <ProgressMap />
    </StudentPageLayout>
  );
};

// Progress Map component
const ProgressMap: React.FC = () => {
  const navigate = useNavigate();
  const {
    topics,
    selectedTopic,
    selectedSubtopic,
    selectedSession,
    notes,
    setSelectedTopic,
    setSelectedSubtopic,
    setSelectedSession,
    updateNote,
    fetchJourneyData,
    isLoading
  } = useLearningJourneyStore();

  const [editingNote, setEditingNote] = useState<string | null>(null);
  const [noteContent, setNoteContent] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("overview");

  // Navigate to Materials page with the selected topic as a filter
  const navigateToMaterials = (topicName: string) => {
    navigate(`/student/materials?topic=${encodeURIComponent(topicName)}`);
  };

  useEffect(() => {
    fetchJourneyData();
  }, [fetchJourneyData]);

  // Find the currently selected topic and subtopic
  const currentTopic = topics.find(t => t.id === selectedTopic);
  const currentSubtopic = currentTopic?.subtopics.find(s => s.id === selectedSubtopic);
  const currentSession = currentSubtopic?.sessions.find(s => s.id === selectedSession);

  // Start editing a note
  const handleEditNote = (id: string, initialContent: string = "") => {
    setEditingNote(id);
    setNoteContent(notes[id] || initialContent);
  };

  // Save the note
  const handleSaveNote = (id: string) => {
    updateNote(id, noteContent);
    setEditingNote(null);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-800 border-green-300";
      case "in_progress": return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "not_started": return "bg-gray-100 text-gray-800 border-gray-300";
      case "scheduled": return "bg-blue-100 text-blue-800 border-blue-300";
      case "cancelled": return "bg-red-100 text-red-800 border-red-300";
      default: return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  // Get progress color
  const getProgressColor = (progress: number) => {
    if (progress >= 80) return "bg-green-500";
    if (progress >= 40) return "bg-yellow-500";
    return "bg-red-500";
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rfpurple-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your learning journey...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Left column - Topic and Subtopic Tree */}
      <div className="lg:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle>Progress Map</CardTitle>
            <CardDescription>Your journey through topics and subtopics</CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="w-full">
              {topics.map((topic) => (
                <AccordionItem key={topic.id} value={topic.id}>
                  <AccordionTrigger
                    onClick={() => setSelectedTopic(topic.id)}
                    className={`${selectedTopic === topic.id ? 'text-rfpurple-600' : ''}`}
                  >
                    <div className="flex items-center">
                      <BookOpen className="h-4 w-4 mr-2" />
                      <span>{topic.name}</span>
                      <Badge className={`ml-2 ${getStatusColor(topic.status)}`}>
                        {topic.progress}%
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="pl-6 space-y-2">
                      {topic.subtopics.map((subtopic) => (
                        <div
                          key={subtopic.id}
                          className={`
                            p-2 rounded-md cursor-pointer transition-colors
                            ${selectedSubtopic === subtopic.id ? 'bg-rfpurple-50 text-rfpurple-600' : 'hover:bg-gray-50'}
                          `}
                          onClick={() => setSelectedSubtopic(subtopic.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <FileText className="h-4 w-4 mr-2" />
                              <span>{subtopic.name}</span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-xs mr-2">{subtopic.sessions.length} sessions</span>
                              <Badge className={`${getStatusColor(subtopic.status)}`}>
                                {subtopic.progress}%
                              </Badge>
                            </div>
                          </div>
                          <div className="mt-1">
                            <Progress
                              value={subtopic.progress}
                              className="h-1"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>
      </div>

      {/* Right column - Detail View */}
      <div className="lg:col-span-2">
        {selectedTopic && currentTopic ? (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{currentTopic.name}</CardTitle>
                  <CardDescription>{currentTopic.description}</CardDescription>
                </div>
                <Badge className={`${getStatusColor(currentTopic.status)}`}>
                  {currentTopic.status.replace('_', ' ')}
                </Badge>
              </div>
              <div className="mt-2">
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Overall Progress</span>
                  <span className="text-sm font-medium">{currentTopic.progress}%</span>
                </div>
                <Progress
                  value={currentTopic.progress}
                  className="h-2"
                />
              </div>
              <div className="mt-4 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => navigateToMaterials(currentTopic.name)}
                >
                  <FileText className="h-4 w-4" />
                  View Topic Materials
                </Button>
              </div>
            </CardHeader>

            <CardContent>
              {selectedSubtopic && currentSubtopic ? (
                <div>
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold flex items-center">
                      {currentSubtopic.name}
                      <Badge className={`ml-2 ${getStatusColor(currentSubtopic.status)}`}>
                        {currentSubtopic.status.replace('_', ' ')}
                      </Badge>
                    </h3>
                    <p className="text-gray-600 mt-1">{currentSubtopic.description}</p>

                    <div className="mt-3">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm font-medium">Subtopic Progress</span>
                        <span className="text-sm font-medium">{currentSubtopic.progress}%</span>
                      </div>
                      <Progress
                        value={currentSubtopic.progress}
                        className="h-2"
                      />
                    </div>
                  </div>

                  <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
                    <TabsList className="grid grid-cols-3 mb-4">
                      <TabsTrigger value="overview">Overview</TabsTrigger>
                      <TabsTrigger value="sessions">Sessions ({currentSubtopic.sessions.length})</TabsTrigger>
                      <TabsTrigger value="notes">Notes & Feedback</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Sessions</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">{currentSubtopic.sessions.length}</div>
                            <div className="text-xs text-gray-500 mt-1">
                              {currentSubtopic.sessions.filter(s => s.status === 'completed').length} completed
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Progress</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">{currentSubtopic.progress}%</div>
                            <div className="text-xs text-gray-500 mt-1">
                              {currentSubtopic.status.replace('_', ' ')}
                            </div>
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm">Next Session</CardTitle>
                          </CardHeader>
                          <CardContent>
                            {currentSubtopic.sessions.find(s => s.status === 'scheduled') ? (
                              <>
                                <div className="text-md font-medium">
                                  {currentSubtopic.sessions.find(s => s.status === 'scheduled')?.date}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {currentSubtopic.sessions.find(s => s.status === 'scheduled')?.time}
                                </div>
                              </>
                            ) : (
                              <div className="text-sm text-gray-500">No upcoming sessions</div>
                            )}
                          </CardContent>
                        </Card>
                      </div>

                      {currentSubtopic.sessions.length > 0 ? (
                        <div className="mt-6">
                          <h4 className="font-medium mb-3">Recent Sessions</h4>
                          <div className="space-y-3">
                            {currentSubtopic.sessions.slice(0, 2).map((session) => (
                              <div
                                key={session.id}
                                className="border rounded-md p-3 hover:border-gray-300 cursor-pointer"
                                onClick={() => {
                                  setSelectedSession(session.id);
                                  setActiveTab("sessions");
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center">
                                    {session.status === 'completed' ? (
                                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                    ) : session.status === 'scheduled' ? (
                                      <Calendar className="h-4 w-4 mr-2 text-blue-500" />
                                    ) : (
                                      <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
                                    )}
                                    <div>
                                      <div className="font-medium text-sm">{new Date(session.date).toLocaleDateString()} at {session.time}</div>
                                      <div className="text-xs text-gray-500">
                                        {session.duration} min with {session.tutorName}
                                      </div>
                                    </div>
                                  </div>
                                  <Badge className={getStatusColor(session.status)}>
                                    {session.status}
                                  </Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                          {currentSubtopic.sessions.length > 2 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="mt-2 text-rfpurple-600"
                              onClick={() => setActiveTab("sessions")}
                            >
                              View all sessions
                            </Button>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-8 border rounded-md mt-4">
                          <GraduationCap className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                          <p className="text-gray-500">No sessions scheduled for this subtopic yet.</p>
                          <Button
                            size="sm"
                            className="mt-2"
                            onClick={() => navigate(`/student/request-booking?topic=${encodeURIComponent(currentTopic.name)}&subtopic=${encodeURIComponent(currentSubtopic.name)}`)}
                          >
                            Request a Session
                          </Button>
                        </div>
                      )}

                      {/* Notes preview */}
                      <div className="mt-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium">Your Notes</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-rfpurple-600"
                            onClick={() => setActiveTab("notes")}
                          >
                            View all notes
                          </Button>
                        </div>
                        <div className="p-3 bg-gray-50 rounded border min-h-[80px]">
                          {notes[currentSubtopic.id] ? (
                            <p className="text-sm">
                              {notes[currentSubtopic.id].length > 150
                                ? notes[currentSubtopic.id].substring(0, 150) + "..."
                                : notes[currentSubtopic.id]}
                            </p>
                          ) : (
                            <span className="text-gray-400 italic text-sm">No notes yet. Click Edit to add notes.</span>
                          )}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="sessions">
                      <div className="mb-4 flex justify-between items-center">
                        <h4 className="font-medium">All Sessions</h4>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-1" /> Request Session
                        </Button>
                      </div>

                      {currentSubtopic.sessions.length === 0 ? (
                        <div className="text-center py-8 border rounded-md">
                          <GraduationCap className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                          <p className="text-gray-500">No sessions scheduled for this subtopic yet.</p>
                          <Button
                            size="sm"
                            className="mt-2"
                            onClick={() => navigate(`/student/request-booking?topic=${encodeURIComponent(currentTopic.name)}&subtopic=${encodeURIComponent(currentSubtopic.name)}`)}
                          >
                            Request a Session
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {currentSubtopic.sessions.map((session) => (
                            <div
                              key={session.id}
                              className={`
                                border rounded-md p-4 transition-colors cursor-pointer
                                ${selectedSession === session.id ? 'border-rfpurple-300 bg-rfpurple-50' : 'hover:border-gray-300'}
                              `}
                              onClick={() => setSelectedSession(session.id)}
                            >
                              <div className="flex justify-between items-start">
                                <div className="flex items-center">
                                  {session.status === 'completed' ? (
                                    <Video className="h-5 w-5 mr-2 text-green-500" />
                                  ) : session.status === 'scheduled' ? (
                                    <Clock className="h-5 w-5 mr-2 text-blue-500" />
                                  ) : (
                                    <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
                                  )}
                                  <div>
                                    <div className="font-medium">{new Date(session.date).toLocaleDateString()} at {session.time}</div>
                                    <div className="text-sm text-gray-500">
                                      {session.duration} min with {session.tutorName}
                                    </div>
                                  </div>
                                </div>
                                <Badge className={getStatusColor(session.status)}>
                                  {session.status}
                                </Badge>
                              </div>

                              {selectedSession === session.id && (
                                <div className="mt-4 pt-4 border-t">
                                  {session.feedback && (
                                    <div className="mb-4">
                                      <div className="flex items-center mb-2">
                                        <h5 className="font-medium text-sm">Feedback</h5>
                                        <div className="ml-2 flex items-center">
                                          {[...Array(5)].map((_, i) => (
                                            <Star
                                              key={i}
                                              className={`h-3 w-3 ${i < session.feedback!.rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                                            />
                                          ))}
                                        </div>
                                      </div>

                                      <div className="bg-gray-50 p-3 rounded-md mb-2">
                                        <p className="text-sm italic">"{session.feedback.comment}"</p>
                                      </div>

                                      {session.feedback.tutorComment && (
                                        <div className="bg-blue-50 p-3 rounded-md">
                                          <p className="text-sm text-blue-800">
                                            <span className="font-medium">Tutor:</span> {session.feedback.tutorComment}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  <div className="mt-3">
                                    <div className="flex justify-between items-center mb-2">
                                      <h5 className="font-medium text-sm">Session Notes</h5>
                                      {editingNote === session.id ? (
                                        <Button size="sm" onClick={() => handleSaveNote(session.id)}>
                                          <Save className="h-3 w-3 mr-1" /> Save
                                        </Button>
                                      ) : (
                                        <Button size="sm" variant="outline" onClick={() => handleEditNote(session.id, session.notes || "")}>
                                          <Edit className="h-3 w-3 mr-1" /> Edit
                                        </Button>
                                      )}
                                    </div>

                                    {editingNote === session.id ? (
                                      <Textarea
                                        value={noteContent}
                                        onChange={(e) => setNoteContent(e.target.value)}
                                        placeholder="Add your notes about this session here..."
                                        className="min-h-[80px]"
                                      />
                                    ) : (
                                      <div className="p-3 bg-white rounded border min-h-[80px]">
                                        {notes[session.id] || session.notes || (
                                          <span className="text-gray-400 italic text-sm">No notes yet. Click Edit to add notes.</span>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="notes">
                      <div className="mb-6 border rounded-md p-4 bg-gray-50">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium">Subtopic Notes</h4>
                          {editingNote === currentSubtopic.id ? (
                            <Button size="sm" onClick={() => handleSaveNote(currentSubtopic.id)}>
                              <Save className="h-4 w-4 mr-1" /> Save
                            </Button>
                          ) : (
                            <Button size="sm" variant="outline" onClick={() => handleEditNote(currentSubtopic.id)}>
                              <Edit className="h-4 w-4 mr-1" /> Edit
                            </Button>
                          )}
                        </div>

                        {editingNote === currentSubtopic.id ? (
                          <Textarea
                            value={noteContent}
                            onChange={(e) => setNoteContent(e.target.value)}
                            placeholder="Add your notes about this subtopic here..."
                            className="min-h-[150px]"
                          />
                        ) : (
                          <div className="p-3 bg-white rounded border min-h-[150px]">
                            {notes[currentSubtopic.id] || (
                              <span className="text-gray-400 italic">No notes yet. Click Edit to add notes.</span>
                            )}
                          </div>
                        )}
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Session Feedback & Notes</h4>
                        {currentSubtopic.sessions.length === 0 ? (
                          <div className="text-center py-6 border rounded-md">
                            <MessageSquare className="h-10 w-10 mx-auto text-gray-300 mb-2" />
                            <p className="text-gray-500">No session feedback available yet.</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {currentSubtopic.sessions.map((session) => (
                              <Card key={session.id}>
                                <CardHeader className="pb-2">
                                  <div className="flex justify-between items-center">
                                    <CardTitle className="text-base">
                                      Session on {new Date(session.date).toLocaleDateString()}
                                    </CardTitle>
                                    <Badge className={getStatusColor(session.status)}>
                                      {session.status}
                                    </Badge>
                                  </div>
                                  <CardDescription>
                                    {session.time} with {session.tutorName} ({session.duration} min)
                                  </CardDescription>
                                </CardHeader>
                                <CardContent>
                                  {session.feedback && (
                                    <div className="mb-4">
                                      <div className="flex items-center mb-2">
                                        <h5 className="font-medium text-sm">Your Feedback</h5>
                                        <div className="ml-2 flex items-center">
                                          {[...Array(5)].map((_, i) => (
                                            <Star
                                              key={i}
                                              className={`h-3 w-3 ${i < session.feedback!.rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                                            />
                                          ))}
                                        </div>
                                      </div>

                                      <div className="bg-gray-50 p-3 rounded-md mb-2">
                                        <p className="text-sm italic">"{session.feedback.comment}"</p>
                                      </div>

                                      {session.feedback.tutorComment && (
                                        <div className="bg-blue-50 p-3 rounded-md">
                                          <p className="text-sm text-blue-800">
                                            <span className="font-medium">Tutor:</span> {session.feedback.tutorComment}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )}

                                  <div className="mt-3">
                                    <div className="flex justify-between items-center mb-2">
                                      <h5 className="font-medium text-sm">Session Notes</h5>
                                      {editingNote === session.id ? (
                                        <Button size="sm" onClick={() => handleSaveNote(session.id)}>
                                          <Save className="h-3 w-3 mr-1" /> Save
                                        </Button>
                                      ) : (
                                        <Button size="sm" variant="outline" onClick={() => handleEditNote(session.id, session.notes || "")}>
                                          <Edit className="h-3 w-3 mr-1" /> Edit
                                        </Button>
                                      )}
                                    </div>

                                    {editingNote === session.id ? (
                                      <Textarea
                                        value={noteContent}
                                        onChange={(e) => setNoteContent(e.target.value)}
                                        placeholder="Add your notes about this session here..."
                                        className="min-h-[80px]"
                                      />
                                    ) : (
                                      <div className="p-3 bg-white rounded border min-h-[80px]">
                                        {notes[session.id] || session.notes || (
                                          <span className="text-gray-400 italic text-sm">No notes yet. Click Edit to add notes.</span>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              ) : (
                <div className="text-center py-12">
                  <BookOpen className="h-16 w-16 mx-auto text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-600">Select a subtopic</h3>
                  <p className="text-gray-500 mt-2">
                    Choose a subtopic from the progress map to view details
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center py-12">
              <GraduationCap className="h-16 w-16 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-600">Select a topic</h3>
              <p className="text-gray-500 mt-2">
                Choose a topic from the progress map to begin exploring your learning journey
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Journey;
-- Fix students table schema to match the trigger function expectations
-- This migration fixes the data type mismatches and missing columns
-- It also handles the materialized view dependency

-- First, let's check the current schema
DO $$
BEGIN
    RAISE NOTICE 'Current students table schema:';

    -- Check if date_of_birth exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'students'
        AND column_name = 'date_of_birth'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '  - date_of_birth: EXISTS';
    ELSE
        RAISE NOTICE '  - date_of_birth: MISSING';
    END IF;

    -- Check subjects_of_interest type
    PERFORM 1 FROM information_schema.columns
    WHERE table_name = 'students'
    AND column_name = 'subjects_of_interest'
    AND table_schema = 'public';

    IF FOUND THEN
        RAISE NOTICE '  - subjects_of_interest: EXISTS (checking type...)';
    ELSE
        RAISE NOTICE '  - subjects_of_interest: MISSING';
    END IF;
END $$;

-- Step 1: Drop the materialized view and its dependencies
DROP TRIGGER IF EXISTS refresh_on_student_change ON students;
DROP TRIGGER IF EXISTS refresh_on_subscription_change ON subscriptions;
DROP MATERIALIZED VIEW IF EXISTS private.student_complete_profile;

-- Add missing date_of_birth column
ALTER TABLE public.students
ADD COLUMN IF NOT EXISTS date_of_birth DATE;

-- Change subjects_of_interest from text to text[] array
-- Use a simpler approach that handles the conversion safely
ALTER TABLE public.students
ALTER COLUMN subjects_of_interest TYPE text[] USING
  CASE
    WHEN subjects_of_interest IS NULL THEN NULL
    ELSE
      CASE
        WHEN subjects_of_interest::text = '' THEN ARRAY[]::text[]
        WHEN subjects_of_interest::text LIKE '{%}' THEN subjects_of_interest::text[]
        ELSE string_to_array(subjects_of_interest::text, ',')
      END
  END;

-- Change learning_goals from text to text[] array
ALTER TABLE public.students
ALTER COLUMN learning_goals TYPE text[] USING
  CASE
    WHEN learning_goals IS NULL THEN NULL
    ELSE
      CASE
        WHEN learning_goals::text = '' THEN ARRAY[]::text[]
        WHEN learning_goals::text LIKE '{%}' THEN learning_goals::text[]
        ELSE string_to_array(learning_goals::text, ',')
      END
  END;

-- Set default values for the array columns
ALTER TABLE public.students
ALTER COLUMN subjects_of_interest SET DEFAULT ARRAY[]::text[];

ALTER TABLE public.students
ALTER COLUMN learning_goals SET DEFAULT ARRAY[]::text[];

-- Add missing columns that might be needed based on the documentation
ALTER TABLE public.students
ADD COLUMN IF NOT EXISTS hobbies text[] DEFAULT ARRAY[]::text[];

ALTER TABLE public.students
ADD COLUMN IF NOT EXISTS interests text[] DEFAULT ARRAY[]::text[];

ALTER TABLE public.students
ADD COLUMN IF NOT EXISTS location text;

-- Verify the changes
DO $$
BEGIN
    -- Check if date_of_birth column exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'students'
        AND column_name = 'date_of_birth'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✓ date_of_birth column added successfully';
    ELSE
        RAISE WARNING '⚠ date_of_birth column was not added';
    END IF;

    -- Check if subjects_of_interest is now an array
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'students'
        AND column_name = 'subjects_of_interest'
        AND data_type = 'ARRAY'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✓ subjects_of_interest is now an array';
    ELSE
        RAISE WARNING '⚠ subjects_of_interest is not an array';
    END IF;

    -- Check if learning_goals is now an array
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'students'
        AND column_name = 'learning_goals'
        AND data_type = 'ARRAY'
        AND table_schema = 'public'
    ) THEN
        RAISE NOTICE '✓ learning_goals is now an array';
    ELSE
        RAISE WARNING '⚠ learning_goals is not an array';
    END IF;
END $$;

RAISE NOTICE 'Students table schema fix completed!';

-- Step 3: Recreate the materialized view with updated schema
-- Note: This assumes the subscriptions table exists (renamed from student_subscriptions)
CREATE MATERIALIZED VIEW IF NOT EXISTS private.student_complete_profile AS
SELECT
    -- Profile data
    p.id,
    p.first_name,
    p.last_name,
    p.email,
    p.user_type,
    p.profile_picture_url,
    p.timezone,
    p.created_at as profile_created_at,
    p.updated_at as profile_updated_at,

    -- Student-specific data (now with correct array types)
    s.education_level,
    s.subjects_of_interest,
    s.learning_goals,
    s.study_preferences,
    s.academic_history,
    s.hobbies,
    s.interests,
    s.location,
    s.date_of_birth,

    -- Enrollment status (aggregated from active subscriptions)
    CASE
        WHEN COUNT(ss.id) FILTER (WHERE ss.status = 'active' AND (ss.access_expires_at IS NULL OR ss.access_expires_at > NOW())) > 0
        THEN true
        ELSE false
    END as is_enrolled,

    -- Active subscriptions count
    COUNT(ss.id) FILTER (WHERE ss.status = 'active' AND (ss.access_expires_at IS NULL OR ss.access_expires_at > NOW())) as active_subscriptions_count,

    -- Subscription details (JSON aggregation for active subscriptions)
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', ss.id,
                'product_id', ss.product_id,
                'product_name', pr.name,
                'status', ss.status,
                'access_expires_at', ss.access_expires_at,
                'days_remaining', CASE
                    WHEN ss.access_expires_at IS NULL THEN NULL
                    ELSE GREATEST(0, EXTRACT(DAY FROM (ss.access_expires_at - NOW())))
                END
            )
        ) FILTER (WHERE ss.status = 'active' AND (ss.access_expires_at IS NULL OR ss.access_expires_at > NOW())),
        '[]'::json
    ) as active_subscriptions,

    -- All subscriptions (including expired/cancelled)
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', ss.id,
                'product_id', ss.product_id,
                'product_name', pr.name,
                'status', ss.status,
                'access_expires_at', ss.access_expires_at,
                'days_remaining', CASE
                    WHEN ss.access_expires_at IS NULL THEN NULL
                    ELSE GREATEST(0, EXTRACT(DAY FROM (ss.access_expires_at - NOW())))
                END
            )
            ORDER BY ss.created_at DESC
        ) FILTER (WHERE ss.id IS NOT NULL),
        '[]'::json
    ) as all_subscriptions,

    -- Earliest active subscription end date
    MIN(ss.access_expires_at) FILTER (WHERE ss.status = 'active' AND (ss.access_expires_at IS NULL OR ss.access_expires_at > NOW())) as earliest_subscription_end,

    -- Latest subscription end date
    MAX(ss.access_expires_at) FILTER (WHERE ss.status = 'active' AND (ss.access_expires_at IS NULL OR ss.access_expires_at > NOW())) as latest_subscription_end,

    -- Total days remaining (sum of all active subscriptions)
    COALESCE(
        SUM(CASE
            WHEN ss.access_expires_at IS NULL THEN 365 -- Assume 1 year for unlimited access
            ELSE GREATEST(0, EXTRACT(DAY FROM (ss.access_expires_at - NOW())))
        END)
        FILTER (WHERE ss.status = 'active' AND (ss.access_expires_at IS NULL OR ss.access_expires_at > NOW())),
        0
    ) as total_days_remaining

FROM profiles p
LEFT JOIN students s ON p.id = s.id
LEFT JOIN subscriptions ss ON p.id = ss.student_id  -- Updated table name
LEFT JOIN products pr ON ss.product_id = pr.id
WHERE p.user_type = 'student'
GROUP BY
    p.id, p.first_name, p.last_name, p.email, p.user_type,
    p.profile_picture_url, p.timezone, p.created_at, p.updated_at,
    s.education_level, s.subjects_of_interest, s.learning_goals,
    s.study_preferences, s.academic_history, s.hobbies, s.interests,
    s.location, s.date_of_birth;

-- Create unique index for fast lookups by student ID
CREATE UNIQUE INDEX IF NOT EXISTS idx_student_complete_profile_id ON private.student_complete_profile (id);

-- Create index for enrollment status queries
CREATE INDEX IF NOT EXISTS idx_student_complete_profile_enrolled ON private.student_complete_profile (is_enrolled);

-- Create index for active subscriptions
CREATE INDEX IF NOT EXISTS idx_student_complete_profile_active_subs ON private.student_complete_profile (active_subscriptions_count);

RAISE NOTICE '✅ Materialized view recreated with updated schema!';

-- Step 4: Create the public function to access the private materialized view
-- This function needs to match the exact structure of the materialized view
CREATE OR REPLACE FUNCTION public.get_student_complete_profile(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    timezone text,
    profile_created_at timestamp with time zone,
    profile_updated_at timestamp with time zone,
    education_level text,
    subjects_of_interest text[],
    learning_goals text[],
    study_preferences jsonb,
    academic_history jsonb,
    hobbies text[],
    interests text[],
    location text,
    date_of_birth date,
    is_enrolled boolean,
    active_subscriptions_count bigint,
    active_subscriptions json,
    all_subscriptions json,
    earliest_subscription_end timestamp with time zone,
    latest_subscription_end timestamp with time zone,
    total_days_remaining numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        p.timezone,
        p.profile_created_at,
        p.profile_updated_at,
        p.education_level,
        p.subjects_of_interest,
        p.learning_goals,
        p.study_preferences,
        p.academic_history,
        p.hobbies,
        p.interests,
        p.location,
        p.date_of_birth,
        p.is_enrolled,
        p.active_subscriptions_count,
        p.active_subscriptions,
        p.all_subscriptions,
        p.earliest_subscription_end,
        p.latest_subscription_end,
        p.total_days_remaining
    FROM private.student_complete_profile p
    WHERE p.id = student_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant appropriate permissions
GRANT EXECUTE ON FUNCTION public.get_student_complete_profile(uuid) TO authenticated;

RAISE NOTICE '✅ Public function get_student_complete_profile created with correct return type!';

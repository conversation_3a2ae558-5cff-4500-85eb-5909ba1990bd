import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/RadioGroup";
import { Label } from "@/components/ui/Label";
import { useTutorOnboardingStore } from "@/store/tutorOnboardingStore";

const TutorOnboardingStepTwo: React.FC = () => {
  const { educationLevel, setEducationLevel } = useTutorOnboardingStore();

  const educationLevels = [
    { id: "bachelors", label: "Bachelor's Degree" },
    { id: "masters", label: "Master's Degree" },
    { id: "phd", label: "PhD" },
    { id: "professional", label: "Professional Certification" },
  ];

  return (
    <div>
      <h2 className="text-2xl font-bold mb-2">
        What is your highest level of education?
      </h2>
      <p className="text-gray-600 mb-6">
        This helps us match you with appropriate students.
      </p>

      <RadioGroup
        value={educationLevel}
        onValueChange={setEducationLevel}
        className="space-y-4"
      >
        {educationLevels.map((level) => (
          <div
            key={level.id}
            className={`
              flex items-center space-x-2 p-4 rounded-lg border-2
              ${
                educationLevel === level.id
                  ? "border-green-500 bg-green-50"
                  : "border-gray-200"
              }
            `}
          >
            <RadioGroupItem value={level.id} id={level.id} />
            <Label htmlFor={level.id} className="flex-grow cursor-pointer">
              {level.label}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default TutorOnboardingStepTwo;

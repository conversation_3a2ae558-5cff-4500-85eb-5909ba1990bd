-- Simplified fix for the get_student_complete_profile function
-- This version is compatible with older PostgreSQL versions and avoids FILTER syntax

-- =====================================================
-- STEP 1: DROP AND RECREATE WITH SIMPLE APPROACH
-- =====================================================

-- Drop the existing function
DROP FUNCTION IF EXISTS public.get_student_complete_profile(uuid);

-- Create a much simpler version that just gets basic profile data
CREATE OR REPLACE FUNCTION public.get_student_complete_profile(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    timezone text,
    profile_created_at timestamp with time zone,
    profile_updated_at timestamp with time zone,
    education_level text,
    subjects_of_interest text[],
    learning_goals text[],
    study_preferences jsonb,
    academic_history jsonb,
    hobbies text[],
    interests text[],
    location text,
    date_of_birth date,
    is_enrolled boolean,
    active_subscriptions_count bigint,
    active_subscriptions json,
    all_subscriptions json,
    earliest_subscription_end timestamp with time zone,
    latest_subscription_end timestamp with time zone,
    total_days_remaining numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        p.timezone,
        -- Explicitly cast timestamps to WITH TIME ZONE
        p.created_at::timestamp with time zone as profile_created_at,
        p.updated_at::timestamp with time zone as profile_updated_at,
        COALESCE(s.education_level, ''::text),
        COALESCE(s.subjects_of_interest, ARRAY[]::text[]),
        COALESCE(s.learning_goals, ARRAY[]::text[]),
        COALESCE(s.study_preferences, '{}'::jsonb),
        COALESCE(s.academic_history, '{}'::jsonb),
        COALESCE(s.hobbies, ARRAY[]::text[]),
        COALESCE(s.interests, ARRAY[]::text[]),
        COALESCE(s.location, ''::text),
        s.date_of_birth,
        
        -- Simple enrollment check
        EXISTS(
            SELECT 1 FROM subscriptions sub 
            WHERE sub.student_id = p.id 
            AND sub.status = 'active' 
            AND (sub.access_expires_at IS NULL OR sub.access_expires_at > NOW())
        ) as is_enrolled,
        
        -- Active subscriptions count
        (
            SELECT COUNT(*)::bigint 
            FROM subscriptions sub 
            WHERE sub.student_id = p.id 
            AND sub.status = 'active' 
            AND (sub.access_expires_at IS NULL OR sub.access_expires_at > NOW())
        ) as active_subscriptions_count,
        
        -- Active subscriptions JSON
        (
            SELECT COALESCE(JSON_AGG(
                JSON_BUILD_OBJECT(
                    'id', sub.id,
                    'product_id', sub.product_id,
                    'status', sub.status,
                    'access_expires_at', sub.access_expires_at,
                    'days_remaining', CASE
                        WHEN sub.access_expires_at IS NULL THEN NULL
                        ELSE GREATEST(0, EXTRACT(DAY FROM (sub.access_expires_at - NOW())))
                    END
                )
            ), '[]'::json)
            FROM subscriptions sub 
            WHERE sub.student_id = p.id 
            AND sub.status = 'active' 
            AND (sub.access_expires_at IS NULL OR sub.access_expires_at > NOW())
        ) as active_subscriptions,
        
        -- All subscriptions JSON
        (
            SELECT COALESCE(JSON_AGG(
                JSON_BUILD_OBJECT(
                    'id', sub.id,
                    'product_id', sub.product_id,
                    'status', sub.status,
                    'access_expires_at', sub.access_expires_at,
                    'days_remaining', CASE
                        WHEN sub.access_expires_at IS NULL THEN NULL
                        ELSE GREATEST(0, EXTRACT(DAY FROM (sub.access_expires_at - NOW())))
                    END
                )
            ), '[]'::json)
            FROM subscriptions sub 
            WHERE sub.student_id = p.id
        ) as all_subscriptions,
        
        -- Earliest subscription end
        (
            SELECT MIN(sub.access_expires_at)::timestamp with time zone
            FROM subscriptions sub 
            WHERE sub.student_id = p.id 
            AND sub.status = 'active'
        ) as earliest_subscription_end,
        
        -- Latest subscription end
        (
            SELECT MAX(sub.access_expires_at)::timestamp with time zone
            FROM subscriptions sub 
            WHERE sub.student_id = p.id 
            AND sub.status = 'active'
        ) as latest_subscription_end,
        
        -- Total days remaining
        (
            SELECT COALESCE(
                SUM(GREATEST(0, EXTRACT(DAY FROM (sub.access_expires_at - NOW())))),
                0::numeric
            )
            FROM subscriptions sub 
            WHERE sub.student_id = p.id 
            AND sub.status = 'active' 
            AND sub.access_expires_at IS NOT NULL
        ) as total_days_remaining
        
    FROM profiles p
    LEFT JOIN students s ON p.id = s.id
    WHERE p.id = student_id AND p.user_type = 'student';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 2: CREATE BASIC PROFILE FUNCTION
-- =====================================================

-- Create a simpler function that just gets basic profile data
CREATE OR REPLACE FUNCTION public.get_basic_student_profile(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    profile_created_at timestamp with time zone,
    profile_updated_at timestamp with time zone,
    education_level text,
    is_enrolled boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        p.created_at::timestamp with time zone as profile_created_at,
        p.updated_at::timestamp with time zone as profile_updated_at,
        COALESCE(s.education_level, ''::text),
        -- Simple enrollment check
        EXISTS(
            SELECT 1 FROM subscriptions sub 
            WHERE sub.student_id = p.id 
            AND sub.status = 'active' 
            AND (sub.access_expires_at IS NULL OR sub.access_expires_at > NOW())
        ) as is_enrolled
        
    FROM profiles p
    LEFT JOIN students s ON p.id = s.id
    WHERE p.id = student_id AND p.user_type = 'student';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 3: GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_student_complete_profile(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_student_complete_profile(uuid) TO anon;
GRANT EXECUTE ON FUNCTION public.get_basic_student_profile(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_basic_student_profile(uuid) TO anon;

-- =====================================================
-- STEP 4: TEST THE FUNCTIONS
-- =====================================================

-- Test the corrected function
SELECT 'Complete Profile Function Test' as test_name;
SELECT * FROM get_student_complete_profile(auth.uid()) LIMIT 1;

-- Test the basic function
SELECT 'Basic Profile Function Test' as test_name;
SELECT * FROM get_basic_student_profile(auth.uid()) LIMIT 1;

-- =====================================================
-- STEP 5: VERIFICATION
-- =====================================================

-- Check that the function exists and has correct return type
SELECT 
    routine_name,
    routine_type,
    'Function recreated successfully' as status
FROM information_schema.routines 
WHERE routine_name IN ('get_student_complete_profile', 'get_basic_student_profile')
ORDER BY routine_name;

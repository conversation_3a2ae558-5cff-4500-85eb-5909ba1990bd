import React from "react";
import { useUpcomingSessionsStore } from "@/store/upcomingSessionsStore";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import { Badge } from "@/components/ui/Badge";
import {
  Search,
  Filter,
  Columns,
  Calendar,
  MoreHorizontal,
  Send,
  RefreshCw,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/Dialog";
import { useProfileData } from "@/hooks/useProfileData";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";

const UpcomingSessions = () => {
  // Use the Zustand store
  const {
    searchTerm,
    setSearchTerm,
    visibleColumns,
    setVisibleColumns,
    isReminderModalOpen,
    setIsReminderModalOpen,
    selectedSession,
    setSelectedSession,
    isCalendarView,
    setIsCalendarView,
    filteredSessions
  } = useUpcomingSessionsStore();

  // Get profile data for the UserNavbar
  const profileData = useProfileData();

  const handleSendReminder = (session: any) => {
    setSelectedSession(session);
    setIsReminderModalOpen(true);
  };

  const handleActionsClick = (session: any, action: string) => {
    if (action === "Send Reminder") {
      handleSendReminder(session);
    } else {
      console.log(`Action ${action} clicked for session ${session.id}`);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Scheduled":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            Scheduled
          </Badge>
        );
      case "Pending Confirmations":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Pending Confirmations
          </Badge>
        );
      case "Cancelled":
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200">
            Cancelled
          </Badge>
        );
      case "Completed":
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            Completed
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200">
            {status}
          </Badge>
        );
    }
  };

  return (
    <TutorPageLayout
      title="Upcoming Sessions"
      profileData={profileData}
      description="Manage your scheduled teaching sessions and stay organized"
    >
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-4 border-b border-gray-200 flex flex-wrap justify-between items-center gap-4">
          <div className="relative flex-grow max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search by student or topic"
              className="pl-10 pr-4 py-2 w-full border-gray-200 rounded-md focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 transition-colors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="border-gray-200 text-gray-700 hover:bg-gray-50 flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {/* Filter options would go here */}
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="border-gray-200 text-gray-700 hover:bg-gray-50 flex items-center gap-2">
                  <Columns className="h-4 w-4" />
                  Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.id}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({ ...visibleColumns, id: checked })
                  }
                >
                  Session ID
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.externalId}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      externalId: checked,
                    })
                  }
                >
                  External ID
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.student}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      student: checked,
                    })
                  }
                >
                  Student
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.country}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      country: checked,
                    })
                  }
                >
                  Country
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.topic}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({ ...visibleColumns, topic: checked })
                  }
                >
                  Topic/Subject
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.subtopic}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      subtopic: checked,
                    })
                  }
                >
                  Subtopic
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.sessionType}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      sessionType: checked,
                    })
                  }
                >
                  Session Type
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.dateTime}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      dateTime: checked,
                    })
                  }
                >
                  Date & Time
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.status}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      status: checked,
                    })
                  }
                >
                  Status
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={visibleColumns.actions}
                  onCheckedChange={(checked) =>
                    setVisibleColumns({
                      ...visibleColumns,
                      actions: checked,
                    })
                  }
                >
                  Actions
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex border border-gray-200 rounded-md overflow-hidden">
              <button
                className={`px-3 py-1.5 text-sm font-medium flex items-center ${
                  !isCalendarView
                    ? 'bg-purple-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setIsCalendarView(false)}
              >
                List
              </button>
              <button
                className={`px-3 py-1.5 text-sm font-medium flex items-center ${
                  isCalendarView
                    ? 'bg-purple-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setIsCalendarView(true)}
              >
                <Calendar className="h-4 w-4 mr-1" /> Calendar
              </button>
            </div>
          </div>
        </div>

        {!isCalendarView ? (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader className="bg-gray-50 font-semibold text-gray-600 uppercase text-xs tracking-wider">
                <TableRow>
                  {visibleColumns.id && <TableHead>Session ID</TableHead>}
                  {visibleColumns.externalId && (
                    <TableHead>External ID</TableHead>
                  )}
                  {visibleColumns.student && <TableHead>Student</TableHead>}
                  {visibleColumns.country && <TableHead>Country</TableHead>}
                  {visibleColumns.topic && (
                    <TableHead>Topic/Subject</TableHead>
                  )}
                  {visibleColumns.subtopic && <TableHead>Subtopic</TableHead>}
                  {visibleColumns.sessionType && (
                    <TableHead>Session Type</TableHead>
                  )}
                  {visibleColumns.dateTime && (
                    <TableHead>Date & Time</TableHead>
                  )}
                  {visibleColumns.status && <TableHead>Status</TableHead>}
                  {visibleColumns.actions && <TableHead>Actions</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSessions.map((session) => (
                  <TableRow
                    key={session.id}
                    className="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150 hover:shadow-sm"
                  >
                    {visibleColumns.id && (
                      <TableCell className="font-medium p-4 align-middle">
                        #{session.id.split("-")[1]}
                      </TableCell>
                    )}
                    {visibleColumns.externalId && (
                      <TableCell>{session.externalId}</TableCell>
                    )}
                    {visibleColumns.student && (
                      <TableCell>
                        <div className="flex items-center">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 text-purple-700 font-semibold text-xs mr-2">
                            {session.student.name
                              .split(" ")
                              .map((n: string) => n[0])
                              .join("")}
                          </div>
                          <div>
                            <div className="font-medium">
                              {session.student.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {session.student.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    )}
                    {visibleColumns.country && (
                      <TableCell>
                        <div className="flex items-center">
                          <span className="ml-1">{session.student.country}</span>
                        </div>
                      </TableCell>
                    )}
                    {visibleColumns.topic && (
                      <TableCell>{session.topic}</TableCell>
                    )}
                    {visibleColumns.subtopic && (
                      <TableCell>{session.subtopic}</TableCell>
                    )}
                    {visibleColumns.sessionType && (
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            session.sessionType === "Group"
                              ? "bg-blue-50 text-blue-700 border-blue-200"
                              : "bg-purple-50 text-purple-700 border-purple-200"
                          }
                        >
                          {session.sessionType}
                        </Badge>
                      </TableCell>
                    )}
                    {visibleColumns.dateTime && (
                      <TableCell>
                        <div className="font-medium">{session.dateTime.date}</div>
                        <div className="text-xs text-gray-500">
                          {session.dateTime.time}
                        </div>
                      </TableCell>
                    )}
                    {visibleColumns.status && (
                      <TableCell>{getStatusBadge(session.status)}</TableCell>
                    )}
                    {visibleColumns.actions && (
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm" className="border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors">
                            Reschedule
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            className="bg-purple-600 hover:bg-purple-700 text-white transition-colors"
                          >
                            Join
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700 transition-colors">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuCheckboxItem
                                onSelect={() =>
                                  handleActionsClick(session, "Send Reminder")
                                }
                              >
                                <Send className="h-4 w-4 mr-2" /> Send Reminder
                              </DropdownMenuCheckboxItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="min-h-[600px] bg-gray-50 rounded-lg p-4 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium">Calendar View</p>
              <p>This feature is coming soon. Stay tuned!</p>
            </div>
          </div>
        )}
      </div>

      {/* Send Reminder Modal */}
      <Dialog open={isReminderModalOpen} onOpenChange={setIsReminderModalOpen}>
        <DialogContent className="max-w-md rounded-lg shadow-lg">
          <DialogHeader className="pb-4 border-b border-gray-100">
            <DialogTitle className="text-xl font-semibold text-gray-900">Send Session Reminder</DialogTitle>
            <DialogDescription className="text-sm text-gray-600 mt-1">
              Send a reminder to the student about the upcoming session.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedSession && (
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 text-purple-700 font-semibold text-sm mr-3">
                    {selectedSession.student.name
                      .split(" ")
                      .map((n: string) => n[0])
                      .join("")}
                  </div>
                  <div>
                    <p className="font-medium">{selectedSession.student.name}</p>
                    <p className="text-sm text-gray-500">{selectedSession.student.email}</p>
                  </div>
                </div>
                <div className="bg-gray-50 p-3 rounded-md">
                  <div className="flex items-center mb-2">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    <p className="text-sm font-medium">Session Details</p>
                  </div>
                  <p className="text-sm ml-6">
                    <span className="font-medium">{selectedSession.topic}</span>
                    <br />
                    {selectedSession.dateTime.date} at {selectedSession.dateTime.time}
                  </p>
                </div>
                <div className="bg-blue-50 p-3 rounded-md">
                  <div className="flex items-center mb-2">
                    <Send className="h-4 w-4 text-blue-500 mr-2" />
                    <p className="text-sm font-medium">Reminder Message</p>
                  </div>
                  <p className="text-sm ml-6">
                    Hi {selectedSession.student.name.split(" ")[0]},<br /><br />
                    This is a reminder about your upcoming {selectedSession.topic} session on {selectedSession.dateTime.date} at {selectedSession.dateTime.time}.<br /><br />
                    Please make sure to join on time. The session link will be available 15 minutes before the start time.
                  </p>
                </div>
              </div>
            )}
          </div>
          <DialogFooter className="pt-4 border-t border-gray-100 flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => setIsReminderModalOpen(false)}
              className="border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                // Send reminder logic would go here
                setIsReminderModalOpen(false);
              }}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              <Send className="h-4 w-4 mr-2" /> Send Reminder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TutorPageLayout>
  );
};

export default UpcomingSessions;

# Database Schema Explanation

## Core Entity Relationships

```
profiles
    ↑
    | (1:N)
    |
batches ----→ batch_topics ----→ batch_subtopics
    |            |                    |
    |            |                    |
    ↓            ↓                    ↓
sessions     topics ----→ subtopics ----→ resources
    ↑            ↑            ↑
    |            |            |
    |            |            |
session_requests  |            |
                  |            |
tutor_availability |            |
    ↑              |            |
    |              |            |
tutor_auto_accept_rules        |
    |                          |
    ↓                          |
rule_topics -------------------+
    |
    ↓
rule_time_ranges
```

## Schema Design Explanation

### 1. Hierarchical Content Structure

The schema follows a hierarchical structure for educational content:
- **subjects**: Top-level categories (Mathematics, Science)
- **topics**: Categories within subjects (Calculus, Genetics)
- **subtopics**: Specific learning units (Integration by Parts, <PERSON><PERSON>'s Laws)
- **resources**: Learning materials associated with subtopics

This structure allows for organized content management and easy navigation through the learning journey.

### 2. Bat<PERSON> and Tutor Assignment System

The schema supports flexible tutor assignment at multiple levels:

- **batches**: Contains a default_tutor_id for the entire batch
- **batch_topics**: Can override the default tutor with custom_tutor_id for specific topics
- **batch_subtopics**: Can override both batch and topic tutors with custom_tutor_id for specific subtopics
- **sessions**: Contains the actual tutor_id for each specific session

The `get_assigned_tutor_for_session` function determines the assigned tutor based on the hierarchy:
1. First checks if a custom tutor is assigned at the subtopic level (if a subtopic is specified)
2. If not, checks if a custom tutor is assigned at the topic level
3. If not, falls back to the default tutor for the batch

This function determines the assigned tutor - the actual tutor for a session is stored directly in the sessions table and can be different from the assigned tutor based on availability, admin decisions, or other factors.

This approach supports your requirement for flexible tutor assignment at different levels while recognizing that the same subtopic can be covered across multiple sessions, each potentially with different tutors.

### 3. Session Management

The schema includes comprehensive session management:

- **sessions**: Records of scheduled or completed learning sessions
- **session_requests**: Requests for new sessions that can be accepted or rejected
- **session_details**: Additional information about completed sessions
- **session_feedback**: Feedback submitted by students or tutors

This structure supports the session lifecycle from request to completion and feedback.

### 4. Tutor Availability and Auto-Accept Rules

The schema includes a sophisticated system for managing tutor availability:

- **tutor_availability**: Records when tutors are available
- **tutor_auto_accept_rules**: Rules for automatically accepting session requests
- **rule_topics**: Topics that a rule applies to
- **rule_time_ranges**: Time ranges that a rule applies to

This supports your requirement for an auto-accept rules engine with topic-specific and time-specific conditions.

### 5. Learning Journey Tracking

The schema supports tracking a student's learning journey:

- **batch_topics.status**: Tracks progress at the topic level (not_started, in_progress, completed)
- **batch_subtopics.status**: Tracks progress at the subtopic level
- **sessions**: Records all learning sessions with their topics and subtopics

This supports your requirement for a learning journey feature with detailed progress tracking.

## Business Logic Support

### 1. Flexible Tutor Assignment

The schema supports your requirement for flexible tutor assignment:

```
Admin creates batch → Assigns default tutor → Can override tutor at topic/subtopic level
```

When a session is created, the system can determine the appropriate tutor using the `get_assigned_tutor_for_session` function.

### 2. Learning Journey Visualization

The schema supports your requirement for a learning journey visualization:

```
Student views journey → System fetches batch_topics and batch_subtopics → Displays progress
```

The modal detail view can be implemented by fetching subtopics and sessions for a selected topic.

### 3. Session Request and Acceptance

The schema supports your requirement for session requests and acceptance:

```
Student creates request → Tutor receives notification → Tutor accepts/rejects → Session is created
```

The auto-accept rules can automatically accept requests that match certain criteria.

### 4. Performance Tracking

The schema supports your requirement for performance tracking:

```
Session completed → Details recorded → Analytics generated → Displayed on dashboard
```

The session_details table stores metrics like tutor talk time, whiteboard interactions, and participation score.

-- Subscription Curriculum Configuration Schema
-- This schema supports the two-step subscription process:
-- Step 1: Purchase subscription (existing)
-- Step 2: Configure curriculum content

-- Subscription Curriculum Configuration table - Main configuration record
-- Note: This table already exists in the database with a different structure
-- The existing table uses JSONB for storing selections and links to workflow_id
-- This schema is kept for reference but should not be executed if the table already exists

CREATE TABLE IF NOT EXISTS subscription_curriculum (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES subscription_workflows(id),
    selection_type TEXT NOT NULL CHECK (selection_type IN ('complete_subjects', 'selected_topics', 'selected_subtopics')),
    selected_subjects JSONB DEFAULT '[]'::jsonb,
    selected_topics JSONB DEFAULT '[]'::jsonb,
    selected_subtopics JSONB DEFAULT '[]'::jsonb,
    estimated_sessions INTEGER,
    configured_by UUID NOT NULL REFERENCES profiles(id),
    configured_by_role TEXT NOT NULL CHECK (configured_by_role IN ('student', 'admin')),
    configuration_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(workflow_id)
);

-- =====================================================
-- ALTERNATIVE NORMALIZED APPROACH (NOT USED)
-- =====================================================
-- The following tables represent an alternative normalized approach
-- They are commented out because the existing database uses JSONB approach above
-- Keep for reference in case you want to migrate to normalized structure later

/*
-- Subscription Subjects table - For booster subscriptions (complete subjects)
CREATE TABLE subscription_subjects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_curriculum_id UUID REFERENCES subscription_curriculum(id) NOT NULL,
    subject_id UUID REFERENCES subjects(id) NOT NULL,
    include_all_topics BOOLEAN DEFAULT TRUE, -- For booster, this is typically true
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(subscription_curriculum_id, subject_id)
);

-- Subscription Topics table - For custom subscriptions (selected topics)
CREATE TABLE subscription_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_curriculum_id UUID REFERENCES subscription_curriculum(id) NOT NULL,
    subject_id UUID REFERENCES subjects(id) NOT NULL,
    topic_id UUID REFERENCES topics(id) NOT NULL,
    include_all_subtopics BOOLEAN DEFAULT TRUE, -- If false, specific subtopics are selected
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(subscription_curriculum_id, topic_id)
);

-- Subscription Subtopics table - For custom subscriptions (selected subtopics)
CREATE TABLE subscription_subtopics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_topic_id UUID REFERENCES subscription_topics(id) NOT NULL,
    subtopic_id UUID REFERENCES subtopics(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(subscription_topic_id, subtopic_id)
);
*/

-- Add indexes for better performance on the JSONB approach
CREATE INDEX IF NOT EXISTS idx_subscription_curriculum_workflow_id ON subscription_curriculum(workflow_id);
CREATE INDEX IF NOT EXISTS idx_subscription_curriculum_configured_by ON subscription_curriculum(configured_by);
CREATE INDEX IF NOT EXISTS idx_subscription_curriculum_selection_type ON subscription_curriculum(selection_type);

-- JSONB indexes for efficient querying of selected items
CREATE INDEX IF NOT EXISTS idx_subscription_curriculum_selected_subjects ON subscription_curriculum USING GIN (selected_subjects);
CREATE INDEX IF NOT EXISTS idx_subscription_curriculum_selected_topics ON subscription_curriculum USING GIN (selected_topics);
CREATE INDEX IF NOT EXISTS idx_subscription_curriculum_selected_subtopics ON subscription_curriculum USING GIN (selected_subtopics);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_subscription_curriculum_updated_at
    BEFORE UPDATE ON subscription_curriculum
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to get subscription curriculum configuration
CREATE OR REPLACE FUNCTION get_subscription_curriculum_config(p_subscription_id UUID)
RETURNS TABLE (
    subscription_id UUID,
    product_type TEXT,
    is_configured BOOLEAN,
    configured_by UUID,
    configured_at TIMESTAMP WITH TIME ZONE,
    subjects JSONB,
    topics JSONB,
    subtopics JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        sc.subscription_id,
        sc.product_type,
        sc.is_configured,
        sc.configured_by,
        sc.configured_at,
        COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'id', s.id,
                    'name', s.name,
                    'description', s.description,
                    'include_all_topics', ss.include_all_topics
                )
            ) FROM subscription_subjects ss
            JOIN subjects s ON s.id = ss.subject_id
            WHERE ss.subscription_curriculum_id = sc.id),
            '[]'::jsonb
        ) as subjects,
        COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'id', t.id,
                    'name', t.name,
                    'subject_id', t.subject_id,
                    'include_all_subtopics', st.include_all_subtopics
                )
            ) FROM subscription_topics st
            JOIN topics t ON t.id = st.topic_id
            WHERE st.subscription_curriculum_id = sc.id),
            '[]'::jsonb
        ) as topics,
        COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'id', sub.id,
                    'name', sub.name,
                    'topic_id', sub.topic_id
                )
            ) FROM subscription_subtopics ssub
            JOIN subscription_topics st ON st.id = ssub.subscription_topic_id
            JOIN subtopics sub ON sub.id = ssub.subtopic_id
            WHERE st.subscription_curriculum_id = sc.id),
            '[]'::jsonb
        ) as subtopics
    FROM subscription_curriculum sc
    WHERE sc.subscription_id = p_subscription_id;
END;
$$ LANGUAGE plpgsql;

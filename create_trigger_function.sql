-- Create the trigger function and fix the trigger conditions
-- This script creates the missing handle_student_candidate_completion function
-- and sets up proper triggers for both INSERT and UPDATE operations

-- First, create the trigger function that handles student candidate completion
CREATE OR REPLACE FUNCTION handle_student_candidate_completion()
RETURNS TRIGGER AS $$
DECLARE
    profile_exists BOOLEAN := FALSE;
    student_exists BOOLEAN := FALSE;
    error_message TEXT;
BEGIN
    -- Log the trigger execution
    INSERT INTO logs (
        user_id,
        level,
        message,
        context,
        created_at
    ) VALUES (
        NEW.id,
        'info',
        'Trigger handle_student_candidate_completion executed',
        jsonb_build_object(
            'source', 'handle_student_candidate_completion',
            'trigger_operation', TG_OP,
            'candidate_id', NEW.id,
            'email', NEW.email,
            'onboarding_completed', NEW.onboarding_completed
        ),
        NOW()
    );

    -- Only proceed if onboarding is completed
    IF NEW.onboarding_completed != TRUE THEN
        RETURN NEW;
    END IF;

    BEGIN
        -- Check if profile already exists
        SELECT EXISTS(SELECT 1 FROM profiles WHERE id = NEW.id) INTO profile_exists;

        -- <PERSON>reate profile if it doesn't exist
        IF NOT profile_exists THEN
            INSERT INTO profiles (
                id,
                first_name,
                last_name,
                user_type,
                email,
                created_at,
                updated_at
            ) VALUES (
                NEW.id,
                NEW.first_name,
                NEW.last_name,
                'student',
                NEW.email,
                NOW(),
                NOW()
            );

            -- Log profile creation
            INSERT INTO logs (
                user_id,
                level,
                message,
                context,
                created_at
            ) VALUES (
                NEW.id,
                'info',
                'Profile created for completed student candidate',
                jsonb_build_object(
                    'source', 'handle_student_candidate_completion',
                    'action', 'profile_created',
                    'candidate_id', NEW.id
                ),
                NOW()
            );
        END IF;

        -- Check if student record already exists
        SELECT EXISTS(SELECT 1 FROM students WHERE id = NEW.id) INTO student_exists;

        -- Create student record if it doesn't exist
        IF NOT student_exists THEN
            INSERT INTO students (
                id,
                candidate_id,
                education_level,
                subjects_of_interest,
                learning_goals,
                date_of_birth,
                created_at,
                updated_at
            ) VALUES (
                NEW.id,
                NEW.id,
                NEW.education_level,
                NEW.subjects_of_interest,
                NEW.learning_goals,
                NEW.date_of_birth,
                NOW(),
                NOW()
            );

            -- Log student creation
            INSERT INTO logs (
                user_id,
                level,
                message,
                context,
                created_at
            ) VALUES (
                NEW.id,
                'info',
                'Student record created for completed candidate',
                jsonb_build_object(
                    'source', 'handle_student_candidate_completion',
                    'action', 'student_created',
                    'candidate_id', NEW.id
                ),
                NOW()
            );
        END IF;

    EXCEPTION WHEN OTHERS THEN
        -- Log any errors
        error_message := SQLERRM;
        INSERT INTO logs (
            user_id,
            level,
            message,
            context,
            created_at
        ) VALUES (
            NEW.id,
            'error',
            'Error in handle_student_candidate_completion: ' || error_message,
            jsonb_build_object(
                'source', 'handle_student_candidate_completion',
                'error', error_message,
                'candidate_id', NEW.id,
                'sqlstate', SQLSTATE
            ),
            NOW()
        );

        -- Re-raise the exception to prevent the trigger from completing silently
        RAISE;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TUTOR TRIGGER FUNCTION
-- =====================================================

-- Create the trigger function that handles tutor candidate completion
CREATE OR REPLACE FUNCTION handle_tutor_candidate_completion()
RETURNS TRIGGER AS $$
DECLARE
    profile_exists BOOLEAN := FALSE;
    tutor_exists BOOLEAN := FALSE;
    error_message TEXT;
BEGIN
    -- Log the trigger execution
    INSERT INTO logs (
        user_id,
        level,
        message,
        context,
        created_at
    ) VALUES (
        NEW.id,
        'info',
        'Trigger handle_tutor_candidate_completion executed',
        jsonb_build_object(
            'source', 'handle_tutor_candidate_completion',
            'trigger_operation', TG_OP,
            'candidate_id', NEW.id,
            'email', NEW.email,
            'onboarding_completed', NEW.onboarding_completed
        ),
        NOW()
    );

    -- Only proceed if onboarding is completed
    IF NEW.onboarding_completed != TRUE THEN
        RETURN NEW;
    END IF;

    BEGIN
        -- Check if profile already exists
        SELECT EXISTS(SELECT 1 FROM profiles WHERE id = NEW.id) INTO profile_exists;

        -- Create profile if it doesn't exist
        IF NOT profile_exists THEN
            INSERT INTO profiles (
                id,
                first_name,
                last_name,
                user_type,
                email,
                created_at,
                updated_at
            ) VALUES (
                NEW.id,
                NEW.first_name,
                NEW.last_name,
                'tutor',
                NEW.email,
                NOW(),
                NOW()
            );

            -- Log profile creation
            INSERT INTO logs (
                user_id,
                level,
                message,
                context,
                created_at
            ) VALUES (
                NEW.id,
                'info',
                'Profile created for completed tutor candidate',
                jsonb_build_object(
                    'source', 'handle_tutor_candidate_completion',
                    'action', 'profile_created',
                    'candidate_id', NEW.id
                ),
                NOW()
            );
        END IF;

        -- Check if tutor record already exists
        SELECT EXISTS(SELECT 1 FROM tutors WHERE id = NEW.id) INTO tutor_exists;

        -- Create tutor record if it doesn't exist
        IF NOT tutor_exists THEN
            INSERT INTO tutors (
                id,
                candidate_id,
                bio,
                hourly_rate,
                subjects_taught,
                education_level,
                teaching_experience,
                created_at,
                updated_at
            ) VALUES (
                NEW.id,
                NEW.id,
                NEW.bio,
                NEW.hourly_rate,
                NEW.subjects_taught,
                NEW.education_level,
                NEW.teaching_experience,
                NOW(),
                NOW()
            );

            -- Log tutor creation
            INSERT INTO logs (
                user_id,
                level,
                message,
                context,
                created_at
            ) VALUES (
                NEW.id,
                'info',
                'Tutor record created for completed candidate',
                jsonb_build_object(
                    'source', 'handle_tutor_candidate_completion',
                    'action', 'tutor_created',
                    'candidate_id', NEW.id
                ),
                NOW()
            );
        END IF;

    EXCEPTION WHEN OTHERS THEN
        -- Log any errors
        error_message := SQLERRM;
        INSERT INTO logs (
            user_id,
            level,
            message,
            context,
            created_at
        ) VALUES (
            NEW.id,
            'error',
            'Error in handle_tutor_candidate_completion: ' || error_message,
            jsonb_build_object(
                'source', 'handle_tutor_candidate_completion',
                'error', error_message,
                'candidate_id', NEW.id,
                'sqlstate', SQLSTATE
            ),
            NOW()
        );

        -- Re-raise the exception to prevent the trigger from completing silently
        RAISE;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers to avoid conflicts
DROP TRIGGER IF EXISTS on_student_candidate_completed ON candidate_student;
DROP TRIGGER IF EXISTS on_student_candidate_updated ON candidate_student;
DROP TRIGGER IF EXISTS on_tutor_candidate_completed ON candidate_tutor;
DROP TRIGGER IF EXISTS on_tutor_candidate_updated ON candidate_tutor;

-- Create trigger for INSERT operations when onboarding_completed = true
CREATE TRIGGER on_student_candidate_completed
    AFTER INSERT
    ON public.candidate_student
    FOR EACH ROW
    WHEN (NEW.onboarding_completed = true)
    EXECUTE FUNCTION handle_student_candidate_completion();

-- Create trigger for UPDATE operations when onboarding_completed changes from false to true
CREATE TRIGGER on_student_candidate_updated
    AFTER UPDATE OF onboarding_completed
    ON public.candidate_student
    FOR EACH ROW
    WHEN (
        OLD.onboarding_completed IS DISTINCT FROM NEW.onboarding_completed
        AND NEW.onboarding_completed = true
    )
    EXECUTE FUNCTION handle_student_candidate_completion();

-- Create tutor triggers
-- Trigger for INSERT operations when onboarding_completed = true
CREATE TRIGGER on_tutor_candidate_completed
    AFTER INSERT
    ON public.candidate_tutor
    FOR EACH ROW
    WHEN (NEW.onboarding_completed = true)
    EXECUTE FUNCTION handle_tutor_candidate_completion();

-- Trigger for UPDATE operations when onboarding_completed changes from false to true
CREATE TRIGGER on_tutor_candidate_updated
    AFTER UPDATE OF onboarding_completed
    ON public.candidate_tutor
    FOR EACH ROW
    WHEN (
        OLD.onboarding_completed IS DISTINCT FROM NEW.onboarding_completed
        AND NEW.onboarding_completed = true
    )
    EXECUTE FUNCTION handle_tutor_candidate_completion();

-- Verify all triggers were created correctly
SELECT
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition,
    'Student: Handles INSERT when onboarding_completed = true' as description
FROM information_schema.triggers
WHERE trigger_name = 'on_student_candidate_completed'

UNION ALL

SELECT
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition,
    'Student: Handles UPDATE when onboarding_completed changes to true' as description
FROM information_schema.triggers
WHERE trigger_name = 'on_student_candidate_updated'

UNION ALL

SELECT
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition,
    'Tutor: Handles INSERT when onboarding_completed = true' as description
FROM information_schema.triggers
WHERE trigger_name = 'on_tutor_candidate_completed'

UNION ALL

SELECT
    trigger_name,
    event_manipulation,
    action_timing,
    action_condition,
    'Tutor: Handles UPDATE when onboarding_completed changes to true' as description
FROM information_schema.triggers
WHERE trigger_name = 'on_tutor_candidate_updated'

ORDER BY trigger_name;

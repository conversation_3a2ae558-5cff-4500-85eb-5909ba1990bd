import React, { useState } from "react";
import { useProfileData } from "@/hooks/useProfileData";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { Card, CardContent } from "@/components/ui/Card";
import { Badge } from "@/components/ui/Badge";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import {
  Search,
  Filter,
  ChevronRight,
  Calendar,
  BookOpen,
  Clock,
  User,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/Dialog";

// Sample student data
const studentsData = [
  {
    id: "STU-1001",
    name: "<PERSON> Johnson",
    email: "<EMAIL>",
    subjects: ["Machine Learning", "Deep Learning"],
    batch: "Fall 2023",
    batchStartDate: "2023-09-01",
    batchEndDate: "2023-12-15",
    status: "Active",
    totalSessions: 24,
    completedSessions: 10,
    upcomingSessions: 2,
    topicBreakdown: [
      { topic: "Neural Networks", subtopics: ["CNN", "RNN", "Transformers"], sessions: 8 },
      { topic: "Reinforcement Learning", subtopics: ["Q-Learning", "Policy Gradients"], sessions: 6 },
      { topic: "Natural Language Processing", subtopics: ["Text Classification", "Named Entity Recognition"], sessions: 10 },
    ],
  },
  {
    id: "STU-1002",
    name: "Bob Smith",
    email: "<EMAIL>",
    subjects: ["Data Science", "Statistics"],
    batch: "Summer 2023",
    batchStartDate: "2023-06-01",
    batchEndDate: "2023-08-30",
    status: "Completed",
    totalSessions: 18,
    completedSessions: 18,
    upcomingSessions: 0,
    topicBreakdown: [
      { topic: "Probability Theory", subtopics: ["Bayes Theorem", "Random Variables"], sessions: 6 },
      { topic: "Statistical Inference", subtopics: ["Hypothesis Testing", "Confidence Intervals"], sessions: 6 },
      { topic: "Data Visualization", subtopics: ["Matplotlib", "Seaborn"], sessions: 6 },
    ],
  },
  {
    id: "STU-1003",
    name: "Charlie Davis",
    email: "<EMAIL>",
    subjects: ["Computer Vision", "Image Processing"],
    batch: "Fall 2023",
    batchStartDate: "2023-09-01",
    batchEndDate: "2023-12-15",
    status: "Active",
    totalSessions: 20,
    completedSessions: 8,
    upcomingSessions: 1,
    topicBreakdown: [
      { topic: "Image Classification", subtopics: ["Feature Extraction", "Transfer Learning"], sessions: 8 },
      { topic: "Object Detection", subtopics: ["YOLO", "R-CNN"], sessions: 6 },
      { topic: "Image Segmentation", subtopics: ["Semantic Segmentation", "Instance Segmentation"], sessions: 6 },
    ],
  },
  {
    id: "STU-1004",
    name: "Diana Wang",
    email: "<EMAIL>",
    subjects: ["Natural Language Processing"],
    batch: "Winter 2023",
    batchStartDate: "2023-01-10",
    batchEndDate: "2023-04-20",
    status: "Paused",
    totalSessions: 15,
    completedSessions: 5,
    upcomingSessions: 0,
    topicBreakdown: [
      { topic: "Text Classification", subtopics: ["Sentiment Analysis", "Topic Modeling"], sessions: 5 },
      { topic: "Language Modeling", subtopics: ["N-grams", "Neural Language Models"], sessions: 5 },
      { topic: "Machine Translation", subtopics: ["Statistical MT", "Neural MT"], sessions: 5 },
    ],
  },
];

const StudentOverview = () => {
  const profileData = useProfileData();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [batchFilter, setBatchFilter] = useState<string[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [showTopicDialog, setShowTopicDialog] = useState(false);

  // Get unique batches and statuses for filters
  const uniqueBatches = Array.from(new Set(studentsData.map(student => student.batch)));
  const uniqueStatuses = Array.from(new Set(studentsData.map(student => student.status)));

  // Filter students based on search term and filters
  const filteredStudents = studentsData.filter(student => {
    const matchesSearch = 
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.subjects.some(subject => subject.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = statusFilter.length === 0 || statusFilter.includes(student.status);
    const matchesBatch = batchFilter.length === 0 || batchFilter.includes(student.batch);
    
    return matchesSearch && matchesStatus && matchesBatch;
  });

  // Format date to display in a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  // Get status badge with appropriate color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Active</Badge>;
      case "Completed":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Completed</Badge>;
      case "Paused":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Paused</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <TutorPageLayout
      title="Student Overview"
      profileData={profileData}
      description="View all students under your guidance and their learning progress"
    >
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search by student name, email, or subject..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                Status
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {uniqueStatuses.map(status => (
                <DropdownMenuCheckboxItem
                  key={status}
                  checked={statusFilter.includes(status)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setStatusFilter([...statusFilter, status]);
                    } else {
                      setStatusFilter(statusFilter.filter(s => s !== status));
                    }
                  }}
                >
                  {status}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Calendar size={16} />
                Batch
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {uniqueBatches.map(batch => (
                <DropdownMenuCheckboxItem
                  key={batch}
                  checked={batchFilter.includes(batch)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setBatchFilter([...batchFilter, batch]);
                    } else {
                      setBatchFilter(batchFilter.filter(b => b !== batch));
                    }
                  }}
                >
                  {batch}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Student Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredStudents.map(student => (
          <Card key={student.id} className="overflow-hidden hover:shadow-md transition-shadow">
            <CardContent className="p-0">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">{student.name}</h3>
                    <p className="text-sm text-gray-500">{student.email}</p>
                  </div>
                  {getStatusBadge(student.status)}
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <BookOpen size={16} className="text-gray-400" />
                    <div>
                      <p className="text-sm font-medium">Subjects</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {student.subjects.map(subject => (
                          <Badge key={subject} variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                            {subject}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar size={16} className="text-gray-400" />
                    <div>
                      <p className="text-sm font-medium">Batch: {student.batch}</p>
                      <p className="text-xs text-gray-500">
                        {formatDate(student.batchStartDate)} - {formatDate(student.batchEndDate)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Clock size={16} className="text-gray-400" />
                    <div>
                      <p className="text-sm font-medium">Sessions</p>
                      <div className="flex gap-4 mt-1">
                        <div>
                          <p className="text-sm font-semibold">{student.totalSessions}</p>
                          <p className="text-xs text-gray-500">Total</p>
                        </div>
                        <div>
                          <p className="text-sm font-semibold">{student.completedSessions}</p>
                          <p className="text-xs text-gray-500">Completed</p>
                        </div>
                        <div>
                          <p className="text-sm font-semibold">{student.upcomingSessions}</p>
                          <p className="text-xs text-gray-500">Upcoming</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="border-t border-gray-100 p-4 bg-gray-50 flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium">Topics & Subtopics</p>
                  <div className="flex gap-2 mt-1">
                    {student.topicBreakdown.map(topic => (
                      <Badge key={topic.topic} variant="outline" className="bg-gray-100 border-gray-200">
                        {topic.topic}: {topic.sessions}
                      </Badge>
                    )).slice(0, 2)}
                    {student.topicBreakdown.length > 2 && (
                      <Badge variant="outline" className="bg-gray-100 border-gray-200">
                        +{student.topicBreakdown.length - 2} more
                      </Badge>
                    )}
                  </div>
                </div>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-purple-600 hover:text-purple-700"
                      onClick={() => setSelectedStudent(student)}
                    >
                      View All <ChevronRight size={16} />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Topic & Subtopic Breakdown</DialogTitle>
                      <DialogDescription>
                        Detailed breakdown of topics and subtopics for {selectedStudent?.name}
                      </DialogDescription>
                    </DialogHeader>
                    {selectedStudent && (
                      <div className="space-y-4 mt-4">
                        {selectedStudent.topicBreakdown.map(topic => (
                          <div key={topic.topic} className="border rounded-lg p-4">
                            <div className="flex justify-between items-center mb-2">
                              <h4 className="font-medium">{topic.topic}</h4>
                              <Badge>{topic.sessions} sessions</Badge>
                            </div>
                            <div className="pl-4 border-l-2 border-gray-200 mt-2">
                              <p className="text-sm text-gray-500 mb-1">Subtopics:</p>
                              <div className="flex flex-wrap gap-2">
                                {topic.subtopics.map(subtopic => (
                                  <Badge key={subtopic} variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                                    {subtopic}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </TutorPageLayout>
  );
};

export default StudentOverview;

import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

type StickyNoticeProps = {
  message: string;
  type?: "info" | "success" | "warning" | "error";
  duration?: number; // in milliseconds, 0 for no auto-dismiss
  onDismiss?: () => void;
};

const StickyNotice = ({
  message,
  type = "info",
  duration = 5000,
  onDismiss,
}: StickyNoticeProps) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onDismiss) onDismiss();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, onDismiss]);

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) onDismiss();
  };

  const bgColors = {
    info: "bg-blue-50 border-blue-200",
    success: "bg-green-50 border-green-200",
    warning: "bg-yellow-50 border-yellow-200",
    error: "bg-red-50 border-red-200",
  };

  const textColors = {
    info: "text-blue-700",
    success: "text-green-700",
    warning: "text-yellow-700",
    error: "text-red-700",
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-4 py-3 rounded-md shadow-md border ${bgColors[type]} max-w-md w-full`}
        >
          <div className="flex justify-between items-center">
            <p className={`text-sm ${textColors[type]}`}>{message}</p>
            <button
              onClick={handleDismiss}
              className={`${textColors[type]} hover:bg-opacity-20 hover:bg-gray-200 rounded-full p-1`}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default StickyNotice;
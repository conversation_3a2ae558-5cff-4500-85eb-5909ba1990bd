import { create } from "zustand";

// Define the profile data interface to match your database schema
export interface ProfileData {
  firstName: string | null;
  lastName: string | null;
  email: string; // Email cannot be null
  userType: string | null;
  profilePictureUrl: string | null;
  timezone: string | null; // User's timezone in IANA format
  createdAt: Date | null;
  updatedAt: Date | null;
}

// Extended ProfileData for students
export interface StudentProfileData extends ProfileData {
  // Onboarding fields
  education_level?: string;
  subjects_of_interest?: string[];
  learning_goals?: string[];
  study_preferences?: {
    preferred_time: string;
    preferred_days: string[];
    learning_style: string;
    communication_preference: string;
  };
  academic_history?: {
    school_name: string;
    achievements: string[];
    favorite_subjects: string[];
  };

  // Additional fields
  hobbies?: string[];
  interests?: string[];
  location?: string;
}

// Extended ProfileData for tutors
export interface TutorProfileData extends ProfileData {
  // Tutor-specific fields
  educationLevel?: string;
  hourlyRate?: number;
  subjectsTaught?: string;
  teachingExperience?: string;
  bio?: string;
  availability?: any; // JSONB field
  verificationStatus?: string;
  cvFilePath?: string;
  rating?: number;
  dateOfBirth?: string | null;

  // Related data
  education?: any[]; // Education records
  availabilitySlots?: any[]; // Availability slots
  specializations?: string[];
  subjects?: string[]; // Parsed from subjectsTaught
}

// Combined ProfileData that can handle both student and tutor data
export interface CombinedProfileData extends ProfileData {
  // Student-specific fields (optional)
  education_level?: string;
  subjects_of_interest?: string[];
  learning_goals?: string[];
  study_preferences?: {
    preferred_time: string;
    preferred_days: string[];
    learning_style: string;
    communication_preference: string;
  };
  academic_history?: {
    school_name: string;
    achievements: string[];
    favorite_subjects: string[];
  };
  hobbies?: string[];
  interests?: string[];
  location?: string;

  // Tutor-specific fields (optional)
  educationLevel?: string;
  hourlyRate?: number;
  subjectsTaught?: string;
  teachingExperience?: string;
  bio?: string;
  availability?: any;
  verificationStatus?: string;
  cvFilePath?: string;
  rating?: number;
  dateOfBirth?: string | null;
  education?: any[];
  availabilitySlots?: any[];
  specializations?: string[];
  subjects?: string[];
}

// Define the profile store interface
interface ProfileStore {
  // State
  profileData: CombinedProfileData; // Using CombinedProfileData to include all possible fields
  isLoading: boolean;
  error: string | null;

  // Actions
  setProfileData: (data: Partial<CombinedProfileData>) => void;
  updateProfile: (userId: string) => Promise<void>;
  fetchStudentData: (userId: string) => Promise<any>;
  updateStudentProfile: (userId: string) => Promise<void>;
  resetProfileData: () => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// Create the store
export const useProfileStore = create<ProfileStore>((set, get) => ({
  // Initial state
  profileData: {
    firstName: null,
    lastName: null,
    email: "", // Initialize with empty string instead of null
    userType: null,
    profilePictureUrl: null,
    timezone: null,
    createdAt: null,
    updatedAt: null,
    // Student-specific fields with empty defaults
    education_level: "",
    subjects_of_interest: [],
    learning_goals: [],
    study_preferences: {
      preferred_time: "",
      preferred_days: [],
      learning_style: "",
      communication_preference: ""
    },
    academic_history: {
      school_name: "",
      grade: "",
      achievements: [],
      favorite_subjects: []
    },
    hobbies: [],
    interests: [],
    location: ""
  },
  isLoading: false,
  error: null,

  // Actions
  setProfileData: (data) => set((state) => ({
    profileData: { ...state.profileData, ...data }
  })),

  updateProfile: async (userId) => {
    const { supabase } = await import("@/lib/supabaseClient");

    set({ isLoading: true, error: null });

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("first_name, last_name, email, user_type, profile_picture_url, created_at, updated_at")
        .eq("id", userId)
        .single();

      if (error) {
        set({ error: error.message, isLoading: false });
        return;
      }

      if (data) {
        set({
          profileData: {
            ...get().profileData, // Keep existing student data
            firstName: data.first_name || null,
            lastName: data.last_name || null,
            email: data.email, // Email is required, no fallback needed
            userType: data.user_type || null,
            profilePictureUrl: data.profile_picture_url || null,
            createdAt: data.created_at ? new Date(data.created_at) : null,
            updatedAt: data.updated_at ? new Date(data.updated_at) : null,
          },
          isLoading: false
        });
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      set({
        error: error instanceof Error ? error.message : "Unknown error occurred",
        isLoading: false
      });
    }
  },

  // New method to fetch student-specific data
  fetchStudentData: async (userId) => {
    const { supabase } = await import("@/lib/supabaseClient");

    try {
      // First check if the student record exists without using .single()
      const { data: checkData, error: checkError } = await supabase
        .from("students")
        .select("id")
        .eq("id", userId);

      // If there's an error checking or no records found
      if (checkError || !checkData || checkData.length === 0) {
        console.log("No student record found during pre-check:", checkError || "No records");
        // Return null instead of throwing an error - this is a valid state
        return null;
      }

      // If we get here, we know the record exists, so we can use .single()
      const { data, error } = await supabase
        .from("students")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        console.log("Error fetching student data after confirming existence:", error);
        return null;
      }

      return data;
    } catch (error) {
      console.error("Unexpected error in fetchStudentData:", error);
      // Don't throw, just return null and let the caller handle it
      return null;
    }
  },

  // New method to update student profile data
  updateStudentProfile: async (userId) => {
    set({ isLoading: true, error: null });

    try {
      // First get the basic profile data
      await get().updateProfile(userId);

      // Then fetch student-specific data
      const studentData = await get().fetchStudentData(userId);

      // Empty defaults for student data
      const emptyStudentData = {
        education_level: "",
        subjects_of_interest: [],
        learning_goals: [],
        study_preferences: {
          preferred_time: "",
          preferred_days: [],
          learning_style: "",
          communication_preference: ""
        },
        academic_history: {
          school_name: "",
          grade: "",
          achievements: [],
          favorite_subjects: []
        },
        hobbies: [],
        interests: [],
        location: ""
      };

      if (studentData) {
        // Update the profile data with student-specific fields
        set((state) => ({
          profileData: {
            ...state.profileData,
            education_level: studentData.education_level || "",
            subjects_of_interest: studentData.subjects_of_interest || [],
            learning_goals: studentData.learning_goals || [],
            study_preferences: studentData.study_preferences || emptyStudentData.study_preferences,
            academic_history: studentData.academic_history || emptyStudentData.academic_history,
            hobbies: studentData.hobbies || [],
            interests: studentData.interests || [],
            location: studentData.location || ""
          },
          isLoading: false,
          error: null
        }));
      } else {
        // If no student data found, use empty defaults and set a user-friendly message
        set((state) => ({
          profileData: {
            ...state.profileData,
            ...emptyStudentData
          },
          isLoading: false,
          error: "No student record found. If issue persists after re-try then contact admin."
        }));
      }
    } catch (error) {
      console.error("Error updating student profile:", error);
      set({
        error: "Error loading student profile data. If issue persists after re-try then contact admin.",
        isLoading: false
      });
      // Don't re-throw the error - we've handled it here
    }
  },

  resetProfileData: () => set({
    profileData: {
      firstName: null,
      lastName: null,
      email: "", // Reset to empty string instead of null
      userType: null,
      profilePictureUrl: null,
      timezone: null,
      createdAt: null,
      updatedAt: null,
      // Reset student-specific fields to empty defaults
      education_level: "",
      subjects_of_interest: [],
      learning_goals: [],
      study_preferences: {
        preferred_time: "",
        preferred_days: [],
        learning_style: "",
        communication_preference: ""
      },
      academic_history: {
        school_name: "",
        achievements: [],
        favorite_subjects: []
      },
      hobbies: [],
      interests: [],
      location: ""
    }
  }),

  setIsLoading: (loading) => set({ isLoading: loading }),

  setError: (error) => set({ error })
}));

#!/bin/bash

# Deploy Supabase Edge Functions to Production
# Usage: ./scripts/deploy-functions.sh [PROJECT_REF]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    print_error "Supabase CLI is not installed. Please install it first."
    exit 1
fi

# Get project ref from argument or prompt
PROJECT_REF=$1
if [ -z "$PROJECT_REF" ]; then
    echo -n "Enter your Supabase project reference: "
    read PROJECT_REF
fi

if [ -z "$PROJECT_REF" ]; then
    print_error "Project reference is required"
    exit 1
fi

print_status "Deploying Edge Functions to project: $PROJECT_REF"

# List of functions to deploy
FUNCTIONS=(
    "teams-auth"
    "teams-meetings"
    "razorpay-create-order"
    "razorpay-verify-payment"
    "razorpay-refund"
)

# Deploy each function
for func in "${FUNCTIONS[@]}"; do
    print_status "Deploying function: $func"
    
    if supabase functions deploy "$func" --project-ref "$PROJECT_REF"; then
        print_status "✅ Successfully deployed $func"
    else
        print_error "❌ Failed to deploy $func"
        exit 1
    fi
    
    echo ""
done

print_status "🎉 All functions deployed successfully!"

# Remind about environment variables
print_warning "Don't forget to set the required environment variables:"
echo "  - FRONTEND_URL=https://www.rflearn.com"
echo "  - ALLOWED_ORIGINS=http://localhost:8080,https://www.rflearn.com"
echo "  - AZURE_TENANT_ID, AZURE_CLIENT_ID, AZURE_CLIENT_SECRET"
echo "  - RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET"
echo ""
echo "Use: supabase secrets set --project-ref $PROJECT_REF VARIABLE_NAME=value"

import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { CreditCard, Smartphone, Wallet, Building2 } from 'lucide-react';

interface PaymentProvider {
  name: string;
  displayName: string;
  supportedMethods: string[];
  icon: React.ReactNode;
  description: string;
}

interface PaymentProviderSelectorProps {
  availableProviders: string[];
  selectedProvider: string;
  onProviderSelect: (provider: string) => void;
  currency: string;
}

const providerConfig: Record<string, PaymentProvider> = {
  razorpay: {
    name: 'razorpay',
    displayName: 'Razorpay',
    supportedMethods: ['Card', 'UPI', 'Net Banking', 'Wallets', 'EMI'],
    icon: <CreditCard className="h-6 w-6" />,
    description: 'Secure payments with multiple options'
  },
  stripe: {
    name: 'stripe',
    displayName: 'Stripe',
    supportedMethods: ['Card', 'Apple Pay', 'Google Pay'],
    icon: <CreditCard className="h-6 w-6" />,
    description: 'International card payments'
  },
  paypal: {
    name: 'paypal',
    displayName: 'PayPal',
    supportedMethods: ['PayPal Account', 'Card'],
    icon: <Wallet className="h-6 w-6" />,
    description: 'Pay with your PayPal account'
  }
};

const PaymentProviderSelector: React.FC<PaymentProviderSelectorProps> = ({
  availableProviders,
  selectedProvider,
  onProviderSelect,
  currency
}) => {
  const getMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case 'card':
        return <CreditCard className="h-4 w-4" />;
      case 'upi':
        return <Smartphone className="h-4 w-4" />;
      case 'net banking':
        return <Building2 className="h-4 w-4" />;
      case 'wallets':
        return <Wallet className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Choose Payment Method</h3>
        <p className="text-sm text-gray-600">
          Select your preferred payment provider for {currency.toUpperCase()} payments
        </p>
      </div>

      <div className="grid gap-4">
        {availableProviders.map((providerName) => {
          const provider = providerConfig[providerName];
          if (!provider) return null;

          const isSelected = selectedProvider === providerName;

          return (
            <Card
              key={providerName}
              className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                isSelected 
                  ? 'ring-2 ring-blue-500 border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onProviderSelect(providerName)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      isSelected ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {provider.icon}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {provider.displayName}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {provider.description}
                      </p>
                    </div>
                  </div>
                  {isSelected && (
                    <Badge variant="default" className="bg-blue-500">
                      Selected
                    </Badge>
                  )}
                </div>

                <div className="mt-3">
                  <p className="text-xs text-gray-500 mb-2">Supported methods:</p>
                  <div className="flex flex-wrap gap-2">
                    {provider.supportedMethods.map((method) => (
                      <div
                        key={method}
                        className="flex items-center space-x-1 bg-gray-100 px-2 py-1 rounded text-xs"
                      >
                        {getMethodIcon(method)}
                        <span>{method}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Special badges for specific providers */}
                {providerName === 'razorpay' && currency.toLowerCase() === 'inr' && (
                  <div className="mt-2">
                    <Badge variant="secondary" className="text-xs">
                      🇮🇳 Optimized for India
                    </Badge>
                  </div>
                )}

                {providerName === 'stripe' && currency.toLowerCase() !== 'inr' && (
                  <div className="mt-2">
                    <Badge variant="secondary" className="text-xs">
                      🌍 International
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {availableProviders.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No Payment Providers Available
            </h3>
            <p className="text-gray-600">
              No payment providers are currently available for {currency.toUpperCase()} payments.
              Please contact support for assistance.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PaymentProviderSelector;

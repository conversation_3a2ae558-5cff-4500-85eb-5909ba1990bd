-- =====================================================
-- BATCHES TABLE RLS POLICIES
-- =====================================================

-- Enable RLS on batches table
ALTER TABLE batches ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view their own batches" ON batches;
DROP POLICY IF EXISTS "Tutors can view assigned batches" ON batches;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all batches" ON batches;
DROP POLICY IF EXISTS "Admins can create batches" ON batches;
DROP POLICY IF EXISTS "Admins can update all batches" ON batches;
DROP POLICY IF EXISTS "Tutors can update assigned batches" ON batches;
DROP POLICY IF EXISTS "Students can update own batches" ON batches;
DROP POLICY IF EXISTS "Admins can delete batches" ON batches;

-- Students can view their own batches
CREATE POLICY "Students can view their own batches" ON batches
    FOR SELECT USING (
        student_id = auth.uid()
    );

-- Tutors can view batches where they are assigned as default tutor
CREATE POLICY "Tutors can view assigned batches" ON batches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() 
            AND user_type = 'tutor'
            AND auth.uid() = batches.default_tutor_id
        )
    );

-- Admins can view all batches
CREATE POLICY "Admins can view all batches" ON batches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Only admins can create batches
CREATE POLICY "Admins can create batches" ON batches
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Admins can update all batches
CREATE POLICY "Admins can update all batches" ON batches
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Tutors can update their assigned batches (basic access control)
CREATE POLICY "Tutors can update assigned batches" ON batches
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() 
            AND user_type = 'tutor'
            AND auth.uid() = batches.default_tutor_id
        )
    );

-- Students can update their own batches (basic access control)
CREATE POLICY "Students can update own batches" ON batches
    FOR UPDATE USING (
        student_id = auth.uid()
    );

-- Only admins can delete batches
CREATE POLICY "Admins can delete batches" ON batches
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- =====================================================
-- SIMPLE HELPER FUNCTION FOR SUBSCRIPTIONS WITHOUT BATCHES
-- =====================================================

-- Function to get subscriptions without batches (simplified version)
CREATE OR REPLACE FUNCTION admin_get_subscriptions_without_batches()
RETURNS TABLE (
    subscription_id UUID,
    student_id UUID,
    student_name TEXT,
    student_email TEXT,
    product_id UUID,
    product_name TEXT,
    product_type TEXT,
    workflow_id UUID,
    current_period_end TIMESTAMP WITH TIME ZONE,
    days_remaining INTEGER,
    admin_assistance_required BOOLEAN,
    admin_assistance_notes TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND user_type = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    RETURN QUERY
    SELECT
        s.id,
        s.student_id,
        CONCAT(COALESCE(p.first_name, ''), ' ', COALESCE(p.last_name, '')) as student_name,
        p.email,
        s.product_id,
        COALESCE(pr.name, 'Unknown Product') as product_name,
        COALESCE(pr.type, 'unknown') as product_type,
        s.workflow_id,
        s.current_period_end,
        GREATEST(0, EXTRACT(DAY FROM (s.current_period_end - NOW()))::INTEGER) as days_remaining,
        COALESCE(sw.admin_assistance_requested, false) as admin_assistance_required,
        sw.admin_assistance_message as admin_assistance_notes
    FROM subscriptions s
    LEFT JOIN profiles p ON s.student_id = p.id
    LEFT JOIN products pr ON s.product_id = pr.id
    LEFT JOIN subscription_workflows sw ON s.workflow_id = sw.id
    WHERE s.status = 'active'
    AND s.current_period_end > NOW() -- Not expired
    AND NOT EXISTS (
        SELECT 1 FROM batches b 
        WHERE b.subscription_id = s.id
    )
    ORDER BY s.created_at DESC;
END;
$$;

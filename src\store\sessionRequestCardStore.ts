import { create } from 'zustand';

interface SessionRequestCardState {
  // Dialog states
  showAcceptDialog: boolean;
  showDeclineDialog: boolean;
  showRejectDialog: boolean;
  showProfileDialog: boolean;
  
  // Message for accept/decline/reject
  message: string;
  
  // Selected request ID for tracking which request is being acted upon
  selectedRequestId: string | null;
  
  // Actions
  setShowAcceptDialog: (show: boolean, requestId?: string) => void;
  setShowDeclineDialog: (show: boolean, requestId?: string) => void;
  setShowRejectDialog: (show: boolean, requestId?: string) => void;
  setShowProfileDialog: (show: boolean, requestId?: string) => void;
  setMessage: (message: string) => void;
  resetState: () => void;
}

export const useSessionRequestCardStore = create<SessionRequestCardState>((set) => ({
  // Initial state
  showAcceptDialog: false,
  showDeclineDialog: false,
  showRejectDialog: false,
  showProfileDialog: false,
  message: '',
  selectedRequestId: null,
  
  // Actions
  setShowAcceptDialog: (show, requestId) => set({ 
    showAcceptDialog: show,
    selectedRequestId: show ? requestId || null : null,
    // Reset message when opening dialog
    message: show ? '' : ''
  }),
  
  setShowDeclineDialog: (show, requestId) => set({ 
    showDeclineDialog: show,
    selectedRequestId: show ? requestId || null : null,
    // Reset message when opening dialog
    message: show ? '' : ''
  }),
  
  setShowRejectDialog: (show, requestId) => set({ 
    showRejectDialog: show,
    selectedRequestId: show ? requestId || null : null,
    // Reset message when opening dialog
    message: show ? '' : ''
  }),
  
  setShowProfileDialog: (show, requestId) => set({ 
    showProfileDialog: show,
    selectedRequestId: show ? requestId || null : null
  }),
  
  setMessage: (message) => set({ message }),
  
  resetState: () => set({
    showAcceptDialog: false,
    showDeclineDialog: false,
    showRejectDialog: false,
    showProfileDialog: false,
    message: '',
    selectedRequestId: null
  })
}));

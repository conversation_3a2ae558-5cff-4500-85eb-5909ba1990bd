import React, { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/Calendar";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/Card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/Select";
import { Textarea } from "@/components/ui/TextArea";
import { Badge } from "@/components/ui/Badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import { useToast } from "@/components/ui/UseToast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/Alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";
import { useSessionStore } from "@/store/sessionStore";
import { useSessionRequestStore } from "@/store/sessionRequestStore";
import { getAvailableTimeSlots, DailyAvailability, TimeSlot } from "@/services/availabilityService";
import { Calendar as CalendarIcon, Clock, AlertCircle, Info, CheckCircle, X } from "lucide-react";

interface StudentAvailabilityViewerProps {
  studentId: string;
  tutorId: string;
  batchId: string;
  onRequestSent?: () => void;
}

const StudentAvailabilityViewer: React.FC<StudentAvailabilityViewerProps> = ({
  studentId,
  tutorId,
  batchId,
  onRequestSent
}) => {
  const { toast } = useToast();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string>("");
  const [selectedSubtopic, setSelectedSubtopic] = useState<string>("");
  const [duration, setDuration] = useState<string>("60");
  const [notes, setNotes] = useState<string>("");
  const [availabilityData, setAvailabilityData] = useState<DailyAvailability[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [topics, setTopics] = useState<{ id: string; name: string; subtopics: { id: string; name: string }[] }[]>([]);
  
  const { createSessionRequest } = useSessionRequestStore();
  
  // Fetch topics and subtopics for the batch
  useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    setTopics([
      {
        id: "topic-001",
        name: "Mathematics",
        subtopics: [
          { id: "subtopic-001", name: "Algebra" },
          { id: "subtopic-002", name: "Calculus" },
          { id: "subtopic-003", name: "Geometry" }
        ]
      },
      {
        id: "topic-002",
        name: "Computer Science",
        subtopics: [
          { id: "subtopic-004", name: "Data Structures" },
          { id: "subtopic-005", name: "Algorithms" },
          { id: "subtopic-006", name: "Machine Learning" }
        ]
      }
    ]);
  }, [batchId]);
  
  // Fetch availability data when date changes
  useEffect(() => {
    if (!selectedDate) return;
    
    setIsLoading(true);
    
    // Calculate start and end dates (7 days from selected date)
    const startDate = new Date(selectedDate);
    const endDate = new Date(selectedDate);
    endDate.setDate(endDate.getDate() + 6);
    
    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];
    
    // Get available time slots
    const availableSlots = getAvailableTimeSlots(
      tutorId,
      studentId,
      startDateStr,
      endDateStr
    );
    
    setAvailabilityData(availableSlots);
    setIsLoading(false);
  }, [selectedDate, tutorId, studentId]);
  
  // Handle time slot selection
  const handleTimeSlotSelect = (timeSlot: TimeSlot) => {
    setSelectedTimeSlot(timeSlot);
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedDate || !selectedTimeSlot || !selectedTopic || !duration) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Format date and time
      const dateStr = selectedDate.toISOString().split('T')[0];
      const timeStr = selectedTimeSlot.startTime;
      
      // Create session request
      await createSessionRequest({
        batchId,
        topicId: selectedTopic,
        subtopicId: selectedSubtopic || undefined,
        requestedTutorId: tutorId,
        studentId,
        requestedDate: dateStr,
        requestedTime: timeStr,
        durationMin: parseInt(duration),
        notes,
        urgency: "medium"
      });
      
      toast({
        title: "Request sent",
        description: "Session request has been sent successfully.",
      });
      
      // Reset form
      setSelectedTimeSlot(null);
      setSelectedTopic("");
      setSelectedSubtopic("");
      setDuration("60");
      setNotes("");
      
      // Call callback if provided
      if (onRequestSent) {
        onRequestSent();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send session request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Get subtopics for selected topic
  const getSubtopicsForTopic = () => {
    const topic = topics.find(t => t.id === selectedTopic);
    return topic ? topic.subtopics : [];
  };
  
  // Get time slots for selected date
  const getTimeSlotsForSelectedDate = () => {
    if (!selectedDate) return [];
    
    const dateStr = selectedDate.toISOString().split('T')[0];
    const dailyAvailability = availabilityData.find(day => day.date === dateStr);
    
    return dailyAvailability ? dailyAvailability.timeSlots : [];
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Schedule a Session</CardTitle>
        <CardDescription>
          View student availability and schedule a session
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Calendar and time slots */}
          <div>
            <h3 className="text-sm font-medium mb-2">Select Date</h3>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border mb-4"
            />
            
            <h3 className="text-sm font-medium mb-2">Available Time Slots</h3>
            {isLoading ? (
              <div className="flex justify-center items-center h-40">
                <p>Loading availability data...</p>
              </div>
            ) : (
              <div className="grid grid-cols-3 gap-2 max-h-60 overflow-y-auto">
                {getTimeSlotsForSelectedDate().map((slot, index) => (
                  <Button
                    key={`${slot.startTime}-${index}`}
                    variant={selectedTimeSlot === slot ? "default" : "outline"}
                    className={`
                      flex items-center justify-center p-2 h-auto
                      ${!slot.isAvailable ? "opacity-50 cursor-not-allowed" : ""}
                      ${selectedTimeSlot === slot ? "bg-primary text-primary-foreground" : ""}
                    `}
                    onClick={() => slot.isAvailable && handleTimeSlotSelect(slot)}
                    disabled={!slot.isAvailable}
                  >
                    <div className="text-center">
                      <span className="text-xs block">
                        {slot.startTime} - {slot.endTime}
                      </span>
                      {!slot.isAvailable && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-xs flex items-center justify-center mt-1">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Conflict
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{slot.conflictReason}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </Button>
                ))}
                
                {getTimeSlotsForSelectedDate().length === 0 && (
                  <div className="col-span-3 text-center py-4 text-gray-500">
                    No time slots available for this date
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Right column - Session details */}
          <div>
            <h3 className="text-sm font-medium mb-2">Session Details</h3>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Topic</label>
                <Select value={selectedTopic} onValueChange={setSelectedTopic}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a topic" />
                  </SelectTrigger>
                  <SelectContent>
                    {topics.map(topic => (
                      <SelectItem key={topic.id} value={topic.id}>
                        {topic.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-1 block">Subtopic (Optional)</label>
                <Select 
                  value={selectedSubtopic} 
                  onValueChange={setSelectedSubtopic}
                  disabled={!selectedTopic}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a subtopic" />
                  </SelectTrigger>
                  <SelectContent>
                    {getSubtopicsForTopic().map(subtopic => (
                      <SelectItem key={subtopic.id} value={subtopic.id}>
                        {subtopic.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-1 block">Duration</label>
                <Select value={duration} onValueChange={setDuration}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select duration" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="45">45 minutes</SelectItem>
                    <SelectItem value="60">60 minutes</SelectItem>
                    <SelectItem value="90">90 minutes</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-1 block">Notes (Optional)</label>
                <Textarea
                  placeholder="Add any notes or specific topics to cover"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          onClick={handleSubmit}
          disabled={isLoading || !selectedDate || !selectedTimeSlot || !selectedTopic || !duration}
        >
          {isLoading ? "Sending Request..." : "Send Session Request"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default StudentAvailabilityViewer;

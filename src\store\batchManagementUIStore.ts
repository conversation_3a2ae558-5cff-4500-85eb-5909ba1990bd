// src/store/batchManagementUIStore.ts
import { create } from "zustand";

interface BatchManagementUIState {
  // UI state
  searchTerm: string;
  isCreateModalOpen: boolean;
  isDeleteDialogOpen: boolean;
  isAssignTutorDialogOpen: boolean;
  selectedBatchId: string | null;
  selectedTutorId: string;

  // Actions
  setSearchTerm: (term: string) => void;
  setIsCreateModalOpen: (isOpen: boolean) => void;
  setIsDeleteDialogOpen: (isOpen: boolean) => void;
  setIsAssignTutorDialogOpen: (isOpen: boolean) => void;
  setSelectedBatchId: (batchId: string | null) => void;
  setSelectedTutorId: (tutorId: string) => void;
  resetSelections: () => void;
}

export const useBatchManagementUIStore = create<BatchManagementUIState>((set) => ({
  // Initial state
  searchTerm: "",
  isCreateModalOpen: false,
  isDeleteDialogOpen: false,
  isAssignTutorDialogOpen: false,
  selectedBatchId: null,
  selectedTutorId: "",

  // Actions
  setSearchTerm: (term) => set({ searchTerm: term }),
  setIsCreateModalOpen: (isOpen) => set({ isCreateModalOpen: isOpen }),
  setIsDeleteDialogOpen: (isOpen) => set({ isDeleteDialogOpen: isOpen }),
  setIsAssignTutorDialogOpen: (isOpen) => set({ isAssignTutorDialogOpen: isOpen }),
  setSelectedBatchId: (batchId) => set({ selectedBatchId: batchId }),
  setSelectedTutorId: (tutorId) => set({ selectedTutorId: tutorId }),
  resetSelections: () => set({
    selectedBatchId: null,
    selectedTutorId: "",
  }),
}));

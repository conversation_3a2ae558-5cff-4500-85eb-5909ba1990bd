-- Fix RLS policies to avoid circular recursion while maintaining admin access
-- This addresses the "infinite recursion detected in policy for relation 'profiles'" error

-- =====================================================
-- STEP 1: FIX PROFILES TABLE RLS POLICIES (NO SELF-REFERENCE)
-- =====================================================

-- Drop problematic self-referencing policies on profiles table
DROP POLICY IF EXISTS "Allow admins to manage all profiles" ON profiles;
DROP POLICY IF EXISTS "Allow admins to view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;

-- Create simple, non-recursive policies for profiles table
CREATE POLICY "users_own_profile_select" ON profiles
    FOR SELECT USING (
        id = auth.uid()
    );

CREATE POLICY "users_own_profile_update" ON profiles
    FOR UPDATE USING (
        id = auth.uid()
    );

-- Admin policy using JWT claims instead of self-referencing query
-- This avoids recursion by using auth metadata instead of querying profiles
CREATE POLICY "admin_all_profiles_select" ON profiles
    FOR SELECT USING (
        (auth.jwt()::jsonb ->> 'user_metadata')::jsonb ->> 'user_type' = 'admin' OR
        (auth.jwt()::jsonb ->> 'app_metadata')::jsonb ->> 'user_type' = 'admin' OR
        auth.role() = 'service_role'
    );

CREATE POLICY "admin_all_profiles_update" ON profiles
    FOR UPDATE USING (
        (auth.jwt()::jsonb ->> 'user_metadata')::jsonb ->> 'user_type' = 'admin' OR
        (auth.jwt()::jsonb ->> 'app_metadata')::jsonb ->> 'user_type' = 'admin' OR
        auth.role() = 'service_role'
    );

CREATE POLICY "admin_all_profiles_insert" ON profiles
    FOR INSERT WITH CHECK (
        (auth.jwt()::jsonb ->> 'user_metadata')::jsonb ->> 'user_type' = 'admin' OR
        (auth.jwt()::jsonb ->> 'app_metadata')::jsonb ->> 'user_type' = 'admin' OR
        auth.role() = 'service_role'
    );

CREATE POLICY "admin_all_profiles_delete" ON profiles
    FOR DELETE USING (
        (auth.jwt()::jsonb ->> 'user_metadata')::jsonb ->> 'user_type' = 'admin' OR
        (auth.jwt()::jsonb ->> 'app_metadata')::jsonb ->> 'user_type' = 'admin' OR
        auth.role() = 'service_role'
    );

-- =====================================================
-- STEP 2: FIX INQUIRIES TABLE POLICIES (NO PROFILES REFERENCE)
-- =====================================================

-- Drop problematic policies that reference profiles table
DROP POLICY IF EXISTS "Allow admin access for deleting inquiries" ON inquiries;
DROP POLICY IF EXISTS "Allow admin access for updating inquiries" ON inquiries;
DROP POLICY IF EXISTS "Allow admin access for viewing inquiries" ON inquiries;

-- Create new policies using JWT claims instead of profiles table lookup
-- Only create if inquiries table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'inquiries') THEN
        -- Users can view inquiries by their email (inquiries table has email column, not user_id)
        EXECUTE 'CREATE POLICY "users_own_inquiries_select" ON inquiries
            FOR SELECT USING (
                email = (auth.jwt()::jsonb ->> ''email'')::text
            )';

        -- Users can insert inquiries with their email
        EXECUTE 'CREATE POLICY "users_insert_inquiries" ON inquiries
            FOR INSERT WITH CHECK (
                email = (auth.jwt()::jsonb ->> ''email'')::text
            )';

        -- Admin access using JWT claims (no profiles table reference)
        EXECUTE 'CREATE POLICY "admin_all_inquiries" ON inquiries
            FOR ALL USING (
                (auth.jwt()::jsonb ->> ''user_metadata'')::jsonb ->> ''user_type'' = ''admin'' OR
                (auth.jwt()::jsonb ->> ''app_metadata'')::jsonb ->> ''user_type'' = ''admin'' OR
                auth.role() = ''service_role''
            )';
    END IF;
END $$;

-- =====================================================
-- STEP 3: DROP AND RECREATE THE FUNCTION WITH CORRECT TYPES
-- =====================================================

-- Drop the existing function
DROP FUNCTION IF EXISTS public.get_student_complete_profile(uuid);

-- Create the corrected function with proper timestamp handling
CREATE OR REPLACE FUNCTION public.get_student_complete_profile(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    timezone text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    education_level text,
    subjects_of_interest text[],
    learning_goals text[],
    study_preferences jsonb,
    academic_history jsonb,
    hobbies text[],
    interests text[],
    location text,
    date_of_birth date,
    is_enrolled boolean,
    active_subscriptions_count bigint,
    active_subscriptions json,
    all_subscriptions json,
    earliest_subscription_end timestamp with time zone,
    latest_subscription_end timestamp with time zone,
    total_days_remaining numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        p.timezone,
        -- Explicitly cast timestamps to WITH TIME ZONE
        p.created_at::timestamp with time zone as created_at,
        p.updated_at::timestamp with time zone as updated_at,
        COALESCE(s.education_level, ''::text),
        COALESCE(s.subjects_of_interest, ARRAY[]::text[]),
        COALESCE(s.learning_goals, ARRAY[]::text[]),
        COALESCE(s.study_preferences, '{}'::jsonb),
        COALESCE(s.academic_history, '{}'::jsonb),
        COALESCE(s.hobbies, ARRAY[]::text[]),
        COALESCE(s.interests, ARRAY[]::text[]),
        COALESCE(s.location, ''::text),
        s.date_of_birth,

        -- Calculate enrollment status
        CASE
            WHEN COUNT(CASE WHEN sub.status = 'active' AND (sub.access_expires_at IS NULL OR sub.access_expires_at > NOW()) THEN 1 END) > 0
            THEN true
            ELSE false
        END as is_enrolled,

        -- Active subscriptions count
        COUNT(CASE WHEN sub.status = 'active' AND (sub.access_expires_at IS NULL OR sub.access_expires_at > NOW()) THEN 1 END) as active_subscriptions_count,

        -- Active subscriptions JSON (using subquery instead of FILTER)
        (
            SELECT COALESCE(JSON_AGG(
                JSON_BUILD_OBJECT(
                    'id', sub2.id,
                    'product_id', sub2.product_id,
                    'status', sub2.status,
                    'access_expires_at', sub2.access_expires_at::timestamp with time zone,
                    'days_remaining', CASE
                        WHEN sub2.access_expires_at IS NULL THEN NULL
                        ELSE GREATEST(0, EXTRACT(DAY FROM (sub2.access_expires_at::timestamp with time zone - NOW())))
                    END
                )
                ORDER BY sub2.created_at DESC
            ), '[]'::json)
            FROM subscriptions sub2
            WHERE sub2.student_id = p.id
            AND sub2.status = 'active'
            AND (sub2.access_expires_at IS NULL OR sub2.access_expires_at > NOW())
        ) as active_subscriptions,

        -- All subscriptions JSON (using subquery instead of FILTER)
        (
            SELECT COALESCE(JSON_AGG(
                JSON_BUILD_OBJECT(
                    'id', sub3.id,
                    'product_id', sub3.product_id,
                    'status', sub3.status,
                    'access_expires_at', sub3.access_expires_at::timestamp with time zone,
                    'days_remaining', CASE
                        WHEN sub3.access_expires_at IS NULL THEN NULL
                        ELSE GREATEST(0, EXTRACT(DAY FROM (sub3.access_expires_at::timestamp with time zone - NOW())))
                    END
                )
                ORDER BY sub3.created_at DESC
            ), '[]'::json)
            FROM subscriptions sub3
            WHERE sub3.student_id = p.id
        ) as all_subscriptions,

        -- Earliest subscription end (using CASE instead of FILTER)
        MIN(CASE WHEN sub.status = 'active' THEN sub.access_expires_at::timestamp with time zone END) as earliest_subscription_end,

        -- Latest subscription end (using CASE instead of FILTER)
        MAX(CASE WHEN sub.status = 'active' THEN sub.access_expires_at::timestamp with time zone END) as latest_subscription_end,

        -- Total days remaining
        COALESCE(
            SUM(CASE
                WHEN sub.status = 'active' AND sub.access_expires_at IS NOT NULL
                THEN GREATEST(0, EXTRACT(DAY FROM (sub.access_expires_at::timestamp with time zone - NOW())))
                ELSE 0
            END),
            0::numeric
        ) as total_days_remaining

    FROM profiles p
    LEFT JOIN students s ON p.id = s.id
    LEFT JOIN subscriptions sub ON p.id = sub.student_id
    WHERE p.id = get_student_complete_profile.student_id AND p.user_type = 'student'
    GROUP BY
        p.id, p.first_name, p.last_name, p.email, p.user_type,
        p.profile_picture_url, p.timezone, p.created_at, p.updated_at,
        s.education_level, s.subjects_of_interest, s.learning_goals,
        s.study_preferences, s.academic_history, s.hobbies, s.interests,
        s.location, s.date_of_birth;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 4: SETUP JWT CLAIMS FOR ADMIN USERS
-- =====================================================

-- For the admin policies to work, you need to set user_type in JWT claims
-- This can be done in several ways:

-- Option 1: Set in user_metadata when creating admin users
-- UPDATE auth.users
-- SET raw_user_meta_data = raw_user_meta_data || '{"user_type": "admin"}'::jsonb
-- WHERE id = 'admin-user-id';

-- Option 2: Set in app_metadata (requires service role)
-- UPDATE auth.users
-- SET raw_app_meta_data = raw_app_meta_data || '{"user_type": "admin"}'::jsonb
-- WHERE id = 'admin-user-id';

-- Option 3: Create a function to sync user_type from profiles to JWT claims
CREATE OR REPLACE FUNCTION sync_user_type_to_jwt()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the user's metadata when user_type changes in profiles
    UPDATE auth.users
    SET raw_user_meta_data = COALESCE(raw_user_meta_data, '{}'::jsonb) ||
        jsonb_build_object('user_type', NEW.user_type)
    WHERE id = NEW.id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically sync user_type to JWT claims
DROP TRIGGER IF EXISTS sync_user_type_trigger ON profiles;
CREATE TRIGGER sync_user_type_trigger
    AFTER INSERT OR UPDATE OF user_type ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION sync_user_type_to_jwt();

-- =====================================================
-- STEP 5: GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_student_complete_profile(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_student_complete_profile(uuid) TO anon;

-- =====================================================
-- STEP 3: CREATE A SIMPLER FALLBACK FUNCTION
-- =====================================================

-- Create a simpler function that just gets basic profile data
CREATE OR REPLACE FUNCTION public.get_basic_student_profile(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    education_level text,
    is_enrolled boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        p.created_at::timestamp with time zone as created_at,
        p.updated_at::timestamp with time zone as updated_at,
        COALESCE(s.education_level, ''::text),
        -- Simple enrollment check
        EXISTS(
            SELECT 1 FROM subscriptions sub
            WHERE sub.student_id = p.id
            AND sub.status = 'active'
            AND (sub.access_expires_at IS NULL OR sub.access_expires_at > NOW())
        ) as is_enrolled

    FROM profiles p
    LEFT JOIN students s ON p.id = s.id
    WHERE p.id = student_id AND p.user_type = 'student';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for the basic function
GRANT EXECUTE ON FUNCTION public.get_basic_student_profile(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_basic_student_profile(uuid) TO anon;

-- =====================================================
-- STEP 4: TEST THE FUNCTIONS
-- =====================================================

-- Test the corrected function
SELECT 'Complete Profile Function Test' as test_name;
SELECT * FROM get_student_complete_profile(auth.uid()) LIMIT 1;

-- Test the basic function
SELECT 'Basic Profile Function Test' as test_name;
SELECT * FROM get_basic_student_profile(auth.uid()) LIMIT 1;

-- =====================================================
-- STEP 6: VERIFICATION AND TESTING
-- =====================================================

-- Check RLS policies on profiles table (should have no self-references)
SELECT
    '=== PROFILES TABLE POLICIES ===' as info,
    policyname,
    cmd as command,
    CASE
        WHEN qual LIKE '%profiles%' AND policyname NOT LIKE '%admin%' THEN 'WARNING: SELF-REFERENCE DETECTED'
        WHEN qual LIKE '%auth.jwt%' THEN 'OK: USES JWT CLAIMS'
        ELSE 'OK: SIMPLE POLICY'
    END as recursion_check
FROM pg_policies
WHERE tablename = 'profiles'
ORDER BY policyname;

-- Check inquiries policies (should have no profiles references)
SELECT
    '=== INQUIRIES TABLE POLICIES ===' as info,
    policyname,
    cmd as command,
    CASE
        WHEN qual LIKE '%profiles%' THEN 'WARNING: PROFILES REFERENCE DETECTED'
        WHEN qual LIKE '%auth.jwt%' THEN 'OK: USES JWT CLAIMS'
        ELSE 'OK: SIMPLE POLICY'
    END as recursion_check
FROM pg_policies
WHERE tablename = 'inquiries'
ORDER BY policyname;

-- Test JWT claims access (for debugging)
SELECT
    'JWT Claims Test' as test_name,
    auth.uid() as current_user_id,
    (auth.jwt()::jsonb ->> 'user_metadata')::jsonb ->> 'user_type' as user_metadata_type,
    (auth.jwt()::jsonb ->> 'app_metadata')::jsonb ->> 'user_type' as app_metadata_type,
    auth.role() as current_role;

-- Check that the function exists and has correct return type
SELECT
    routine_name,
    routine_type,
    data_type,
    'Function recreated successfully' as status
FROM information_schema.routines
WHERE routine_name IN ('get_student_complete_profile', 'get_basic_student_profile')
ORDER BY routine_name;

-- Test the profiles query that was failing
SELECT 'Testing profiles access...' as test_name;
-- Uncomment to test the exact query that was failing:
-- SELECT first_name, last_name, email, user_type, profile_picture_url, created_at, updated_at
-- FROM profiles
-- WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- =====================================================
-- STEP 7: SETUP INSTRUCTIONS FOR ADMIN USERS
-- =====================================================

/*
TO SET UP ADMIN USERS:

1. AUTOMATIC SYNC (Recommended):
   The trigger function will automatically sync user_type from profiles to JWT claims.
   Just update the user_type in profiles:

   UPDATE profiles SET user_type = 'admin' WHERE id = 'user-id';

2. MANUAL SYNC (If needed):
   If you need to manually set JWT claims:

   -- For user_metadata:
   UPDATE auth.users
   SET raw_user_meta_data = COALESCE(raw_user_meta_data, '{}'::jsonb) || '{"user_type": "admin"}'::jsonb
   WHERE id = 'admin-user-id';

   -- For app_metadata (more secure, requires service role):
   UPDATE auth.users
   SET raw_app_meta_data = COALESCE(raw_app_meta_data, '{}'::jsonb) || '{"user_type": "admin"}'::jsonb
   WHERE id = 'admin-user-id';

3. VERIFY ADMIN ACCESS:
   After setting up, the admin user should be able to:
   - View all profiles via PostgREST
   - Access all inquiries
   - Perform admin operations without recursion errors

BENEFITS OF THIS APPROACH:
- No circular recursion (uses JWT claims instead of self-referencing queries)
- Maintains proper RLS for both users and admins
- Automatic synchronization between profiles.user_type and JWT claims
- Works with PostgREST and all standard database queries
- More secure than function-based admin access
*/

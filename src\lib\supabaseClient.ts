import { createClient } from "@supabase/supabase-js";
import { config, validateEnvironmentConfig } from "@/config/environment";

// Validate configuration
if (!validateEnvironmentConfig(config)) {
  throw new Error("Invalid environment configuration. Please check your environment variables.");
}

// Create Supabase client with environment-specific configuration
export const supabase = createClient(config.supabase.url, config.supabase.anonKey, {
  auth: {
    flowType: 'pkce',
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  }
});

// Log Supabase configuration in development
if (config.environment === 'development' && config.features.enableDebugLogs) {
  console.log('🔗 Supabase Client Configuration:', {
    url: config.supabase.url,
    environment: config.environment,
    oauthRedirectBase: config.oauth.redirectBaseUrl,
  });
}

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Separator } from '@/components/ui/Separator';
import LoadingSpinner from '@/components/LoadingSpinner';
import { useTeamsIntegration } from '@/hooks/useTeamsIntegration';
import { TeamsSessionData } from '@/services/teamsServiceBackend';
import { 
  Video, 
  Users, 
  Calendar, 
  Clock, 
  ExternalLink, 
  Shield,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';

interface TeamsMeetingManagerProps {
  sessionId: string;
  sessionData: {
    subject: string;
    startTime: string;
    endTime: string;
    studentEmail: string;
    tutorEmail: string;
    description?: string;
  };
  existingMeetingUrl?: string;
  onMeetingCreated?: (meetingUrl: string) => void;
  className?: string;
}

export const TeamsMeetingManager: React.FC<TeamsMeetingManagerProps> = ({
  sessionId,
  sessionData,
  existingMeetingUrl,
  onMeetingCreated,
  className = '',
}) => {
  const {
    isAuthenticated,
    isInitializing,
    isCreatingMeeting,
    authenticate,
    createAndStoreMeeting,
    signOut,
    isTeamsEnabled,
  } = useTeamsIntegration();

  const [showDetails, setShowDetails] = useState(false);

  // If Teams is not enabled, don't render the component
  if (!isTeamsEnabled) {
    return null;
  }

  const handleCreateMeeting = async () => {
    const teamsSessionData: TeamsSessionData = {
      subject: sessionData.subject,
      startTime: sessionData.startTime,
      endTime: sessionData.endTime,
      participants: [sessionData.studentEmail, sessionData.tutorEmail],
      description: sessionData.description,
    };

    const success = await createAndStoreMeeting(sessionId, teamsSessionData);
    
    if (success && onMeetingCreated) {
      // The meeting URL will be available in the database after creation
      // You might want to refetch the session data or pass the URL differently
      onMeetingCreated('Meeting created successfully');
    }
  };

  const handleJoinMeeting = () => {
    if (existingMeetingUrl) {
      window.open(existingMeetingUrl, '_blank', 'noopener,noreferrer');
    }
  };

  if (isInitializing) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <LoadingSpinner />
          <span className="ml-2">Initializing Teams integration...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Video className="h-5 w-5 text-blue-600" />
            <CardTitle>Microsoft Teams Meeting</CardTitle>
          </div>
          <Badge variant={existingMeetingUrl ? "default" : "secondary"}>
            {existingMeetingUrl ? "Ready" : "Not Set Up"}
          </Badge>
        </div>
        <CardDescription>
          Manage Teams meeting for this tutoring session
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Session Details */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span>{format(new Date(sessionData.startTime), 'PPP')}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4 text-gray-500" />
            <span>
              {format(new Date(sessionData.startTime), 'p')} - {format(new Date(sessionData.endTime), 'p')}
            </span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4 text-gray-500" />
            <span>{sessionData.studentEmail}, {sessionData.tutorEmail}</span>
          </div>
        </div>

        <Separator />

        {/* Authentication Status */}
        <div className="flex items-center gap-2">
          {isAuthenticated ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm text-green-600">Connected to Teams</span>
            </>
          ) : (
            <>
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <span className="text-sm text-orange-600">Not connected to Teams</span>
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          {existingMeetingUrl ? (
            // Meeting already exists
            <div className="space-y-2">
              <Button 
                onClick={handleJoinMeeting}
                className="w-full"
                size="lg"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Join Teams Meeting
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setShowDetails(!showDetails)}
                className="w-full"
                size="sm"
              >
                {showDetails ? 'Hide' : 'Show'} Meeting Details
              </Button>

              {showDetails && (
                <div className="bg-gray-50 p-3 rounded-lg text-sm">
                  <p className="font-medium mb-1">Meeting URL:</p>
                  <p className="text-gray-600 break-all">{existingMeetingUrl}</p>
                </div>
              )}
            </div>
          ) : (
            // No meeting exists yet
            <div className="space-y-2">
              {!isAuthenticated ? (
                <Button 
                  onClick={authenticate}
                  className="w-full"
                  variant="outline"
                >
                  <Video className="h-4 w-4 mr-2" />
                  Connect to Teams
                </Button>
              ) : (
                <Button 
                  onClick={handleCreateMeeting}
                  disabled={isCreatingMeeting}
                  className="w-full"
                  size="lg"
                >
                  {isCreatingMeeting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Meeting...
                    </>
                  ) : (
                    <>
                      <Video className="h-4 w-4 mr-2" />
                      Create Teams Meeting
                    </>
                  )}
                </Button>
              )}
            </div>
          )}

          {/* Sign Out Option */}
          {isAuthenticated && (
            <Button
              variant="ghost"
              onClick={signOut}
              className="w-full"
              size="sm"
            >
              Sign out from Teams
            </Button>
          )}
        </div>

        {/* Security Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <Shield className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium">Secure Meeting</p>
              <p className="text-blue-600">
                Teams meetings are secured with organizational authentication and lobby controls.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TeamsMeetingManager;

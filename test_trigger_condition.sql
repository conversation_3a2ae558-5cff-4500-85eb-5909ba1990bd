-- Test to verify the trigger condition issue

-- First, let's see the current state of the candidate record
SELECT 
    id,
    first_name,
    last_name,
    email,
    onboarding_completed,
    created_at,
    updated_at
FROM candidate_student 
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Test 1: Try to manually trigger by setting onboarding_completed to false first, then true
-- This should trigger the function if our theory is correct

-- Step 1: Set to false
UPDATE candidate_student 
SET onboarding_completed = false,
    updated_at = NOW()
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Verify it's false
SELECT 
    id,
    first_name,
    onboarding_completed,
    updated_at
FROM candidate_student 
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Step 2: Now set to true (this should trigger the function)
UPDATE candidate_student 
SET onboarding_completed = true,
    updated_at = NOW()
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Check if records were created
SELECT 'After trigger test - Profiles:' as check_type, COUNT(*) as count
FROM profiles 
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d'

UNION ALL

SELECT 'After trigger test - Students:' as check_type, COUNT(*) as count
FROM students 
WHERE id = '715d2b84-cc4a-443b-bee1-74e80725b21d'

UNION ALL

SELECT 'After trigger test - Logs:' as check_type, COUNT(*) as count
FROM logs 
WHERE user_id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Check the actual records if they exist
SELECT 'Profile record:' as type, p.*
FROM profiles p
WHERE p.id = '715d2b84-cc4a-443b-bee1-74e80725b21d'

UNION ALL

SELECT 'Student record:' as type, 
       s.id::text, 
       s.education_level, 
       s.subjects_of_interest::text, 
       s.learning_goals::text, 
       s.date_of_birth::text,
       s.location,
       s.created_at::text,
       s.updated_at::text
FROM students s
WHERE s.id = '715d2b84-cc4a-443b-bee1-74e80725b21d';

-- Check any logs that were created
SELECT 
    created_at,
    level,
    message,
    data,
    context
FROM logs 
WHERE user_id = '715d2b84-cc4a-443b-bee1-74e80725b21d'
ORDER BY created_at DESC;

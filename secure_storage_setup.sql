-- =====================================================
-- SECURE STORAGE SETUP - AUTHENTICATED USERS ONLY
-- =====================================================

-- This script creates a secure storage setup where only authenticated users
-- can read profile pictures, which is much better for privacy and security

-- =====================================================
-- STEP 1: CREATE OR UPDATE BUCKET (PRIVATE)
-- =====================================================

-- Create student-uploads bucket with PRIVATE access (more secure)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'student-uploads',
    'student-uploads',
    false, -- PRIVATE bucket - more secure
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO UPDATE SET
    public = false, -- Ensure it's private
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- =====================================================
-- STEP 2: DROP EXISTING CONFLICTING POLICIES
-- =====================================================

-- Remove any existing policies that might be conflicting
DROP POLICY IF EXISTS "Students can upload their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Students can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Public can view student uploads" ON storage.objects;
DROP POLICY IF EXISTS "Admins can manage all student uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to student-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to student-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated update to student-uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated delete from student-uploads" ON storage.objects;

-- =====================================================
-- STEP 3: CREATE SECURE AUTHENTICATED-ONLY POLICIES
-- =====================================================

-- Policy 1: Allow authenticated users to upload to student-uploads
CREATE POLICY "Authenticated users can upload to student-uploads" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Policy 2: Allow authenticated users to read from student-uploads
-- This is more secure than public access
CREATE POLICY "Authenticated users can read student-uploads" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Policy 3: Allow authenticated users to update files in student-uploads
CREATE POLICY "Authenticated users can update student-uploads" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Policy 4: Allow authenticated users to delete files in student-uploads
CREATE POLICY "Authenticated users can delete student-uploads" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated'
    );

-- Optional: More restrictive policy - users can only access their own files
-- Uncomment these and comment out the above if you want even more security

/*
-- Policy 2 Alternative: Users can only read their own files
CREATE POLICY "Users can read their own files only" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Policy 3 Alternative: Users can only update their own files
CREATE POLICY "Users can update their own files only" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );

-- Policy 4 Alternative: Users can only delete their own files
CREATE POLICY "Users can delete their own files only" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'student-uploads' AND
        auth.role() = 'authenticated' AND
        (storage.foldername(name))[1] = 'profiles' AND
        (storage.foldername(name))[2] = auth.uid()::text
    );
*/

-- =====================================================
-- STEP 4: GRANT PERMISSIONS (AUTHENTICATED ONLY)
-- =====================================================

-- Grant necessary permissions to authenticated users only
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- Note: We're NOT granting permissions to 'anon' users for security

-- =====================================================
-- STEP 5: VERIFICATION
-- =====================================================

-- Check bucket configuration
SELECT 
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'student-uploads';

-- Check policies
SELECT 
    policyname,
    cmd as command,
    permissive,
    roles
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects'
AND policyname LIKE '%student-uploads%'
ORDER BY policyname;

-- =====================================================
-- SECURITY BENEFITS
-- =====================================================

/*
This secure setup provides:

1. ✅ PRIVATE bucket (public = false)
2. ✅ Only authenticated users can read files
3. ✅ No anonymous access to profile pictures
4. ✅ Better privacy protection
5. ✅ Prevents unauthorized access to user images

Profile pictures will still display correctly because:
- Your app users are authenticated when viewing profiles
- Supabase automatically includes auth headers in requests
- The Avatar component will work with authenticated requests

This is much more secure than public access!
*/

/**
 * Storage utility functions for handling user-type specific storage buckets
 */

export type UserType = 'student' | 'tutor' | 'admin';

/**
 * Get the appropriate storage bucket name based on user type
 */
export const getBucketForUserType = (userType: UserType | string | null): string => {
  switch (userType) {
    case 'student':
      return 'student-uploads';
    case 'tutor':
      return 'tutor-uploads';
    case 'admin':
      return 'admin-uploads';
    default:
      // Default to student-uploads for unknown user types
      return 'student-uploads';
  }
};

/**
 * Get all supported storage buckets for a user type (including legacy buckets)
 */
export const getSupportedBucketsForUserType = (userType: UserType | string | null): string[] => {
  switch (userType) {
    case 'student':
      return ['student-uploads'];
    case 'tutor':
      return ['tutor-uploads', 'avatars']; // Support both new and legacy bucket
    case 'admin':
      return ['admin-uploads'];
    default:
      // Fallback: try all buckets for unknown user types
      return ['student-uploads', 'tutor-uploads', 'admin-uploads', 'avatars'];
  }
};

/**
 * Generate the file path for profile pictures based on user type and user ID
 */
export const generateProfilePicturePath = (
  userType: UserType | string | null,
  userId: string,
  fileName: string
): string => {
  return `profiles/${userId}/${fileName}`;
};

/**
 * Generate the file path for CV files based on user type and user ID
 */
export const generateCVFilePath = (
  userType: UserType | string | null,
  userId: string,
  fileName: string
): string => {
  return `cv/${userId}/${fileName}`;
};

/**
 * Get the full storage URL for a file
 */
export const getStorageUrl = (
  userType: UserType | string | null,
  filePath: string,
  supabaseUrl: string
): string => {
  const bucket = getBucketForUserType(userType);
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${filePath}`;
};

/**
 * Extract bucket and file path from a storage URL
 */
export const parseStorageUrl = (url: string): { bucket: string; filePath: string } | null => {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    
    // Find the bucket in the URL
    const allBuckets = ['student-uploads', 'tutor-uploads', 'admin-uploads', 'avatars'];
    let bucketIndex = -1;
    let foundBucket = '';
    
    for (const bucket of allBuckets) {
      bucketIndex = pathParts.findIndex(part => part === bucket);
      if (bucketIndex !== -1) {
        foundBucket = bucket;
        break;
      }
    }
    
    if (bucketIndex === -1) {
      return null;
    }
    
    const filePath = pathParts.slice(bucketIndex + 1).join('/');
    
    return {
      bucket: foundBucket,
      filePath
    };
  } catch (error) {
    console.error('Error parsing storage URL:', error);
    return null;
  }
};

/**
 * Check if a URL is a Supabase storage URL
 */
export const isStorageUrl = (url: string): boolean => {
  return url.includes('/storage/v1/object/');
};

/**
 * Validate file for profile picture upload
 */
export const validateProfilePictureFile = (file: File): { valid: boolean; error?: string } => {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size must be less than 5MB'
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'File must be a JPEG, PNG, JPG, or WebP image'
    };
  }

  return { valid: true };
};

/**
 * Validate file for CV upload
 */
export const validateCVFile = (file: File): { valid: boolean; error?: string } => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size must be less than 10MB'
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'File must be a PDF, DOC, or DOCX document'
    };
  }

  return { valid: true };
};

/**
 * Generate a unique filename for uploads
 */
export const generateUniqueFileName = (originalFileName: string): string => {
  const fileExt = originalFileName.split('.').pop();
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  return `${timestamp}-${randomString}.${fileExt}`;
};

import React from 'react';
import ProfileTimezoneSettings from './ProfileTimezoneSettings';
import TimezoneSelect from './TimezoneSelect';

// Example of how to use the ProfileTimezoneSettings component in a profile page
const ProfileSettingsPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Profile Settings</h1>

      {/* Other profile settings sections */}
      <div className="mb-6">
        {/* Personal information, etc. */}
      </div>

      {/* Timezone settings section */}
      <ProfileTimezoneSettings
        className="mb-6"
        onSaved={() => console.log('Timezone saved successfully')}
      />

      {/* Other profile settings sections */}
      <div className="mb-6">
        {/* Notification preferences, etc. */}
      </div>
    </div>
  );
};

// Example of using multiple TimezoneSelect components with different instance IDs
const MultipleTimezoneExample: React.FC = () => {
  const [userTimezone, setUserTimezone] = React.useState('');
  const [meetingTimezone, setMeetingTimezone] = React.useState('');

  return (
    <div className="space-y-4">
      <TimezoneSelect
        value={userTimezone}
        onChange={setUserTimezone}
        label="Your Timezone"
        instanceId="user-timezone"
        showCurrentTime={true}
      />

      <TimezoneSelect
        value={meetingTimezone}
        onChange={setMeetingTimezone}
        label="Meeting Timezone"
        instanceId="meeting-timezone"
        showCurrentTime={true}
      />
    </div>
  );
};

export default ProfileSettingsPage;
export { MultipleTimezoneExample };

// Note: This is just an example file showing how to use the components.
// You should integrate these components into your actual profile settings pages.

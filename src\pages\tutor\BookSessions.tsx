import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/Select";
import { Badge } from "@/components/ui/Badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/Tabs";
import { Calendar } from "@/components/ui/Calendar";
import { Textarea } from "@/components/ui/TextArea";
import { useToast } from "@/components/ui/UseToast";
import { Alert, AlertDescription } from "@/components/ui/Alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/Tooltip";
import TutorPageLayout from "@/components/layouts/TutorPageLayout";
import { useProfileData } from "@/hooks/useProfileData";
import { useAuth } from "@/context/AuthContext";
import { useTutorScheduleStore } from "@/stores/useTutorScheduleStore";
import { useSessionRequestStore } from "@/store/sessionRequestStore";
import { getAvailableTimeSlots, DailyAvailability, TimeSlot } from "@/services/availabilityService";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  Search,
  Calendar as CalendarIcon,
  Clock,
  Users,
  BookOpen,
  Filter,
  AlertCircle,
  CheckCircle,
  User,
  GraduationCap,
  MapPin,
  Package,
} from "lucide-react";

interface AssignedStudent {
  id: string;
  name: string;
  email: string;
  batchName: string;
  batchId: string;
  packageName: string;
  remainingSessions?: number;
  profileImage?: string;
  subjects: string[];
  topics: string[];
  enrollmentDate: string;
  lastSessionDate?: string;
}

interface BookingFormData {
  studentId: string;
  selectedDate: Date | undefined;
  selectedTimeSlot: TimeSlot | null;
  selectedTopic: string;
  selectedSubtopic: string;
  duration: string;
  notes: string;
}

const TutorBookSessionsPage: React.FC = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const profileData = useProfileData();

  // State for students and filtering
  const [assignedStudents, setAssignedStudents] = useState<AssignedStudent[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<AssignedStudent[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBatch, setSelectedBatch] = useState<string>("all");
  const [selectedSubject, setSelectedSubject] = useState<string>("all");
  const [selectedTopic, setSelectedTopic] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  // State for booking form
  const [selectedStudent, setSelectedStudent] = useState<AssignedStudent | null>(null);
  const [bookingData, setBookingData] = useState<BookingFormData>({
    studentId: "",
    selectedDate: undefined,
    selectedTimeSlot: null,
    selectedTopic: "",
    selectedSubtopic: "",
    duration: "60",
    notes: "",
  });

  // State for availability
  const [availabilityData, setAvailabilityData] = useState<DailyAvailability[]>([]);
  const [availabilityLoading, setAvailabilityLoading] = useState(false);

  // State for topics
  const [topics, setTopics] = useState<{ id: string; name: string; subtopics: { id: string; name: string }[] }[]>([]);

  const { createSessionRequest } = useSessionRequestStore();
  const { students, fetchStudents } = useTutorScheduleStore();

  // Fetch assigned students
  useEffect(() => {
    const fetchAssignedStudents = async () => {
      setIsLoading(true);
      try {
        // Get tutor ID from auth user
        const tutorId = user?.id;

        if (tutorId) {
          // Fetch students assigned to this tutor using Zustand store
          await fetchStudents(tutorId);
        }

        // Simulate additional student data (in real app, this would come from API)
        await new Promise(resolve => setTimeout(resolve, 1000));

        const mockStudents: AssignedStudent[] = [
          {
            id: "student-001",
            name: "Alice Johnson",
            email: "<EMAIL>",
            batchName: "Advanced Mathematics Batch A",
            batchId: "batch-001",
            packageName: "Premium Package",
            remainingSessions: 8,
            profileImage: "/api/placeholder/40/40",
            subjects: ["Mathematics", "Physics"],
            topics: ["Calculus", "Linear Algebra", "Quantum Physics"],
            enrollmentDate: "2024-01-15",
            lastSessionDate: "2024-01-20",
          },
          {
            id: "student-002",
            name: "Bob Chen",
            email: "<EMAIL>",
            batchName: "CS Fundamentals Batch B",
            batchId: "batch-002",
            packageName: "Standard Package",
            remainingSessions: 12,
            profileImage: "/api/placeholder/40/40",
            subjects: ["Computer Science", "Data Science"],
            topics: ["Machine Learning", "Algorithms", "Python"],
            enrollmentDate: "2024-01-10",
            lastSessionDate: "2024-01-18",
          },
          {
            id: "student-003",
            name: "Carol Rodriguez",
            email: "<EMAIL>",
            batchName: "Science Prep Batch C",
            batchId: "batch-003",
            packageName: "Premium Package",
            remainingSessions: 6,
            profileImage: "/api/placeholder/40/40",
            subjects: ["Chemistry", "Biology"],
            topics: ["Organic Chemistry", "Molecular Biology", "Biochemistry"],
            enrollmentDate: "2024-01-05",
            lastSessionDate: "2024-01-22",
          },
          {
            id: "student-004",
            name: "David Kim",
            email: "<EMAIL>",
            batchName: "Advanced Mathematics Batch A",
            batchId: "batch-001",
            packageName: "Standard Package",
            remainingSessions: 4,
            profileImage: "/api/placeholder/40/40",
            subjects: ["Mathematics", "Statistics"],
            topics: ["Statistics", "Probability", "Data Analysis"],
            enrollmentDate: "2024-01-12",
            lastSessionDate: "2024-01-19",
          },
          {
            id: "student-005",
            name: "Emma Wilson",
            email: "<EMAIL>",
            batchName: "CS Fundamentals Batch B",
            batchId: "batch-002",
            packageName: "Premium Package",
            remainingSessions: 10,
            profileImage: "/api/placeholder/40/40",
            subjects: ["Computer Science", "Web Development"],
            topics: ["React", "Node.js", "Database Design"],
            enrollmentDate: "2024-01-08",
            lastSessionDate: "2024-01-21",
          },
        ];

        setAssignedStudents(mockStudents);
        setFilteredStudents(mockStudents);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load assigned students.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignedStudents();
  }, [toast, user?.id, fetchStudents]);

  // Mock topics data
  useEffect(() => {
    setTopics([
      {
        id: "topic-001",
        name: "Mathematics",
        subtopics: [
          { id: "subtopic-001", name: "Algebra" },
          { id: "subtopic-002", name: "Calculus" },
          { id: "subtopic-003", name: "Geometry" },
          { id: "subtopic-004", name: "Statistics" },
          { id: "subtopic-005", name: "Linear Algebra" },
        ]
      },
      {
        id: "topic-002",
        name: "Computer Science",
        subtopics: [
          { id: "subtopic-006", name: "Data Structures" },
          { id: "subtopic-007", name: "Algorithms" },
          { id: "subtopic-008", name: "Machine Learning" },
          { id: "subtopic-009", name: "Web Development" },
          { id: "subtopic-010", name: "Database Design" },
        ]
      },
      {
        id: "topic-003",
        name: "Physics",
        subtopics: [
          { id: "subtopic-011", name: "Mechanics" },
          { id: "subtopic-012", name: "Thermodynamics" },
          { id: "subtopic-013", name: "Quantum Physics" },
          { id: "subtopic-014", name: "Electromagnetism" },
        ]
      },
      {
        id: "topic-004",
        name: "Chemistry",
        subtopics: [
          { id: "subtopic-015", name: "Organic Chemistry" },
          { id: "subtopic-016", name: "Inorganic Chemistry" },
          { id: "subtopic-017", name: "Physical Chemistry" },
          { id: "subtopic-018", name: "Biochemistry" },
        ]
      },
      {
        id: "topic-005",
        name: "Biology",
        subtopics: [
          { id: "subtopic-019", name: "Molecular Biology" },
          { id: "subtopic-020", name: "Cell Biology" },
          { id: "subtopic-021", name: "Genetics" },
          { id: "subtopic-022", name: "Ecology" },
        ]
      },
    ]);
  }, []);

  // Filter students based on search and filters
  useEffect(() => {
    let filtered = assignedStudents;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(student =>
        student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.subjects.some(subject => subject.toLowerCase().includes(searchQuery.toLowerCase())) ||
        student.topics.some(topic => topic.toLowerCase().includes(searchQuery.toLowerCase())) ||
        student.batchName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Batch filter
    if (selectedBatch !== "all") {
      filtered = filtered.filter(student => student.batchId === selectedBatch);
    }

    // Subject filter
    if (selectedSubject !== "all") {
      filtered = filtered.filter(student => student.subjects.includes(selectedSubject));
    }

    // Topic filter
    if (selectedTopic !== "all") {
      filtered = filtered.filter(student => student.topics.some(topic => topic.toLowerCase().includes(selectedTopic.toLowerCase())));
    }

    setFilteredStudents(filtered);
  }, [assignedStudents, searchQuery, selectedBatch, selectedSubject, selectedTopic]);

  // Fetch availability when student and date are selected
  useEffect(() => {
    if (!selectedStudent || !bookingData.selectedDate) return;

    setAvailabilityLoading(true);

    // Calculate start and end dates (7 days from selected date)
    const startDate = new Date(bookingData.selectedDate);
    const endDate = new Date(bookingData.selectedDate);
    endDate.setDate(endDate.getDate() + 6);

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    // Get available time slots
    const availableSlots = getAvailableTimeSlots(
      user?.id || "",
      selectedStudent.id,
      startDateStr,
      endDateStr
    );

    setAvailabilityData(availableSlots);
    setAvailabilityLoading(false);
  }, [selectedStudent, bookingData.selectedDate, user?.id]);

  // Get unique batches, subjects, and topics for filters
  const uniqueBatches = Array.from(new Set(assignedStudents.map(student => student.batchName)));
  const uniqueSubjects = Array.from(new Set(assignedStudents.flatMap(student => student.subjects)));
  const uniqueTopics = Array.from(new Set(assignedStudents.flatMap(student => student.topics)));

  // Handle student selection
  const handleStudentSelect = (student: AssignedStudent) => {
    setSelectedStudent(student);
    setBookingData(prev => ({
      ...prev,
      studentId: student.id,
      selectedDate: undefined,
      selectedTimeSlot: null,
      selectedTopic: "",
      selectedSubtopic: "",
    }));
  };

  // Handle time slot selection
  const handleTimeSlotSelect = (timeSlot: TimeSlot) => {
    setBookingData(prev => ({
      ...prev,
      selectedTimeSlot: timeSlot,
    }));
  };

  // Get time slots for selected date
  const getTimeSlotsForSelectedDate = () => {
    if (!bookingData.selectedDate) return [];

    const dateStr = bookingData.selectedDate.toISOString().split('T')[0];
    const dailyAvailability = availabilityData.find(day => day.date === dateStr);

    return dailyAvailability ? dailyAvailability.timeSlots : [];
  };

  // Get subtopics for selected topic
  const getSubtopicsForTopic = () => {
    const topic = topics.find(t => t.id === bookingData.selectedTopic);
    return topic ? topic.subtopics : [];
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedStudent || !bookingData.selectedDate || !bookingData.selectedTimeSlot || !bookingData.selectedTopic || !bookingData.duration) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setAvailabilityLoading(true);

    try {
      // Format date and time
      const dateStr = bookingData.selectedDate.toISOString().split('T')[0];
      const timeStr = bookingData.selectedTimeSlot.startTime;

      // Create session request
      await createSessionRequest({
        batchId: selectedStudent.batchId,
        topicId: bookingData.selectedTopic,
        subtopicId: bookingData.selectedSubtopic || undefined,
        requestedTutorId: user?.id || "",
        studentId: selectedStudent.id,
        requestedDate: dateStr,
        requestedTime: timeStr,
        durationMin: parseInt(bookingData.duration),
        notes: bookingData.notes,
        urgency: "medium"
      });

      toast({
        title: "Request sent",
        description: `Session request sent to ${selectedStudent.name} successfully.`,
      });

      // Reset form
      setBookingData(prev => ({
        ...prev,
        selectedDate: undefined,
        selectedTimeSlot: null,
        selectedTopic: "",
        selectedSubtopic: "",
        duration: "60",
        notes: "",
      }));

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send session request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAvailabilityLoading(false);
    }
  };

  return (
    <TutorPageLayout
      title="Book Sessions"
      profileData={profileData}
      description="Find your assigned students and book sessions with them."
    >
      <div className="space-y-6">
        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2" />
              Find Your Students
            </CardTitle>
            <CardDescription>
              Search and filter through your assigned students to book sessions.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Search</label>
                <Input
                  placeholder="Search by name, email, subject, or topic..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Batch</label>
                <Select value={selectedBatch} onValueChange={setSelectedBatch}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Batches" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Batches</SelectItem>
                    {uniqueBatches.map(batch => (
                      <SelectItem key={batch} value={assignedStudents.find(s => s.batchName === batch)?.batchId || ""}>
                        {batch}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Subject</label>
                <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Subjects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Subjects</SelectItem>
                    {uniqueSubjects.map(subject => (
                      <SelectItem key={subject} value={subject}>
                        {subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-1 block">Topic</label>
                <Select value={selectedTopic} onValueChange={setSelectedTopic}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Topics" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Topics</SelectItem>
                    {uniqueTopics.map(topic => (
                      <SelectItem key={topic} value={topic}>
                        {topic}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedBatch("all");
                    setSelectedSubject("all");
                    setSelectedTopic("all");
                  }}
                  className="w-full"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Student List */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Your Assigned Students ({filteredStudents.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center h-40">
                    <LoadingSpinner />
                  </div>
                ) : filteredStudents.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No students found matching your criteria.</p>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {filteredStudents.map((student) => (
                      <div
                        key={student.id}
                        className={`
                          p-4 border rounded-lg cursor-pointer transition-all
                          ${selectedStudent?.id === student.id
                            ? "border-primary bg-primary/5"
                            : "border-gray-200 hover:border-gray-300"
                          }
                        `}
                        onClick={() => handleStudentSelect(student)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                              <User className="h-5 w-5 text-gray-500" />
                            </div>
                            <div>
                              <h3 className="font-medium">{student.name}</h3>
                              <p className="text-sm text-gray-500">{student.email}</p>
                              <div className="flex items-center mt-1">
                                <MapPin className="h-3 w-3 mr-1 text-gray-400" />
                                <span className="text-xs text-gray-500">{student.batchName}</span>
                              </div>
                              <div className="flex items-center mt-1">
                                <Package className="h-3 w-3 mr-1 text-gray-400" />
                                <span className="text-xs text-gray-500">{student.packageName}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            {student.remainingSessions && (
                              <Badge variant="secondary" className="text-xs mb-1">
                                {student.remainingSessions} sessions left
                              </Badge>
                            )}
                            {student.lastSessionDate && (
                              <p className="text-xs text-gray-400">
                                Last: {new Date(student.lastSessionDate).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="mt-3">
                          <div className="flex flex-wrap gap-1 mb-2">
                            {student.subjects.map((subject) => (
                              <Badge key={subject} variant="outline" className="text-xs">
                                {subject}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {student.topics.slice(0, 3).map((topic) => (
                              <Badge key={topic} variant="secondary" className="text-xs">
                                {topic}
                              </Badge>
                            ))}
                            {student.topics.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{student.topics.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Booking Form */}
          <div>
            {selectedStudent ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CalendarIcon className="h-5 w-5 mr-2" />
                    Book Session with {selectedStudent.name}
                  </CardTitle>
                  <CardDescription>
                    Select a date, time, and session details to book your session.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="datetime" className="space-y-4">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="datetime">Date & Time</TabsTrigger>
                      <TabsTrigger value="details">Session Details</TabsTrigger>
                    </TabsList>

                    <TabsContent value="datetime" className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium mb-2">Select Date</h3>
                        <Calendar
                          mode="single"
                          selected={bookingData.selectedDate}
                          onSelect={(date) => setBookingData(prev => ({ ...prev, selectedDate: date, selectedTimeSlot: null }))}
                          className="rounded-md border"
                        />
                      </div>

                      {bookingData.selectedDate && (
                        <div>
                          <h3 className="text-sm font-medium mb-2">Available Time Slots</h3>
                          {availabilityLoading ? (
                            <div className="flex justify-center items-center h-20">
                              <LoadingSpinner />
                            </div>
                          ) : (
                            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                              {getTimeSlotsForSelectedDate().map((slot, index) => (
                                <Button
                                  key={`${slot.startTime}-${index}`}
                                  variant={bookingData.selectedTimeSlot === slot ? "default" : "outline"}
                                  className={`
                                    flex items-center justify-center p-2 h-auto text-xs
                                    ${!slot.isAvailable ? "opacity-50 cursor-not-allowed" : ""}
                                  `}
                                  onClick={() => slot.isAvailable && handleTimeSlotSelect(slot)}
                                  disabled={!slot.isAvailable}
                                >
                                  <div className="text-center">
                                    <span className="block">
                                      {slot.startTime} - {slot.endTime}
                                    </span>
                                    {!slot.isAvailable && (
                                      <span className="text-xs flex items-center justify-center mt-1">
                                        <AlertCircle className="h-3 w-3 mr-1" />
                                        Busy
                                      </span>
                                    )}
                                  </div>
                                </Button>
                              ))}

                              {getTimeSlotsForSelectedDate().length === 0 && (
                                <div className="col-span-2 text-center py-4 text-gray-500 text-sm">
                                  No time slots available for this date
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="details" className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">Topic *</label>
                        <Select
                          value={bookingData.selectedTopic}
                          onValueChange={(value) => setBookingData(prev => ({ ...prev, selectedTopic: value, selectedSubtopic: "" }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a topic" />
                          </SelectTrigger>
                          <SelectContent>
                            {topics.map(topic => (
                              <SelectItem key={topic.id} value={topic.id}>
                                {topic.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Subtopic (Optional)</label>
                        <Select
                          value={bookingData.selectedSubtopic}
                          onValueChange={(value) => setBookingData(prev => ({ ...prev, selectedSubtopic: value }))}
                          disabled={!bookingData.selectedTopic}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a subtopic" />
                          </SelectTrigger>
                          <SelectContent>
                            {getSubtopicsForTopic().map(subtopic => (
                              <SelectItem key={subtopic.id} value={subtopic.id}>
                                {subtopic.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Duration *</label>
                        <Select
                          value={bookingData.duration}
                          onValueChange={(value) => setBookingData(prev => ({ ...prev, duration: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select duration" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="30">30 minutes</SelectItem>
                            <SelectItem value="45">45 minutes</SelectItem>
                            <SelectItem value="60">60 minutes</SelectItem>
                            <SelectItem value="90">90 minutes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">Notes (Optional)</label>
                        <Textarea
                          placeholder="Add any notes or specific topics to cover"
                          value={bookingData.notes}
                          onChange={(e) => setBookingData(prev => ({ ...prev, notes: e.target.value }))}
                          className="min-h-[80px]"
                        />
                      </div>
                    </TabsContent>
                  </Tabs>

                  <div className="mt-6 pt-4 border-t">
                    <Button
                      onClick={handleSubmit}
                      disabled={
                        availabilityLoading ||
                        !bookingData.selectedDate ||
                        !bookingData.selectedTimeSlot ||
                        !bookingData.selectedTopic ||
                        !bookingData.duration
                      }
                      className="w-full"
                    >
                      {availabilityLoading ? "Sending Request..." : "Send Session Request"}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center h-96 text-center">
                  <BookOpen className="h-16 w-16 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">Select a Student</h3>
                  <p className="text-gray-500">
                    Choose a student from the list to start booking a session.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </TutorPageLayout>
  );
};

export default TutorBookSessionsPage;

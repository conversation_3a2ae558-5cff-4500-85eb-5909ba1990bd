-- Payment Integration Database Functions
-- This file contains stored procedures and functions for payment processing

-- =====================================================
-- 1. UTILITY FUNCTIONS
-- =====================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_payments_updated_at ON payments;
CREATE TRIGGER update_payments_updated_at
    BEFORE UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subscriptions_updated_at ON subscriptions;
CREATE TRIGGER update_subscriptions_updated_at
    BEFORE UPDATE ON subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_invoices_updated_at ON invoices;
CREATE TRIGGER update_invoices_updated_at
    BEFORE UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payment_methods_updated_at ON payment_methods;
CREATE TRIGGER update_payment_methods_updated_at
    BEFORE UPDATE ON payment_methods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 2. PAYMENT CREATION FUNCTIONS
-- =====================================================

-- Function to create a payment record
CREATE OR REPLACE FUNCTION create_payment_record(
    p_workflow_id UUID,
    p_student_id UUID,
    p_stripe_payment_intent_id TEXT,
    p_amount DECIMAL(10,2),
    p_currency TEXT DEFAULT 'usd',
    p_description TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    payment_id UUID;
BEGIN
    INSERT INTO payments (
        workflow_id,
        student_id,
        stripe_payment_intent_id,
        amount,
        currency,
        status,
        description
    ) VALUES (
        p_workflow_id,
        p_student_id,
        p_stripe_payment_intent_id,
        p_amount,
        p_currency,
        'pending',
        p_description
    ) RETURNING id INTO payment_id;

    -- Update workflow with payment information
    UPDATE subscription_workflows
    SET
        stripe_payment_intent_id = p_stripe_payment_intent_id,
        payment_status = 'pending',
        total_amount = p_amount,
        currency = p_currency
    WHERE id = p_workflow_id;

    RETURN payment_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update payment status
CREATE OR REPLACE FUNCTION update_payment_status(
    p_stripe_payment_intent_id TEXT,
    p_status TEXT,
    p_stripe_charge_id TEXT DEFAULT NULL,
    p_failure_code TEXT DEFAULT NULL,
    p_failure_message TEXT DEFAULT NULL,
    p_receipt_url TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    payment_record RECORD;
BEGIN
    -- Update payment record
    UPDATE payments
    SET
        status = p_status,
        stripe_charge_id = p_stripe_charge_id,
        failure_code = p_failure_code,
        failure_message = p_failure_message,
        receipt_url = p_receipt_url,
        succeeded_at = CASE WHEN p_status = 'succeeded' THEN NOW() ELSE succeeded_at END,
        failed_at = CASE WHEN p_status = 'failed' THEN NOW() ELSE failed_at END
    WHERE stripe_payment_intent_id = p_stripe_payment_intent_id
    RETURNING * INTO payment_record;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Update workflow status
    UPDATE subscription_workflows
    SET
        payment_status = p_status,
        payment_completed_at = CASE WHEN p_status = 'succeeded' THEN NOW() ELSE payment_completed_at END,
        status = CASE
            WHEN p_status = 'succeeded' THEN 'completed'
            WHEN p_status = 'failed' THEN 'in_progress' -- Allow retry
            ELSE status
        END
    WHERE stripe_payment_intent_id = p_stripe_payment_intent_id;

    -- If payment succeeded, create/activate subscription
    IF p_status = 'succeeded' THEN
        PERFORM activate_subscription_for_workflow(payment_record.workflow_id);
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. SUBSCRIPTION MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to activate subscription after successful payment
CREATE OR REPLACE FUNCTION activate_subscription_for_workflow(
    p_workflow_id UUID
)
RETURNS UUID AS $$
DECLARE
    workflow_record RECORD;
    product_record RECORD;
    subscription_id UUID;
    access_end_date TIMESTAMP;
BEGIN
    -- Get workflow and product information
    SELECT sw.*, p.duration_days, p.price, p.type
    INTO workflow_record
    FROM subscription_workflows sw
    JOIN products p ON p.id = sw.product_id
    WHERE sw.id = p_workflow_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Workflow not found: %', p_workflow_id;
    END IF;

    -- Calculate access end date
    access_end_date := NOW() + INTERVAL '1 day' * workflow_record.duration_days;

    -- Create subscription record
    INSERT INTO subscriptions (
        student_id,
        product_id,
        workflow_id,
        stripe_customer_id,
        status,
        current_period_start,
        current_period_end,
        amount,
        currency,
        interval_type,
        access_granted_at,
        access_expires_at
    ) VALUES (
        workflow_record.student_id,
        workflow_record.product_id,
        p_workflow_id,
        workflow_record.stripe_customer_id,
        'active',
        NOW(),
        access_end_date,
        workflow_record.total_amount,
        workflow_record.currency,
        'one_time', -- Most products are one-time purchases
        NOW(),
        access_end_date
    ) RETURNING id INTO subscription_id;

    RETURN subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user has active subscription for product
CREATE OR REPLACE FUNCTION has_active_subscription(
    p_student_id UUID,
    p_product_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO subscription_count
    FROM subscriptions
    WHERE student_id = p_student_id
    AND product_id = p_product_id
    AND status = 'active'
    AND (access_expires_at IS NULL OR access_expires_at > NOW());

    RETURN subscription_count > 0;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. PAYMENT METHOD FUNCTIONS
-- =====================================================

-- Function to save payment method
CREATE OR REPLACE FUNCTION save_payment_method(
    p_student_id UUID,
    p_stripe_payment_method_id TEXT,
    p_stripe_customer_id TEXT,
    p_type TEXT,
    p_card_brand TEXT DEFAULT NULL,
    p_card_last4 TEXT DEFAULT NULL,
    p_card_exp_month INTEGER DEFAULT NULL,
    p_card_exp_year INTEGER DEFAULT NULL,
    p_is_default BOOLEAN DEFAULT FALSE
)
RETURNS UUID AS $$
DECLARE
    payment_method_id UUID;
BEGIN
    -- If this is set as default, unset other defaults
    IF p_is_default THEN
        UPDATE payment_methods
        SET is_default = FALSE
        WHERE student_id = p_student_id AND is_default = TRUE;
    END IF;

    -- Insert new payment method
    INSERT INTO payment_methods (
        student_id,
        stripe_payment_method_id,
        stripe_customer_id,
        type,
        card_brand,
        card_last4,
        card_exp_month,
        card_exp_year,
        is_default
    ) VALUES (
        p_student_id,
        p_stripe_payment_method_id,
        p_stripe_customer_id,
        p_type,
        p_card_brand,
        p_card_last4,
        p_card_exp_month,
        p_card_exp_year,
        p_is_default
    ) RETURNING id INTO payment_method_id;

    RETURN payment_method_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. REPORTING FUNCTIONS
-- =====================================================

-- Function to get payment summary for student
CREATE OR REPLACE FUNCTION get_student_payment_summary(
    p_student_id UUID
)
RETURNS TABLE (
    total_payments BIGINT,
    total_amount DECIMAL(10,2),
    successful_payments BIGINT,
    failed_payments BIGINT,
    active_subscriptions BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(p.id) as total_payments,
        COALESCE(SUM(p.amount), 0) as total_amount,
        COUNT(CASE WHEN p.status = 'succeeded' THEN 1 END) as successful_payments,
        COUNT(CASE WHEN p.status = 'failed' THEN 1 END) as failed_payments,
        (SELECT COUNT(*) FROM subscriptions s WHERE s.student_id = p_student_id AND s.status = 'active') as active_subscriptions
    FROM payments p
    WHERE p.student_id = p_student_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get revenue summary
CREATE OR REPLACE FUNCTION get_revenue_summary(
    p_start_date TIMESTAMP DEFAULT NULL,
    p_end_date TIMESTAMP DEFAULT NULL
)
RETURNS TABLE (
    total_revenue DECIMAL(10,2),
    successful_payments BIGINT,
    failed_payments BIGINT,
    average_order_value DECIMAL(10,2)
) AS $$
DECLARE
    start_date TIMESTAMP := COALESCE(p_start_date, NOW() - INTERVAL '30 days');
    end_date TIMESTAMP := COALESCE(p_end_date, NOW());
BEGIN
    RETURN QUERY
    SELECT
        COALESCE(SUM(CASE WHEN p.status = 'succeeded' THEN p.amount ELSE 0 END), 0) as total_revenue,
        COUNT(CASE WHEN p.status = 'succeeded' THEN 1 END) as successful_payments,
        COUNT(CASE WHEN p.status = 'failed' THEN 1 END) as failed_payments,
        COALESCE(AVG(CASE WHEN p.status = 'succeeded' THEN p.amount END), 0) as average_order_value
    FROM payments p
    WHERE p.created_at >= start_date AND p.created_at <= end_date;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on payment tables
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_events ENABLE ROW LEVEL SECURITY;

-- Payments table policies
CREATE POLICY "Students can view their own payments" ON payments
    FOR SELECT USING (
        student_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

CREATE POLICY "Students can insert their own payments" ON payments
    FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "System can update payments" ON payments
    FOR UPDATE USING (
        student_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Subscriptions table policies
CREATE POLICY "Students can view their own subscriptions" ON subscriptions
    FOR SELECT USING (
        student_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

CREATE POLICY "Students can insert their own subscriptions" ON subscriptions
    FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "System can update subscriptions" ON subscriptions
    FOR UPDATE USING (
        student_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Invoices table policies
CREATE POLICY "Students can view their own invoices" ON invoices
    FOR SELECT USING (
        student_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Payment methods table policies
CREATE POLICY "Students can manage their own payment methods" ON payment_methods
    FOR ALL USING (
        student_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

-- Payment events table policies (admin only for security)
CREATE POLICY "Admins can view payment events" ON payment_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

CREATE POLICY "System can insert payment events" ON payment_events
    FOR INSERT WITH CHECK (true); -- Allow system inserts

-- =====================================================
-- 7. HELPER VIEWS
-- =====================================================

-- View for active subscriptions with product details
CREATE OR REPLACE VIEW active_subscriptions_view AS
SELECT
    s.*,
    p.name as product_name,
    p.description as product_description,
    p.type as product_type,
    pr.display_name as student_name,
    pr.email as student_email
FROM subscriptions s
JOIN products p ON p.id = s.product_id
JOIN profiles pr ON pr.id = s.student_id
WHERE s.status = 'active'
AND (s.access_expires_at IS NULL OR s.access_expires_at > NOW());

-- View for payment history with workflow details
CREATE OR REPLACE VIEW payment_history_view AS
SELECT
    p.*,
    sw.product_type,
    sw.current_step,
    prod.name as product_name,
    prof.display_name as student_name,
    prof.email as student_email
FROM payments p
JOIN subscription_workflows sw ON sw.id = p.workflow_id
LEFT JOIN products prod ON prod.id = sw.product_id
JOIN profiles prof ON prof.id = p.student_id
ORDER BY p.created_at DESC;

-- View for revenue analytics
CREATE OR REPLACE VIEW revenue_analytics_view AS
SELECT
    DATE_TRUNC('day', p.created_at) as payment_date,
    COUNT(*) as total_payments,
    COUNT(CASE WHEN p.status = 'succeeded' THEN 1 END) as successful_payments,
    COUNT(CASE WHEN p.status = 'failed' THEN 1 END) as failed_payments,
    SUM(CASE WHEN p.status = 'succeeded' THEN p.amount ELSE 0 END) as daily_revenue,
    AVG(CASE WHEN p.status = 'succeeded' THEN p.amount END) as avg_order_value
FROM payments p
GROUP BY DATE_TRUNC('day', p.created_at)
ORDER BY payment_date DESC;
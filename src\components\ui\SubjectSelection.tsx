import React from "react";
import { Check, Plus, X } from "lucide-react";
import { Badge } from "@/components/ui/Badge";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { useGuestTutorFormStore } from "@/store/guestTutorFormStore";

export interface Subject {
  id: string;
  name: string;
  icon: React.ReactNode;
}

interface SubjectSelectionProps {
  showCustomInput?: boolean;
  commonSubjectsOverride?: Subject[];
  useSimpleToggle?: boolean;
}

// Common subjects that can be reused across the application
export const commonSubjects: Subject[] = [
  { id: "math", name: "Mathematics", icon: <span>📐</span> },
  { id: "science", name: "Science", icon: <span>🔬</span> },
  { id: "english", name: "English", icon: <span>📚</span> },
  { id: "history", name: "History", icon: <span>🏛️</span> },
  { id: "programming", name: "Programming", icon: <span>💻</span> },
  { id: "languages", name: "Languages", icon: <span>🌎</span> },
  { id: "physics", name: "Physics", icon: <span>⚛️</span> },
  { id: "chemistry", name: "Chemistry", icon: <span>🧪</span> },
  { id: "biology", name: "Biology", icon: <span>🧬</span> },
  { id: "art", name: "Art", icon: <span>🎨</span> },
  { id: "geography", name: "Geography", icon: <span>🌍</span> },
];

const SubjectSelection = React.forwardRef<HTMLDivElement, SubjectSelectionProps>(({
  showCustomInput = true,
  commonSubjectsOverride,
  useSimpleToggle = false,
}, ref) => {
  const {
    subjects,
    setSubjects,
    customSubjectInput,
    setCustomSubjectInput,
    touchField,
  } = useGuestTutorFormStore();

  const subjectsToUse = commonSubjectsOverride || commonSubjects;

  const toggleSubject = (subjectId: string) => {
    // Mark field as touched
    touchField("subjects");

    if (subjects.value.includes(subjectId)) {
      const newSubjects = subjects.value.filter((id) => id !== subjectId);
      // Always validate, even when removing subjects
      const validation = useGuestTutorFormStore
        .getState()
        .validateField("subjects", newSubjects);
      setSubjects(newSubjects, validation.isValid, validation.error);
    } else {
      const newSubjects = [...subjects.value, subjectId];
      const validation = useGuestTutorFormStore
        .getState()
        .validateField("subjects", newSubjects);
      setSubjects(newSubjects, validation.isValid, validation.error);
    }
  };

  const handleAddCustomSubject = () => {
    // Mark field as touched
    touchField("subjects");

    if (
      customSubjectInput &&
      customSubjectInput.trim() &&
      !subjects.value.includes(customSubjectInput.trim())
    ) {
      const newSubjects = [...subjects.value, customSubjectInput.trim()];
      const validation = useGuestTutorFormStore
        .getState()
        .validateField("subjects", newSubjects);
      setSubjects(newSubjects, validation.isValid, validation.error);
      setCustomSubjectInput(""); // Reset the input after adding
    }
  };

  const handleRemoveSubject = (subject: string) => {
    // Mark field as touched
    touchField("subjects");

    const newSubjects = subjects.value.filter((s) => s !== subject);
    const validation = useGuestTutorFormStore
      .getState()
      .validateField("subjects", newSubjects);
    setSubjects(newSubjects, validation.isValid, validation.error);
  };

  // Render error message only if field has been touched
  const renderErrorMessage = () => {
    if (subjects.touched && subjects.error) {
      return (
        <div className="text-sm font-medium text-destructive mt-2">
          {subjects.error}
        </div>
      );
    }
    return null;
  };

  // Simple toggle buttons for subjects (used in BecomeTutor)
  if (useSimpleToggle) {
    return (
      <div ref={ref} className="space-y-4">
        {/* Add error message display */}
        {renderErrorMessage()}
        {subjects.value.length > 0 && (
          <div className="mb-4">
            <p className="text-sm text-muted-foreground mb-2">
              Selected subjects:
            </p>
            <div className="flex flex-wrap gap-2">
              {subjects.value.map((subject) => (
                <Badge
                  key={subject}
                  variant="secondary"
                  className="px-3 py-1.5 text-sm"
                >
                  {subject}
                  <X
                    className="h-3 w-3 ml-2 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveSubject(subject);
                    }}
                  />
                </Badge>
              ))}
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
          {subjectsToUse.map((subject) => (
            <Button
              key={subject.id}
              type="button"
              variant={
                subjects.value.includes(subject.id) ? "default" : "outline"
              }
              className="justify-start"
              onClick={() => toggleSubject(subject.id)}
            >
              {subjects.value.includes(subject.id) && (
                <Check className="h-4 w-4 mr-2" />
              )}
              {subject.name}
            </Button>
          ))}
        </div>

        {showCustomInput && (
          <div className="mt-4">
            <p className="text-sm font-medium mb-2">Add a custom subject:</p>
            <div className="flex gap-2">
              <Input
                value={customSubjectInput}
                onChange={(e) => setCustomSubjectInput(e.target.value)}
                placeholder="e.g. Spanish, Guitar, Calculus..."
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleAddCustomSubject();
                  }
                }}
              />
              <Button
                type="button"
                onClick={handleAddCustomSubject}
                variant="outline"
                disabled={!customSubjectInput.trim()}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Original card-based UI
  return (
    <div ref={ref}>
      {/* Add error message display */}
      {renderErrorMessage()}
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-4">
        {subjectsToUse.map((subject) => (
          <div
            key={subject.id}
            onClick={() => toggleSubject(subject.id)}
            className={`
              p-3 rounded-lg border cursor-pointer transition-all
              flex items-center gap-2
              ${
                subjects.value.includes(subject.id)
                  ? "border-primary bg-primary/10"
                  : "border-gray-200 hover:border-gray-300"
              }
            `}
          >
            <div className="text-lg">{subject.icon}</div>
            <span className="font-medium text-sm">{subject.name}</span>
            {subjects.value.includes(subject.id) && (
              <Check className="h-4 w-4 ml-auto text-primary" />
            )}
          </div>
        ))}
      </div>

      {subjects.value.length > 0 && (
        <div className="mb-4">
          <p className="text-sm font-medium mb-2">Selected subjects:</p>
          <div className="flex flex-wrap gap-2">
            {subjects.value.map((subject) => (
              <Badge key={subject} variant="secondary" className="px-3 py-1">
                {subject}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveSubject(subject);
                  }}
                />
              </Badge>
            ))}
          </div>
        </div>
      )}

      {showCustomInput && (
        <div className="mt-4">
          <p className="text-sm font-medium mb-2">Add a custom subject:</p>
          <div className="flex gap-2">
            <Input
              value={customSubjectInput}
              onChange={(e) => setCustomSubjectInput(e.target.value)}
              placeholder="e.g. Spanish, Guitar, Calculus..."
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleAddCustomSubject();
                }
              }}
            />
            <Button
              type="button"
              onClick={handleAddCustomSubject}
              variant="outline"
              disabled={!customSubjectInput.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
});

SubjectSelection.displayName = "SubjectSelection";

export default SubjectSelection;

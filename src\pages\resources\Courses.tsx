
import { useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import { BookOpen, Clock, Award, Users, Search } from "lucide-react";

const coursesData = [
  {
    id: 1,
    title: "Introduction to Reinforcement Learning",
    description: "Learn the fundamentals of reinforcement learning algorithms and applications.",
    level: "Beginner",
    duration: "6 weeks",
    students: 2345,
    instructor: "Dr. <PERSON>",
    image: "https://images.unsplash.com/photo-1620641788421-7a1c342ea42e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["AI", "Machine Learning", "Reinforcement Learning"],
  },
  {
    id: 2,
    title: "Advanced Mathematics for Machine Learning",
    description: "Master the mathematical concepts essential for advanced machine learning algorithms.",
    level: "Advanced",
    duration: "8 weeks",
    students: 1872,
    instructor: "Prof. <PERSON>",
    image: "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Mathematics", "Calculus", "Linear Algebra", "Machine Learning"],
  },
  {
    id: 3,
    title: "Applied Reinforcement Learning in Robotics",
    description: "Apply reinforcement learning techniques to solve real-world robotics problems.",
    level: "Intermediate",
    duration: "10 weeks",
    students: 967,
    instructor: "Dr. Robert Kim",
    image: "https://images.unsplash.com/photo-1581092921461-7031e4bfb83a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Robotics", "AI", "Reinforcement Learning", "Programming"],
  },
  {
    id: 4,
    title: "Natural Language Processing Fundamentals",
    description: "Explore the basics of NLP and build applications that understand human language.",
    level: "Beginner",
    duration: "7 weeks",
    students: 1543,
    instructor: "Dr. Emily Rodriguez",
    image: "https://images.unsplash.com/photo-1516321165247-4aa89a48be28?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["NLP", "AI", "Machine Learning", "Language"],
  },
  {
    id: 5,
    title: "Deep Reinforcement Learning Mastery",
    description: "Master advanced deep reinforcement learning algorithms and architectures.",
    level: "Advanced",
    duration: "12 weeks",
    students: 756,
    instructor: "Prof. James Wilson",
    image: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Deep Learning", "Reinforcement Learning", "Neural Networks", "AI"],
  },
  {
    id: 6,
    title: "Reinforcement Learning for Game Development",
    description: "Apply RL techniques to create intelligent agents for games and simulations.",
    level: "Intermediate",
    duration: "9 weeks",
    students: 1203,
    instructor: "Dr. Lisa Park",
    image: "https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    tags: ["Game Development", "Reinforcement Learning", "AI", "Programming"],
  },
];

const Courses = () => {
  const [searchTerm, setSearchTerm] = useState("");
  
  const filteredCourses = coursesData.filter(
    (course) => 
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              Explore Our Courses
            </h1>
            <p className="mt-4 text-xl text-gray-500 max-w-2xl mx-auto">
              Discover specialized courses focusing on reinforcement learning and AI technologies.
            </p>
          </div>
          
          <div className="flex justify-center mb-10">
            <div className="relative w-full max-w-xl">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                placeholder="Search courses by title, description, or tags..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {filteredCourses.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900">No courses found</h3>
              <p className="mt-2 text-gray-500">
                Try adjusting your search terms or browse all our courses.
              </p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setSearchTerm("")}
              >
                View All Courses
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredCourses.map((course) => (
                <Card key={course.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <div className="h-48 overflow-hidden">
                    <img 
                      src={course.image} 
                      alt={course.title} 
                      className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <Badge variant="outline" className="bg-rfpurple-50 text-rfpurple-700 mb-2">
                        {course.level}
                      </Badge>
                      <div className="flex items-center text-gray-500 text-sm">
                        <Clock size={14} className="mr-1" />
                        {course.duration}
                      </div>
                    </div>
                    <CardTitle className="text-xl">{course.title}</CardTitle>
                    <CardDescription className="text-gray-600 line-clamp-2">
                      {course.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {course.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="bg-gray-100 text-gray-700">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between pt-2">
                      <div className="text-sm text-gray-500 flex items-center">
                        <Users size={14} className="mr-1" />
                        {course.students.toLocaleString()} students
                      </div>
                      <div className="text-sm text-gray-700">
                        by {course.instructor}
                      </div>
                    </div>
                    
                    <Button className="w-full mt-4 button-gradient text-white" asChild>
                      <Link to={`/courses/${course.id}`}>Learn More</Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Courses;

-- Emergency Profile Fix - Minimal approach to get profile data working
-- This bypasses complex subscription logic and just gets basic profile data

-- =====================================================
-- STEP 1: CREATE MINIMAL PROFILE FUNCTION
-- =====================================================

-- Drop existing functions that might be causing issues
DROP FUNCTION IF EXISTS public.get_student_complete_profile(uuid);
DROP FUNCTION IF EXISTS public.get_basic_student_profile(uuid);

-- Create the simplest possible profile function
CREATE OR REPLACE FUNCTION public.get_student_profile_minimal(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    profile_created_at timestamp with time zone,
    profile_updated_at timestamp with time zone
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        -- Simple timestamp casting
        CASE 
            WHEN p.created_at IS NOT NULL THEN p.created_at AT TIME ZONE 'UTC'
            ELSE NULL
        END as profile_created_at,
        CASE 
            WHEN p.updated_at IS NOT NULL THEN p.updated_at AT TIME ZONE 'UTC'
            ELSE NULL
        END as profile_updated_at
    FROM profiles p
    WHERE p.id = student_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_student_profile_minimal(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_student_profile_minimal(uuid) TO anon;

-- =====================================================
-- STEP 2: CREATE BASIC STUDENT PROFILE FUNCTION
-- =====================================================

-- Create basic student profile function with minimal complexity
CREATE OR REPLACE FUNCTION public.get_basic_student_profile(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    profile_created_at timestamp with time zone,
    profile_updated_at timestamp with time zone,
    education_level text,
    is_enrolled boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        -- Simple timestamp casting
        CASE 
            WHEN p.created_at IS NOT NULL THEN p.created_at AT TIME ZONE 'UTC'
            ELSE NULL
        END as profile_created_at,
        CASE 
            WHEN p.updated_at IS NOT NULL THEN p.updated_at AT TIME ZONE 'UTC'
            ELSE NULL
        END as profile_updated_at,
        COALESCE(s.education_level, ''::text),
        -- Simple enrollment check without complex logic
        false as is_enrolled  -- Simplified for now
        
    FROM profiles p
    LEFT JOIN students s ON p.id = s.id
    WHERE p.id = student_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_basic_student_profile(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_basic_student_profile(uuid) TO anon;

-- =====================================================
-- STEP 3: CREATE COMPLETE PROFILE FUNCTION (SIMPLIFIED)
-- =====================================================

-- Create complete profile function with all required fields but simplified logic
CREATE OR REPLACE FUNCTION public.get_student_complete_profile(student_id uuid)
RETURNS TABLE(
    id uuid,
    first_name text,
    last_name text,
    email text,
    user_type text,
    profile_picture_url text,
    timezone text,
    profile_created_at timestamp with time zone,
    profile_updated_at timestamp with time zone,
    education_level text,
    subjects_of_interest text[],
    learning_goals text[],
    study_preferences jsonb,
    academic_history jsonb,
    hobbies text[],
    interests text[],
    location text,
    date_of_birth date,
    is_enrolled boolean,
    active_subscriptions_count bigint,
    active_subscriptions json,
    all_subscriptions json,
    earliest_subscription_end timestamp with time zone,
    latest_subscription_end timestamp with time zone,
    total_days_remaining numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.first_name,
        p.last_name,
        p.email,
        p.user_type,
        p.profile_picture_url,
        p.timezone,
        -- Simple timestamp casting
        CASE 
            WHEN p.created_at IS NOT NULL THEN p.created_at AT TIME ZONE 'UTC'
            ELSE NULL
        END as profile_created_at,
        CASE 
            WHEN p.updated_at IS NOT NULL THEN p.updated_at AT TIME ZONE 'UTC'
            ELSE NULL
        END as profile_updated_at,
        COALESCE(s.education_level, ''::text),
        COALESCE(s.subjects_of_interest, ARRAY[]::text[]),
        COALESCE(s.learning_goals, ARRAY[]::text[]),
        COALESCE(s.study_preferences, '{}'::jsonb),
        COALESCE(s.academic_history, '{}'::jsonb),
        COALESCE(s.hobbies, ARRAY[]::text[]),
        COALESCE(s.interests, ARRAY[]::text[]),
        COALESCE(s.location, ''::text),
        s.date_of_birth,
        
        -- Simplified fields to avoid complex queries
        false as is_enrolled,
        0::bigint as active_subscriptions_count,
        '[]'::json as active_subscriptions,
        '[]'::json as all_subscriptions,
        NULL::timestamp with time zone as earliest_subscription_end,
        NULL::timestamp with time zone as latest_subscription_end,
        0::numeric as total_days_remaining
        
    FROM profiles p
    LEFT JOIN students s ON p.id = s.id
    WHERE p.id = student_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_student_complete_profile(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_student_complete_profile(uuid) TO anon;

-- =====================================================
-- STEP 4: TEST ALL FUNCTIONS
-- =====================================================

-- Test minimal function
SELECT 'Minimal Profile Test' as test_name;
SELECT * FROM get_student_profile_minimal(auth.uid()) LIMIT 1;

-- Test basic function
SELECT 'Basic Profile Test' as test_name;
SELECT * FROM get_basic_student_profile(auth.uid()) LIMIT 1;

-- Test complete function
SELECT 'Complete Profile Test' as test_name;
SELECT * FROM get_student_complete_profile(auth.uid()) LIMIT 1;

-- =====================================================
-- STEP 5: VERIFICATION
-- =====================================================

-- Check that all functions exist
SELECT 
    routine_name,
    'Function created successfully' as status
FROM information_schema.routines 
WHERE routine_name IN (
    'get_student_profile_minimal', 
    'get_basic_student_profile', 
    'get_student_complete_profile'
)
ORDER BY routine_name;

-- Check if profile data exists for current user
SELECT 
    'Profile Data Check' as check_type,
    id,
    first_name,
    last_name,
    email,
    user_type
FROM profiles 
WHERE id = auth.uid()
LIMIT 1;

import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useSubscriptionWorkflowStore } from "@/store/subscriptionWorkflowStore";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { 
  Breadcrumb, 
  BreadcrumbList, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbSeparator, 
  BreadcrumbPage 
} from "@/components/ui/Breadcrumb";
import { 
  BookOpen, 
  ArrowRight,
  ArrowLeft,
  Home
} from "lucide-react";
import { Link } from "react-router-dom";
import { ROUTES } from "@/routes/RouteConfig";

const NewSubscriptionConfigure: React.FC = () => {
  const navigate = useNavigate();
  const { displayName } = useProfileData();
  const { currentWorkflow } = useSubscriptionWorkflowStore();

  const handleContinue = () => {
    navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PRICING.path);
  };

  const handleBack = () => {
    navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_SELECT.path);
  };

  return (
    <StudentPageLayout
      title="Configure Curriculum"
      profileData={{
        displayName: displayName || "Student",
        email: "",
        photoUrl: ""
      }}
      description="Select your topics and subtopics"
    >
      {/* Breadcrumb Navigation */}
      <div className="mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_DASHBOARD.path} className="flex items-center">
                  <Home className="h-4 w-4 mr-1" />
                  Dashboard
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_SUBSCRIPTIONS.path}>
                  Subscriptions
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_NEW_SUBSCRIPTION.path}>
                  New Subscription
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Configure Curriculum</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Configure Curriculum
            </CardTitle>
            <CardDescription>
              Select your topics and subtopics for your {currentWorkflow?.product_type} package
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Curriculum Configuration
              </h3>
              <p className="text-gray-600 mb-6">
                This step will allow you to customize your learning curriculum by selecting specific topics and subtopics.
              </p>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-800 text-sm">
                  <strong>Coming Soon:</strong> Advanced curriculum configuration with topic selection, 
                  subtopic filtering, and personalized learning paths.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button onClick={handleContinue}>
            Continue
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </StudentPageLayout>
  );
};

export default NewSubscriptionConfigure;

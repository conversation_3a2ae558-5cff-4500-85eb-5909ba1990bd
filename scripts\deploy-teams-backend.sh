#!/bin/bash

# Teams Backend Deployment Script
# This script deploys the Supabase Edge Functions for Teams integration

set -e

echo "🚀 Deploying Microsoft Teams Backend Integration"
echo "================================================"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

echo "✅ Supabase CLI found"

# Deploy Edge Functions
echo ""
echo "📦 Deploying Edge Functions..."

echo "  → Deploying teams-auth function..."
supabase functions deploy teams-auth

echo "  → Deploying teams-meetings function..."
supabase functions deploy teams-meetings

echo "✅ Edge Functions deployed successfully"

# Run database migrations
echo ""
echo "🗄️  Running database migrations..."
supabase db push

echo "✅ Database migrations completed"

# Set environment variables (if provided)
echo ""
echo "🔧 Setting up environment variables..."

if [ -z "$AZURE_CLIENT_ID" ]; then
    echo "⚠️  AZURE_CLIENT_ID not set. Please set it manually:"
    echo "   supabase secrets set AZURE_CLIENT_ID=your_client_id"
else
    echo "  → Setting AZURE_CLIENT_ID..."
    supabase secrets set AZURE_CLIENT_ID="$AZURE_CLIENT_ID"
fi

if [ -z "$AZURE_CLIENT_SECRET" ]; then
    echo "⚠️  AZURE_CLIENT_SECRET not set. Please set it manually:"
    echo "   supabase secrets set AZURE_CLIENT_SECRET=your_client_secret"
else
    echo "  → Setting AZURE_CLIENT_SECRET..."
    supabase secrets set AZURE_CLIENT_SECRET="$AZURE_CLIENT_SECRET"
fi

if [ -z "$AZURE_TENANT_ID" ]; then
    echo "⚠️  AZURE_TENANT_ID not set. Please set it manually:"
    echo "   supabase secrets set AZURE_TENANT_ID=your_tenant_id"
else
    echo "  → Setting AZURE_TENANT_ID..."
    supabase secrets set AZURE_TENANT_ID="$AZURE_TENANT_ID"
fi

if [ -z "$FRONTEND_URL" ]; then
    echo "  → Setting default FRONTEND_URL..."
    supabase secrets set FRONTEND_URL="http://localhost:8080"
else
    echo "  → Setting FRONTEND_URL..."
    supabase secrets set FRONTEND_URL="$FRONTEND_URL"
fi

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Update your Azure App Registration redirect URI to:"
echo "   https://your-supabase-project.supabase.co/functions/v1/teams-auth/callback"
echo ""
echo "2. Test the integration by running your frontend and trying to connect to Teams"
echo ""
echo "3. Check function logs if you encounter issues:"
echo "   supabase functions logs teams-auth"
echo "   supabase functions logs teams-meetings"
echo ""
echo "📖 For detailed setup instructions, see TEAMS_BACKEND_IMPLEMENTATION.md"

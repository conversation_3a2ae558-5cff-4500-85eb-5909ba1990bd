import React, { useState, useEffect } from "react";
import { Calendar } from "./ui/Calendar";
import { Button } from "./ui/Button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/Select";

const timeSlots = ["9:00 AM", "10:00 AM", "11:00 AM", "1:00 PM", "3:00 PM"];

// Common timezones - you can expand this list as needed
const timezones = [
  { value: "UTC-8", label: "Pacific Time (PT)" },
  { value: "UTC-7", label: "Mountain Time (MT)" },
  { value: "UTC-6", label: "Central Time (CT)" },
  { value: "UTC-5", label: "Eastern Time (ET)" },
  { value: "UTC+0", label: "Greenwich Mean Time (GMT)" },
  { value: "UTC+1", label: "Central European Time (CET)" },
  { value: "UTC+8", label: "China Standard Time (CST)" },
];

const DateTimeSelector = ({ tutorName }) => {
  const [date, setDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedTimezone, setSelectedTimezone] = useState(null);
  const [userTimezone, setUserTimezone] = useState("");

  // Get user's timezone on component mount
  useEffect(() => {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    setUserTimezone(timezone);
  }, []);

  const handleTimeSelect = (time) => {
    setSelectedTime(time);
  };

  return (
    <div className="bg-white rounded-lg p-6 shadow-md">
      <h2 className="text-2xl font-bold mb-2">Select Date and Time</h2>
      <p className="text-gray-600 mb-6">
        Choose a date and time slot for your session with {tutorName}.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Select a Date</h3>
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            className="border rounded-md"
          />
          <p className="mt-4 text-sm text-gray-500">
            Available days: Mon, Wed, Fri, Sun
          </p>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Select a Time Slot</h3>
          <div className="grid grid-cols-2 gap-3">
            {timeSlots.map((time) => (
              <Button
                key={time}
                variant={selectedTime === time ? "default" : "outline"}
                className={`${
                  selectedTime === time ? "bg-purple-500" : "bg-gray-50"
                }`}
                onClick={() => handleTimeSelect(time)}
              >
                {time}
              </Button>
            ))}
          </div>

          {/* Timezone selector - only shown after time is selected */}
          {selectedTime && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-2">
                Select Timezone (optional)
              </h3>
              <Select
                onValueChange={setSelectedTimezone}
                defaultValue={userTimezone}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DateTimeSelector;

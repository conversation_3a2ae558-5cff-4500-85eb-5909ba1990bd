import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/UseToast";
import { useNavigate } from "react-router-dom";
import {
  checkEmailVerification,
  sendVerificationEmail,
  setupVerificationCountdown,
} from "@/services/emailVerificationService";
import { z } from "zod";

interface UseEmailVerificationProps {
  source?: string;
  onVerificationSuccess?: (email?: string) => void;
}

export const useEmailVerification = ({
  source = "default",
  onVerificationSuccess,
}: UseEmailVerificationProps = {}) => {
  const { toast } = useToast();
  const navigate = useNavigate();

  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [emailVerified, setEmailVerified] = useState(false);
  const [verificationCountdown, setVerificationCountdown] = useState(0);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
  const [emailChecked, setEmailChecked] = useState(false);
  const [emailValid, setEmailValid] = useState(false);

  // Function to validate email format using Zod
  const validateEmailFormat = async (email: string) => {
    if (!email) return false;

    setIsCheckingEmail(true);
    try {
      // Use Zod for email validation
      const emailSchema = z
        .string()
        .email({ message: "Invalid email address" });

      // This will throw if validation fails
      emailSchema.parse(email);

      setEmailChecked(true);
      setEmailValid(true);
      return true;
    } catch (error) {
      console.error("Email validation error:", error);
      setEmailChecked(true);
      setEmailValid(false);
      return false;
    } finally {
      setIsCheckingEmail(false);
    }
  };

  // Function to handle email verification
  const handleVerifyEmail = async (email: string) => {
    try {
      setIsVerifyingEmail(true);

      // Validate email first
      const isValid = await validateEmailFormat(email);
      if (!isValid) {
        toast({
          title: "Invalid email",
          description: "Please enter a valid email address",
          variant: "destructive",
        });
        return;
      }

      // Send verification email
      const { success, error, message, countdownSeconds, redirectPath } =
        await sendVerificationEmail(email, source);

      if (!success) {
        throw new Error(error);
      }

      // Success case
      toast({
        title: "Verification email sent",
        description: message,
      });

      // Set cooldown timer using the configured seconds
      const cooldownTime = countdownSeconds || 60;
      setVerificationCountdown(cooldownTime);

      // Use the imported setupVerificationCountdown function
      const countdown = setupVerificationCountdown(
        cooldownTime,
        (seconds) => setVerificationCountdown(seconds),
        () => setVerificationCountdown(0)
      );
      countdown.start();

      // Navigate to the configured redirect path
      navigate(redirectPath || "/confirm-email", {
        state: { email: email },
      });
    } catch (error) {
      console.error("Error sending verification email:", error);
      toast({
        title: "Verification failed",
        description: error.message || "Failed to send verification email",
        variant: "destructive",
      });
    } finally {
      setIsVerifyingEmail(false);
    }
  };

  // Function to check email verification status
  const checkVerificationStatus = async () => {
    try {
      const { verified, email, message } = await checkEmailVerification(source);

      if (verified) {
        setEmailVerified(true);

        if (message) {
          toast({
            title: "Email Verified",
            description: message,
          });
        }

        if (onVerificationSuccess) {
          onVerificationSuccess(email);
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error("Error checking email verification:", error);
      return false;
    }
  };

  // Set up polling to check verification status
  useEffect(() => {
    // Check immediately on mount
    checkVerificationStatus();

    // Set up polling to check every 3 seconds if not verified
    const interval = setInterval(() => {
      if (!emailVerified) {
        checkVerificationStatus();
      } else {
        clearInterval(interval);
      }
    }, 3000);

    // Listen for storage events (in case verification happens in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "emailConfirmed" && e.newValue === "true") {
        checkVerificationStatus();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      clearInterval(interval);
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [emailVerified, source]);

  return {
    isVerifyingEmail,
    emailVerified,
    verificationCountdown,
    isCheckingEmail,
    emailChecked,
    emailValid,
    validateEmailFormat,
    handleVerifyEmail,
    checkVerificationStatus,
  };
};

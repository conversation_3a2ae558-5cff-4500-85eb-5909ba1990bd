
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/TextArea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/Label";
import { ArrowLeft } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const formSchema = z.object({
  userType: z.enum(["parent", "student"]),
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  phone: z.string().optional(),
  message: z.string().optional(),
});

type UserInfoFormProps = {
  onBack: () => void;
  onSubmit: (data: z.infer<typeof formSchema>) => void;
  selectedDateTime: string;
};

const UserInfoForm = ({ onBack, onSubmit, selectedDateTime }: UserInfoFormProps) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userType: "parent",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      message: "",
    },
  });

  const handleSubmit = (data: z.infer<typeof formSchema>) => {
    onSubmit(data);
  };

  return (
    <div className="max-w-3xl mx-auto px-4">
      <h1 className="text-3xl font-bold mb-6">Your information</h1>
      
      <div className="mb-6">
        <p className="text-lg mb-2">
          {selectedDateTime} <span className="text-blue-600 cursor-pointer">Edit</span>
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="userType"
            render={({ field }) => (
              <FormItem className="mb-6">
                <div className="flex gap-8">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem 
                      value="parent" 
                      id="parent" 
                      checked={field.value === "parent"}
                      onClick={() => field.onChange("parent")}
                      className="h-5 w-5 border-2 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                    />
                    <Label htmlFor="parent" className="text-lg">Parent</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem 
                      value="student" 
                      id="student" 
                      checked={field.value === "student"}
                      onClick={() => field.onChange("student")}
                      className="h-5 w-5 border-2"
                    />
                    <Label htmlFor="student" className="text-lg">Student</Label>
                  </div>
                </div>
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">First name</FormLabel>
                  <FormControl>
                    <Input placeholder="" {...field} className="h-12 text-base" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Last name *</FormLabel>
                  <FormControl>
                    <Input placeholder="" {...field} className="h-12 text-base" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Your email address *</FormLabel>
                  <FormControl>
                    <Input placeholder="" {...field} className="h-12 text-base" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base">Phone Number ?</FormLabel>
                  <FormControl>
                    <Input placeholder="" {...field} className="h-12 text-base" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base">Message / Instructions (optional)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder=""
                    className="min-h-[120px] text-base"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-between pt-6">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft size={18} />
              Back
            </Button>
            <Button 
              type="submit" 
              className="bg-blue-600 hover:bg-blue-700 text-base px-10"
            >
              Confirm
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default UserInfoForm;


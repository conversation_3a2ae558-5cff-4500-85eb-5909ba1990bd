import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Avatar, AvatarFallback } from "@/components/ui/Avatar";
import { Edit, Plus, GraduationCap } from "lucide-react";

interface EducationProps {
  education: {
    id: string;
    institution: string;
    degree: string;
    fieldOfStudy: string;
    startDate: string;
    endDate: string | null;
  }[];
}

const Education: React.FC<EducationProps> = ({ education }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center">
          <CardTitle className="text-xl">Education</CardTitle>
          <span className="ml-2 text-sm text-gray-500">{education.length} records</span>
        </div>
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
          <Button variant="ghost" size="sm">
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {education.map((edu) => (
            <div key={edu.id} className="flex">
              <Avatar className="h-10 w-10 mr-4 mt-1">
                <AvatarFallback className="bg-gray-100 text-gray-600">
                  <GraduationCap className="h-5 w-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-medium">{edu.institution}</h3>
                <p className="text-sm text-gray-600">{edu.degree}, {edu.fieldOfStudy}</p>
                <div className="text-xs text-gray-500 mt-1">
                  {edu.startDate} - {edu.endDate || "Currently"}
                </div>
              </div>
              <div className="ml-auto">
                <div className="h-2 w-2 rounded-full bg-gray-200"></div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default Education;

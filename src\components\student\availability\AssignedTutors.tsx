import React, { useState } from "react";
import { useStudentAvailabilityStore } from "@/store/studentAvailabilityStore";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";
import { Badge } from "@/components/ui/Badge";
import { Input } from "@/components/ui/Input";
import { Search, Mail, BookOpen, Layers, Calendar } from "lucide-react";
import { Button } from "@/components/ui/Button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/Table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";

const AssignedTutors: React.FC = () => {
  const { assignedTutors } = useStudentAvailabilityStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [assignmentFilter, setAssignmentFilter] = useState<string[]>([]);

  // Filter tutors based on search query and assignment type filter
  const filteredTutors = assignedTutors.filter((tutor) => {
    const matchesSearch =
      searchQuery === "" ||
      tutor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tutor.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tutor.assignmentName.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesAssignmentType =
      assignmentFilter.length === 0 ||
      assignmentFilter.includes(tutor.assignmentType);

    return matchesSearch && matchesAssignmentType;
  });

  // Toggle assignment type filter
  const toggleAssignmentFilter = (type: string) => {
    if (assignmentFilter.includes(type)) {
      setAssignmentFilter(assignmentFilter.filter((t) => t !== type));
    } else {
      setAssignmentFilter([...assignmentFilter, type]);
    }
  };

  // Get assignment type icon
  const getAssignmentTypeIcon = (type: string) => {
    switch (type) {
      case "batch":
        return <Layers className="h-4 w-4" />;
      case "topic":
        return <BookOpen className="h-4 w-4" />;
      case "subtopic":
        return <BookOpen className="h-4 w-4" />;
      case "session":
        return <Calendar className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // Get assignment type badge color
  const getAssignmentTypeBadgeClass = (type: string) => {
    switch (type) {
      case "batch":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "topic":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "subtopic":
        return "bg-cyan-100 text-cyan-800 border-cyan-200";
      case "session":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap justify-between items-center gap-2">
        <div className="relative flex-grow max-w-md">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tutors..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <Badge
            variant={
              assignmentFilter.includes("batch") ? "default" : "outline"
            }
            className={`cursor-pointer ${
              !assignmentFilter.includes("batch")
                ? getAssignmentTypeBadgeClass("batch")
                : ""
            }`}
            onClick={() => toggleAssignmentFilter("batch")}
          >
            <Layers className="h-3 w-3 mr-1" /> Batch
          </Badge>
          <Badge
            variant={
              assignmentFilter.includes("topic") ? "default" : "outline"
            }
            className={`cursor-pointer ${
              !assignmentFilter.includes("topic")
                ? getAssignmentTypeBadgeClass("topic")
                : ""
            }`}
            onClick={() => toggleAssignmentFilter("topic")}
          >
            <BookOpen className="h-3 w-3 mr-1" /> Topic
          </Badge>
          <Badge
            variant={
              assignmentFilter.includes("subtopic") ? "default" : "outline"
            }
            className={`cursor-pointer ${
              !assignmentFilter.includes("subtopic")
                ? getAssignmentTypeBadgeClass("subtopic")
                : ""
            }`}
            onClick={() => toggleAssignmentFilter("subtopic")}
          >
            <BookOpen className="h-3 w-3 mr-1" /> Subtopic
          </Badge>
          <Badge
            variant={
              assignmentFilter.includes("session") ? "default" : "outline"
            }
            className={`cursor-pointer ${
              !assignmentFilter.includes("session")
                ? getAssignmentTypeBadgeClass("session")
                : ""
            }`}
            onClick={() => toggleAssignmentFilter("session")}
          >
            <Calendar className="h-3 w-3 mr-1" /> Session
          </Badge>
        </div>
      </div>

      {filteredTutors.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-500">No tutors found matching your criteria.</p>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Tutor</TableHead>
              <TableHead>Assignment Type</TableHead>
              <TableHead>Assignment</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTutors.map((tutor) => (
              <TableRow key={tutor.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={tutor.photoUrl} />
                      <AvatarFallback>
                        {tutor.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{tutor.name}</div>
                      <div className="text-sm text-gray-500">{tutor.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={getAssignmentTypeBadgeClass(tutor.assignmentType)}
                  >
                    {getAssignmentTypeIcon(tutor.assignmentType)}
                    <span className="ml-1 capitalize">{tutor.assignmentType}</span>
                  </Badge>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block max-w-[200px] truncate">
                          {tutor.assignmentName}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{tutor.assignmentName}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="icon">
                    <Mail className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
};

export default AssignedTutors;

# Azure App Registration Walkthrough

## 🎯 **What We're Setting Up**
We're creating an Azure app registration that will allow your tutoring platform to:
- Authenticate users with Microsoft accounts
- Create Teams meetings programmatically
- Access Microsoft Graph API for meeting management

## 📋 **Prerequisites**
- Azure account with admin access
- Microsoft 365 subscription (for Teams functionality)
- Your application domains ready

## 🚀 **Step-by-Step Process**

### **Step 1: Access Azure Portal**
1. Open your browser and go to: [https://portal.azure.com](https://portal.azure.com)
2. Sign in with your Azure account
3. Make sure you're in the correct tenant/directory

### **Step 2: Navigate to App Registrations**
1. In the Azure portal search bar, type: `App registrations`
2. Click on **App registrations** from the results
   - **Note:** The service is now under "Microsoft Entra ID" (formerly Azure Active Directory)
   - If you don't see it directly, you can also search for `Microsoft Entra ID` or `Azure Active Directory`
3. Click the **+ New registration** button

### **Step 3: Configure Basic App Information**
Fill out the registration form:

**Name:** `rfLearn Teams`

**Supported account types:** Select one of:
- ✅ **Accounts in this organizational directory only** (Single tenant) - *Recommended for most cases*
- ⚠️ **Accounts in any organizational directory** (Multi-tenant) - *Only if you need multi-tenant support*

**Redirect URI:**
- **Type:** Web
- **URL:** `https://your-supabase-project.supabase.co/functions/v1/teams-auth/callback`
- **Note:** Replace `your-supabase-project` with your actual Supabase project reference

Click **Register** to create the app.

### **Step 4: Note Down Important IDs**
After registration, you'll see the **Overview** page. Copy these values:

**Application (client) ID:** `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
→ This is your `VITE_AZURE_CLIENT_ID`

**Directory (tenant) ID:** `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
→ This is your `VITE_AZURE_TENANT_ID`

### **Step 5: Create Client Secret**
1. In the left sidebar, click **Certificates & secrets**
2. Click **+ New client secret**
3. **Description:** `RFLearn Teams Integration Secret`
4. **Expires:** Select `24 months` (recommended)
5. Click **Add**
6. **⚠️ IMPORTANT:** Copy the **Value** immediately (you can only see it once!)
   → This is your `AZURE_CLIENT_SECRET` (backend only)

### **Step 6: Configure API Permissions**
1. In the left sidebar, click **API permissions**
2. Click **+ Add a permission**
3. Select **Microsoft Graph**
4. Select **Application permissions**
5. Search for and add these permissions:
   - ✅ `OnlineMeetings.ReadWrite.All`
   - ✅ `User.Read.All`
   - ✅ `Calendars.ReadWrite.All` (optional, for calendar integration)
6. Click **Add permissions**
7. **⚠️ CRITICAL:** Click **Grant admin consent for [Your Organization]**
8. Confirm by clicking **Yes**

### **Step 7: Configure Authentication Settings**
1. In the left sidebar, click **Authentication**
2. Under **Platform configurations**, click **+ Add a platform**
3. Select **Web**
4. Add these Redirect URIs:
   - `http://localhost:8080/auth/teams/callback` (development)
   - `https://staging.rflearn.com/auth/teams/callback` (staging)
   - `https://rflearn.com/auth/teams/callback` (production)
5. Under **Implicit grant and hybrid flows**, check:
   - ✅ **Access tokens**
   - ✅ **ID tokens**
6. Click **Save**

### **Step 8: Verify Configuration**
Go back to **Overview** and verify:
- ✅ Application ID is visible
- ✅ Directory ID is visible
- ✅ Redirect URIs are configured
- ✅ API permissions are granted with admin consent

## 🔑 **Environment Variables Setup**

**No frontend environment variables needed!** All sensitive data is now handled server-side.

Set these in Supabase Edge Functions:

```bash
# Set in Supabase (keep secret)
supabase secrets set AZURE_CLIENT_ID=your_application_client_id_from_step_4
supabase secrets set AZURE_CLIENT_SECRET=your_client_secret_from_step_5
supabase secrets set AZURE_TENANT_ID=your_directory_tenant_id_from_step_4
supabase secrets set FRONTEND_URL=http://localhost:8080
```

### **Generate Webhook Secret:**
```bash
# On Windows (PowerShell)
[System.Web.Security.Membership]::GeneratePassword(32, 0)

# On Linux/Mac
openssl rand -hex 32

# Or use online generator: https://www.random.org/strings/
```

## ✅ **Testing Your Setup**

1. **Deploy the backend functions:**
   ```bash
   # Run the deployment script
   ./scripts/deploy-teams-backend.ps1
   # or
   ./scripts/deploy-teams-backend.sh
   ```

2. **Start your development server:**
   ```bash
   npm run dev
   ```

3. **Test authentication:**
   - Navigate to any page with Teams integration
   - Click "Connect to Teams"
   - You should be redirected to Microsoft login
   - After successful login, you should be redirected back with `?teams_success=true`

4. **Test meeting creation:**
   - Try creating a test meeting from a session page
   - Verify that a Teams meeting URL is generated and stored

## 🚨 **Common Issues & Solutions**

### **Issue: "AADSTS50011: The reply URL specified in the request does not match"**
**Solution:** 
- Check that redirect URIs in Azure exactly match your application URLs
- Ensure no trailing slashes or extra characters
- Verify you're using the correct environment (dev/staging/prod)

### **Issue: "AADSTS65001: The user or administrator has not consented"**
**Solution:**
- Go back to API permissions in Azure
- Click "Grant admin consent" again
- Make sure all permissions show "Granted for [Organization]"

### **Issue: "Authentication fails silently"**
**Solution:**
- Check browser console for errors
- Verify Client ID and Tenant ID are correct
- Ensure popup blockers are disabled
- Try incognito/private browsing mode

### **Issue: "Meeting creation fails"**
**Solution:**
- Verify API permissions are granted
- Check that user has Teams license
- Ensure OnlineMeetings.ReadWrite.All permission is granted

## 🔒 **Security Best Practices**

1. **Never expose client secret in frontend code**
2. **Use HTTPS in production redirect URIs**
3. **Regularly rotate client secrets (before expiry)**
4. **Monitor API usage and permissions**
5. **Use least privilege principle for permissions**

## 📞 **Need Help?**

If you encounter issues:
1. Check the test page at `/test-teams` for configuration validation
2. Review browser console for detailed error messages
3. Verify all steps were completed correctly
4. Check Azure portal for any error messages or warnings

## 🎉 **Success Indicators**

You'll know everything is working when:
- ✅ Test page shows "Teams Enabled: Yes"
- ✅ Authentication status shows "Yes" after connecting
- ✅ Meeting creation generates a valid Teams URL
- ✅ Join meeting button opens Teams successfully

---

**Next:** Once this is complete, your Teams integration will be fully functional!

import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Edit } from "lucide-react";
import EditSectionModal, { FormInput } from "@/components/student/profile/EditSectionModal";
import { StudentExtendedProfileData } from "@/pages/student/Profile";

interface StudyPreferencesSectionProps {
  profileData: StudentExtendedProfileData;
  editData: Partial<StudentExtendedProfileData>;
  activeSectionEdit: string | null;
  setActiveSectionEdit: (section: string | null) => void;
  updateEditData: (field: string, value: any) => void;
  updateProfile: (data: Partial<StudentExtendedProfileData>) => Promise<void>;
}

const StudyPreferencesSection: React.FC<StudyPreferencesSectionProps> = ({
  profileData,
  editData,
  activeSectionEdit,
  setActiveSectionEdit,
  updateEditD<PERSON>,
  updatePro<PERSON>le
}) => {
  return (
    <>
      {/* Study Preferences Section */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Study Preferences</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setActiveSectionEdit('study')}
          >
            <Edit className="h-3.5 w-3.5 mr-1" />
            Edit
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Preferred Time:</h3>
              <p className="capitalize mt-1">
                {profileData.study_preferences?.preferred_time || "Not specified"}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Preferred Days:</h3>
              <div className="flex flex-wrap gap-2 mt-1">
                {profileData.study_preferences?.preferred_days?.length > 0 ? (
                  profileData.study_preferences.preferred_days.map((day, index) => (
                    <Badge key={index} variant="outline" className="bg-purple-50 capitalize">
                      {day}
                    </Badge>
                  ))
                ) : (
                  <p className="text-gray-500">No preferred days specified</p>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Learning Style:</h3>
              <p className="capitalize mt-1">
                {profileData.study_preferences?.learning_style || "Not specified"}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Communication Preference:</h3>
              <p className="capitalize mt-1">
                {profileData.study_preferences?.communication_preference || "Not specified"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Study Preferences Edit Modal */}
      <EditSectionModal
        isOpen={activeSectionEdit === 'study'}
        onClose={() => setActiveSectionEdit(null)}
        title="Edit Study Preferences"
        onSubmit={() => {
          // Save changes
          updateProfile(editData);
          setActiveSectionEdit(null);
        }}
        layout="default"
      >
        <FormInput
          label="Preferred Time"
          id="preferred_time"
          placeholder="e.g., morning, afternoon, evening"
          helpText="What time of day do you prefer to study?"
          value={editData.study_preferences?.preferred_time || ""}
          onChange={(e) => updateEditData("study_preferences", {
            ...editData.study_preferences,
            preferred_time: e.target.value
          })}
        />

        <FormInput
          label="Preferred Days"
          id="preferred_days"
          placeholder="e.g., Monday, Wednesday, Friday"
          helpText="Which days of the week work best for your schedule?"
          value={editData.study_preferences?.preferred_days?.join(', ') || ''}
          onChange={(e) => {
            const daysArray = e.target.value.split(',').map(item => item.trim()).filter(Boolean);
            updateEditData("study_preferences", {
              ...editData.study_preferences,
              preferred_days: daysArray
            });
          }}
        />

        <FormInput
          label="Learning Style"
          id="learning_style"
          placeholder="e.g., visual, auditory, kinesthetic"
          helpText="How do you learn best? Visual (seeing), auditory (hearing), or kinesthetic (doing)?"
          value={editData.study_preferences?.learning_style || ""}
          onChange={(e) => updateEditData("study_preferences", {
            ...editData.study_preferences,
            learning_style: e.target.value
          })}
        />

        <FormInput
          label="Communication Preference"
          id="communication_preference"
          placeholder="e.g., video, chat, email"
          helpText="How do you prefer to communicate with your tutor?"
          value={editData.study_preferences?.communication_preference || ""}
          onChange={(e) => updateEditData("study_preferences", {
            ...editData.study_preferences,
            communication_preference: e.target.value
          })}
        />
      </EditSectionModal>
    </>
  );
};

export default StudyPreferencesSection;

import { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { supabase } from "../../lib/supabaseClient";
import { MessageSquare, CheckCircle, Eye } from "lucide-react";

interface Inquiry {
  id: string;
  created_at: string;
  name: string;
  email: string;
  phone: string | null;
  inquiry_type: string;
  message: string;
  status: string;
}

const InquiriesManagement = () => {
  console.log("InquiriesManagement component rendering - START");
  const { user } = useAuth();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newCount, setNewCount] = useState(0);

  console.log("InquiriesManagement hooks initialized");

  useEffect(() => {
    console.log("InquiriesManagement useEffect triggered");
    fetchInquiries();
  }, []);

  const fetchInquiries = async () => {
    console.log("fetchInquiries called");
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("inquiries")
        .select("*")
        .order("created_at", { ascending: false });

      console.log("Supabase response:", { data, error });

      if (error) throw error;
      
      setInquiries(data || []);
      
      // Count new inquiries
      const newInquiries = data?.filter(item => item.status === 'new') || [];
      setNewCount(newInquiries.length);
    } catch (err: any) {
      console.error("Error fetching inquiries:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const updateInquiryStatus = async (id: string, status: string) => {
    try {
      const { error } = await supabase
        .from("inquiries")
        .update({ status })
        .eq("id", id);

      if (error) throw error;

      // Update local state
      setInquiries(
        inquiries.map((inquiry) =>
          inquiry.id === id ? { ...inquiry, status } : inquiry
        )
      );
    } catch (err: any) {
      setError(err.message);
    }
  };

  const deleteInquiry = async (id: string) => {
    try {
      const { error } = await supabase.from("inquiries").delete().eq("id", id);

      if (error) throw error;

      // Update local state
      setInquiries(inquiries.filter((inquiry) => inquiry.id !== id));
    } catch (err: any) {
      setError(err.message);
    }
  };

  console.log("InquiriesManagement about to render UI");
  console.log("Loading state:", loading);
  console.log("Error state:", error);
  console.log("Inquiries count:", inquiries.length);

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold mb-4">Inquiries Management</h1>
      </div>

      {loading && <div className="p-4">Loading inquiries...</div>}
      {error && <div className="p-4 text-red-500">Error: {error}</div>}

      {!loading && !error && inquiries.length === 0 ? (
        <p>No inquiries found.</p>
      ) : (
        !loading &&
        !error && (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-200">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 border">Date</th>
                  <th className="py-2 px-4 border">Name</th>
                  <th className="py-2 px-4 border">Email</th>
                  <th className="py-2 px-4 border">Phone</th>
                  <th className="py-2 px-4 border">Type</th>
                  <th className="py-2 px-4 border">Message</th>
                  <th className="py-2 px-4 border">Status</th>
                  <th className="py-2 px-4 border">Actions</th>
                </tr>
              </thead>
              <tbody>
                {inquiries.map((inquiry) => (
                  <tr key={inquiry.id}>
                    <td className="py-2 px-4 border">
                      {new Date(inquiry.created_at).toLocaleDateString()}
                    </td>
                    <td className="py-2 px-4 border">{inquiry.name}</td>
                    <td className="py-2 px-4 border">{inquiry.email}</td>
                    <td className="py-2 px-4 border">{inquiry.phone}</td>
                    <td className="py-2 px-4 border">{inquiry.inquiry_type}</td>
                    <td className="py-2 px-4 border">{inquiry.message}</td>
                    <td className="py-2 px-4 border">
                      <select
                        value={inquiry.status}
                        onChange={(e) =>
                          updateInquiryStatus(inquiry.id, e.target.value)
                        }
                        className="p-1 border rounded"
                      >
                        <option value="new">New</option>
                        <option value="in_progress">In Progress</option>
                        <option value="resolved">Resolved</option>
                      </select>
                    </td>
                    <td className="py-2 px-4 border">
                      <button
                        onClick={() => deleteInquiry(inquiry.id)}
                        className="bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )
      )}
    </div>
  );
};

export default InquiriesManagement;




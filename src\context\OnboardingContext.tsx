import React, { createContext, useContext, useState, ReactNode } from "react";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "./AuthContext";
import { useToast } from "@/components/ui/UseToast";

interface OnboardingContextType {
  currentStep: number;
  totalSteps: number;
  nextStep: () => void;
  prevStep: () => void;
  completeOnboarding: () => Promise<void>;
  // Personal information
  firstName: string;
  setFirstName: (name: string) => void;
  lastName: string;
  setLastName: (name: string) => void;
  dateOfBirth: string | null;
  setDateOfBirth: (date: string | null) => void;
  // Subject selection
  selectedSubjects: string[];
  setSelectedSubjects: (subjects: string[]) => void;
  // Education level
  educationLevel: string;
  setEducationLevel: (level: string) => void;
  // Learning goals
  learningGoals: string[];
  setLearningGoals: (goals: string[]) => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
);

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (!context) {
    throw new Error("useOnboarding must be used within an OnboardingProvider");
  }
  return context;
}

interface OnboardingProviderProps {
  children: ReactNode;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({
  children,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Step management
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4; // Updated to include personal info step

  // Step 4: Personal information
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [dateOfBirth, setDateOfBirth] = useState<string | null>(null);

  // Step 1: Subject selection
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);

  // Step 3: Education level
  const [educationLevel, setEducationLevel] = useState("");

  // Step 2: Learning goals
  const [learningGoals, setLearningGoals] = useState<string[]>([]);

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeOnboarding = async () => {
    if (!user) {
      toast({
        title: "Not logged in",
        description: "You must be logged in to complete onboarding",
        variant: "destructive",
      });
      return;
    }

    try {
      // Save all onboarding data
      const { error } = await supabase.from("candidate_student").upsert({
        id: user.id,
        email: user.email,
        first_name: firstName,
        last_name: lastName,
        date_of_birth: dateOfBirth,
        education_level: educationLevel,
        subjects_of_interest: selectedSubjects,
        learning_goals: learningGoals,
        onboarding_completed: true,
      });

      if (error) throw error;

      toast({
        title: "Onboarding Complete",
        description: "Your profile has been set up successfully!",
      });

      // Don't navigate here - let the calling component handle navigation
      // This prevents conflicts with the process modal's redirect logic
    } catch (error: any) {
      console.error("Error completing onboarding:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to complete onboarding",
        variant: "destructive",
      });
    }
  };

  const value = {
    currentStep,
    totalSteps,
    nextStep,
    prevStep,
    completeOnboarding,
    firstName,
    setFirstName,
    lastName,
    setLastName,
    dateOfBirth,
    setDateOfBirth,
    selectedSubjects,
    setSelectedSubjects,
    educationLevel,
    setEducationLevel,
    learningGoals,
    setLearningGoals,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
};

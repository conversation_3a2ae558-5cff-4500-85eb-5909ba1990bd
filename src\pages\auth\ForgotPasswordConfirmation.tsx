import { Link } from "react-router-dom";

const ForgotPasswordConfirmation = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-purple-700">Check Your Email</h1>

        <div className="text-center">
          <p className="mb-4">
            We've sent a password reset link to your email address.
          </p>
          <p className="mb-6">
            Please check your inbox and follow the instructions to reset your
            password.
          </p>

          <div className="text-sm text-gray-600">
            <p>Didn't receive an email?</p>
            <p>
              Check your spam folder or{" "}
              <Link
                to="/forgot-password"
                className="text-purple-600 hover:underline"
              >
                try again
              </Link>
            </p>
          </div>
        </div>

        <div className="pt-4">
          <Link
            to="/login"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Return to Login
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordConfirmation;


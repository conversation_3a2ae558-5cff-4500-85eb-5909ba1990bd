import { create } from 'zustand';

// List of common timezones with their IANA names and display names
export const COMMON_TIMEZONES = [
  { value: 'America/New_York', label: 'Eastern Time (ET) - New York' },
  { value: 'America/Chicago', label: 'Central Time (CT) - Chicago' },
  { value: 'America/Denver', label: 'Mountain Time (MT) - Denver' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT) - Los Angeles' },
  { value: 'America/Anchorage', label: 'Alaska Time - Anchorage' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time - Honolulu' },
  { value: 'America/Toronto', label: 'Eastern Time - Toronto' },
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT) - London' },
  { value: 'Europe/Paris', label: 'Central European Time (CET) - Paris' },
  { value: 'Europe/Berlin', label: 'Central European Time (CET) - Berlin' },
  { value: 'Europe/Moscow', label: 'Moscow Time - Moscow' },
  { value: 'Asia/Dubai', label: 'Gulf Standard Time - Dubai' },
  { value: 'Asia/Kolkata', label: 'India Standard Time - Mumbai' },
  { value: 'Asia/Shanghai', label: 'China Standard Time - Shanghai' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time - Tokyo' },
  { value: 'Asia/Seoul', label: 'Korea Standard Time - Seoul' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time - Sydney' },
  { value: 'Pacific/Auckland', label: 'New Zealand Time - Auckland' },
];

// Function to get the user's local timezone
export const getUserTimezone = (): string => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (e) {
    console.error('Error getting user timezone:', e);
    return 'UTC';
  }
};

// Function to format the current time in a given timezone
export const formatTimeInTimezone = (timezone: string): string => {
  try {
    return new Date().toLocaleTimeString('en-US', { 
      timeZone: timezone,
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  } catch (e) {
    console.error(`Error formatting time for timezone ${timezone}:`, e);
    return '(invalid timezone)';
  }
};

interface TimezoneState {
  // State for each timezone selector instance
  instances: Record<string, {
    currentTime: string;
    searchTerm: string;
    isOpen: boolean;
    filteredTimezones: typeof COMMON_TIMEZONES;
  }>;
  
  // Actions
  initializeInstance: (instanceId: string) => void;
  updateCurrentTime: (instanceId: string, timezone: string) => void;
  setSearchTerm: (instanceId: string, searchTerm: string) => void;
  setIsOpen: (instanceId: string, isOpen: boolean) => void;
  updateFilteredTimezones: (instanceId: string, searchTerm: string) => void;
  cleanupInstance: (instanceId: string) => void;
}

export const useTimezoneStore = create<TimezoneState>((set, get) => ({
  instances: {},

  initializeInstance: (instanceId: string) => {
    set((state) => ({
      instances: {
        ...state.instances,
        [instanceId]: {
          currentTime: '',
          searchTerm: '',
          isOpen: false,
          filteredTimezones: COMMON_TIMEZONES,
        }
      }
    }));
  },

  updateCurrentTime: (instanceId: string, timezone: string) => {
    const currentTime = formatTimeInTimezone(timezone);
    set((state) => ({
      instances: {
        ...state.instances,
        [instanceId]: {
          ...state.instances[instanceId],
          currentTime
        }
      }
    }));
  },

  setSearchTerm: (instanceId: string, searchTerm: string) => {
    set((state) => ({
      instances: {
        ...state.instances,
        [instanceId]: {
          ...state.instances[instanceId],
          searchTerm
        }
      }
    }));
    
    // Update filtered timezones
    get().updateFilteredTimezones(instanceId, searchTerm);
  },

  setIsOpen: (instanceId: string, isOpen: boolean) => {
    set((state) => ({
      instances: {
        ...state.instances,
        [instanceId]: {
          ...state.instances[instanceId],
          isOpen
        }
      }
    }));
  },

  updateFilteredTimezones: (instanceId: string, searchTerm: string) => {
    const filtered = searchTerm
      ? COMMON_TIMEZONES.filter(tz => 
          tz.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tz.value.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : COMMON_TIMEZONES;

    set((state) => ({
      instances: {
        ...state.instances,
        [instanceId]: {
          ...state.instances[instanceId],
          filteredTimezones: filtered
        }
      }
    }));
  },

  cleanupInstance: (instanceId: string) => {
    set((state) => {
      const { [instanceId]: removed, ...rest } = state.instances;
      return { instances: rest };
    });
  },
}));

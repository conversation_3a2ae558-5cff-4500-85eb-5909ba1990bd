import { create } from "zustand";

interface TodaySession {
  id: string;
  time: string;
  subject: string;
  studentName: string;
  grade: string;
  status: "completed" | "current" | "upcoming";
  timeRemaining?: string;
}

interface UpcomingSession {
  id: string;
  date: string;
  day: string;
  time: string;
  subject: string;
  studentName: string;
  grade: string;
  topics: string[];
  platform: "zoom" | "google-meet" | "other";
  duration: string;
  timeUntil: string;
}

interface PastSession {
  id: string;
  date: string;
  time: string;
  studentName: string;
  subject: string;
  topic: string;
  duration: string;
  status: "completed" | "missed" | "canceled";
  rating: number;
}

interface Student {
  id: string;
  name: string;
  grade: string;
  subjects: string[];
  photo: string;
}

interface TutorDashboardState {
  // UI State
  showWelcomeMessage: boolean;
  selectedDate: Date;
  pastSessionsPage: number;
  pastSessionsFilter: string;

  // Data
  todaySessions: TodaySession[];
  upcomingSessions: UpcomingSession[];
  pastSessions: PastSession[];
  students: Student[];

  // Actions
  setShowWelcomeMessage: (show: boolean) => void;
  setSelectedDate: (date: Date) => void;
  setPastSessionsPage: (page: number) => void;
  setPastSessionsFilter: (filter: string) => void;
}

// Sample data
const sampleTodaySessions: TodaySession[] = [
  {
    id: "ts1",
    time: "09:30 AM",
    subject: "Algebra Fundamentals",
    studentName: "Alex Johnson",
    grade: "Grade 11",
    status: "completed"
  },
  {
    id: "ts2",
    time: "10:15 AM",
    subject: "Physics: Wave Mechanics",
    studentName: "Emma & Michael",
    grade: "Grade 11",
    status: "current"
  },
  {
    id: "ts3",
    time: "02:00 PM",
    subject: "Chemistry: Organic Compounds",
    studentName: "Sarah Williams",
    grade: "Grade 12",
    status: "upcoming",
    timeRemaining: "2 hours 40 min"
  }
];

const sampleUpcomingSessions: UpcomingSession[] = [
  {
    id: "us1",
    date: "Jul 5",
    day: "Friday",
    time: "09:00 AM",
    subject: "Calculus: Integrals & Applications",
    studentName: "Daniel Zhang",
    grade: "Grade 12",
    topics: ["Integration by Parts", "Area Under Curves", "Practice Problems"],
    platform: "zoom",
    duration: "60 minutes",
    timeUntil: "Starts in 19h 32m"
  },
  {
    id: "us2",
    date: "Jul 6",
    day: "Saturday",
    time: "02:30 PM",
    subject: "Biology: Genetics & Heredity",
    studentName: "Group Session",
    grade: "3 students",
    topics: ["Mendel's Laws", "Punnett Squares", "Genetic Disorders"],
    platform: "google-meet",
    duration: "90 minutes",
    timeUntil: "In 2 days"
  }
];

const samplePastSessions: PastSession[] = [
  {
    id: "ps1",
    date: "July 3, 2023",
    time: "10:30 AM - 11:30 AM",
    studentName: "Alex Johnson",
    subject: "Algebra Fundamentals",
    topic: "Equations & Inequalities",
    duration: "60 min",
    status: "completed",
    rating: 4.5
  },
  {
    id: "ps2",
    date: "July 1, 2023",
    time: "2:30 PM - 3:30 PM",
    studentName: "Sarah Williams",
    subject: "Chemistry",
    topic: "Acid-Base Reactions",
    duration: "60 min",
    status: "completed",
    rating: 5.0
  },
  {
    id: "ps3",
    date: "June 29, 2023",
    time: "3:00 PM - 4:00 PM",
    studentName: "Michael Lee",
    subject: "Physics",
    topic: "Forces & Motion",
    duration: "60 min",
    status: "missed",
    rating: 4.0
  }
];

const sampleStudents: Student[] = [
  {
    id: "st1",
    name: "Alex Johnson",
    grade: "Grade 11",
    subjects: ["Algebra", "Trigonometry"],
    photo: "/images/students/alex.jpg"
  },
  {
    id: "st2",
    name: "Sarah Williams",
    grade: "Grade 12",
    subjects: ["Organic Chemistry", "Reactions"],
    photo: "/images/students/sarah.jpg"
  },
  {
    id: "st3",
    name: "Michael Lee",
    grade: "Grade 11",
    subjects: ["Physics", "Mechanics"],
    photo: "/images/students/michael.jpg"
  }
];

export const useTutorDashboardStore = create<TutorDashboardState>((set) => ({
  // Initial UI state
  showWelcomeMessage: false,
  selectedDate: new Date(),
  pastSessionsPage: 1,
  pastSessionsFilter: "Last 7 days",

  // Initial data
  todaySessions: sampleTodaySessions,
  upcomingSessions: sampleUpcomingSessions,
  pastSessions: samplePastSessions,
  students: sampleStudents,

  // Actions
  setShowWelcomeMessage: (show) => set({ showWelcomeMessage: show }),
  setSelectedDate: (date) => set({ selectedDate: date }),
  setPastSessionsPage: (page) => set({ pastSessionsPage: page }),
  setPastSessionsFilter: (filter) => set({ pastSessionsFilter: filter }),
}));
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  BookOpen,
  FileText,
  Video,
  Download,
  Search,
  Filter,
  Clock,
  Bell,
  Mail,
  ArrowRight,
  Sparkles,
  Target,
  Users,
  Calendar,
  Star,
  PlayCircle,
  BookMarked,
  GraduationCap,
  Lightbulb,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Input } from '@/components/ui/Input';

const Resources = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleNotifyMe = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      // Here you would typically send the email to your backend
      console.log('Subscribed email:', email);
    }
  };

  // Preview of upcoming resource categories
  const upcomingResources = [
    {
      category: "Study Guides",
      icon: <BookOpen className="w-6 h-6" />,
      description: "Comprehensive study materials for all subjects",
      items: ["Topic summaries", "Practice questions", "Exam strategies"],
      color: "from-blue-500 to-blue-600",
      estimatedCount: "50+"
    },
    {
      category: "Video Tutorials",
      icon: <Video className="w-6 h-6" />,
      description: "Step-by-step video explanations and demonstrations",
      items: ["Concept explanations", "Problem solving", "Interactive demos"],
      color: "from-purple-500 to-purple-600",
      estimatedCount: "100+"
    },
    {
      category: "Practice Tests",
      icon: <FileText className="w-6 h-6" />,
      description: "Mock exams and practice assessments",
      items: ["Timed practice tests", "Instant feedback", "Performance analytics"],
      color: "from-green-500 to-green-600",
      estimatedCount: "30+"
    },
    {
      category: "Templates & Tools",
      icon: <Download className="w-6 h-6" />,
      description: "Downloadable templates and learning tools",
      items: ["Note templates", "Study planners", "Progress trackers"],
      color: "from-orange-500 to-orange-600",
      estimatedCount: "25+"
    },
    {
      category: "Research Papers",
      icon: <BookMarked className="w-6 h-6" />,
      description: "Latest research and academic publications",
      items: ["Academic papers", "Case studies", "Industry reports"],
      color: "from-indigo-500 to-indigo-600",
      estimatedCount: "75+"
    },
    {
      category: "Interactive Simulations",
      icon: <PlayCircle className="w-6 h-6" />,
      description: "Hands-on learning experiences and simulations",
      items: ["Virtual labs", "Interactive models", "Simulation exercises"],
      color: "from-pink-500 to-pink-600",
      estimatedCount: "40+"
    }
  ];

  const features = [
    {
      icon: <Search className="w-5 h-5" />,
      title: "Smart Search",
      description: "Find exactly what you need with AI-powered search"
    },
    {
      icon: <Filter className="w-5 h-5" />,
      title: "Advanced Filters",
      description: "Filter by subject, difficulty, type, and more"
    },
    {
      icon: <Download className="w-5 h-5" />,
      title: "Offline Access",
      description: "Download resources for offline studying"
    },
    {
      icon: <TrendingUp className="w-5 h-5" />,
      title: "Progress Tracking",
      description: "Track your learning progress across all resources"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative py-20 px-4 text-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div className="relative max-w-6xl mx-auto">
          <div className="inline-flex items-center gap-2 bg-orange-100 text-orange-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            Coming Soon
          </div>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Learning <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Resources</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            We're building an extensive library of study materials, practice tests, video tutorials, and interactive tools 
            to supercharge your learning journey. Get ready for something amazing!
          </p>
          
          {/* Coming Soon Animation */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="w-32 h-32 rounded-full flex items-center justify-center">
                <img
                  src="/favicon.png"
                  alt="rfLearn Logo"
                  className="w-16 h-16 object-contain"
                />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
                <Sparkles className="w-4 h-4 text-yellow-800" />
              </div>
            </div>
          </div>

          {/* Notify Me Form */}
          <div className="max-w-md mx-auto">
            {!isSubscribed ? (
              <form onSubmit={handleNotifyMe} className="flex gap-2">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="flex-1"
                />
                <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <Bell className="w-4 h-4 mr-2" />
                  Notify Me
                </Button>
              </form>
            ) : (
              <div className="bg-green-100 text-green-800 px-6 py-3 rounded-lg flex items-center gap-2">
                <Star className="w-5 h-5" />
                <span>Thanks! We'll notify you when resources are available.</span>
              </div>
            )}
            <p className="text-sm text-gray-500 mt-2">
              Be the first to know when our resource library launches
            </p>
          </div>
        </div>
      </section>

      {/* What's Coming Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Target className="w-4 h-4" />
              What's Coming
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Comprehensive Learning Resources</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We're curating the most comprehensive collection of learning materials to support every aspect of your educational journey
            </p>
          </div>

          {/* Resource Categories Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {upcomingResources.map((resource, index) => (
              <Card key={index} className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${resource.color}`}></div>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${resource.color} flex items-center justify-center text-white group-hover:scale-110 transition-transform`}>
                      {resource.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{resource.category}</CardTitle>
                      <Badge variant="secondary" className="text-xs">{resource.estimatedCount} items</Badge>
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm">{resource.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {resource.items.map((item, idx) => (
                      <li key={idx} className="flex items-center gap-2 text-sm text-gray-500">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Preview */}
      <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Powerful Features Coming Soon</h2>
            <p className="text-xl text-gray-600">Advanced tools to enhance your learning experience</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 mx-auto mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Development Timeline</h2>
            <p className="text-xl text-gray-600">Here's what we're working on and when to expect it</p>
          </div>

          <div className="space-y-8">
            {[
              {
                phase: "Phase 1",
                title: "Foundation Resources",
                description: "Basic study guides and practice materials",
                timeline: "Q2 2025",
                status: "in-progress",
                items: ["Study guides", "Practice questions", "Basic templates"]
              },
              {
                phase: "Phase 2", 
                title: "Interactive Content",
                description: "Video tutorials and interactive simulations",
                timeline: "Q3 2025",
                status: "planned",
                items: ["Video library", "Interactive demos", "Virtual labs"]
              },
              {
                phase: "Phase 3",
                title: "Advanced Features",
                description: "AI-powered recommendations and analytics",
                timeline: "Q4 2025", 
                status: "planned",
                items: ["Smart search", "Progress analytics", "Personalized recommendations"]
              }
            ].map((phase, index) => (
              <div key={index} className="flex gap-6 items-start">
                <div className="flex-shrink-0">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${
                    phase.status === 'in-progress' ? 'bg-blue-500' : 'bg-gray-400'
                  }`}>
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-xl font-semibold">{phase.title}</h3>
                    <Badge variant={phase.status === 'in-progress' ? 'default' : 'secondary'}>
                      {phase.timeline}
                    </Badge>
                    {phase.status === 'in-progress' && (
                      <Badge className="bg-green-100 text-green-700">In Progress</Badge>
                    )}
                  </div>
                  <p className="text-gray-600 mb-3">{phase.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {phase.items.map((item, idx) => (
                      <span key={idx} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {item}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Start Learning?</h2>
          <p className="text-xl mb-8 opacity-90">
            While we're building our resource library, you can already start your learning journey with our expert tutors
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
              <Link to="/register" className="flex items-center gap-2">
                <GraduationCap className="w-5 h-5" />
                Start Learning Now
              </Link>
            </Button>
            <Button size="lg" className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-blue-600 transition-colors">
              <Link to="/how-it-works" className="flex items-center gap-2">
                <Lightbulb className="w-5 h-5" />
                Learn How It Works
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Resources;

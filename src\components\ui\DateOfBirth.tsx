import React, { useRef, useEffect, KeyboardEvent } from "react";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { useDateOfBirthStore } from "@/store/dateOfBirthStore";
import { useAutoFocusNextField } from "@/hooks/useAutoFocusNextField";

interface DateOfBirthProps {
  value: string | null;
  onChange: (date: string | null) => void;
  id?: string;
  className?: string;
}

const DateOfBirth: React.FC<DateOfBirthProps> = ({
  value,
  onChange,
  id = "date-of-birth",
  className = "",
}) => {
  // Get state and actions from Zustand store
  const {
    month,
    day,
    year,
    formattedDate,
    setMonth,
    setDay,
    setYear,
    touchField,
    reset,
  } = useDateOfBirthStore();

  // Track if we've initialized from props to prevent flickering
  const initializedRef = useRef(false);

  // Initialize with provided value only once
  useEffect(() => {
    if (value && !initializedRef.current) {
      reset(value);
      initializedRef.current = true;
    } else if (!initializedRef.current) {
      // If no initial value, still mark as initialized after component mount
      initializedRef.current = true;
    }
  }, [value, reset]);

  // Sync with parent component when our formatted date changes
  // but only if the user has actually made changes
  useEffect(() => {
    if (onChange && formattedDate !== value && initializedRef.current) {
      onChange(formattedDate);
    }
  }, [formattedDate, onChange, value]);

  // Refs for focus management
  const monthRef = useRef<HTMLInputElement>(null);
  const dayRef = useRef<HTMLInputElement>(null);
  const yearRef = useRef<HTMLInputElement>(null);

  // Auto-focus to next field when current field is complete
  useAutoFocusNextField({
    value: month.value,
    error: month.error,
    initialized: initializedRef.current,
    expectedLength: 2,
    nextFieldRef: dayRef,
  });

  useAutoFocusNextField({
    value: day.value,
    error: day.error,
    initialized: initializedRef.current,
    expectedLength: 2,
    nextFieldRef: yearRef,
  });

  // Handle backspace key for navigation between fields
  const handleKeyDown = (
    e: KeyboardEvent<HTMLInputElement>,
    field: "month" | "day" | "year"
  ) => {
    if (e.key === "Backspace") {
      if (field === "day" && !day.value && monthRef.current) {
        monthRef.current.focus();
      } else if (field === "year" && !year.value && dayRef.current) {
        dayRef.current.focus();
      }
    }
  };

  // Helper to render error message
  const renderError = (field: { error: string | null; touched: boolean }) => {
    if (field.touched && field.error) {
      return <p className="text-xs text-destructive mt-1">{field.error}</p>;
    }
    return null;
  };

  return (
    <div className={`space-y-1 ${className}`}>
      <div className="flex gap-2">
        <div className="flex flex-col">
          <Label htmlFor={`${id}-month`} className="text-xs mb-1">
            Month
          </Label>
          <Input
            id={`${id}-month`}
            ref={monthRef}
            value={month.value}
            onChange={(e) => setMonth(e.target.value)}
            onBlur={() => touchField("month")}
            placeholder="MM"
            maxLength={2}
            className={`w-20 text-center ${
              month.touched && month.error ? "border-destructive" : ""
            }`}
            inputMode="numeric"
          />
          {renderError(month)}
        </div>

        <div className="flex flex-col">
          <Label htmlFor={`${id}-day`} className="text-xs mb-1">
            Day
          </Label>
          <Input
            id={`${id}-day`}
            ref={dayRef}
            value={day.value}
            onChange={(e) => setDay(e.target.value)}
            onBlur={() => touchField("day")}
            onKeyDown={(e) => handleKeyDown(e, "day")}
            placeholder="DD"
            maxLength={2}
            className={`w-20 text-center ${
              day.touched && day.error ? "border-destructive" : ""
            }`}
            inputMode="numeric"
          />
          {renderError(day)}
        </div>

        <div className="flex flex-col">
          <Label htmlFor={`${id}-year`} className="text-xs mb-1">
            Year
          </Label>
          <Input
            id={`${id}-year`}
            ref={yearRef}
            value={year.value}
            onChange={(e) => setYear(e.target.value)}
            onBlur={() => touchField("year")}
            onKeyDown={(e) => handleKeyDown(e, "year")}
            placeholder="YYYY"
            maxLength={4}
            className={`w-24 text-center ${
              year.touched && year.error ? "border-destructive" : ""
            }`}
            inputMode="numeric"
          />
          {renderError(year)}
        </div>
      </div>
    </div>
  );
};

export default DateOfBirth;

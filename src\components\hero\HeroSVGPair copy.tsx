import React from "react";

const HeroSVGPair: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div style={{ display: 'flex', gap: '8px', width: '100%', height: '100%' }}>
      {/* Hero Left SVG */}
      <svg
        style={{ width: '52.5%', height: '100%' }}
        viewBox="0 0 969 818"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="none"
      >
        <path d="M912 467.216V20C912 8.9543 903.046 0 892 0H20.5C9.4543 0 0.5 8.95432 0.5 20V797.5C0.5 808.546 9.45432 817.5 20.5 817.5H624.5C635.546 817.5 644.5 808.546 644.5 797.5V713.5C644.5 702.454 653.454 693.5 664.5 693.5H948.5C959.546 693.5 968.5 684.546 968.5 673.5V540.284C968.5 534.98 966.393 529.893 962.642 526.142L917.858 481.358C914.107 477.607 912 472.52 912 467.216Z" fill="#FFF0EA"/>
      </svg>

      {/* Hero Right SVG */}
      <svg
        style={{ width: '52.5%', height: '100%' }}
        viewBox="0 0 912 818"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        preserveAspectRatio="none"
      >
        <path d="M0 433.805V19C0 8.507 8.507 0 19 0H843.6C854.093 0 862.6 8.507 862.6 19V757.125C862.6 767.618 854.093 776.125 843.6 776.125H363.375C353.382 776.125 344.375 767.618 344.375 757.125V678.375C344.375 668.382 335.868 659.375 325.375 659.375H72.675C62.182 659.375 53.675 650.868 53.675 640.375V503.02C53.675 498.182 51.673 493.549 48.11 490.085L5.567 447.541C2.003 444.077 0 439.444 0 434.605Z" fill="#F3EDFD"/>
        <g opacity="0.4">
          <mask id="mask0_241_2" style={{maskType:"luminance"}} maskUnits="userSpaceOnUse" x="0" y="-1" width="908" height="818">
            <path d="M0 456.058V19.037C0 7.966 8.95 -1 20 -1H888C899.05 -1 908 7.966 908 19.037V796.963C908 808.034 899.05 817 888 817H382.5C371.45 817 362.5 808.034 362.5 796.963V712.809C362.5 701.739 353.55 692.772 342.5 692.772H76.5C65.45 692.772 56.5 683.806 56.5 672.735V529.252C56.5 523.943 54.39 518.843 50.64 515.086L5.86 470.224C2.11 466.467 0 461.368 0 456.058Z" fill="white"/>
          </mask>
          <g mask="url(#mask0_241_2)">
            <g opacity="0.4">
              <path d="M850 780V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M785 780V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M720 780V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M670 780V427" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M670 587V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M605 780V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M539 780V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M473 780V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M408 780V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M342 760V693" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M342 653V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M276 650V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M211 650V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M145 650V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M80 650V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M14 400V0" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 719H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M342 653H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 653H342" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M0 587L850 587" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 522H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 456H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 390H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 324H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 258H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 193H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
              <path d="M0 127H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M0 61H850" stroke="#6E94CC" strokeWidth="1.96" strokeLinecap="round" strokeLinejoin="round" strokeDasharray="1.96 7.83"/>
            </g>
          </g>
        </g>
        <path d="M785.877 -97.348C789.277 -97.348 792.033 -97.7109 792.033 -98.1587C792.033 -97.7109 794.791 -97.348 798.191 -97.348C794.791 -97.348 792.033 -96.9849 792.033 -96.5371C792.033 -96.9849 789.277 -97.348 785.877 -97.348Z" fill="#FFBBA3"/>
        <path d="M785.877 -103.834C789.277 -103.834 792.033 -104.197 792.033 -104.645C792.033 -104.197 794.791 -103.834 798.191 -103.834C794.791 -103.834 792.033 -103.471 792.033 -103.023C792.033 -103.471 789.277 -103.834 785.877 -103.834Z" fill="#FFBBA3"/>
        <path d="M638.101 -97.348C641.502 -97.348 644.258 -97.7109 644.258 -98.1587C644.258 -97.7109 647.015 -97.348 650.416 -97.348C647.015 -97.348 644.258 -96.9849 644.258 -96.5371C644.258 -96.9849 641.502 -97.348 638.101 -97.348Z" fill="#FFBBA3"/>
        <rect x="792.034" y="596.258" width="49.2586" height="49.2586" rx="5" transform="rotate(-180 792.034 596.258)" fill="#FF946F"/>
        <rect x="792.034" y="596.258" width="49.2586" height="49.2586" rx="5" transform="rotate(-180 792.034 596.258)" fill="#FFBBA3"/>
        <rect x="841.293" y="645.517" width="49.2586" height="49.2586" rx="5" transform="rotate(-180 841.293 645.517)" fill="#FF946F"/>
        <rect x="841.293" y="645.517" width="49.2586" height="49.2586" rx="5" transform="rotate(-180 841.293 645.517)" fill="#FFBBA3"/>
        <rect x="792.034" y="694.775" width="49.2586" height="49.2586" rx="5" transform="rotate(-180 792.034 694.775)" fill="#FF946F"/>
        <rect x="792.034" y="694.775" width="49.2586" height="49.2586" rx="5" transform="rotate(-180 792.034 694.775)" fill="#FFBBA3"/>
        <rect x="742.775" y="645.517" width="98.5172" height="49.2586" rx="5" transform="rotate(-180 742.775 645.517)" fill="#FFBBA3"/>
        <rect x="644.259" y="694.775" width="49.2586" height="49.2586" rx="5" transform="rotate(-180 644.259 694.775)" fill="#FFBBA3"/>
        <rect x="427.5" y="239.75" width="110.75" height="110.75" rx="5" transform="rotate(90 427.5 239.75)" fill="white"/>
        <rect x="427.5" y="239.75" width="110.75" height="110.75" rx="5" transform="rotate(90 427.5 239.75)" fill="white"/>
        <rect x="316.75" y="291" width="110.75" height="110.75" rx="5" transform="rotate(90 316.75 291)" fill="white"/>
        <rect x="538.249" y="239.75" width="110.75" height="110.75" rx="5" transform="rotate(90 538.249 239.75)" fill="white"/>
        <rect x="648.999" y="239.75" width="110.75" height="110.75" rx="5" transform="rotate(90 648.999 239.75)" fill="white"/>
        <rect x="648.999" y="350.5" width="110.75" height="110.75" rx="5" transform="rotate(90 648.999 350.5)" fill="white"/>
        <rect x="759.75" y="350.5" width="110.75" height="110.75" rx="5" transform="rotate(90 759.75 350.5)" fill="white"/>
        <rect x="427.5" y="350.5" width="110.75" height="110.75" rx="5" transform="rotate(90 427.5 350.5)" fill="white"/>
        <rect x="538.249" y="461.25" width="110.75" height="110.75" rx="5" transform="rotate(90 538.249 461.25)" fill="white"/>
        <rect x="316.75" y="461.25" width="110.75" height="110.75" rx="5" transform="rotate(90 316.75 461.25)" fill="white"/>
        <rect x="538.249" y="129" width="110.75" height="110.75" rx="5" transform="rotate(90 538.249 129)" fill="white"/>
        <rect x="538.249" y="129" width="110.75" height="110.75" rx="5" transform="rotate(90 538.249 129)" fill="white"/>
        <rect x="172" y="107" width="219" height="105" rx="10" fill="#FFDCD0"/>
        <text x="205" y="155" fill="#3E1168" fontSize="30" fontFamily="Arial" fontWeight="bold">AI Adapted</text>
        <text x="205" y="180" fill="#3E1168" fontSize="14" fontFamily="Arial" fontWeight="bold">Learning Resources</text>
        <rect x="172" y="211" width="86" height="86" rx="5" transform="rotate(90 172 211)" fill="#AF59FF"/>
        <rect x="172" y="211" width="86" height="86" rx="5" transform="rotate(90 172 211)" fill="#FF946F"/>
        <circle cx="128.5" cy="253.5" r="24.75" stroke="white" strokeWidth="1.5"/>
        <circle cx="128.5" cy="253.5" r="13.75" stroke="white" strokeWidth="1.5"/>
        <rect x="543" y="284" width="219" height="105" rx="10" fill="#E7CDFF"/>
        <path d="M706.304 341.876C710.23 339.461 712.832 335.333 712.832 330.647C712.832 323.218 706.304 317.172 698.281 317.172C690.259 317.172 683.731 323.218 683.731 330.647C683.731 335.333 686.326 339.461 690.259 341.876C681.364 344.917 675 352.814 675 362.089H678.88C678.88 352.183 687.585 344.122 698.281 344.122C708.978 344.122 717.683 352.183 717.683 362.089H721.563C721.563 352.814 715.198 344.911 706.304 341.876ZM698.281 340.529C692.396 340.529 687.611 336.098 687.611 330.647C687.611 325.196 692.396 320.765 698.281 320.765C704.167 320.765 708.952 325.196 708.952 330.647C708.952 336.098 704.167 340.529 698.281 340.529Z" fill="white"/>
        <path d="M725.706 332.893C729.632 330.477 732.234 326.35 732.234 321.664C732.234 314.234 725.706 308.188 717.683 308.188C713.796 308.188 710.27 309.615 707.655 311.921C706.685 312.777 705.852 313.755 705.164 314.823C706.377 315.278 707.524 315.837 708.592 316.504C709.28 315.472 710.159 314.562 711.194 313.827C712.997 312.547 715.238 311.776 717.677 311.776C723.562 311.776 728.347 316.207 728.347 321.658C728.347 326.18 725.044 329.998 720.56 331.163C719.643 331.4 718.679 331.539 717.677 331.539C717.336 331.539 716.995 331.521 716.661 331.491C716.588 332.784 716.372 334.04 716.005 335.242C716.562 335.2 717.106 335.133 717.677 335.133C718.463 335.133 719.236 335.187 720.003 335.272C729.606 336.341 737.078 343.922 737.078 353.1H740.958C740.958 343.825 734.594 335.922 725.699 332.887L725.706 332.893Z" fill="#FF946F"/>
        {/* Circular dot pattern with varying sizes and opacity */}
        {/* Row 1 - 3 dots */}
        <circle cx="758.5" cy="275.5" r="1" fill="#3E1168" fillOpacity="0.3"/>
        <circle cx="766.5" cy="275.5" r="1.2" fill="#3E1168" fillOpacity="0.4"/>
        <circle cx="774.5" cy="275.5" r="1" fill="#3E1168" fillOpacity="0.3"/>

        {/* Row 2 - 5 dots */}
        <circle cx="750.5" cy="281.5" r="1" fill="#3E1168" fillOpacity="0.3"/>
        <circle cx="758.5" cy="281.5" r="1.3" fill="#3E1168" fillOpacity="0.5"/>
        <circle cx="766.5" cy="281.5" r="1.5" fill="#3E1168" fillOpacity="0.7"/>
        <circle cx="774.5" cy="281.5" r="1.3" fill="#3E1168" fillOpacity="0.5"/>
        <circle cx="782.5" cy="281.5" r="1" fill="#3E1168" fillOpacity="0.3"/>

        {/* Row 3 - 7 dots */}
        <circle cx="746.5" cy="287.5" r="0.8" fill="#3E1168" fillOpacity="0.2"/>
        <circle cx="754.5" cy="287.5" r="1.2" fill="#3E1168" fillOpacity="0.4"/>
        <circle cx="762.5" cy="287.5" r="1.5" fill="#3E1168" fillOpacity="0.6"/>
        <circle cx="770.5" cy="287.5" r="1.8" fill="#3E1168" fillOpacity="0.8"/>
        <circle cx="778.5" cy="287.5" r="1.5" fill="#3E1168" fillOpacity="0.6"/>
        <circle cx="786.5" cy="287.5" r="1.2" fill="#3E1168" fillOpacity="0.4"/>

        {/* Row 4 - 9 dots (center row) */}
        <circle cx="742.5" cy="293.5" r="0.8" fill="#3E1168" fillOpacity="0.2"/>
        <circle cx="750.5" cy="293.5" r="1.2" fill="#3E1168" fillOpacity="0.4"/>
        <circle cx="758.5" cy="293.5" r="1.6" fill="#3E1168" fillOpacity="0.7"/>
        <circle cx="766.5" cy="293.5" r="2" fill="#3E1168" fillOpacity="0.9"/>
        <circle cx="774.5" cy="293.5" r="2.2" fill="#3E1168"/>
        <circle cx="782.5" cy="293.5" r="2" fill="#3E1168" fillOpacity="0.9"/>
        <circle cx="790.5" cy="293.5" r="1.6" fill="#3E1168" fillOpacity="0.7"/>


        {/* Row 5 - 7 dots */}
        <circle cx="746.5" cy="299.5" r="0.8" fill="#3E1168" fillOpacity="0.2"/>
        <circle cx="754.5" cy="299.5" r="1.2" fill="#3E1168" fillOpacity="0.4"/>
        <circle cx="762.5" cy="299.5" r="1.5" fill="#3E1168" fillOpacity="0.6"/>
        <circle cx="770.5" cy="299.5" r="1.8" fill="#3E1168" fillOpacity="0.8"/>
        <circle cx="778.5" cy="299.5" r="1.5" fill="#3E1168" fillOpacity="0.6"/>
        <circle cx="786.5" cy="299.5" r="1.2" fill="#3E1168" fillOpacity="0.4"/>
        <circle cx="794.5" cy="299.5" r="0.8" fill="#3E1168" fillOpacity="0.2"/>

        {/* Row 6 - 5 dots */}
        <circle cx="750.5" cy="305.5" r="1" fill="#3E1168" fillOpacity="0.3"/>
        <circle cx="758.5" cy="305.5" r="1.3" fill="#3E1168" fillOpacity="0.5"/>
        <circle cx="766.5" cy="305.5" r="1.5" fill="#3E1168" fillOpacity="0.7"/>
        <circle cx="774.5" cy="305.5" r="1.3" fill="#3E1168" fillOpacity="0.5"/>
        <circle cx="782.5" cy="305.5" r="1" fill="#3E1168" fillOpacity="0.3"/>

        {/* Row 7 - 3 dots */}
        <circle cx="758.5" cy="311.5" r="1" fill="#3E1168" fillOpacity="0.3"/>
        <circle cx="766.5" cy="311.5" r="1.2" fill="#3E1168" fillOpacity="0.4"/>
        <circle cx="774.5" cy="311.5" r="1" fill="#3E1168" fillOpacity="0.3"/>
        <text x="600" y="330" fill="#3E1168" fontSize="28" fontWeight="bold" fontFamily="Arial">+120</text>
        <text x="600" y="350" fill="#3E1168" fontSize="15" fontWeight="bold" fontFamily="Arial">Mentors</text>
      </svg>
    </div>
  );
};

export default HeroSVGPair;

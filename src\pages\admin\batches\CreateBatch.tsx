// src/pages/admin/batches/CreateBatch.tsx
import React from "react";
import { useNavigate } from "react-router-dom";
import AdminPageLayout from "@/components/layouts/AdminPageLayout";
import BatchCreationForm from "@/components/admin/batches/BatchCreationForm";
import BatchCreationSidebar from "@/components/admin/batches/BatchCreationSidebar";
import { useProfileData } from "@/hooks/useProfileData";
import { useToast } from "@/hooks/useToast";
import { Button } from "@/components/ui/Button";
import { ArrowLeft } from "lucide-react";

const CreateBatch: React.FC = () => {
  const navigate = useNavigate();
  const profileData = useProfileData();
  const { toast } = useToast();

  const handleBatchCreationSuccess = () => {
    toast({
      title: "Batch Created",
      description: "The batch has been created successfully",
      type: "success",
    });
    
    // Navigate back to the batch management page after a short delay
    setTimeout(() => {
      navigate("/admin/batches");
    }, 1500);
  };

  return (
    <AdminPageLayout
      title="Create Batch"
      description="Create a new batch and assign students, subjects, and tutors"
      profileData={profileData}
      actions={
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate("/admin/batches")}
          className="flex items-center gap-1"
        >
          <ArrowLeft size={16} />
          Back to Batch Management
        </Button>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl">
        <div className="lg:max-w-2xl">
          <BatchCreationForm
            onSuccess={handleBatchCreationSuccess}
            onCancel={() => navigate("/admin/batches")}
          />
        </div>

        {/* Subscription Details Panel - Only visible on larger screens */}
        <div className="hidden lg:block">
          <BatchCreationSidebar />
        </div>
      </div>
    </AdminPageLayout>
  );
};

export default CreateBatch;

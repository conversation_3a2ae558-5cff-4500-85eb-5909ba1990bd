import { refreshToken } from '@/utils/supabase/middleware';
import config from './config';

/**
 * Enhanced API client that handles token refresh before making authenticated requests
 */
export const apiClient = {
  /**
   * Make an authenticated GET request
   */
  async get(url: string, options = {}) {
    // Refresh the token before making the request
    await refreshToken();
    
    const response = await fetch(`${config.apiUrl}${url}`, {
      method: 'GET',
      headers: {
        ...config.defaultHeaders,
        ...(options.headers || {})
      },
      credentials: config.withCredentials ? 'include' : 'same-origin',
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    return response.json();
  },
  
  /**
   * Make an authenticated POST request
   */
  async post(url: string, data: any, options = {}) {
    // Refresh the token before making the request
    await refreshToken();
    
    const response = await fetch(`${config.apiUrl}${url}`, {
      method: 'POST',
      headers: {
        ...config.defaultHeaders,
        ...(options.headers || {})
      },
      body: JSON.stringify(data),
      credentials: config.withCredentials ? 'include' : 'same-origin',
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    return response.json();
  },
  
  // Add other methods (PUT, DELETE, etc.) following the same pattern
};


import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { useProfileData } from "@/hooks/useProfileData";
import StudentPageLayout from "@/components/layouts/StudentPageLayout";
import { useSubscriptionWorkflowStore } from "@/store/subscriptionWorkflowStore";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage
} from "@/components/ui/Breadcrumb";
import {
  Calculator,
  ArrowRight,
  ArrowLeft,
  Home,
  DollarSign
} from "lucide-react";
import { Link } from "react-router-dom";
import { ROUTES } from "@/routes/RouteConfig";

const NewSubscriptionPricing: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { displayName } = useProfileData();
  const { currentWorkflow, selectedProduct, isLoading, getActiveWorkflow } = useSubscriptionWorkflowStore();

  // Load workflow data if missing
  useEffect(() => {
    const loadWorkflowData = async () => {
      if (!user?.id) return;

      // If we don't have a current workflow or selected product, try to load the active workflow
      if (!currentWorkflow || !selectedProduct) {
        console.log('Missing workflow data, attempting to load active workflow');
        await getActiveWorkflow(user.id);
      }
    };

    loadWorkflowData();
  }, [user?.id, currentWorkflow, selectedProduct, getActiveWorkflow]);

  const handleContinue = () => {
    navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_PURCHASE.path);
  };

  const handleBack = () => {
    if (currentWorkflow?.product_type === 'booster') {
      // For booster, go back to product selection
      navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_SELECT.path);
    } else {
      // For custom/preparation, go back to curriculum configuration
      navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_CONFIGURE.path);
    }
  };

  return (
    <StudentPageLayout
      title="Review Pricing"
      profileData={{
        displayName: displayName || "Student",
        email: "",
        photoUrl: ""
      }}
      description="Review your pricing details"
    >
      {/* Breadcrumb Navigation */}
      <div className="mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_DASHBOARD.path} className="flex items-center">
                  <Home className="h-4 w-4 mr-1" />
                  Dashboard
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_SUBSCRIPTIONS.path}>
                  Subscriptions
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to={ROUTES.STUDENT_NEW_SUBSCRIPTION.path}>
                  New Subscription
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Review Pricing</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              Price Calculation
            </CardTitle>
            <CardDescription>
              Review your pricing details for the {currentWorkflow?.product_type} package
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading workflow data...</p>
              </div>
            ) : selectedProduct ? (
              <div className="space-y-6">
                {/* Selected Product Summary */}
                <div className="bg-blue-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">Selected Package</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">{selectedProduct.name}</p>
                      <p className="text-sm text-gray-600">{selectedProduct.description}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-blue-600">${selectedProduct.price.toFixed(2)}</p>
                      <p className="text-sm text-gray-500">Duration: {selectedProduct.duration_days} days</p>
                    </div>
                  </div>
                </div>

                {/* Pricing Breakdown */}
                <div className="border rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <DollarSign className="h-5 w-5 mr-2" />
                    Pricing Breakdown
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Base Price</span>
                      <span>${selectedProduct.price.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Duration</span>
                      <span>{selectedProduct.duration_days} days</span>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Price per day</span>
                      <span>${(selectedProduct.price / selectedProduct.duration_days).toFixed(2)}</span>
                    </div>
                    <hr />
                    <div className="flex justify-between font-semibold text-lg">
                      <span>Total</span>
                      <span>${selectedProduct.price.toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                {/* Features Included */}
                {selectedProduct.features && (
                  <div className="border rounded-lg p-6">
                    <h3 className="text-lg font-semibold mb-4">What's Included</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {Object.entries(selectedProduct.features).map(([key, value]) => (
                        <div key={key} className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                          <span className="text-sm">
                            {key.split('_').map(word =>
                              word.charAt(0).toUpperCase() + word.slice(1)
                            ).join(' ')}: {String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12">
                <Calculator className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No Product Selected
                </h3>
                <p className="text-gray-600 mb-6">
                  Please go back and select a product to see pricing details.
                </p>
                <Button onClick={() => navigate(ROUTES.STUDENT_NEW_SUBSCRIPTION_SELECT.path)}>
                  Select Product
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          {selectedProduct && (
            <Button onClick={handleContinue}>
              Continue to Purchase
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </StudentPageLayout>
  );
};

export default NewSubscriptionPricing;

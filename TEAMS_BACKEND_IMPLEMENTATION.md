# Microsoft Teams Backend Implementation

## 🎯 **Overview**

This implementation uses **Supabase Edge Functions** for server-side Teams authentication and meeting creation, providing better security and user experience compared to the previous SPA approach.

## 🏗️ **Architecture**

```
Frontend (React) → Supabase Edge Functions → Microsoft Graph API
                ↓
            Database (user_integrations table)
```

### **Key Benefits:**
- ✅ **More Secure**: Tokens stored server-side, never exposed to frontend
- ✅ **Better UX**: No popup windows or CORS issues
- ✅ **Simpler Flow**: Direct redirects instead of popup handling
- ✅ **Production Ready**: Proper token refresh and error handling

## 📁 **File Structure**

### **Backend (Supabase Edge Functions)**
```
supabase/
├── functions/
│   ├── teams-auth/
│   │   └── index.ts          # OAuth flow handler
│   └── teams-meetings/
│       └── index.ts          # Meeting creation API
└── migrations/
    └── 20241208_create_user_integrations.sql
```

### **Frontend**
```
src/
├── services/
│   └── teamsServiceBackend.ts    # Backend API client
├── hooks/
│   └── useTeamsIntegration.ts    # React hook (updated)
└── components/meetings/
    └── TeamsMeetingManager.tsx   # UI component (updated)
```

## 🔧 **Setup Instructions**

### **1. Deploy Supabase Edge Functions**

```bash
# Deploy the functions
supabase functions deploy teams-auth
supabase functions deploy teams-meetings

# Set environment variables
supabase secrets set AZURE_CLIENT_ID=your_client_id
supabase secrets set AZURE_CLIENT_SECRET=your_client_secret
supabase secrets set AZURE_TENANT_ID=your_tenant_id
supabase secrets set FRONTEND_URL=http://localhost:8080
```

### **2. Run Database Migration**

```bash
supabase db push
```

### **3. Update Azure App Registration**

**Redirect URI:** Change from frontend callback to:
```
https://your-supabase-project.supabase.co/functions/v1/teams-auth/callback
```

**Platform:** Keep as "Web" (not SPA)

### **4. Environment Variables**

No frontend environment variables needed! All sensitive data is now server-side.

## 🔄 **Authentication Flow**

### **Step 1: Initiate Authentication**
```typescript
// User clicks "Connect to Teams"
const success = await teamsService.authenticate(currentUrl);
// User is redirected to Microsoft OAuth
```

### **Step 2: OAuth Callback**
```
Microsoft → Supabase Edge Function → Exchange code for tokens → Store in database → Redirect back to frontend
```

### **Step 3: Success Handling**
```typescript
// Frontend detects success from URL parameters
useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('teams_success') === 'true') {
    setIsAuthenticated(true);
    // Show success message
  }
}, []);
```

## 📊 **Database Schema**

### **user_integrations Table**
```sql
CREATE TABLE user_integrations (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    provider TEXT CHECK (provider IN ('microsoft_teams', 'google_meet', 'zoom')),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMPTZ NOT NULL,
    scope TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, provider)
);
```

## 🔌 **API Endpoints**

### **Teams Authentication**
```
GET /functions/v1/teams-auth?action=initiate&returnUrl=...
GET /functions/v1/teams-auth/callback?code=...&state=...
```

### **Teams Meetings**
```
GET  /functions/v1/teams-meetings          # Check auth status
POST /functions/v1/teams-meetings          # Create meeting
```

## 💻 **Usage Examples**

### **Check Authentication Status**
```typescript
const isAuthenticated = await teamsService.isAuthenticated();
```

### **Authenticate User**
```typescript
const success = await teamsService.authenticate('/dashboard');
// User will be redirected to Microsoft, then back to /dashboard
```

### **Create Meeting**
```typescript
const success = await teamsService.createAndStoreMeeting(sessionId, {
  subject: 'Math Tutoring Session',
  startTime: '2024-12-08T10:00:00Z',
  endTime: '2024-12-08T11:00:00Z',
  participants: ['<EMAIL>', '<EMAIL>'],
});
```

## 🔒 **Security Features**

### **Token Management**
- ✅ Access tokens stored server-side only
- ✅ Automatic token refresh
- ✅ Secure token exchange
- ✅ RLS policies on user_integrations table

### **Authentication**
- ✅ User must be authenticated with Supabase
- ✅ Users can only access their own integrations
- ✅ Admin access for management

## 🚨 **Error Handling**

### **Frontend Error Detection**
```typescript
useEffect(() => {
  const teamsError = urlParams.get('teams_error');
  if (teamsError) {
    toast({
      title: "Authentication Failed",
      description: `Failed to connect to Teams: ${teamsError}`,
      variant: "destructive",
    });
  }
}, []);
```

### **Common Error Codes**
- `missing_parameters` - OAuth callback missing code/state
- `token_exchange_failed` - Failed to exchange code for tokens
- `database_error` - Failed to store tokens
- `processing_failed` - General processing error

## 🧪 **Testing**

### **1. Test Authentication**
```typescript
// In your component
const { authenticate, isAuthenticated } = useTeamsIntegration();

// Click button to test
<Button onClick={() => authenticate()}>
  Connect to Teams
</Button>
```

### **2. Test Meeting Creation**
```typescript
const { createAndStoreMeeting } = useTeamsIntegration();

const testMeeting = async () => {
  const success = await createAndStoreMeeting('test-session-id', {
    subject: 'Test Meeting',
    startTime: new Date(Date.now() + 3600000).toISOString(),
    endTime: new Date(Date.now() + 7200000).toISOString(),
    participants: ['<EMAIL>'],
  });
  console.log('Meeting created:', success);
};
```

## 🔄 **Migration from SPA**

### **Removed Files**
- ❌ `src/services/teamsService.ts`
- ❌ `src/pages/auth/TeamsCallback.tsx`
- ❌ `src/pages/TestTeamsIntegration.tsx`
- ❌ MSAL packages from package.json

### **Updated Files**
- ✅ `src/hooks/useTeamsIntegration.ts` - Uses backend service
- ✅ `src/components/meetings/TeamsMeetingManager.tsx` - Updated imports
- ✅ `src/config/environment.ts` - Simplified configuration
- ✅ `src/App.tsx` - Removed old routes

## 🎉 **Success Indicators**

You'll know everything is working when:
- ✅ Authentication redirects to Microsoft and back successfully
- ✅ URL shows `?teams_success=true` after authentication
- ✅ Meeting creation generates valid Teams URLs
- ✅ Tokens are stored in `user_integrations` table
- ✅ No CORS or popup-related errors

## 🔧 **Troubleshooting**

### **Authentication Issues**
1. Check Azure redirect URI matches Supabase function URL
2. Verify environment variables are set in Supabase
3. Check Supabase function logs for errors

### **Meeting Creation Issues**
1. Verify user is authenticated first
2. Check API permissions in Azure
3. Ensure tokens haven't expired

### **Database Issues**
1. Run the migration to create `user_integrations` table
2. Check RLS policies are enabled
3. Verify user has proper permissions

---

**Next Steps:** Deploy the Edge Functions and test the authentication flow!
